name: Build and deploy to Demo

on:
  push:
    branches: ['master', 'dev']

jobs:
  build:
    name: Build node apps
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Call build workflow
        uses: ./.github/actions/build-action

  docker:
    name: Build docker image and publish
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Call docker workflow
        uses: ./.github/actions/docker-action
        with:
          dockerfile: apps/coverapp/Dockerfile
          docker_username: ${{ secrets.DOCKER_USERNAME }}
          docker_password: ${{ secrets.DOCKER_PASSWORD }}
          image_name: zsmartex/coverapp
          image_tag: latest
          storage: true
          redis_host: redis
          redis_port: "6379"

      - name: Call docker workflow
        uses: ./.github/actions/docker-action
        with:
          dockerfile: apps/castle/Dockerfile
          docker_username: ${{ secrets.DOCKER_USERNAME }}
          docker_password: ${{ secrets.DOCKER_PASSWORD }}
          image_name: zsmartex/castle
          image_tag: latest
          storage: true
          redis_host: redis
          redis_port: "6379"

  deploy:
    name: Deploy to demo server
    needs: docker
    runs-on: ubuntu-latest
    steps:
    - name: executing remote ssh commands using password
      uses: appleboy/ssh-action@master
      with:
        host: ${{ secrets.SSH_HOST }}
        port: ${{ secrets.SSH_PORT }}
        username: ${{ secrets.SSH_USERNAME }}
        password: ${{ secrets.SSH_PASSWORD }}
        script:  |
          eval "$(rbenv init - zsh)"
          cd ~/z-dax
          docker pull zsmartex/coverapp
          docker pull zsmartex/castle
          rake render:config service:app
