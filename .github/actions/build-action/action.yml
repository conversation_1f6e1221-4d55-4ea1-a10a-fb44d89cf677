name: Build node apps
description: 'Runs a composite step action'

runs:
  using: "composite"
  steps:
    - uses: pnpm/action-setup@v2
      name: Install pnpm
      id: pnpm-install
      with:
        version: 7
        run_install: false

    - name: Get pnpm store directory
      id: pnpm-cache
      shell: bash
      run: |
        echo "STORE_PATH=$(pnpm store path)" >> $GITHUB_OUTPUT

    - uses: actions/cache@v3
      name: Setup pnpm cache
      with:
        path: ${{ steps.pnpm-cache.outputs.STORE_PATH }}
        key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
        restore-keys: |
          ${{ runner.os }}-pnpm-store-

    - name: Install dependencies
      shell: bash
      run: pnpm install

    - name: Build apps
      shell: bash
      run: pnpm run build
