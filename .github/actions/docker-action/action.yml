name: Build docker image and publish
description: 'Runs a composite step action'

inputs:
  dockerfile:
    required: true
    description: "Dockerfile path"
  docker_username:
    required: true
    description: "Dockerhub username for publish to dockerhub"
  docker_password:
    required: true
    description: "Dockerhub password"
  image_name:
    required: true
    description: "Image name"
  image_tag:
    required: true
    description: "Image tag"
  storage:
    required: true
    default: "true"
    description: "Storage"
  redis_host:
    required: true
    default: localhost
    description: "Redis host"
  redis_port:
    required: true
    default: "6379"
    description: "Redis port"

outputs:
  random:
    description: "Docker image name with tag"
    value: ${{ inputs.image_name }}:${{ inputs.image_tag }}

runs:
  using: "composite"
  steps:
    # Login against a Docker registry except on PR
    # https://github.com/docker/login-action
    - name: Log into Dockerhub registry
      uses: docker/login-action@v2
      with:
        username: ${{ inputs.docker_username }}
        password: ${{ inputs.docker_password }}

    - name: Extract Docker metadata
      id: meta
      uses: docker/metadata-action@v4
      with:
        images: ${{ inputs.image_name }}:${{ inputs.image_tag }}

    - name: Build and push Docker image
      uses: docker/build-push-action@v4
      with:
        context: .
        file: ${{ inputs.dockerfile }}
        build-args: |
          STORAGE=${{ inputs.storage }}
          REDIS_HOST=${{ inputs.redis_host }}
          REDIS_PORT=${{ inputs.redis_port }}
        push: true
        tags: ${{ inputs.image_name }}:${{ inputs.image_tag }}
        labels: ${{ steps.meta.outputs.labels }}
