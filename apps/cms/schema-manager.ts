import "dotenv/config";
import fs from "fs/promises";
import path from "path";
import {
    createDirectus,
    rest,
    schemaSnapshot,
    staticToken,
    schemaApply,
    schemaDiff,
    type DirectusClient,
    type RestClient,
} from "@directus/sdk";

type Environment = "staging" | "production";

type DirectusInstance = DirectusClient<any> & RestClient<any>;

type SchemaConfig = {
    staging: {
        url: string;
        token: string;
    };
    production: {
        url: string;
        token: string;
    };
    outputDir: string;
};

type SchemaSnapshot = {
    version: number;
    directus: string;
    vendor: string;
    collections: any[];
    fields: any[];
    relations: any[];
};

class SchemaManager {
    private config: SchemaConfig;
    private stagingClient: DirectusInstance;
    private productionClient: DirectusInstance;

    constructor() {
        this.config = this.validateAndLoadConfig();
        this.stagingClient = this.createClient("staging");
        this.productionClient = this.createClient("production");
    }

    private validateAndLoadConfig(): SchemaConfig {
        const requiredEnvVars = [
            "LOCAL_URL",
            "LOCAL_ADMIN_TOKEN",
            "PROD_URL",
            "PROD_ADMIN_TOKEN",
        ];

        const missingVars = requiredEnvVars.filter(
            (varName) => !process.env[varName]
        );
        if (missingVars.length > 0) {
            throw new Error(
                `Missing required environment variables: ${missingVars.join(
                    ", "
                )}`
            );
        }

        return {
            staging: {
                url: process.env.LOCAL_URL!,
                token: process.env.LOCAL_ADMIN_TOKEN!,
            },
            production: {
                url: process.env.PROD_URL!,
                token: process.env.PROD_ADMIN_TOKEN!,
            },
            outputDir: path.join(process.cwd(), "schema-snapshots"),
        };
    }

    private createClient(environment: Environment): DirectusInstance {
        const config = this.config[environment];
        return createDirectus(config.url)
            .with(staticToken(config.token))
            .with(rest());
    }

    private async ensureOutputDirectory(): Promise<void> {
        try {
            await fs.access(this.config.outputDir);
        } catch {
            await fs.mkdir(this.config.outputDir, { recursive: true });
            console.log(
                `📁 Created output directory: ${this.config.outputDir}`
            );
        }
    }

    private generateTimestamp(): string {
        return new Date().toISOString().replace(/[:.]/g, "-").slice(0, -5);
    }

    private async saveSchemaSnapshot(
        snapshot: SchemaSnapshot,
        environment: Environment
    ): Promise<string> {
        await this.ensureOutputDirectory();

        const timestamp = this.generateTimestamp();
        const filename = `schema-${environment}-${timestamp}.json`;
        const filepath = path.join(this.config.outputDir, filename);

        const enhancedSnapshot = {
            ...snapshot,
            metadata: {
                environment,
                timestamp: new Date().toISOString(),
                generatedBy: "schema-manager",
                version: "1.0.0",
            },
        };

        await fs.writeFile(filepath, JSON.stringify(enhancedSnapshot, null, 2));
        console.log(`💾 Schema snapshot saved: ${filepath}`);

        return filepath;
    }

    async getStagingSchema(): Promise<SchemaSnapshot> {
        try {
            console.log("🔄 Fetching staging schema snapshot...");
            const snapshot = (await this.stagingClient.request(
                schemaSnapshot()
            )) as SchemaSnapshot;
            console.log(
                `✅ Staging schema fetched successfully (${snapshot.collections.length} collections, ${snapshot.fields.length} fields)`
            );
            return snapshot;
        } catch (error) {
            console.error("❌ Failed to fetch staging schema:", error);
            throw new Error(
                `Failed to fetch staging schema: ${
                    error instanceof Error ? error.message : "Unknown error"
                }`
            );
        }
    }

    async compareSchemas(stagingSnapshot: SchemaSnapshot): Promise<any> {
        try {
            console.log("🔍 Comparing schemas...");
            const differences = (await this.productionClient.request(
                schemaDiff(stagingSnapshot)
            )) as any;

            if (!differences || Object.keys(differences).length === 0) {
                console.log("✅ No schema differences found");
                return null;
            }

            console.log("📋 Schema differences detected:");
            if (differences.collections?.length) {
                console.log(
                    `  - Collections: ${differences.collections.length} changes`
                );
            }
            if (differences.fields?.length) {
                console.log(`  - Fields: ${differences.fields.length} changes`);
            }
            if (differences.relations?.length) {
                console.log(
                    `  - Relations: ${differences.relations.length} changes`
                );
            }

            return differences;
        } catch (error) {
            console.error("❌ Failed to compare schemas:", error);
            throw new Error(
                `Schema comparison failed: ${
                    error instanceof Error ? error.message : "Unknown error"
                }`
            );
        }
    }

    async applySchemaChanges(differences: any): Promise<void> {
        try {
            console.log("🚀 Applying schema changes to production...");
            await this.productionClient.request(schemaApply(differences));
            console.log("✅ Schema changes applied successfully to production");
        } catch (error) {
            console.error("❌ Failed to apply schema changes:", error);
            throw new Error(
                `Schema application failed: ${
                    error instanceof Error ? error.message : "Unknown error"
                }`
            );
        }
    }

    async saveSchemaChanges(differences: any): Promise<string> {
        await this.ensureOutputDirectory();

        const timestamp = this.generateTimestamp();
        const filename = `schema-diff-${timestamp}.json`;
        const filepath = path.join(this.config.outputDir, filename);

        const enhancedDiff = {
            differences,
            metadata: {
                timestamp: new Date().toISOString(),
                generatedBy: "schema-manager",
                version: "1.0.0",
            },
        };

        await fs.writeFile(filepath, JSON.stringify(enhancedDiff, null, 2));
        console.log(`💾 Schema differences saved: ${filepath}`);

        return filepath;
    }

    async syncSchemas(
        options: {
            saveSnapshot?: boolean;
            saveDifferences?: boolean;
            dryRun?: boolean;
        } = {}
    ): Promise<{
        success: boolean;
        snapshotPath?: string;
        differencesPath?: string;
        message: string;
    }> {
        const {
            saveSnapshot = true,
            saveDifferences = true,
            dryRun = false,
        } = options;

        try {
            console.log("🎯 Starting schema synchronization...");
            console.log(`📍 Mode: ${dryRun ? "DRY RUN" : "APPLY CHANGES"}`);

            // Get staging schema
            const stagingSnapshot = await this.getStagingSchema();

            // Save staging snapshot if requested
            let snapshotPath: string | undefined;
            if (saveSnapshot) {
                snapshotPath = await this.saveSchemaSnapshot(
                    stagingSnapshot,
                    "staging"
                );
            }

            // Compare schemas
            const differences = await this.compareSchemas(stagingSnapshot);

            if (!differences) {
                return {
                    success: true,
                    snapshotPath,
                    message: "No schema changes detected. Schemas are in sync.",
                };
            }

            // Save differences if requested
            let differencesPath: string | undefined;
            if (saveDifferences) {
                differencesPath = await this.saveSchemaChanges(differences);
            }

            // Apply changes if not dry run
            if (!dryRun) {
                await this.applySchemaChanges(differences);
            } else {
                console.log(
                    "🔍 DRY RUN: Schema changes would be applied in actual run"
                );
            }

            return {
                success: true,
                snapshotPath,
                differencesPath,
                message: dryRun
                    ? "Schema differences detected and saved. Run without --dry-run to apply changes."
                    : "Schema synchronization completed successfully.",
            };
        } catch (error) {
            console.error("💥 Schema synchronization failed:", error);
            return {
                success: false,
                message: `Schema synchronization failed: ${
                    error instanceof Error ? error.message : "Unknown error"
                }`,
            };
        }
    }
}

export { SchemaManager };

// CLI Usage
async function main(): Promise<void> {
    const manager = new SchemaManager();

    // Parse command line arguments
    const args = process.argv.slice(2);
    const dryRun = args.includes("--dry-run");
    const skipSnapshot = args.includes("--skip-snapshot");
    const skipDifferences = args.includes("--skip-differences");

    console.log("🔧 Directus Schema Manager");
    console.log("========================");

    const result = await manager.syncSchemas({
        saveSnapshot: !skipSnapshot,
        saveDifferences: !skipDifferences,
        dryRun,
    });

    console.log("\n" + "=".repeat(50));
    console.log(result.success ? "✅ RESULT: SUCCESS" : "❌ RESULT: FAILED");
    console.log("=".repeat(50));
    console.log(result.message);

    if (result.snapshotPath) {
        console.log(`📄 Snapshot: ${result.snapshotPath}`);
    }
    if (result.differencesPath) {
        console.log(`📋 Differences: ${result.differencesPath}`);
    }

    process.exit(result.success ? 0 : 1);
}

// Run if this file is executed directly (ES modules check)
if (import.meta.url === `file://${process.argv[1]}`) {
    main().catch((error) => {
        console.error("💥 Unhandled error:", error);
        process.exit(1);
    });
}
