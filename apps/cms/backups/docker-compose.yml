services:
    database:
        image: postgis/postgis:16-master
        platform: linux/amd64
        volumes:
            - ./data/database:/var/lib/postgresql/data
        environment:
            POSTGRES_USER: ${DB_USER}
            POSTGRES_PASSWORD: ${DB_PASSWORD}
            POSTGRES_DB: ${DB_DATABASE}
        healthcheck:
            test: ["CMD", "pg_isready", "-U", "${DB_USER}", "-d", "${DB_DATABASE}", "-h", "localhost"]
            interval: 10s
            timeout: 5s
            retries: 5
            start_interval: 5s
            start_period: 30s

    cache:
        image: redis:6
        healthcheck:
            test: ["CMD-SHELL", "[ $$(redis-cli ping) = 'PONG' ]"]
            interval: 10s
            timeout: 5s
            retries: 5
            start_interval: 5s
            start_period: 30s

    directus:
        image: directus/directus:11.7.2
        ports:
            - ${DIRECTUS_PORT}:8055
        volumes:
            - ./uploads:/directus/uploads
            - ./extensions:/directus/extensions
        depends_on:
            database:
                condition: service_healthy
            cache:
                condition: service_healthy
        environment:
            SECRET: ${DIRECTUS_SECRET}

            DB_CLIENT: "pg"
            DB_HOST: "database"
            DB_PORT: "5432"
            DB_DATABASE: ${DB_DATABASE}
            DB_USER: ${DB_USER}
            DB_PASSWORD: ${DB_PASSWORD}

            CACHE_ENABLED: ${CACHE_ENABLED}
            CACHE_AUTO_PURGE: ${CACHE_AUTO_PURGE}
            CACHE_STORE: "redis"
            REDIS: "redis://cache:6379"

            ADMIN_EMAIL: ${ADMIN_EMAIL}
            ADMIN_PASSWORD: ${ADMIN_PASSWORD}

            PUBLIC_URL: ${PUBLIC_URL}

            CORS_ENABLED: ${CORS_ENABLED}
            CORS_ORIGIN: ${CORS_ORIGIN}

            EXTENSIONS_PATH: ${EXTENSIONS_PATH}
            EXTENSIONS_AUTO_RELOAD: ${EXTENSIONS_AUTO_RELOAD}
