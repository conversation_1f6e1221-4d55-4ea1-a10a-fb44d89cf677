import{useApi as e,defineInterface as t,defineDisplay as n}from"@directus/extensions-sdk";import*as a from"vue";import{Fragment as i,defineComponent as o,Comment as r,mergeProps as l,cloneVNode as s,h as d,openBlock as u,createBlock as c,unref as p,withCtx as f,renderSlot as v,getCurrentScope as m,onScopeDispose as h,toValue as g,watch as b,computed as y,getCurrentInstance as w,ref as x,nextTick as _,onMounted as k,inject as A,provide as $,toRefs as S,withKeys as C,onUnmounted as O,createVNode as T,createCommentVNode as I,watchEffect as E,markRaw as F,createElementBlock as P,renderList as L,withModifiers as M,normalizeStyle as V,resolveComponent as j,createElementVNode as B,normalizeClass as N,toDisplayString as z,createTextVNode as R,resolveDirective as D,withDirectives as U,createSlots as K}from"vue";import{useI18n as q}from"vue-i18n";function G(e){return e.charAt(0).toUpperCase()+e.substring(1)}var H=["2D","3D","4WD","A2O","AI","API","BIOS","CC","CCTV","CCV","CD","CD-ROM","CIA","CMS","COBOL","CSS","CSV","CV","DB","DIY","DNA","DVD","E3","EIN","ESPN","FAQ","FAQs","FBI","FORTRAN","FPS","FTP","HTML","HTTP","HTTPS","ID","IP","ISO","JS","JSON","LASER","M2A","M2M","M2MM","M2O","MMORPG","NAFTA","NASA","NDA","O2A","O2M","PDF","PHP","POP","RAM","RNGR","ROM","RPG","RTFM","RTS","SCUBA","SDK","SITCOM","SKU","SMTP","SQL","SSL","SSN","SWAT","TBS","TLS","TNA","TS","TTL","TV","UI","URL","USB","UWP","VIP","W3C","WWE","WWF","WWW","WYSIWYG"],W=["a","an","the"],Y=["and","that","but","or","as","if","when","than","because","while","where","after","so","though","since","until","whether","before","although","nor","like","once","unless","now","except"],J=["about","above","across","after","against","along","among","around","at","because of","before","behind","below","beneath","beside","besides","between","beyond","but","by","concerning","despite","down","during","except","excepting","for","from","in","in front of","inside","in spite of","instead of","into","like","near","of","off","on","onto","out","outside","over","past","regarding","since","through","throughout","to","toward","under","underneath","until","up","upon","up to","with","within","without","with regard to","with respect to"],X=["2FA","4K","5K","8K","AGI","BI","ChatGPT","CTA","DateTime","FMS","GitHub","GPT","HD","IBMid","IDs","iMac","IMAX","iOS","iPad","iPhone","iPod","LDAP","LinkedIn","LLM","macOS","McDonalds","ML","MySQL","NLG","NLP","NLU","OpenAI","PDFs","PIM","PEFT","pH","PostgreSQL","SEO","TTS","UHD","UUID","XSS","YouTube"];function Q(e,t,n){const a=e.toLowerCase(),i=e.toUpperCase();for(const e of X)if(e.toLowerCase()===a)return e;return H.includes(i)?i:0===t||t===n.length-1||e.length>=4?e:J.includes(a)||Y.includes(a)||W.includes(a)?a:e}function Z(e,t){return`${e} ${t}`}function ee(e,t=new RegExp("\\s|-|_","g")){return(n=e,n.replace(/([a-z\d])([A-Z])/g,"$1_$2").replace(/([A-Z]+)([A-Z][a-z\d]+)/g,"$1_$2").toLowerCase()).split(t).map(G).map(Q).reduce(Z);var n}var te="object"==typeof global&&global&&global.Object===Object&&global,ne="object"==typeof self&&self&&self.Object===Object&&self,ae=te||ne||Function("return this")(),ie=ae.Symbol,oe=Object.prototype,re=oe.hasOwnProperty,le=oe.toString,se=ie?ie.toStringTag:void 0;var de=Object.prototype.toString;var ue=ie?ie.toStringTag:void 0;function ce(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":ue&&ue in Object(e)?function(e){var t=re.call(e,se),n=e[se];try{e[se]=void 0;var a=!0}catch(e){}var i=le.call(e);return a&&(t?e[se]=n:delete e[se]),i}(e):function(e){return de.call(e)}(e)}function pe(e){return"symbol"==typeof e||function(e){return null!=e&&"object"==typeof e}(e)&&"[object Symbol]"==ce(e)}var fe=Array.isArray,ve=ie?ie.prototype:void 0,me=ve?ve.toString:void 0;function he(e){if("string"==typeof e)return e;if(fe(e))return function(e,t){for(var n=-1,a=null==e?0:e.length,i=Array(a);++n<a;)i[n]=t(e[n],n,e);return i}(e,he)+"";if(pe(e))return me?me.call(e):"";var t=e+"";return"0"==t&&1/e==-1/0?"-0":t}function ge(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}var be,ye=ae["__core-js_shared__"],we=(be=/[^.]+$/.exec(ye&&ye.keys&&ye.keys.IE_PROTO||""))?"Symbol(src)_1."+be:"";var xe=Function.prototype.toString;var _e=/^\[object .+?Constructor\]$/,ke=Function.prototype,Ae=Object.prototype,$e=ke.toString,Se=Ae.hasOwnProperty,Ce=RegExp("^"+$e.call(Se).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function Oe(e){if(!ge(e)||(t=e,we&&we in t))return!1;var t,n=function(e){if(!ge(e))return!1;var t=ce(e);return"[object Function]"==t||"[object GeneratorFunction]"==t||"[object AsyncFunction]"==t||"[object Proxy]"==t}(e)?Ce:_e;return n.test(function(e){if(null!=e){try{return xe.call(e)}catch(e){}try{return e+""}catch(e){}}return""}(e))}function Te(e,t){var n=function(e,t){return null==e?void 0:e[t]}(e,t);return Oe(n)?n:void 0}var Ie=function(){try{var e=Te(Object,"defineProperty");return e({},"",{}),e}catch(e){}}(),Ee=/^(?:0|[1-9]\d*)$/;function Fe(e,t){var n=typeof e;return!!(t=null==t?9007199254740991:t)&&("number"==n||"symbol"!=n&&Ee.test(e))&&e>-1&&e%1==0&&e<t}function Pe(e,t){return e===t||e!=e&&t!=t}var Le=Object.prototype.hasOwnProperty;function Me(e,t,n){var a=e[t];Le.call(e,t)&&Pe(a,n)&&(void 0!==n||t in e)||function(e,t,n){"__proto__"==t&&Ie?Ie(e,t,{configurable:!0,enumerable:!0,value:n,writable:!0}):e[t]=n}(e,t,n)}var Ve=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,je=/^\w*$/;var Be=Te(Object,"create");var Ne=Object.prototype.hasOwnProperty;var ze=Object.prototype.hasOwnProperty;function Re(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var a=e[t];this.set(a[0],a[1])}}function De(e,t){for(var n=e.length;n--;)if(Pe(e[n][0],t))return n;return-1}Re.prototype.clear=function(){this.__data__=Be?Be(null):{},this.size=0},Re.prototype.delete=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t},Re.prototype.get=function(e){var t=this.__data__;if(Be){var n=t[e];return"__lodash_hash_undefined__"===n?void 0:n}return Ne.call(t,e)?t[e]:void 0},Re.prototype.has=function(e){var t=this.__data__;return Be?void 0!==t[e]:ze.call(t,e)},Re.prototype.set=function(e,t){var n=this.__data__;return this.size+=this.has(e)?0:1,n[e]=Be&&void 0===t?"__lodash_hash_undefined__":t,this};var Ue=Array.prototype.splice;function Ke(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var a=e[t];this.set(a[0],a[1])}}Ke.prototype.clear=function(){this.__data__=[],this.size=0},Ke.prototype.delete=function(e){var t=this.__data__,n=De(t,e);return!(n<0)&&(n==t.length-1?t.pop():Ue.call(t,n,1),--this.size,!0)},Ke.prototype.get=function(e){var t=this.__data__,n=De(t,e);return n<0?void 0:t[n][1]},Ke.prototype.has=function(e){return De(this.__data__,e)>-1},Ke.prototype.set=function(e,t){var n=this.__data__,a=De(n,e);return a<0?(++this.size,n.push([e,t])):n[a][1]=t,this};var qe=Te(ae,"Map");function Ge(e,t){var n,a,i=e.__data__;return("string"==(a=typeof(n=t))||"number"==a||"symbol"==a||"boolean"==a?"__proto__"!==n:null===n)?i["string"==typeof t?"string":"hash"]:i.map}function He(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var a=e[t];this.set(a[0],a[1])}}He.prototype.clear=function(){this.size=0,this.__data__={hash:new Re,map:new(qe||Ke),string:new Re}},He.prototype.delete=function(e){var t=Ge(this,e).delete(e);return this.size-=t?1:0,t},He.prototype.get=function(e){return Ge(this,e).get(e)},He.prototype.has=function(e){return Ge(this,e).has(e)},He.prototype.set=function(e,t){var n=Ge(this,e),a=n.size;return n.set(e,t),this.size+=n.size==a?0:1,this};function We(e,t){if("function"!=typeof e||null!=t&&"function"!=typeof t)throw new TypeError("Expected a function");var n=function(){var a=arguments,i=t?t.apply(this,a):a[0],o=n.cache;if(o.has(i))return o.get(i);var r=e.apply(this,a);return n.cache=o.set(i,r)||o,r};return n.cache=new(We.Cache||He),n}We.Cache=He;var Ye=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,Je=/\\(\\)?/g,Xe=function(e){var t=We(e,(function(e){return 500===n.size&&n.clear(),e})),n=t.cache;return t}((function(e){var t=[];return 46===e.charCodeAt(0)&&t.push(""),e.replace(Ye,(function(e,n,a,i){t.push(a?i.replace(Je,"$1"):n||e)})),t}));function Qe(e,t){return fe(e)?e:function(e,t){if(fe(e))return!1;var n=typeof e;return!("number"!=n&&"symbol"!=n&&"boolean"!=n&&null!=e&&!pe(e))||je.test(e)||!Ve.test(e)||null!=t&&e in Object(t)}(e,t)?[e]:Xe(function(e){return null==e?"":he(e)}(e))}function Ze(e){if("string"==typeof e||pe(e))return e;var t=e+"";return"0"==t&&1/e==-1/0?"-0":t}function et(e,t,n){var a=null==e?void 0:function(e,t){for(var n=0,a=(t=Qe(t,e)).length;null!=e&&n<a;)e=e[Ze(t[n++])];return n&&n==a?e:void 0}(e,t);return void 0===a?n:a}function tt(e,t,n){return null==e?e:function(e,t,n,a){if(!ge(e))return e;for(var i=-1,o=(t=Qe(t,e)).length,r=o-1,l=e;null!=l&&++i<o;){var s=Ze(t[i]),d=n;if("__proto__"===s||"constructor"===s||"prototype"===s)return e;if(i!=r){var u=l[s];void 0===(d=a?a(u,s,l):void 0)&&(d=ge(u)?u:Fe(t[i+1])?[]:{})}Me(l,s,d),l=l[s]}return e}(e,t,n)}function nt(e){return e?e.flatMap((e=>e.type===i?nt(e.children):[e])):[]}const at=o({name:"PrimitiveSlot",inheritAttrs:!1,setup:(e,{attrs:t,slots:n})=>()=>{if(!n.default)return null;const e=nt(n.default()),a=e.findIndex((e=>e.type!==r));if(-1===a)return e;const i=e[a];delete i.props?.ref;const o=i.props?l(t,i.props):t;t.class&&i.props?.class&&delete i.props.class;const d=s(i,o);for(const e in o)e.startsWith("on")&&(d.props||={},d.props[e]=o[e]);return 1===e.length?d:(e[a]=d,e)}}),it=["area","img","input"],ot=o({name:"Primitive",inheritAttrs:!1,props:{asChild:{type:Boolean,default:!1},as:{type:[String,Object],default:"div"}},setup(e,{attrs:t,slots:n}){const a=e.asChild?"template":e.as;return"string"==typeof a&&it.includes(a)?()=>d(a,t):"template"!==a?()=>d(e.as,t,{default:n.default}):()=>d(at,t,{default:n.default})}}),rt=o({__name:"VisuallyHidden",props:{feature:{default:"focusable"},asChild:{type:Boolean},as:{default:"span"}},setup:e=>(e,t)=>(u(),c(p(ot),{as:e.as,"as-child":e.asChild,"aria-hidden":"focusable"===e.feature?"true":void 0,"data-hidden":"fully-hidden"===e.feature?"":void 0,tabindex:"fully-hidden"===e.feature?"-1":void 0,style:{position:"absolute",border:0,width:"1px",height:"1px",padding:0,margin:"-1px",overflow:"hidden",clip:"rect(0, 0, 0, 0)",clipPath:"inset(50%)",whiteSpace:"nowrap",wordWrap:"normal"}},{default:f((()=>[v(e.$slots,"default")])),_:3},8,["as","as-child","aria-hidden","data-hidden","tabindex"]))});function lt(e){return!!m()&&(h(e),!0)}const st="undefined"!=typeof window&&"undefined"!=typeof document;"undefined"!=typeof WorkerGlobalScope&&(globalThis,WorkerGlobalScope);const dt=Object.prototype.toString,ut=()=>{},ct=pt();function pt(){var e,t;return st&&(null==(e=null==window?void 0:window.navigator)?void 0:e.userAgent)&&(/iP(?:ad|hone|od)/.test(window.navigator.userAgent)||(null==(t=null==window?void 0:window.navigator)?void 0:t.maxTouchPoints)>2&&/iPad|Macintosh/.test(null==window?void 0:window.navigator.userAgent))}function ft(e){return Array.isArray(e)?e:[e]}function vt(e,t=200,n={}){return function(e,t){return function(...n){return new Promise(((a,i)=>{Promise.resolve(e((()=>t.apply(this,n)),{fn:t,thisArg:this,args:n})).then(a).catch(i)}))}}(function(e,t={}){let n,a,i=ut;const o=e=>{clearTimeout(e),i(),i=ut};let r;return l=>{const s=g(e),d=g(t.maxWait);return n&&o(n),s<=0||void 0!==d&&d<=0?(a&&(o(a),a=null),Promise.resolve(l())):new Promise(((e,u)=>{i=t.rejectOnCancel?u:e,r=l,d&&!a&&(a=setTimeout((()=>{n&&o(n),a=null,e(r())}),d)),n=setTimeout((()=>{a&&o(a),a=null,e(l())}),s)}))}}(t,n),e)}const mt=g;const ht=st?window:void 0;function gt(e){var t;const n=g(e);return null!=(t=null==n?void 0:n.$el)?t:n}function bt(...e){const t=[],n=()=>{t.forEach((e=>e())),t.length=0},a=y((()=>{const t=ft(g(e[0])).filter((e=>null!=e));return t.every((e=>"string"!=typeof e))?t:void 0})),i=(o=()=>{var t,n;return[null!=(n=null==(t=a.value)?void 0:t.map((e=>gt(e))))?n:[ht].filter((e=>null!=e)),ft(g(a.value?e[1]:e[0])),ft(p(a.value?e[2]:e[1])),g(a.value?e[3]:e[2])]},r=([e,a,i,o])=>{if(n(),!(null==e?void 0:e.length)||!(null==a?void 0:a.length)||!(null==i?void 0:i.length))return;const r=(l=o,"[object Object]"===dt.call(l)?{...o}:o);var l;t.push(...e.flatMap((e=>a.flatMap((t=>i.map((n=>((e,t,n,a)=>(e.addEventListener(t,n,a),()=>e.removeEventListener(t,n,a)))(e,t,n,r))))))))},l={flush:"post"},b(o,r,{...l,immediate:!0}));var o,r,l;return lt(n),()=>{i(),n()}}let yt=!1;function wt(e,t,n={}){const{window:a=ht,ignore:i=[],capture:o=!0,detectIframe:r=!1}=n;if(!a)return ut;if(ct&&!yt){yt=!0;const e={passive:!0};Array.from(a.document.body.children).forEach((t=>bt(t,"click",ut,e))),bt(a.document.documentElement,"click",ut,e)}let l=!0;const s=e=>g(i).some((t=>{if("string"==typeof t)return Array.from(a.document.querySelectorAll(t)).some((t=>t===e.target||e.composedPath().includes(t)));{const n=gt(t);return n&&(e.target===n||e.composedPath().includes(n))}}));const d=n=>{const a=gt(e);null!=n.target&&(a instanceof Element||!function(e){const t=g(e);return t&&16===t.$.subTree.shapeFlag}(e)||!function(e,t){const n=g(e),a=n.$.subTree&&n.$.subTree.children;return!(null==a||!Array.isArray(a))&&a.some((e=>e.el===t.target||t.composedPath().includes(e.el)))}(e,n))&&a&&a!==n.target&&!n.composedPath().includes(a)&&(0===n.detail&&(l=!s(n)),l?t(n):l=!0)};let u=!1;const c=[bt(a,"click",(e=>{u||(u=!0,setTimeout((()=>{u=!1}),0),d(e))}),{passive:!0,capture:o}),bt(a,"pointerdown",(t=>{const n=gt(e);l=!s(t)&&!(!n||t.composedPath().includes(n))}),{passive:!0}),r&&bt(a,"blur",(n=>{setTimeout((()=>{var i;const o=gt(e);"IFRAME"!==(null==(i=a.document.activeElement)?void 0:i.tagName)||(null==o?void 0:o.contains(a.document.activeElement))||t(n)}),0)}),{passive:!0})].filter(Boolean);return()=>c.forEach((e=>e()))}function xt(e){const t=function(){const e=x(!1),t=w();return t&&k((()=>{e.value=!0}),t),e}();return y((()=>(t.value,Boolean(e()))))}function _t(...e){let t,n,a={};3===e.length?(t=e[0],n=e[1],a=e[2]):2===e.length?"object"==typeof e[1]?(t=!0,n=e[0],a=e[1]):(t=e[0],n=e[1]):(t=!0,n=e[0]);const{target:i=ht,eventName:o="keydown",passive:r=!1,dedupe:l=!1}=a,s="function"==typeof(d=t)?d:"string"==typeof d?e=>e.key===d:Array.isArray(d)?e=>d.includes(e.key):()=>!0;var d;return bt(i,o,(e=>{e.repeat&&g(l)||s(e)&&n(e)}),r)}function kt(e,t,n,a={}){var i,o,r;const{clone:l=!1,passive:s=!1,eventName:d,deep:u=!1,defaultValue:c,shouldEmit:p}=a,f=w(),v=n||(null==f?void 0:f.emit)||(null==(i=null==f?void 0:f.$emit)?void 0:i.bind(f))||(null==(r=null==(o=null==f?void 0:f.proxy)?void 0:o.$emit)?void 0:r.bind(null==f?void 0:f.proxy));let m=d;t||(t="modelValue"),m=m||`update:${t.toString()}`;const h=e=>{return l?"function"==typeof l?l(e):(t=e,JSON.parse(JSON.stringify(t))):e;var t},g=()=>void 0!==e[t]?h(e[t]):c,k=e=>{p?p(e)&&v(m,e):v(m,e)};if(s){const n=g(),a=x(n);let i=!1;return b((()=>e[t]),(e=>{i||(i=!0,a.value=h(e),_((()=>i=!1)))})),b(a,(n=>{i||n===e[t]&&!u||k(n)}),{deep:u}),a}return y({get:()=>g(),set(e){k(e)}})}function At(e,t){const n=Symbol("string"!=typeof e||t?t:`${e}Context`);return[t=>{const a=A(n,t);if(a)return a;if(null===a)return a;throw new Error(`Injection \`${n.toString()}\` not found. Component must be used within ${Array.isArray(e)?`one of the following components: ${e.join(", ")}`:`\`${e}\``}`)},e=>($(n,e),e)]}function $t(e){return"string"==typeof e?`'${e}'`:(new St).serialize(e)}const St=function(){class e{#e=new Map;compare(e,t){const n=typeof e,a=typeof t;return"string"===n&&"string"===a?e.localeCompare(t):"number"===n&&"number"===a?e-t:String.prototype.localeCompare.call(this.serialize(e,!0),this.serialize(t,!0))}serialize(e,t){if(null===e)return"null";switch(typeof e){case"string":return t?e:`'${e}'`;case"bigint":return`${e}n`;case"object":return this.$object(e);case"function":return this.$function(e)}return String(e)}serializeObject(e){const t=Object.prototype.toString.call(e);if("[object Object]"!==t)return this.serializeBuiltInType(t.length<10?`unknown:${t}`:t.slice(8,-1),e);const n=e.constructor,a=n===Object||void 0===n?"":n.name;if(""!==a&&globalThis[a]===n)return this.serializeBuiltInType(a,e);if("function"==typeof e.toJSON){const t=e.toJSON();return a+(null!==t&&"object"==typeof t?this.$object(t):`(${this.serialize(t)})`)}return this.serializeObjectEntries(a,Object.entries(e))}serializeBuiltInType(e,t){const n=this["$"+e];if(n)return n.call(this,t);if("function"==typeof t?.entries)return this.serializeObjectEntries(e,t.entries());throw new Error(`Cannot serialize ${e}`)}serializeObjectEntries(e,t){const n=Array.from(t).sort(((e,t)=>this.compare(e[0],t[0])));let a=`${e}{`;for(let e=0;e<n.length;e++){const[t,i]=n[e];a+=`${this.serialize(t,!0)}:${this.serialize(i)}`,e<n.length-1&&(a+=",")}return a+"}"}$object(e){let t=this.#e.get(e);return void 0===t&&(this.#e.set(e,`#${this.#e.size}`),t=this.serializeObject(e),this.#e.set(e,t)),t}$function(e){const t=Function.prototype.toString.call(e);return"[native code] }"===t.slice(-15)?`${e.name||""}()[native]`:`${e.name}(${e.length})${t.replace(/\s*\n\s*/g,"")}`}$Array(e){let t="[";for(let n=0;n<e.length;n++)t+=this.serialize(e[n]),n<e.length-1&&(t+=",");return t+"]"}$Date(e){try{return`Date(${e.toISOString()})`}catch{return"Date(null)"}}$ArrayBuffer(e){return`ArrayBuffer[${new Uint8Array(e).join(",")}]`}$Set(e){return`Set${this.$Array(Array.from(e).sort(((e,t)=>this.compare(e,t))))}`}$Map(e){return this.serializeObjectEntries("Map",e.entries())}}for(const t of["Error","RegExp","URL"])e.prototype["$"+t]=function(e){return`${t}(${e})`};for(const t of["Int8Array","Uint8Array","Uint8ClampedArray","Int16Array","Uint16Array","Int32Array","Uint32Array","Float32Array","Float64Array"])e.prototype["$"+t]=function(e){return`${t}[${e.join(",")}]`};for(const t of["BigInt64Array","BigUint64Array"])e.prototype["$"+t]=function(e){return`${t}[${e.join("n,")}${e.length>0?"n":""}]`};return e}();function Ct(e,t){return e===t||$t(e)===$t(t)}function Ot({type:e,defaultValue:t,modelValue:n}){return e||function({type:e,defaultValue:t,modelValue:n}){const a=n||t;return void 0!==n||void 0!==t?Array.isArray(a)?"multiple":"single":e??"single"}({type:e,defaultValue:t,modelValue:n})}function Tt({type:e,defaultValue:t}){return void 0!==t?t:"single"===e?void 0:[]}function It(e,t){const n=y((()=>Ot(e))),a=kt(e,"modelValue",t,{defaultValue:Tt(e),passive:void 0===e.modelValue,deep:!0});const i=y((()=>"single"===n.value));return{modelValue:a,changeModelValue:function(e){if("single"===n.value)a.value=Ct(e,a.value)?void 0:e;else{const n=Array.isArray(a.value)?[...a.value||[]]:[a.value].filter(Boolean);if(i=e,null!=(t=n)&&(Array.isArray(t)?t.some((e=>Ct(e,i))):Ct(t,i))){const t=n.findIndex((t=>Ct(t,e)));n.splice(t,1)}else n.push(e);a.value=n}var t,i},isSingle:i}}const[Et,Ft]=At("ConfigProvider");function Pt(e){const t=Et({dir:x("ltr")});return y((()=>e?.value||t.dir?.value||"ltr"))}function Lt(){const e=w(),t=x(),n=y((()=>["#text","#comment"].includes(t.value?.$el.nodeName)?t.value?.$el.nextElementSibling:gt(t))),a=Object.assign({},e.exposed),i={};for(const t in e.props)Object.defineProperty(i,t,{enumerable:!0,configurable:!0,get:()=>e.props[t]});if(Object.keys(a).length>0)for(const e in a)Object.defineProperty(i,e,{enumerable:!0,configurable:!0,get:()=>a[e]});return Object.defineProperty(i,"$el",{enumerable:!0,configurable:!0,get:()=>e.vnode.el}),e.exposed=i,{forwardRef:function(n){t.value=n,n&&(Object.defineProperty(i,"$el",{enumerable:!0,configurable:!0,get:()=>n instanceof Element?n:n.$el}),e.exposed=i)},currentRef:t,currentElement:n}}const[Mt,Vt]=At("AccordionRoot"),jt=o({__name:"AccordionRoot",props:{collapsible:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},dir:{},orientation:{default:"vertical"},unmountOnHide:{type:Boolean,default:!0},asChild:{type:Boolean},as:{},type:{},modelValue:{},defaultValue:{}},emits:["update:modelValue"],setup(e,{emit:t}){const n=e,a=t,{dir:i,disabled:o,unmountOnHide:r}=S(n),l=Pt(i),{modelValue:s,changeModelValue:d,isSingle:m}=It(n,a),{forwardRef:h,currentElement:g}=Lt();return Vt({disabled:o,direction:l,orientation:n.orientation,parentElement:g,isSingle:m,collapsible:n.collapsible,modelValue:s,changeModelValue:d,unmountOnHide:r}),(e,t)=>(u(),c(p(ot),{ref:p(h),"as-child":e.asChild,as:e.as},{default:f((()=>[v(e.$slots,"default",{modelValue:p(s)})])),_:3},8,["as-child","as"]))}}),[Bt,Nt]=At("CollapsibleRoot"),zt=o({__name:"CollapsibleRoot",props:{defaultOpen:{type:Boolean,default:!1},open:{type:Boolean,default:void 0},disabled:{type:Boolean},unmountOnHide:{type:Boolean,default:!0},asChild:{type:Boolean},as:{}},emits:["update:open"],setup(e,{expose:t,emit:n}){const a=e,i=kt(a,"open",n,{defaultValue:a.defaultOpen,passive:void 0===a.open}),{disabled:o,unmountOnHide:r}=S(a);return Nt({contentId:"",disabled:o,open:i,unmountOnHide:r,onOpenToggle:()=>{i.value=!i.value}}),t({open:i}),Lt(),(e,t)=>(u(),c(p(ot),{as:e.as,"as-child":a.asChild,"data-state":p(i)?"open":"closed","data-disabled":p(o)?"":void 0},{default:f((()=>[v(e.$slots,"default",{open:p(i)})])),_:3},8,["as","as-child","data-state","data-disabled"]))}}),Rt=["INPUT","TEXTAREA"];function Dt(e,t,n,a={}){if(!t||a.enableIgnoredElement&&Rt.includes(t.nodeName))return null;const{arrowKeyOptions:i="both",attributeName:o="[data-reka-collection-item]",itemsArray:r=[],loop:l=!0,dir:s="ltr",preventScroll:d=!0,focus:u=!1}=a,[c,p,f,v,m,h]=["ArrowRight"===e.key,"ArrowLeft"===e.key,"ArrowUp"===e.key,"ArrowDown"===e.key,"Home"===e.key,"End"===e.key],g=f||v,b=c||p;if(!m&&!h&&(!g&&!b||"vertical"===i&&b||"horizontal"===i&&g))return null;const y=n?Array.from(n.querySelectorAll(o)):r;if(!y.length)return null;d&&e.preventDefault();let w=null;if(b||g){w=Ut(y,t,{goForward:g?v:"ltr"===s?c:p,loop:l})}else m?w=y.at(0)||null:h&&(w=y.at(-1)||null);return u&&w?.focus(),w}function Ut(e,t,n,a=e.length){if(0==--a)return null;const i=e.indexOf(t),o=n.goForward?i+1:i-1;if(!n.loop&&(o<0||o>=e.length))return null;const r=e[(o+e.length)%e.length];if(!r)return null;return r.hasAttribute("disabled")&&"false"!==r.getAttribute("disabled")?Ut(e,r,n,a):r}const[Kt,qt]=At("AccordionItem"),Gt=o({__name:"AccordionItem",props:{disabled:{type:Boolean},value:{},unmountOnHide:{type:Boolean},asChild:{type:Boolean},as:{}},setup(e,{expose:t}){const n=e,a=Mt(),i=y((()=>a.isSingle.value?n.value===a.modelValue.value:Array.isArray(a.modelValue.value)&&a.modelValue.value.includes(n.value))),o=y((()=>a.disabled.value||n.disabled)),r=y((()=>o.value?"":void 0)),l=y((()=>i.value?"open":"closed"));t({open:i,dataDisabled:r});const{currentRef:s,currentElement:d}=Lt();function m(e){const t=e.target;if(-1===Array.from(a.parentElement.value?.querySelectorAll("[data-reka-collection-item]")??[]).findIndex((e=>e===t)))return null;Dt(e,d.value,a.parentElement.value,{arrowKeyOptions:a.orientation,dir:a.direction.value,focus:!0})}return qt({open:i,dataState:l,disabled:o,dataDisabled:r,triggerId:"",currentRef:s,currentElement:d,value:y((()=>n.value))}),(e,t)=>(u(),c(p(zt),{"data-orientation":p(a).orientation,"data-disabled":r.value,"data-state":l.value,disabled:o.value,open:i.value,as:n.as,"as-child":n.asChild,"unmount-on-hide":p(a).unmountOnHide.value,onKeydown:C(m,["up","down","left","right","home","end"])},{default:f((()=>[v(e.$slots,"default",{open:i.value})])),_:3},8,["data-orientation","data-disabled","data-state","disabled","open","as","as-child","unmount-on-hide"]))}});let Ht=0;function Wt(e,t="reka"){if(e)return e;const n=Et({useId:void 0});return a.useId?`${t}-${a.useId()}`:n.useId?`${t}-${n.useId()}`:`${t}-${++Ht}`}function Yt(e,t){const n=x(e);return{state:n,dispatch:e=>{n.value=function(e){return t[n.value][e]??n.value}(e)}}}function Jt(e){return e&&getComputedStyle(e).animationName||"none"}const Xt=o({name:"Presence",props:{present:{type:Boolean,required:!0},forceMount:{type:Boolean}},slots:{},setup(e,{slots:t,expose:n}){const{present:a,forceMount:i}=S(e),o=x(),{isPresent:r}=function(e,t){const n=x({}),a=x("none"),i=x(e),o=e.value?"mounted":"unmounted";let r;const l=t.value?.ownerDocument.defaultView??ht,{state:s,dispatch:d}=Yt(o,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}}),u=e=>{if(st){const n=new CustomEvent(e,{bubbles:!1,cancelable:!1});t.value?.dispatchEvent(n)}};b(e,(async(e,i)=>{const o=i!==e;if(await _(),o){const o=a.value,r=Jt(t.value);e?(d("MOUNT"),u("enter"),"none"===r&&u("after-enter")):"none"===r||"undefined"===r||"none"===n.value?.display?(d("UNMOUNT"),u("leave"),u("after-leave")):i&&o!==r?(d("ANIMATION_OUT"),u("leave")):(d("UNMOUNT"),u("after-leave"))}}),{immediate:!0});const c=e=>{const n=Jt(t.value),a=n.includes(e.animationName),o="mounted"===s.value?"enter":"leave";if(e.target===t.value&&a&&(u(`after-${o}`),d("ANIMATION_END"),!i.value)){const e=t.value.style.animationFillMode;t.value.style.animationFillMode="forwards",r=l?.setTimeout((()=>{"forwards"===t.value?.style.animationFillMode&&(t.value.style.animationFillMode=e)}))}e.target===t.value&&"none"===n&&d("ANIMATION_END")},p=e=>{e.target===t.value&&(a.value=Jt(t.value))},f=b(t,((e,t)=>{e?(n.value=getComputedStyle(e),e.addEventListener("animationstart",p),e.addEventListener("animationcancel",c),e.addEventListener("animationend",c)):(d("ANIMATION_END"),void 0!==r&&l?.clearTimeout(r),t?.removeEventListener("animationstart",p),t?.removeEventListener("animationcancel",c),t?.removeEventListener("animationend",c))}),{immediate:!0}),v=b(s,(()=>{const e=Jt(t.value);a.value="mounted"===s.value?e:"none"}));return O((()=>{f(),v()})),{isPresent:y((()=>["mounted","unmountSuspended"].includes(s.value)))}}(a,o);n({present:r});let l=t.default({present:r.value});l=nt(l||[]);const s=w();if(l&&l?.length>1){const e=s?.parent?.type.name?`<${s.parent.type.name} />`:"component";throw new Error([`Detected an invalid children for \`${e}\` for  \`Presence\` component.`,"","Note: Presence works similarly to `v-if` directly, but it waits for animation/transition to finished before unmounting. So it expect only one direct child of valid VNode type.","You can apply a few solutions:",["Provide a single child element so that `presence` directive attach correctly.","Ensure the first child is an actual element instead of a raw text node or comment node."].map((e=>`  - ${e}`)).join("\n")].join("\n"))}return()=>i.value||a.value||r.value?d(t.default({present:r.value})[0],{ref:e=>{const t=gt(e);return void 0===t?.hasAttribute||(t?.hasAttribute("data-reka-popper-content-wrapper")?o.value=t.firstElementChild:o.value=t),t}}):null}}),Qt=o({inheritAttrs:!1,__name:"CollapsibleContent",props:{forceMount:{type:Boolean},asChild:{type:Boolean},as:{}},emits:["contentFound"],setup(e,{emit:t}){const n=e,a=t,i=Bt();i.contentId||=Wt(void 0,"reka-collapsible-content");const o=x(),{forwardRef:r,currentElement:s}=Lt(),d=x(0),m=x(0),h=y((()=>i.open.value)),g=x(h.value),w=x();b((()=>[h.value,o.value?.present]),(async()=>{await _();const e=s.value;if(!e)return;w.value=w.value||{transitionDuration:e.style.transitionDuration,animationName:e.style.animationName},e.style.transitionDuration="0s",e.style.animationName="none";const t=e.getBoundingClientRect();m.value=t.height,d.value=t.width,g.value||(e.style.transitionDuration=w.value.transitionDuration,e.style.animationName=w.value.animationName)}),{immediate:!0});const A=y((()=>g.value&&i.open.value));return k((()=>{requestAnimationFrame((()=>{g.value=!1}))})),bt(s,"beforematch",(e=>{requestAnimationFrame((()=>{i.onOpenToggle(),a("contentFound")}))})),(e,t)=>(u(),c(p(Xt),{ref_key:"presentRef",ref:o,present:e.forceMount||p(i).open.value,"force-mount":!0},{default:f((({present:t})=>[T(p(ot),l(e.$attrs,{id:p(i).contentId,ref:p(r),"as-child":n.asChild,as:e.as,hidden:t?void 0:p(i).unmountOnHide.value?"":"until-found","data-state":A.value?void 0:p(i).open.value?"open":"closed","data-disabled":p(i).disabled?.value?"":void 0,style:{"--reka-collapsible-content-height":`${m.value}px`,"--reka-collapsible-content-width":`${d.value}px`}}),{default:f((()=>[!p(i).unmountOnHide.value||t?v(e.$slots,"default",{key:0}):I("",!0)])),_:2},1040,["id","as-child","as","hidden","data-state","data-disabled","style"])])),_:3},8,["present"]))}}),Zt=o({__name:"AccordionContent",props:{forceMount:{type:Boolean},asChild:{type:Boolean},as:{}},setup(e){const t=e,n=Mt(),a=Kt();return Lt(),(e,i)=>(u(),c(p(Qt),{role:"region","as-child":t.asChild,as:e.as,"force-mount":t.forceMount,"aria-labelledby":p(a).triggerId,"data-state":p(a).dataState.value,"data-disabled":p(a).dataDisabled.value,"data-orientation":p(n).orientation,style:{"--reka-accordion-content-width":"var(--reka-collapsible-content-width)","--reka-accordion-content-height":"var(--reka-collapsible-content-height)"},onContentFound:i[0]||(i[0]=e=>p(n).changeModelValue(p(a).value.value))},{default:f((()=>[v(e.$slots,"default")])),_:3},8,["as-child","as","force-mount","aria-labelledby","data-state","data-disabled","data-orientation"]))}}),en=o({__name:"AccordionHeader",props:{asChild:{type:Boolean},as:{default:"h3"}},setup(e){const t=e,n=Mt(),a=Kt();return Lt(),(e,i)=>(u(),c(p(ot),{as:t.as,"as-child":t.asChild,"data-orientation":p(n).orientation,"data-state":p(a).dataState.value,"data-disabled":p(a).dataDisabled.value},{default:f((()=>[v(e.$slots,"default")])),_:3},8,["as","as-child","data-orientation","data-state","data-disabled"]))}}),tn=o({__name:"CollapsibleTrigger",props:{asChild:{type:Boolean},as:{default:"button"}},setup(e){const t=e;Lt();const n=Bt();return(e,a)=>(u(),c(p(ot),{type:"button"===e.as?"button":void 0,as:e.as,"as-child":t.asChild,"aria-controls":p(n).contentId,"aria-expanded":p(n).open.value,"data-state":p(n).open.value?"open":"closed","data-disabled":p(n).disabled?.value?"":void 0,disabled:p(n).disabled?.value,onClick:p(n).onOpenToggle},{default:f((()=>[v(e.$slots,"default")])),_:3},8,["type","as","as-child","aria-controls","aria-expanded","data-state","data-disabled","disabled","onClick"]))}}),nn=o({__name:"AccordionTrigger",props:{asChild:{type:Boolean},as:{}},setup(e){const t=e,n=Mt(),a=Kt();function i(){const e=n.isSingle.value&&a.open.value&&!n.collapsible;a.disabled.value||e||n.changeModelValue(a.value.value)}return a.triggerId||=Wt(void 0,"reka-accordion-trigger"),(e,o)=>(u(),c(p(tn),{id:p(a).triggerId,ref:p(a).currentRef,"data-reka-collection-item":"",as:t.as,"as-child":t.asChild,"aria-disabled":p(a).disabled.value||void 0,"aria-expanded":p(a).open.value||!1,"data-disabled":p(a).dataDisabled.value,"data-orientation":p(n).orientation,"data-state":p(a).dataState.value,disabled:p(a).disabled.value,onClick:i},{default:f((()=>[v(e.$slots,"default")])),_:3},8,["id","as","as-child","aria-disabled","aria-expanded","data-disabled","data-orientation","data-state","disabled"]))}});function an(){let e=document.activeElement;if(null==e)return null;for(;null!=e&&null!=e.shadowRoot&&null!=e.shadowRoot.activeElement;)e=e.shadowRoot.activeElement;return e}function on(){const e=x(),t=y((()=>["#text","#comment"].includes(e.value?.$el.nodeName)?e.value?.$el.nextElementSibling:gt(e)));return{primitiveElement:e,currentElement:t}}function rn(e){return y((()=>!mt(e)||Boolean(gt(e)?.closest("form"))))}const ln={bubbles:!1,cancelable:!0},sn={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function dn(e,t=!1){const n=an();for(const a of e){if(a===n)return;if(a.focus({preventScroll:t}),an()!==n)return}}const un="data-reka-collection-item";function cn(e={}){const{key:t="",isProvider:n=!1}=e,a=`${t}CollectionProvider`;let i;if(n){const e=x(new Map),t=x();i={collectionRef:t,itemMap:e},$(a,i)}else i=A(a);const r=o({name:"CollectionSlot",setup(e,{slots:t}){const{primitiveElement:n,currentElement:a}=on();return b(a,(()=>{i.collectionRef.value=a.value})),()=>d(at,{ref:n},t)}}),l=o({name:"CollectionItem",inheritAttrs:!1,props:{value:{validator:()=>!0}},setup(e,{slots:t,attrs:n}){const{primitiveElement:a,currentElement:o}=on();return E((t=>{if(o.value){const n=F(o.value);i.itemMap.value.set(n,{ref:o.value,value:e.value}),t((()=>i.itemMap.value.delete(n)))}})),()=>d(at,{...n,[un]:"",ref:a},t)}});return{getItems:(e=!1)=>{const t=i.collectionRef.value;if(!t)return[];const n=Array.from(t.querySelectorAll(`[${un}]`)),a=Array.from(i.itemMap.value.values()).sort(((e,t)=>n.indexOf(e.ref)-n.indexOf(t.ref)));return e?a:a.filter((e=>""!==e.ref.dataset.disabled))},reactiveItems:y((()=>Array.from(i.itemMap.value.values()))),itemMapSize:y((()=>i.itemMap.value.size)),CollectionSlot:r,CollectionItem:l}}const[pn,fn]=At("RovingFocusGroup"),vn=o({__name:"RovingFocusGroup",props:{orientation:{default:void 0},dir:{},loop:{type:Boolean,default:!1},currentTabStopId:{},defaultCurrentTabStopId:{},preventScrollOnEntryFocus:{type:Boolean,default:!1},asChild:{type:Boolean},as:{}},emits:["entryFocus","update:currentTabStopId"],setup(e,{expose:t,emit:n}){const a=e,i=n,{loop:o,orientation:r,dir:l}=S(a),s=Pt(l),d=kt(a,"currentTabStopId",i,{defaultValue:a.defaultCurrentTabStopId,passive:void 0===a.currentTabStopId}),m=x(!1),h=x(!1),g=x(0),{getItems:b,CollectionSlot:y}=cn({isProvider:!0});function w(e){const t=!h.value;if(e.currentTarget&&e.target===e.currentTarget&&t&&!m.value){const t=new CustomEvent("rovingFocusGroup.onEntryFocus",ln);if(e.currentTarget.dispatchEvent(t),i("entryFocus",t),!t.defaultPrevented){const e=b().map((e=>e.ref)).filter((e=>""!==e.dataset.disabled));dn([e.find((e=>""===e.getAttribute("data-active"))),e.find((e=>e.id===d.value)),...e].filter(Boolean),a.preventScrollOnEntryFocus)}}h.value=!1}function _(){setTimeout((()=>{h.value=!1}),1)}return t({getItems:b}),fn({loop:o,dir:s,orientation:r,currentTabStopId:d,onItemFocus:e=>{d.value=e},onItemShiftTab:()=>{m.value=!0},onFocusableItemAdd:()=>{g.value++},onFocusableItemRemove:()=>{g.value--}}),(e,t)=>(u(),c(p(y),null,{default:f((()=>[T(p(ot),{tabindex:m.value||0===g.value?-1:0,"data-orientation":p(r),as:e.as,"as-child":e.asChild,dir:p(s),style:{outline:"none"},onMousedown:t[0]||(t[0]=e=>h.value=!0),onMouseup:_,onFocus:w,onBlur:t[1]||(t[1]=e=>m.value=!1)},{default:f((()=>[v(e.$slots,"default")])),_:3},8,["tabindex","data-orientation","as","as-child","dir"])])),_:3}))}}),mn=o({inheritAttrs:!1,__name:"VisuallyHiddenInputBubble",props:{name:{},value:{},checked:{type:Boolean,default:void 0},required:{type:Boolean},disabled:{type:Boolean},feature:{default:"fully-hidden"}},setup(e){const t=e,{primitiveElement:n,currentElement:a}=on(),i=y((()=>t.checked??t.value));return b(i,((e,t)=>{if(!a.value)return;const n=a.value,i=window.HTMLInputElement.prototype,o=Object.getOwnPropertyDescriptor(i,"value").set;if(o&&e!==t){const t=new Event("input",{bubbles:!0}),a=new Event("change",{bubbles:!0});o.call(n,e),n.dispatchEvent(t),n.dispatchEvent(a)}})),(e,a)=>(u(),c(rt,l({ref_key:"primitiveElement",ref:n},{...t,...e.$attrs},{as:"input"}),null,16))}}),hn=o({inheritAttrs:!1,__name:"VisuallyHiddenInput",props:{name:{},value:{},checked:{type:Boolean,default:void 0},required:{type:Boolean},disabled:{type:Boolean},feature:{default:"fully-hidden"}},setup(e){const t=e,n=y((()=>"object"==typeof t.value&&Array.isArray(t.value)&&0===t.value.length&&t.required)),a=y((()=>"string"==typeof t.value||"number"==typeof t.value||"boolean"==typeof t.value?[{name:t.name,value:t.value}]:"object"==typeof t.value&&Array.isArray(t.value)?t.value.flatMap(((e,n)=>"object"==typeof e?Object.entries(e).map((([e,a])=>({name:`[${t.name}][${n}][${e}]`,value:a}))):{name:`[${t.name}][${n}]`,value:e})):null===t.value||"object"!=typeof t.value||Array.isArray(t.value)?[]:Object.entries(t.value).map((([e,n])=>({name:`[${t.name}][${e}]`,value:n})))));return(e,o)=>n.value?(u(),c(mn,l({key:e.name},{...t,...e.$attrs},{name:e.name,value:e.value}),null,16,["name","value"])):(u(!0),P(i,{key:1},L(a.value,(n=>(u(),c(mn,l({key:n.name,ref_for:!0},{...t,...e.$attrs},{name:n.name,value:n.value}),null,16,["name","value"])))),128))}}),gn=o({__name:"RovingFocusItem",props:{tabStopId:{},focusable:{type:Boolean,default:!0},active:{type:Boolean},allowShiftKey:{type:Boolean},asChild:{type:Boolean},as:{default:"span"}},setup(e){const t=e,n=pn(),a=Wt(),i=y((()=>t.tabStopId||a)),o=y((()=>n.currentTabStopId.value===i.value)),{getItems:r,CollectionItem:l}=cn();function s(e){if("Tab"===e.key&&e.shiftKey)return void n.onItemShiftTab();if(e.target!==e.currentTarget)return;const a=function(e,t,n){const a=function(e,t){return"rtl"!==t?e:"ArrowLeft"===e?"ArrowRight":"ArrowRight"===e?"ArrowLeft":e}(e.key,n);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(a)||"horizontal"===t&&["ArrowUp","ArrowDown"].includes(a)))return sn[a]}(e,n.orientation.value,n.dir.value);if(void 0!==a){if(e.metaKey||e.ctrlKey||e.altKey||!t.allowShiftKey&&e.shiftKey)return;e.preventDefault();let l=[...r().map((e=>e.ref)).filter((e=>""!==e.dataset.disabled))];if("last"===a)l.reverse();else if("prev"===a||"next"===a){"prev"===a&&l.reverse();const t=l.indexOf(e.currentTarget);l=n.loop.value?(o=t+1,(i=l).map(((e,t)=>i[(o+t)%i.length]))):l.slice(t+1)}_((()=>dn(l)))}var i,o}return k((()=>{t.focusable&&n.onFocusableItemAdd()})),O((()=>{t.focusable&&n.onFocusableItemRemove()})),(e,t)=>(u(),c(p(l),null,{default:f((()=>[T(p(ot),{tabindex:o.value?0:-1,"data-orientation":p(n).orientation.value,"data-active":e.active?"":void 0,"data-disabled":e.focusable?void 0:"",as:e.as,"as-child":e.asChild,onMousedown:t[0]||(t[0]=t=>{e.focusable?p(n).onItemFocus(i.value):t.preventDefault()}),onFocus:t[1]||(t[1]=e=>p(n).onItemFocus(i.value)),onKeydown:s},{default:f((()=>[v(e.$slots,"default")])),_:3},8,["tabindex","data-orientation","data-active","data-disabled","as","as-child"])])),_:3}))}}),[bn,yn]=At("RadioGroupRoot"),wn=o({__name:"RadioGroupRoot",props:{modelValue:{},defaultValue:{},disabled:{type:Boolean,default:!1},orientation:{default:void 0},dir:{},loop:{type:Boolean,default:!0},asChild:{type:Boolean},as:{},name:{},required:{type:Boolean,default:!1}},emits:["update:modelValue"],setup(e,{emit:t}){const n=e,a=t,{forwardRef:i,currentElement:o}=Lt(),r=kt(n,"modelValue",a,{defaultValue:n.defaultValue,passive:void 0===n.modelValue}),{disabled:l,loop:s,orientation:d,name:m,required:h,dir:g}=S(n),b=Pt(g),y=rn(o);return yn({modelValue:r,changeModelValue:e=>{r.value=e},disabled:l,loop:s,orientation:d,name:m?.value,required:h}),(e,t)=>(u(),c(p(vn),{"as-child":"",orientation:p(d),dir:p(b),loop:p(s)},{default:f((()=>[T(p(ot),{ref:p(i),role:"radiogroup","data-disabled":p(l)?"":void 0,"as-child":e.asChild,as:e.as,"aria-orientation":p(d),"aria-required":p(h),dir:p(b)},{default:f((()=>[v(e.$slots,"default",{modelValue:p(r)}),p(y)&&p(m)?(u(),c(p(hn),{key:0,required:p(h),disabled:p(l),value:p(r),name:p(m)},null,8,["required","disabled","value","name"])):I("",!0)])),_:3},8,["data-disabled","as-child","as","aria-orientation","aria-required","dir"])])),_:3},8,["orientation","dir","loop"]))}});function xn(e,t,n){!function(e,t,n){const a=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&a.addEventListener(e,t,{once:!0}),a.dispatchEvent(i)}("radio.select",n,{originalEvent:e,value:t})}const _n=o({__name:"Radio",props:{id:{},value:{},disabled:{type:Boolean,default:!1},checked:{type:Boolean,default:void 0},asChild:{type:Boolean},as:{default:"button"},name:{},required:{type:Boolean}},emits:["update:checked","select"],setup(e,{emit:t}){const n=e,a=t,i=kt(n,"checked",a,{passive:void 0===n.checked}),{value:o}=S(n),{forwardRef:r,currentElement:s}=Lt(),d=rn(s),m=y((()=>n.id&&s.value?document.querySelector(`[for="${n.id}"]`)?.innerText??n.value:void 0));function h(e){xn(e,n.value,(e=>{a("select",e),e?.defaultPrevented||(i.value=!0,d.value&&e.stopPropagation())}))}return(e,t)=>(u(),c(p(ot),l(e.$attrs,{id:e.id,ref:p(r),role:"radio",type:"button"===e.as?"button":void 0,as:e.as,"aria-checked":p(i),"aria-label":m.value,"as-child":e.asChild,disabled:e.disabled?"":void 0,"data-state":p(i)?"checked":"unchecked","data-disabled":e.disabled?"":void 0,value:p(o),required:e.required,name:e.name,onClick:M(h,["stop"])}),{default:f((()=>[v(e.$slots,"default",{checked:p(i)}),p(d)&&e.name?(u(),c(p(hn),{key:0,type:"radio",tabindex:"-1",value:p(o),checked:!!p(i),name:e.name,disabled:e.disabled,required:e.required},null,8,["value","checked","name","disabled","required"])):I("",!0)])),_:3},16,["id","type","as","aria-checked","aria-label","as-child","disabled","data-state","data-disabled","value","required","name"]))}}),[kn,An]=At("RadioGroupItem"),$n=o({inheritAttrs:!1,__name:"RadioGroupItem",props:{id:{},value:{},disabled:{type:Boolean,default:!1},asChild:{type:Boolean},as:{default:"button"},name:{},required:{type:Boolean}},emits:["select"],setup(e,{emit:t}){const n=e,a=t,{forwardRef:i,currentElement:o}=Lt(),r=bn(),s=y((()=>r.disabled.value||n.disabled)),d=y((()=>r.required.value||n.required)),m=y((()=>Ct(r.modelValue?.value,n.value)));An({disabled:s,checked:m});const h=x(!1),g=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"];function b(){setTimeout((()=>{h.value&&o.value?.click()}),0)}return bt("keydown",(e=>{g.includes(e.key)&&(h.value=!0)})),bt("keyup",(()=>{h.value=!1})),(e,t)=>(u(),c(p(gn),{checked:m.value,disabled:s.value,"as-child":"",focusable:!s.value,active:m.value},{default:f((()=>[T(_n,l({...e.$attrs,...n},{ref:p(i),checked:m.value,required:d.value,disabled:s.value,"onUpdate:checked":t[0]||(t[0]=t=>p(r).changeModelValue(e.value)),onSelect:t[1]||(t[1]=e=>a("select",e)),onKeydown:t[2]||(t[2]=C(M((()=>{}),["prevent"]),["enter"])),onFocus:b}),{default:f((()=>[v(e.$slots,"default",{checked:m.value,required:d.value,disabled:s.value})])),_:3},16,["checked","required","disabled"])])),_:3},8,["checked","disabled","focusable","active"]))}}),Sn=o({__name:"RadioGroupIndicator",props:{forceMount:{type:Boolean},asChild:{type:Boolean},as:{default:"span"}},setup(e){const{forwardRef:t}=Lt(),n=kn();return(e,a)=>(u(),c(p(Xt),{present:e.forceMount||p(n).checked.value},{default:f((()=>[T(p(ot),l({ref:p(t),"data-state":p(n).checked.value?"checked":"unchecked","data-disabled":p(n).disabled.value?"":void 0,"as-child":e.asChild,as:e.as},e.$attrs),{default:f((()=>[v(e.$slots,"default")])),_:3},16,["data-state","data-disabled","as-child","as"])])),_:3},8,["present"]))}}),[Cn,On]=At("TabsRoot"),Tn=o({__name:"TabsRoot",props:{defaultValue:{},orientation:{default:"horizontal"},dir:{},activationMode:{default:"automatic"},modelValue:{},unmountOnHide:{type:Boolean,default:!0},asChild:{type:Boolean},as:{}},emits:["update:modelValue"],setup(e,{emit:t}){const n=e,a=t,{orientation:i,unmountOnHide:o,dir:r}=S(n),l=Pt(r);Lt();const s=kt(n,"modelValue",a,{defaultValue:n.defaultValue,passive:void 0===n.modelValue}),d=x();return On({modelValue:s,changeModelValue:e=>{s.value=e},orientation:i,dir:l,unmountOnHide:o,activationMode:n.activationMode,baseId:Wt(void 0,"reka-tabs"),tabsList:d}),(e,t)=>(u(),c(p(ot),{dir:p(l),"data-orientation":p(i),"as-child":e.asChild,as:e.as},{default:f((()=>[v(e.$slots,"default",{modelValue:p(s)})])),_:3},8,["dir","data-orientation","as-child","as"]))}}),In=o({__name:"TabsList",props:{loop:{type:Boolean,default:!0},asChild:{type:Boolean},as:{}},setup(e){const t=e,{loop:n}=S(t),{forwardRef:a,currentElement:i}=Lt(),o=Cn();return o.tabsList=i,(e,t)=>(u(),c(p(vn),{"as-child":"",orientation:p(o).orientation.value,dir:p(o).dir.value,loop:p(n)},{default:f((()=>[T(p(ot),{ref:p(a),role:"tablist","as-child":e.asChild,as:e.as,"aria-orientation":p(o).orientation.value},{default:f((()=>[v(e.$slots,"default")])),_:3},8,["as-child","as","aria-orientation"])])),_:3},8,["orientation","dir","loop"]))}});function En(e,t){return`${e}-trigger-${t}`}function Fn(e,t){return`${e}-content-${t}`}const Pn=o({__name:"TabsContent",props:{value:{},forceMount:{type:Boolean},asChild:{type:Boolean},as:{}},setup(e){const t=e,{forwardRef:n}=Lt(),a=Cn(),i=y((()=>En(a.baseId,t.value))),o=y((()=>Fn(a.baseId,t.value))),r=y((()=>t.value===a.modelValue.value)),l=x(r.value);return k((()=>{requestAnimationFrame((()=>{l.value=!1}))})),(e,t)=>(u(),c(p(Xt),{present:e.forceMount||r.value,"force-mount":""},{default:f((({present:t})=>[T(p(ot),{id:o.value,ref:p(n),"as-child":e.asChild,as:e.as,role:"tabpanel","data-state":r.value?"active":"inactive","data-orientation":p(a).orientation.value,"aria-labelledby":i.value,hidden:!t,tabindex:"0",style:V({animationDuration:l.value?"0s":void 0})},{default:f((()=>[!p(a).unmountOnHide.value||t?v(e.$slots,"default",{key:0}):I("",!0)])),_:2},1032,["id","as-child","as","data-state","data-orientation","aria-labelledby","hidden","style"])])),_:3},8,["present"]))}}),Ln=o({__name:"TabsTrigger",props:{value:{},disabled:{type:Boolean,default:!1},asChild:{type:Boolean},as:{default:"button"}},setup(e){const t=e,{forwardRef:n}=Lt(),a=Cn(),i=y((()=>En(a.baseId,t.value))),o=y((()=>Fn(a.baseId,t.value))),r=y((()=>t.value===a.modelValue.value));return(e,t)=>(u(),c(p(gn),{"as-child":"",focusable:!e.disabled,active:r.value},{default:f((()=>[T(p(ot),{id:i.value,ref:p(n),role:"tab",type:"button"===e.as?"button":void 0,as:e.as,"as-child":e.asChild,"aria-selected":r.value?"true":"false","aria-controls":o.value,"data-state":r.value?"active":"inactive",disabled:e.disabled,"data-disabled":e.disabled?"":void 0,"data-orientation":p(a).orientation.value,onMousedown:t[0]||(t[0]=M((t=>{e.disabled||!1!==t.ctrlKey?t.preventDefault():p(a).changeModelValue(e.value)}),["left"])),onKeydown:t[1]||(t[1]=C((t=>p(a).changeModelValue(e.value)),["enter","space"])),onFocus:t[2]||(t[2]=()=>{const t="manual"!==p(a).activationMode;r.value||e.disabled||!t||p(a).changeModelValue(e.value)})},{default:f((()=>[v(e.$slots,"default")])),_:3},8,["id","type","as","as-child","aria-selected","aria-controls","data-state","disabled","data-disabled","data-orientation"])])),_:3},8,["focusable","active"]))}}),Mn=o({__name:"TabsIndicator",props:{asChild:{type:Boolean},as:{}},setup(e){const t=e,n=Cn();Lt();const a=x(),i=x({size:null,position:null});function o(){a.value=n.tabsList.value?.querySelector('[role="tab"][data-state="active"]'),a.value&&("horizontal"===n.orientation.value?i.value={size:a.value.offsetWidth,position:a.value.offsetLeft}:i.value={size:a.value.offsetHeight,position:a.value.offsetTop})}return b((()=>[n.modelValue.value,n?.dir.value]),(async()=>{await _(),o()}),{immediate:!0}),function(e,t,n={}){const{window:a=ht,...i}=n;let o;const r=xt((()=>a&&"ResizeObserver"in a)),l=()=>{o&&(o.disconnect(),o=void 0)},s=y((()=>{const t=g(e);return Array.isArray(t)?t.map((e=>gt(e))):[gt(t)]})),d=b(s,(e=>{if(l(),r.value&&a){o=new ResizeObserver(t);for(const t of e)t&&o.observe(t,i)}}),{immediate:!0,flush:"post"}),u=()=>{l(),d()};lt(u)}([n.tabsList,a],o),(e,n)=>"number"==typeof i.value.size?(u(),c(p(ot),l({key:0},t,{style:{"--reka-tabs-indicator-size":`${i.value.size}px`,"--reka-tabs-indicator-position":`${i.value.position}px`}}),{default:f((()=>[v(e.$slots,"default")])),_:3},16,["style"])):I("",!0)}});var Vn="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},jn={},Bn={},Nn={};function zn(e){const t=e.split("/"),n=t.indexOf("admin");return`${t.slice(0,n).join("/")}/`}Object.defineProperty(Nn,"__esModule",{value:!0}),Nn.getRootPath=function(){return zn(window.location.pathname)},Nn.getPublicURL=function(){return zn(window.location.href)},Nn.extract=zn,Object.defineProperty(Bn,"__esModule",{value:!0}),Bn.getAssetUrl=function(e,t){const n=new URL(`assets/${e}`,(0,Rn.getPublicURL)());t&&n.searchParams.set("download","");return n.href};const Rn=Nn;var Dn,Un,Kn;function qn(e){return e?e.replaceAll(/<[^>]*>/g," ").replaceAll(/\s+/g," ").trim():""}function Gn(e){return e?e.split(/\s+/).filter(Boolean).length:0}function Hn(e,t){if(!e||!t)return 0;const n=e.toLowerCase(),a=t.toLowerCase();return 0===a.length?0:n.split(a).length-1}function Wn(e){return e?e.toLowerCase().replaceAll(/[^\w\s-]/g,"").replaceAll(/[\s_]+/g,"-").replaceAll(/-+/g,"-"):""}function Yn(e,t){return e?e.length>t?`${e.slice(0,t)}...`:e:""}Dn=jn,Un=Vn&&Vn.__createBinding||(Object.create?function(e,t,n,a){void 0===a&&(a=n);var i=Object.getOwnPropertyDescriptor(t,n);i&&!("get"in i?!t.__esModule:i.writable||i.configurable)||(i={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,a,i)}:function(e,t,n,a){void 0===a&&(a=n),e[a]=t[n]}),Kn=Vn&&Vn.__exportStar||function(e,t){for(var n in e)"default"===n||Object.prototype.hasOwnProperty.call(t,n)||Un(t,e,n)},Object.defineProperty(Dn,"__esModule",{value:!0}),Kn(Bn,Dn),Kn(Nn,Dn);const Jn={class:"seo-image-preview"},Xn={class:"preview-container"},Qn={class:"og-preview-container"},Zn={class:"preview-image-container"},ea=["src"],ta={key:0,class:"preview-domain-badge"},na={key:0,class:"preview-content empty-content"},aa={key:1,class:"preview-content"},ia={class:"preview-domain"},oa={class:"preview-title"},ra={class:"preview-description"},la={class:"preview-info"},sa={class:"platform-selection"};var da=o({__name:"OgImagePreview",props:{title:{},description:{},ogImage:{}},setup(e){const t=e,n=x("linkedin"),a=y((()=>{if(!t.ogImage)return null;return`${jn.getRootPath()}assets/${t.ogImage}?width=1200&height=630`})),i=y((()=>"undefined"!=typeof window?new URL(window.location.href).hostname:""));return(e,o)=>{const r=j("v-icon");return u(),P("div",Jn,[o[10]||(o[10]=B("div",{class:"preview-header"},[B("label",{class:"label field-label type-label"},"Social Image Preview")],-1)),B("div",Xn,[B("div",Qn,[B("div",{class:N(["og-preview",`og-preview--${n.value}`])},[B("div",Zn,[I(" Default slot for image upload  "),v(e.$slots,"default",{},(()=>[B("img",{src:a.value,class:"preview-image",alt:"OG Image Preview"},null,8,ea)]),!0),"x"===n.value?(u(),P("div",ta,z(i.value),1)):I("v-if",!0)]),t.ogImage?(u(),P("div",aa,[B("div",ia,z(i.value),1),B("div",oa,z(p(Yn)(e.title,60)||"Enter a title to see preview"),1),B("div",ra,z(p(Yn)(e.description,160)||"Enter a description to see preview"),1)])):(u(),P("div",na,o[1]||(o[1]=[B("div",{class:"empty-state-message"},[B("p",null," Upload an image to preview how your links will appear when shared on social media ")],-1)])))],2)]),B("div",la,[o[8]||(o[8]=B("h3",null,"Preview how your content will look when shared on social media.",-1)),B("div",sa,[T(p(wn),{modelValue:n.value,"onUpdate:modelValue":o[0]||(o[0]=e=>n.value=e),class:"platform-switcher"},{default:f((()=>[T(p($n),{value:"facebook",class:"platform-tab"},{default:f((()=>[T(r,{name:"facebook",small:""}),o[3]||(o[3]=B("span",null,"Facebook",-1)),T(p(Sn),{class:"platform-indicator"},{default:f((()=>o[2]||(o[2]=[B("div",{class:"indicator-bar"},null,-1)]))),_:1})])),_:1}),T(p($n),{value:"linkedin",class:"platform-tab"},{default:f((()=>[T(r,{name:"linkedin",small:""}),o[5]||(o[5]=B("span",null,"LinkedIn",-1)),T(p(Sn),{class:"platform-indicator"},{default:f((()=>o[4]||(o[4]=[B("div",{class:"indicator-bar"},null,-1)]))),_:1})])),_:1}),T(p($n),{value:"x",class:"platform-tab"},{default:f((()=>[T(r,{name:"x_twitter",small:""}),o[7]||(o[7]=B("span",null,"X",-1)),T(p(Sn),{class:"platform-indicator"},{default:f((()=>o[6]||(o[6]=[B("div",{class:"indicator-bar"},null,-1)]))),_:1})])),_:1})])),_:1},8,["modelValue"])]),o[9]||(o[9]=B("div",{class:"recommendations"},[B("h4",null,"Recommend Image Size"),B("p",null,[R("• 1200 X 630 pixels"),B("br"),R("• 1.91:1 aspect ratio")])],-1))])])])}}}),ua=[],ca=[];function pa(e,t){if(e&&"undefined"!=typeof document){var n,a=!0===t.prepend?"prepend":"append",i=!0===t.singleTag,o="string"==typeof t.container?document.querySelector(t.container):document.getElementsByTagName("head")[0];if(i){var r=ua.indexOf(o);-1===r&&(r=ua.push(o)-1,ca[r]={}),n=ca[r]&&ca[r][a]?ca[r][a]:ca[r][a]=l()}else n=l();65279===e.charCodeAt(0)&&(e=e.substring(1)),n.styleSheet?n.styleSheet.cssText+=e:n.appendChild(document.createTextNode(e))}function l(){var e=document.createElement("style");if(e.setAttribute("type","text/css"),t.attributes)for(var n=Object.keys(t.attributes),i=0;i<n.length;i++)e.setAttribute(n[i],t.attributes[n[i]]);var r="prepend"===a?"afterbegin":"beforeend";return o.insertAdjacentElement(r,e),e}}pa('.field[data-v-e0cb82f2] {\n  display: flex;\n  flex-direction: column;\n}\n\n.label[data-v-e0cb82f2] {\n  margin-bottom: 0.5rem;\n}\n\n.seo-image-preview[data-v-e0cb82f2] {\n  width: 100%;\n  container-type: inline-size;\n}\n\n.preview-container[data-v-e0cb82f2] {\n  display: flex;\n  gap: 24px;\n  align-items: flex-start;\n}\n@container (max-width: 768px) {\n  .preview-container[data-v-e0cb82f2] {\n    flex-direction: column;\n  }\n}\n\n.og-preview-container[data-v-e0cb82f2] {\n  background-color: var(--theme--background-subdued);\n  border: 1px solid var(--theme--border-color);\n  border-radius: var(--theme--border-radius);\n  overflow: hidden;\n  width: 500px;\n  max-width: 100%;\n  margin-top: 0.5rem;\n}\n@container (max-width: 768px) {\n  .og-preview-container[data-v-e0cb82f2] {\n    width: 100%;\n  }\n}\n\n.preview-info[data-v-e0cb82f2] {\n  flex: 1;\n  margin-top: 0.5rem;\n}\n.preview-info h3[data-v-e0cb82f2] {\n  font-size: 16px;\n  font-weight: 600;\n  margin-bottom: 16px;\n  line-height: 1.4;\n}\n.preview-info .recommendations[data-v-e0cb82f2] {\n  background-color: var(--theme--background-subdued);\n  border-radius: var(--theme--border-radius);\n  padding: 16px;\n}\n.preview-info .recommendations h4[data-v-e0cb82f2] {\n  font-size: 14px;\n  font-weight: 600;\n  margin-bottom: 8px;\n}\n.preview-info .recommendations p[data-v-e0cb82f2] {\n  font-size: 14px;\n  color: var(--theme--foreground-subdued);\n}\n\n.empty-content[data-v-e0cb82f2] {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  text-align: center;\n  min-height: 80px;\n}\n\n.empty-state-message[data-v-e0cb82f2] {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  color: var(--theme--foreground-subdued);\n}\n.empty-state-message p[data-v-e0cb82f2] {\n  margin-top: 8px;\n  font-size: 14px;\n}\n\n.og-preview[data-v-e0cb82f2] {\n  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;\n  color: var(--theme--foreground);\n  background-color: var(--theme--background);\n}\n\n.preview-image-container[data-v-e0cb82f2] {\n  width: 100%;\n  padding-top: 52.5%;\n  position: relative;\n  background-color: var(--theme--background-subdued);\n  border-bottom: 1px solid var(--theme--border-color);\n  overflow: hidden;\n}\n.preview-image-container [data-v-e0cb82f2] > * {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n\n.preview-content[data-v-e0cb82f2] {\n  padding: 10px 12px;\n}\n\n.preview-domain[data-v-e0cb82f2] {\n  font-size: 12px;\n  color: var(--theme--foreground-subdued);\n  margin-bottom: 4px;\n  text-transform: uppercase;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.preview-title[data-v-e0cb82f2] {\n  font-size: 16px;\n  font-weight: 600;\n  line-height: 1.3;\n  margin-bottom: 4px;\n  color: var(--theme--foreground);\n  display: -webkit-box;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.preview-description[data-v-e0cb82f2] {\n  font-size: 14px;\n  line-height: 1.4;\n  color: var(--theme--foreground-subdued);\n  display: -webkit-box;\n  -webkit-line-clamp: 3;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n\n.preview-header[data-v-e0cb82f2] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.platform-selection[data-v-e0cb82f2] {\n  margin-bottom: 16px;\n}\n\n.platforms-label[data-v-e0cb82f2] {\n  margin-bottom: 8px;\n  font-weight: 600;\n  font-size: 14px;\n}\n\n.platform-switcher[data-v-e0cb82f2] {\n  display: flex;\n  flex-direction: column;\n  position: relative;\n  border-left: var(--theme--border-width) solid var(--theme--border-color);\n  border-bottom: none;\n}\n@container (max-width: 768px) {\n  .platform-switcher[data-v-e0cb82f2] {\n    flex-direction: row;\n    border-bottom: var(--theme--border-width) solid var(--theme--border-color);\n    border-left: none;\n  }\n}\n\n.platform-tab[data-v-e0cb82f2] {\n  background-color: transparent;\n  padding: 10px 16px;\n  min-width: fit-content;\n  display: flex;\n  align-items: center;\n  font-size: 15px;\n  color: var(--theme--foreground-normal);\n  user-select: none;\n  border: none;\n  border-radius: 0 var(--theme--border-radius, 4px) var(--theme--border-radius, 4px) 0;\n  cursor: pointer;\n  outline: none;\n  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out;\n  gap: 8px;\n  position: relative;\n}\n@container (max-width: 768px) {\n  .platform-tab[data-v-e0cb82f2] {\n    border-radius: var(--theme--border-radius, 4px) var(--theme--border-radius, 4px) 0 0;\n  }\n}\n.platform-tab[data-v-e0cb82f2]:hover {\n  color: var(--theme--primary);\n  background-color: var(--theme--background-subdued);\n}\n.platform-tab[data-state=checked][data-v-e0cb82f2] {\n  color: var(--theme--primary);\n}\n\n.platform-indicator[data-v-e0cb82f2] {\n  position: absolute;\n  left: 0;\n  top: 0;\n  height: 100%;\n  width: 2px;\n  transform: translateX(-1px);\n}\n@container (max-width: 768px) {\n  .platform-indicator[data-v-e0cb82f2] {\n    left: 0;\n    right: 0;\n    bottom: 0;\n    top: auto;\n    width: 100%;\n    height: 2px;\n    transform: translateY(1px);\n  }\n}\n.platform-indicator .indicator-bar[data-v-e0cb82f2] {\n  background-color: var(--theme--primary);\n  width: 100%;\n  height: 100%;\n  border-radius: 9999px;\n}\n\n.og-preview--facebook .preview-content[data-v-e0cb82f2] {\n  background-color: var(--theme--background-subdued);\n}\n.og-preview--facebook .preview-description[data-v-e0cb82f2] {\n  -webkit-line-clamp: 1;\n}\n\n.og-preview--linkedin[data-v-e0cb82f2] {\n  border-radius: 2px;\n}\n.og-preview--linkedin .preview-content[data-v-e0cb82f2] {\n  background-color: var(--theme--background);\n  padding: 12px;\n  display: flex;\n  flex-direction: column;\n}\n.og-preview--linkedin .preview-title[data-v-e0cb82f2] {\n  font-weight: 600;\n  font-size: 14px;\n  order: 1;\n}\n.og-preview--linkedin .preview-description[data-v-e0cb82f2] {\n  font-size: 12px;\n  display: none;\n  order: 3;\n}\n.og-preview--linkedin .preview-domain[data-v-e0cb82f2] {\n  font-size: 12px;\n  order: 2;\n}\n\n.og-preview--x[data-v-e0cb82f2] {\n  background-color: transparent;\n  border-radius: var(--theme--border-radius);\n  overflow: hidden;\n  /* Only hide content when image exists */\n  /* Keep empty content visible */\n}\n.og-preview--x .preview-image-container[data-v-e0cb82f2] {\n  position: relative;\n  border-bottom: none;\n  background-color: transparent;\n}\n.og-preview--x .preview-domain-badge[data-v-e0cb82f2] {\n  position: absolute;\n  bottom: 8px;\n  left: 8px;\n  background-color: rgba(0, 0, 0, 0.6);\n  color: white;\n  padding: 2px 6px;\n  border-radius: 4px;\n  font-size: 12px;\n  font-weight: 500;\n}\n.og-preview--x:not(:has(.empty-content)) .preview-content[data-v-e0cb82f2] {\n  display: none;\n}\n.og-preview--x .empty-content[data-v-e0cb82f2] {\n  display: flex;\n}\n.og-preview--x .preview-domain[data-v-e0cb82f2] {\n  display: none;\n}\n.og-preview--x .preview-title[data-v-e0cb82f2] {\n  display: none;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n.og-preview--x .preview-description[data-v-e0cb82f2] {\n  display: none;\n}',{});var fa=(e,t)=>{const n=e.__vccOpts||e;for(const[e,a]of t)n[e]=a;return n},va=fa(da,[["__scopeId","data-v-e0cb82f2"],["__file","OgImagePreview.vue"]]);const ma={class:"field"},ha={class:"search-container"},ga={class:"search-preview"},ba={class:"preview-url"},ya=["src"],wa={class:"preview-url-text"},xa={class:"truncate"},_a={class:"url-path-container truncate"},ka={class:"preview-url-path"},Aa={class:"preview-url-path"},$a={class:"preview-title"},Sa={class:"preview-description"};var Ca=o({__name:"SearchPreview",props:{title:{},metaDescription:{},collection:{}},setup(e){const t=x("");function n(){var e,t,n;return(null==(n=null==(t=null==(e=null==window?void 0:window.document)?void 0:e.title)?void 0:t.split("·")[1])?void 0:n.trim())||""}return k((()=>{"undefined"!=typeof window&&(t.value=window.location.origin)})),(e,a)=>{return u(),P("div",ma,[a[2]||(a[2]=B("label",{class:"label field-label type-label"},"Search Preview",-1)),B("div",ha,[B("div",ga,[B("div",ba,[B("img",{src:(null==(i=window.document.querySelector("link[rel='apple-touch-icon']"))?void 0:i.getAttribute("href"))||(null==(o=window.document.querySelector("link[rel='icon']"))?void 0:o.getAttribute("href"))||(null==(r=window.document.querySelector("link[rel='shortcut icon']"))?void 0:r.getAttribute("href"))||"/favicon.ico",class:"preview-url-favicon",width:"20",height:"20",alt:"favicon",onError:a[0]||(a[0]=e=>e.target.src="🌐")},null,40,ya),B("div",wa,[B("p",xa,z(n()),1),B("p",_a,[B("span",ka,z(t.value),1),a[1]||(a[1]=B("span",{class:"preview-url-arrow"},"›",-1)),B("span",Aa,z(e.collection),1)])])]),B("div",$a,z(p(Yn)(e.title,60)||"Enter a title to see preview"),1),B("div",Sa,z(p(Yn)(e.metaDescription,160)||"Enter a meta description to see preview"),1)])])]);var i,o,r}}});pa(".field[data-v-59e1871b] {\n  display: flex;\n  flex-direction: column;\n}\n\n.label[data-v-59e1871b] {\n  margin-bottom: 0.5rem;\n}\n\n.search-container[data-v-59e1871b] {\n  background-color: var(--theme--background);\n  border: 2px solid var(--theme--border-color);\n  border-radius: var(--theme--border-radius);\n  padding: 1.5rem;\n}\n\n.search-preview[data-v-59e1871b] {\n  border-radius: var(--theme--border-radius);\n  font-family: arial, sans-serif;\n  max-width: 600px;\n  padding: 0;\n}\n\n.preview-url[data-v-59e1871b] {\n  align-items: center;\n  color: var(--theme--foreground);\n  display: flex;\n  font-size: 12px;\n  gap: 8px;\n  line-height: 1.3;\n  margin-bottom: 4px;\n}\n.preview-url .preview-url-favicon[data-v-59e1871b] {\n  margin-right: 4px;\n}\n.preview-url .preview-url-text[data-v-59e1871b] {\n  color: color-mix(in srgb, var(--theme--foreground), #fff 20%);\n  flex: 1;\n  font-size: 12px;\n  min-width: 0;\n  width: 100%;\n}\n.preview-url .preview-url-text p[data-v-59e1871b] {\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n.preview-url .url-path-container[data-v-59e1871b] {\n  align-items: center;\n  display: flex;\n}\n.preview-url .url-path-container .preview-url-arrow[data-v-59e1871b] {\n  flex-shrink: 0;\n  margin: 0 4px;\n}\n.preview-url .url-path-container .preview-url-path[data-v-59e1871b] {\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.preview-title[data-v-59e1871b] {\n  color: var(--theme--primary);\n  cursor: pointer;\n  font-size: 20px;\n  font-weight: 400;\n  line-height: 1.3;\n  margin-bottom: 3px;\n}\n.preview-title[data-v-59e1871b]:hover {\n  text-decoration: underline;\n}\n\n.preview-description[data-v-59e1871b] {\n  color: var(--theme--foreground-subdued);\n  font-size: 14px;\n  line-height: 1.58;\n  word-wrap: break-word;\n}",{});var Oa=fa(Ca,[["__scopeId","data-v-59e1871b"],["__file","SearchPreview.vue"]]);const Ta={title:{field:"title",minLength:45,maxLength:60},meta_description:{field:"meta_description",minLength:130,maxLength:160}};function Ia(e){var t,n;if(!e.focusKeyphrase)return{id:"title",title:"SEO Title",status:"neutral",message:"Add a focus keyphrase first"};const a=(null==(t=e.title)?void 0:t.length)||0,i=(null==(n=e.title)?void 0:n.toLowerCase().includes(e.focusKeyphrase.toLowerCase()))||!1,o=a>=Ta.title.minLength&&a<=Ta.title.maxLength;if(!e.title)return{id:"title",title:"SEO Title",status:"error",message:"Add a title"};if(!i)return{id:"title",title:"SEO Title",status:"warning",message:"Your focus keyphrase is not in the SEO title",details:{length:a,hasKeyphrase:i,minLength:Ta.title.minLength,maxLength:Ta.title.maxLength}};if(!o){return{id:"title",title:"SEO Title",status:"warning",message:a<Ta.title.minLength?`Title is too short (${a} chars). Ideal: ${Ta.title.minLength}-${Ta.title.maxLength} characters.`:`Title is too long (${a} chars). Ideal: ${Ta.title.minLength}-${Ta.title.maxLength} characters.`,details:{length:a,hasKeyphrase:i,minLength:Ta.title.minLength,maxLength:Ta.title.maxLength}}}return{id:"title",title:"SEO Title",status:"good",message:"Your SEO title contains the focus keyphrase and has a good length",details:{length:a,hasKeyphrase:i,minLength:Ta.title.minLength,maxLength:Ta.title.maxLength}}}function Ea(e){var t,n;if(!e.focusKeyphrase)return{id:"description",title:"Meta Description",status:"neutral",message:"Add a focus keyphrase first"};const a=(null==(t=e.description)?void 0:t.length)||0,i=(null==(n=e.description)?void 0:n.toLowerCase().includes(e.focusKeyphrase.toLowerCase()))||!1,o=a>=Ta.meta_description.minLength&&a<=Ta.meta_description.maxLength;if(!e.description)return{id:"description",title:"Meta Description",status:"error",message:"Add a meta description"};if(!i)return{id:"description",title:"Meta Description",status:"warning",message:"Your focus keyphrase is not in the meta description",details:{length:a,hasKeyphrase:i,minLength:Ta.meta_description.minLength,maxLength:Ta.meta_description.maxLength}};if(!o){return{id:"description",title:"Meta Description",status:"warning",message:a<Ta.meta_description.minLength?`Meta description is too short (${a} chars). Ideal: ${Ta.meta_description.minLength}-${Ta.meta_description.maxLength} characters.`:`Meta description is too long (${a} chars). Ideal: ${Ta.meta_description.minLength}-${Ta.meta_description.maxLength} characters.`,details:{length:a,hasKeyphrase:i,minLength:Ta.meta_description.minLength,maxLength:Ta.meta_description.maxLength}}}return{id:"description",title:"Meta Description",status:"good",message:"Your meta description contains the focus keyphrase and has a good length",details:{length:a,hasKeyphrase:i,minLength:Ta.meta_description.minLength,maxLength:Ta.meta_description.maxLength}}}function Fa(e){if(!e.focusKeyphrase)return{id:"slug",title:"URL Slug",status:"neutral",message:"Add a focus keyphrase first"};if(!e.slug)return{id:"slug",title:"URL Slug",status:"error",message:"Add a slug"};const t=Wn(e.slug),n=Wn(e.focusKeyphrase),a=t.includes(n);return a?{id:"slug",title:"URL Slug",status:"good",message:"Your slug contains the focus keyphrase",details:{hasKeyphrase:a}}:{id:"slug",title:"URL Slug",status:"warning",message:"Your focus keyphrase is not in the URL slug",details:{hasKeyphrase:a}}}function Pa(e){if(!e.focusKeyphrase)return{id:"content",title:"Content",status:"neutral",message:"Add a focus keyphrase first"};if(!e.combinedContent)return{id:"content",title:"Content",status:"neutral",message:"No content provided for analysis"};const t=(n=e.combinedContent)&&(a=qn(n))?a.replaceAll(/\*\*(.+?)\*\*/g,"$1").replaceAll(/\*(.+?)\*/g,"$1").replaceAll(/__(.+?)__/g,"$1").replaceAll(/_(.+?)_/g,"$1").replaceAll(/```(.+?)```/g,"$1").replaceAll(/`(.+?)`/g,"$1").replaceAll(/\[(.+?)\]\(.+?\)/g,"$1").replaceAll(/!\[(.+?)\]\(.+?\)/g,"$1").replaceAll(/#{1,6}\s+(.+)/g,"$1").replaceAll(/\n+/g," ").replaceAll(/\s+/g," ").trim():"";var n,a;const i=Gn(t);if(i<50)return{id:"content",title:"Content",status:"warning",message:"Your content is too short for a proper keyword density analysis",details:{wordCount:i,density:0,occurrences:0,optimal:!1}};const o=Hn(t,e.focusKeyphrase),r=function(e,t){const n=Gn(e);return 0!==n&&t?Hn(e,t)/n*100:0}(t,e.focusKeyphrase);if(0===o)return{id:"content",title:"Content",status:"error",message:"Your focus keyphrase does not appear in the content",details:{wordCount:i,density:0,occurrences:0,optimal:!1}};if(!(r>=.5&&r<=2)){return{id:"content",title:"Content",status:"warning",message:r<.5?`Keyword density (${r.toFixed(1)}%) is too low. Optimal: 0.5%-2.0%.`:`Keyword density (${r.toFixed(1)}%) is too high. Optimal: 0.5%-2.0%.`,details:{wordCount:i,density:r,occurrences:o,optimal:!1}}}return{id:"content",title:"Content",status:"good",message:`Your content has a good keyword density (${r.toFixed(1)}%)`,details:{wordCount:i,density:r,occurrences:o,optimal:!0}}}function La(e){if(!e.focusKeyphrase)return{id:"image_alt",title:"Image Alt Text",status:"neutral",message:"Add a focus keyphrase first"};if(!e.combinedContent)return{id:"image_alt",title:"Image Alt Text",status:"neutral",message:"No content to analyze for images"};const t=function(e){if(!e)return[];const t=[],n=/<img[^>]*alt=["']([^"']*)["'][^>]*>/gi;let a;for(;null!==(a=n.exec(e));){const e=a[1];e&&e.trim()&&t.push(e.trim())}const i=/!\[([^\]]+)\]\([^)]+\)/g;for(;null!==(a=i.exec(e));){const e=a[1];e&&e.trim()&&t.push(e.trim())}return t}(e.combinedContent);if(0===t.length)return{id:"image_alt",title:"Image Alt Text",status:"neutral",message:"No images found in the content",details:{imageCount:0,altTexts:[]}};return t.some((t=>t.toLowerCase().includes(e.focusKeyphrase.toLowerCase())))?{id:"image_alt",title:"Image Alt Text",status:"good",message:"At least one image has an alt attribute with the focus keyphrase",details:{imageCount:t.length,altTexts:t}}:{id:"image_alt",title:"Image Alt Text",status:"warning",message:"None of your images have alt attributes containing your focus keyphrase",details:{imageCount:t.length,altTexts:t}}}function Ma(e){if(!e.focusKeyphrase)return{id:"subheadings",title:"Subheadings",status:"neutral",message:"Add a focus keyphrase first"};if(!e.combinedContent)return{id:"subheadings",title:"Subheadings",status:"neutral",message:"No content to analyze for subheadings"};const t=function(e){if(!e)return[];const t=[],n=/<h([2-6])[^>]*>(.*?)<\/h\1>/gi;let a;for(;null!==(a=n.exec(e));){const e=qn(a[2]||"");e&&t.push(e)}const i=/^#{2,6}\s+(\S.*)$/gm;for(;null!==(a=i.exec(e));){const e=a[1];e&&e.trim()&&t.push(e.trim())}return t}(e.combinedContent);if(0===t.length)return{id:"subheadings",title:"Subheadings",status:"warning",message:"No subheadings found in the content",details:{subheadingCount:0,subheadings:[]}};return t.some((t=>t.toLowerCase().includes(e.focusKeyphrase.toLowerCase())))?{id:"subheadings",title:"Subheadings",status:"good",message:"Your focus keyphrase appears in at least one subheading",details:{subheadingCount:t.length,subheadings:t}}:{id:"subheadings",title:"Subheadings",status:"warning",message:"Your subheadings don't contain the focus keyphrase",details:{subheadingCount:t.length,subheadings:t}}}const Va={class:"progress-bar"};var ja=o({__name:"ProgressBar",props:{progress:{},status:{}},setup:e=>(e,t)=>(u(),P("div",Va,[B("div",{class:N(["progress",e.status]),style:V({width:`${e.progress}%`})},null,6)]))});pa("\n.progress-bar[data-v-ca1c03b7] {\n\theight: 5px;\n\tbackground-color: var(--theme--background-subdued);\n\tborder-radius: 2.5px;\n\tmargin: 0.5rem 0;\n}\n.progress[data-v-ca1c03b7] {\n\theight: 100%;\n\tborder-radius: 2.5px;\n\ttransition: width 0.3s;\n}\n.progress.too-short[data-v-ca1c03b7] {\n\tbackground-color: var(--theme--warning);\n}\n.progress.ideal[data-v-ca1c03b7] {\n\tbackground-color: var(--theme--success);\n}\n.progress.too-long[data-v-ca1c03b7],\n.progress.missing[data-v-ca1c03b7] {\n\tbackground-color: var(--theme--danger);\n}\n",{});var Ba=fa(ja,[["__scopeId","data-v-ca1c03b7"],["__file","ProgressBar.vue"]]);const Na={class:"analysis-text"},za={class:"analysis-title"},Ra={class:"analysis-title-text"},Da={class:"analysis-title-subtext"},Ua={key:0,class:"analysis-details"},Ka={key:0},qa={key:0},Ga={key:0};var Ha=o({__name:"AnalysisResult",props:{result:{}},setup(e){const t=e,n=y((()=>{const e=t.result.details;return e?"title"!==t.result.id&&"description"!==t.result.id||"number"!=typeof e.length||"number"!=typeof e.maxLength?"content"===t.result.id&&"number"==typeof e.wordCount?{type:"content",data:e}:"image_alt"===t.result.id&&"number"==typeof e.imageCount?{type:"image",data:e}:"subheadings"===t.result.id&&"number"==typeof e.subheadingCount?{type:"subheadings",data:e}:null:{type:"length",data:e}:null})),a=y((()=>{const e=n.value;if(!e||"length"!==e.type||0===e.data.length||!e.data.maxLength)return 0;const{length:t,maxLength:a}=e.data;return Math.min(100,100*(null!=t?t:0/a))})),o=y((()=>{const e=n.value;if(!e||"length"!==e.type||0===e.data.length||!e.data.minLength||!e.data.maxLength)return"missing";const{length:t,minLength:a,maxLength:i}=e.data;return t&&t<a?"too-short":t&&t>i?"too-long":"ideal"})),r=y((()=>{const e=n.value;return e&&"content"===e.type&&"number"==typeof e.data.density?Math.min(100,50*e.data.density):0})),l=y((()=>{const e=n.value;return e&&"content"===e.type&&"number"==typeof e.data.density?e.data.optimal?"ideal":"too-short":"missing"}));return(e,t)=>(u(),P("div",{class:N(["analysis-result",e.result.status])},[B("div",{class:N(["analysis-dot",e.result.status])},null,2),B("div",Na,[B("p",za,[B("span",Ra,z(e.result.title),1),B("span",Da,z(e.result.message),1)]),n.value?(u(),P("div",Ua,[I(" Title & Description details "),"length"===n.value.type?(u(),P("div",Ka,[R(" Length: "+z(n.value.data.length)+" characters ",1),T(Ba,{progress:a.value,status:o.value,class:"analysis-meter"},null,8,["progress","status"])])):"content"===n.value.type?(u(),P(i,{key:1},[I(" Content details "),B("div",null,[R(" Word count: "+z(n.value.data.wordCount)+" ",1),n.value.data.occurrences&&n.value.data.occurrences>0&&"number"==typeof n.value.data.density?(u(),P(i,{key:0},[t[0]||(t[0]=B("br",null,null,-1)),R("Occurrences: "+z(n.value.data.occurrences)+" times ",1),t[1]||(t[1]=B("br",null,null,-1)),R("Density: "+z(n.value.data.density.toFixed(1))+"% ",1),T(Ba,{progress:r.value,status:l.value,class:"analysis-meter"},null,8,["progress","status"])],64)):I("v-if",!0)])],2112)):"image"===n.value.type?(u(),P(i,{key:2},[I(" Image alt text details "),B("div",null,[R(" Found "+z(n.value.data.imageCount)+" images"+z("good"===e.result.status?" with proper alt text":"")+" ",1),n.value.data.altTexts&&n.value.data.altTexts.length>0?(u(),P("ul",qa,[(u(!0),P(i,null,L(n.value.data.altTexts,((e,t)=>(u(),P("li",{key:t},' "'+z(e)+'" ',1)))),128))])):I("v-if",!0)])],2112)):"subheadings"===n.value.type?(u(),P(i,{key:3},[I(" Subheadings details "),B("div",null,[R(" Found "+z(n.value.data.subheadingCount)+" subheadings ",1),n.value.data.subheadings&&n.value.data.subheadings.length>0?(u(),P("ul",Ga,[(u(!0),P(i,null,L(n.value.data.subheadings,((e,t)=>(u(),P("li",{key:t},' "'+z(e)+'" ',1)))),128))])):I("v-if",!0)])],2112)):I("v-if",!0)])):I("v-if",!0)])],2))}});pa(".analysis-result[data-v-ba10090b] {\n  display: flex;\n  align-items: start;\n  gap: 12px;\n  padding: 8px;\n  border-bottom: 1px dashed var(--theme--border-color);\n}\n.analysis-result .analysis-dot[data-v-ba10090b] {\n  margin-top: 0.5rem;\n  width: 8px;\n  height: 8px;\n  border-radius: 50%;\n  background-color: var(--theme--foreground-subdued);\n}\n.analysis-result .analysis-dot.good[data-v-ba10090b] {\n  background-color: var(--theme--success);\n}\n.analysis-result .analysis-dot.warning[data-v-ba10090b] {\n  background-color: var(--theme--warning);\n}\n.analysis-result .analysis-dot.error[data-v-ba10090b] {\n  background-color: var(--theme--danger);\n}\n.analysis-result .v-icon[data-v-ba10090b] {\n  margin-top: 0.25rem;\n  flex-shrink: 0;\n}\n\n.analysis-result[data-v-ba10090b]:last-child {\n  border-bottom: none;\n  padding-bottom: 0;\n}\n\n.analysis-text[data-v-ba10090b] {\n  flex: 1;\n  flex-direction: column;\n  min-width: 0;\n}\n\n.analysis-title[data-v-ba10090b] {\n  margin: 0 0 4px 0;\n  display: flex;\n  flex-wrap: wrap;\n  align-items: baseline;\n  gap: 0.5rem;\n}\n\n.analysis-title-text[data-v-ba10090b] {\n  line-height: 1.5;\n  font-size: 1rem;\n  font-weight: bold;\n  color: var(--theme--foreground);\n  flex-shrink: 0;\n  white-space: nowrap;\n}\n\n.analysis-title-subtext[data-v-ba10090b] {\n  line-height: 1.5;\n  font-size: 0.9rem;\n  font-weight: 400;\n  color: var(--theme--foreground-subdued);\n}\n\n.analysis-details[data-v-ba10090b] {\n  font-size: 0.85em;\n  color: var(--theme--foreground-subdued);\n  margin-top: 4px;\n}\n.analysis-details ul[data-v-ba10090b] {\n  margin: 4px 0 0 1rem;\n  padding: 0;\n  list-style: disc;\n}\n.analysis-details li[data-v-ba10090b] {\n  margin-bottom: 2px;\n}\n.analysis-details .analysis-meter[data-v-ba10090b] {\n  display: inline-block;\n  width: 100px;\n  margin: 4px 0 0 8px;\n  vertical-align: middle;\n}",{});var Wa=fa(Ha,[["__scopeId","data-v-ba10090b"],["__file","AnalysisResult.vue"]]);const Ya={class:"seo-analysis-container field"},Ja={class:"header"},Xa={class:"action-bar"},Qa={key:0,class:"empty-state"},Za={key:0,class:"all-neutral-state"},ei={key:1,class:"no-results-state"};var ti=o({__name:"Analysis",props:{focusKeyphrase:{},title:{},description:{},slugField:{},contentFields:{}},setup(e){const t=e,n=A("values"),a=x([]),o=y((()=>{if(!t.contentFields||!n)return{};return(Array.isArray(t.contentFields)?t.contentFields:[t.contentFields]).reduce(((e,t)=>{const a=et(n.value,t);return null!=a&&(e[t]=a),e}),{})})),r=y((()=>{let e="";if(!t.contentFields||!o.value)return e;const n=Array.isArray(t.contentFields)?t.contentFields:[t.contentFields];for(const t of n){const n=o.value[t];if(n)if("string"==typeof n)e+=` ${n}`;else try{e+=` ${JSON.stringify(n)}`}catch{console.warn(`[SEO Plugin] Could not stringify content for field: ${t}`)}}return e.trim()})),l=y((()=>n.value[t.slugField]||"")),s=y((()=>({focusKeyphrase:t.focusKeyphrase,title:t.title,description:t.description,slug:l.value,combinedContent:r.value}))),d=vt((()=>{if(!t.focusKeyphrase)return void(a.value=[]);const e=t.contentFields&&(Array.isArray(t.contentFields)?t.contentFields.length>0:!!t.contentFields);a.value=[Ia(s.value),Ea(s.value),Fa(s.value),...e?[Pa(s.value),La(s.value),Ma(s.value)]:[]]}),500);b([()=>t.focusKeyphrase,()=>t.title,()=>t.description,()=>l.value,()=>o.value],(()=>{d()}),{deep:!0,immediate:!0});const v=y((()=>a.value)),m=y((()=>v.value.filter((e=>"error"===e.status)))),h=y((()=>v.value.filter((e=>"warning"===e.status)))),g=y((()=>v.value.filter((e=>"good"===e.status)))),w=x(["problems","improvements"]),_=y((()=>[{id:"problems",title:"Problems",icon:"error",iconClass:"error",results:m.value},{id:"improvements",title:"Improvements",icon:"warning",iconClass:"warning",results:h.value},{id:"good",title:"Good results",icon:"check_circle",iconClass:"good",results:g.value}])),k=y((()=>_.value.filter((e=>e.results.length>0)).map((e=>e.id))));function $(){w.value=k.value}function S(){w.value=[]}const C=y((()=>v.value.length>0&&0===k.value.length)),O=y((()=>w.value.length>0)),E=y((()=>w.value.length<k.value.length));return(e,t)=>{const n=j("v-icon"),a=j("v-button"),o=D("tooltip");return u(),P("div",Ya,[B("div",Ja,[t[1]||(t[1]=B("label",{class:"label field-label type-label"},"Analysis",-1)),B("div",Xa,[U((u(),c(a,{disabled:!O.value,icon:"",small:"",secondary:"",title:"Collapse all",onClick:S},{default:f((()=>[T(n,{name:"unfold_less"})])),_:1},8,["disabled"])),[[o,"Collapse all"]]),U((u(),c(a,{disabled:!E.value,icon:"",small:"",secondary:"",title:"Expand all",onClick:$},{default:f((()=>[T(n,{name:"unfold_more"})])),_:1},8,["disabled"])),[[o,"Expand all"]])])]),e.focusKeyphrase?(u(),P(i,{key:1},[T(p(jt),{modelValue:w.value,"onUpdate:modelValue":t[0]||(t[0]=e=>w.value=e),type:"multiple",collapsible:"",class:"analysis-accordion","unmount-on-hide":!1},{default:f((()=>[(u(!0),P(i,null,L(_.value,(e=>(u(),P(i,{key:e.id},[e.results.length>0?(u(),c(p(Gt),{key:0,value:e.id,class:"accordion-item"},{default:f((()=>[T(p(en),{class:"accordion-header"},{default:f((()=>[T(p(nn),{class:"accordion-trigger"},{default:f((()=>[B("div",{class:N(["section-title",e.id])},[T(n,{name:e.icon,class:N(e.iconClass)},null,8,["name","class"]),R(" "+z(e.title)+" ("+z(e.results.length)+") ",1)],2),T(n,{name:"chevron_right",class:"section-icon"})])),_:2},1024)])),_:2},1024),T(p(Zt),{class:"accordion-content"},{default:f((()=>[(u(!0),P(i,null,L(e.results,(e=>(u(),c(Wa,{key:e.id,result:e},null,8,["result"])))),128))])),_:2},1024)])),_:2},1032,["value"])):I("v-if",!0)],64)))),128))])),_:1},8,["modelValue"]),C.value?(u(),P("div",Za," We found nothing to report! This might happen if content is missing or too short. ")):0===v.value.length&&e.focusKeyphrase?(u(),P("div",ei," Could not generate analysis results. Check configuration and content fields. ")):I("v-if",!0)],64)):(u(),P("div",Qa," Enter a focus keyphrase to analyze your content. "))])}}});pa(".header[data-v-73a6c4ed] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.label[data-v-73a6c4ed] {\n  margin-bottom: 0.5rem;\n}\n\n.type-label[data-v-73a6c4ed] {\n  margin-bottom: 1rem;\n}\n\n.empty-state[data-v-73a6c4ed],\n.all-neutral-state[data-v-73a6c4ed],\n.no-results-state[data-v-73a6c4ed] {\n  color: var(--theme--foreground-subdued);\n  font-style: italic;\n  padding: 12px 8px;\n  border: 1px dashed var(--theme--border-color);\n  border-radius: var(--theme--border-radius);\n  background-color: var(--theme--background-subdued);\n  text-align: center;\n  margin-top: 16px;\n}\n\n.action-bar[data-v-73a6c4ed] {\n  margin-bottom: 16px;\n  margin-top: -4px;\n  display: flex;\n  gap: 8px;\n  justify-content: flex-end;\n}\n\n.analysis-accordion[data-v-73a6c4ed] {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n}\n.analysis-accordion .accordion-item[data-v-73a6c4ed] {\n  border: 1px solid var(--theme--border-color);\n  border-radius: var(--theme--border-radius);\n  overflow: hidden;\n  transition: border-color 0.15s ease-in-out;\n}\n.analysis-accordion .accordion-item[data-v-73a6c4ed]:focus-within {\n  border-color: var(--theme--primary);\n}\n.analysis-accordion .accordion-item .accordion-header[data-v-73a6c4ed] {\n  margin: 0;\n}\n.analysis-accordion .accordion-item .accordion-header .accordion-trigger[data-v-73a6c4ed] {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  width: 100%;\n  padding: 8px;\n  background-color: var(--theme--background-subdued);\n  border: none;\n  font-size: inherit;\n  font-family: inherit;\n  text-align: left;\n  cursor: pointer;\n  user-select: none;\n  transition: background-color 0.15s ease-in-out;\n}\n.analysis-accordion .accordion-item .accordion-header .accordion-trigger .section-title[data-v-73a6c4ed] {\n  display: flex;\n  align-items: center;\n  font-weight: bold;\n}\n.analysis-accordion .accordion-item .accordion-header .accordion-trigger .section-title.problems[data-v-73a6c4ed] {\n  color: var(--theme--danger);\n}\n.analysis-accordion .accordion-item .accordion-header .accordion-trigger .section-title.improvements[data-v-73a6c4ed] {\n  color: var(--theme--warning);\n}\n.analysis-accordion .accordion-item .accordion-header .accordion-trigger .section-title.good[data-v-73a6c4ed] {\n  color: var(--theme--success);\n}\n.analysis-accordion .accordion-item .accordion-header .accordion-trigger .section-title > .v-icon[data-v-73a6c4ed] {\n  margin-right: 8px;\n}\n.analysis-accordion .accordion-item .accordion-header .accordion-trigger .section-title > .v-icon.error[data-v-73a6c4ed] {\n  --v-icon-color: var(--theme--danger);\n}\n.analysis-accordion .accordion-item .accordion-header .accordion-trigger .section-title > .v-icon.warning[data-v-73a6c4ed] {\n  --v-icon-color: var(--theme--warning);\n}\n.analysis-accordion .accordion-item .accordion-header .accordion-trigger .section-title > .v-icon.good[data-v-73a6c4ed] {\n  --v-icon-color: var(--theme--success);\n}\n.analysis-accordion .accordion-item .accordion-header .accordion-trigger .section-icon[data-v-73a6c4ed] {\n  margin-left: 8px;\n  color: var(--theme--foreground-subdued);\n  transition: transform 0.2s ease-in-out;\n}\n.analysis-accordion .accordion-item .accordion-header .accordion-trigger[data-state=open][data-v-73a6c4ed] {\n  border-bottom: 1px solid var(--theme--border-color);\n}\n.analysis-accordion .accordion-item .accordion-header .accordion-trigger[data-state=open] > .section-icon[data-v-73a6c4ed] {\n  transform: rotate(90deg);\n}\n.analysis-accordion .accordion-item .accordion-header .accordion-trigger[data-v-73a6c4ed]:hover {\n  background-color: var(--theme--background-normal);\n}\n.analysis-accordion .accordion-item .accordion-content[data-v-73a6c4ed] {\n  overflow: hidden;\n  background-color: var(--theme--background);\n}\n.analysis-accordion .accordion-item .accordion-content[data-state=open][data-v-73a6c4ed] {\n  padding: 8px 8px 16px 8px;\n  animation: slideDown-73a6c4ed 150ms ease-out;\n  height: auto;\n}\n.analysis-accordion .accordion-item .accordion-content[data-state=closed][data-v-73a6c4ed] {\n  padding-top: 0;\n  padding-bottom: 0;\n  padding-left: 8px;\n  padding-right: 8px;\n  animation: slideUp-73a6c4ed 150ms ease-out forwards;\n}\n\n@keyframes slideDown-73a6c4ed {\n  from {\n    height: 0;\n  }\n  to {\n    height: var(--reka-accordion-content-height);\n  }\n}\n@keyframes slideUp-73a6c4ed {\n  from {\n    height: var(--reka-accordion-content-height);\n  }\n  to {\n    height: 0;\n  }\n}",{});var ni=fa(ti,[["__scopeId","data-v-73a6c4ed"],["__file","Analysis.vue"]]);const ai={class:"field focus-keyphrase-container"},ii={class:"field-input-group"},oi={key:1,class:"keyphrase-placeholder"};var ri=o({__name:"FocusKeyphrase",props:{modelValue:{},disabled:{type:Boolean}},emits:["update:modelValue"],setup(e,{emit:t}){const n=e,a=t,i=y({get:()=>n.modelValue||"",set:e=>a("update:modelValue",e)}),o=x(null),r=x(!n.modelValue&&!n.disabled);function l(){n.disabled||(r.value=!0)}function s(){r.value=!1}return wt(o,(()=>{n.disabled||s()})),_t("Enter",(e=>{r.value&&!n.disabled&&(e.preventDefault(),s())})),(e,t)=>{const n=j("v-input"),a=j("v-chip"),d=j("v-icon"),p=j("v-button"),v=D("tooltip");return u(),P("div",ai,[B("div",ii,[t[1]||(t[1]=B("label",{class:"label field-label type-label"},"Focus Keyphrase",-1)),r.value&&!e.disabled?(u(),c(n,{key:0,ref_key:"inputRef",ref:o,modelValue:i.value,"onUpdate:modelValue":t[0]||(t[0]=e=>i.value=e),autofocus:"",placeholder:"Enter the main keyword or phrase",onBlur:s},null,8,["modelValue"])):(u(),P("div",{key:1,class:N([{disabled:e.disabled},"keyphrase-display"])},[U((u(),P("div",{class:N(["keyphrase-text",{"not-clickable":e.disabled}]),onClick:l},[i.value?(u(),c(a,{key:0},{default:f((()=>[R(z(i.value),1)])),_:1})):(u(),P("span",oi,"Enter the main keyword or phrase"))],2)),[[v,e.disabled?null:"Click to edit"]]),e.disabled?U((u(),c(d,{key:1,name:"lock",small:"",class:"lock-icon"},null,512)),[[v,"Input disabled"]]):U((u(),c(p,{key:0,small:"",secondary:"",icon:"",onClick:M(l,["stop"])},{default:f((()=>[T(d,{name:"edit"})])),_:1})),[[v,"Edit"]])],2)),t[2]||(t[2]=B("div",{class:"hint"}," The main phrase you want this content to rank for in search engines. ",-1))])])}}});pa("\n.field[data-v-6541ea4f] {\n\tdisplay: flex;\n\tflex-direction: column;\n}\n.field-input-group[data-v-6541ea4f] {\n\tmargin-bottom: 1.5rem;\n}\n.label[data-v-6541ea4f] {\n\tmargin-bottom: 0.5rem;\n\tfont-weight: bold;\n}\n.type-label[data-v-6541ea4f] {}\n.hint[data-v-6541ea4f] {\n\tmargin-top: 4px;\n\tfont-size: 0.8rem;\n\tcolor: var(--theme--foreground-subdued);\n}\n.keyphrase-display[data-v-6541ea4f] {\n\tdisplay: flex;\n\tflex-grow: 1;\n\twidth: 100%;\n\talign-items: start;\n\tgap: 8px;\n\n\tcursor: pointer;\n\ttransition: border-color var(--theme--transitions-fast) ease-in-out;\n}\n.keyphrase-display[data-v-6541ea4f]:not(.disabled):hover {\n\tborder-color: var(--theme--primary);\n}\n.keyphrase-display.disabled[data-v-6541ea4f] {\n\tcursor: not-allowed;\n\tbackground-color: var(--theme--background-subdued);\n\tborder-color: var(--theme--form--field--input--border-color-disabled);\n}\n.keyphrase-text[data-v-6541ea4f] {\n\t/* flex-grow: 1; */\n\tpadding-right: 8px;\n\tcolor: var(--theme----theme--form--field--input--foreground);\n}\n.keyphrase-text.not-clickable[data-v-6541ea4f] {\n\tcursor: not-allowed;\n}\n.keyphrase-placeholder[data-v-6541ea4f] {\n\tcolor: var(--theme--form--field--input--foreground-subdued);\n\tfont-style: italic;\n}\n.lock-icon[data-v-6541ea4f] {\n\tcolor: var(--theme--foreground-subdued);\n}\n[data-v-6541ea4f] .v-input {\n\twidth: 100%;\n}\n",{});var li=fa(ri,[["__scopeId","data-v-6541ea4f"],["__file","FocusKeyphrase.vue"]]),si=(0).constructor,di=si.isFinite;si.isInteger;var ui=[].constructor.isArray;function ci(e){return null!==e&&"object"==typeof e}function pi(e,t){return void 0===t&&(t=0),"string"==typeof e&&e.length>=t}function fi(e){return di(e)}function vi(e){return ui(e)}function mi(e,t){return ci(e)&&t in e}var hi=new(function(){function e(e){this.size=e,this.reset()}return e.prototype.reset=function(){this.oldestIndex=0,this.map={},this.cachedKeys=new Array(this.size)},e.prototype.get=function(e){return this.map[e]},e.prototype.set=function(e,t){this.map[e]=t;var n=this.cachedKeys[this.oldestIndex];void 0!==n&&delete this.map[n],this.cachedKeys[this.oldestIndex]=e,this.oldestIndex++,this.oldestIndex%=this.size},e}())(1e3);function gi(e){var t=e.charAt(0),n=e.substr(-1);if("'\"`".includes(t)||"'\"`".includes(n)){if(e.length<2||t!==n)throw new SyntaxError('Mismatching string quotation: "'+e+'"');return e.substring(1,e.length-1)}if(e.includes("["))throw new SyntaxError('Missing ] in varName "'+e+'"');return"+"===t?e.substr(1):e}function bi(e,t,n){var a=t.trim();if(""===a)return e;if(a.startsWith(".")){if(!n)throw new SyntaxError('Unexpected . at the start of "'+t+'"');if(""===(a=a.substr(1).trim()))return e}else if(n)throw new SyntaxError('Missing . at the start of "'+t+'"');if(a.endsWith("."))throw new SyntaxError('Unexpected "." at the end of "'+t+'"');for(var i=0,o=a.split(".");i<o.length;i++){var r=o[i].trim();if(""===r)throw new SyntaxError('Empty prop name when parsing "'+t+'"');e.push(r)}return e}function yi(e){if(!pi(e))throw new TypeError("Cannot parse path. Expected string. Got a "+typeof e);for(var t,n,a=0,i=!1,o=new Array(0),r=0;r<e.length&&-1!==(t=e.indexOf("[",r));r=a){if(-1===(a=e.indexOf("]",t)))throw new SyntaxError('Missing ] in varName "'+e+'"');if(0===(n=e.substring(t+1,a).trim()).length)throw new SyntaxError("Unexpected token ]");a++,bi(o,e.substring(r,t),i),o.push(gi(n)),i=!0}return bi(o,e.substring(a),i)}function wi(e,t,n){if(void 0===n&&(n={}),!ci(n))throw new TypeError("get expects an object option. Got "+typeof n);var a=n.depth,i=void 0===a?10:a;if(!fi(i)||i<=0)throw new RangeError("Expected a positive number for depth. Got "+i);var o=Array.isArray(t)?t:yi.cached(t),r=function(){return o.join(" > ")};if(o.length>i)throw new ReferenceError("The path cannot be deeper than "+i+' levels. Got "'+r()+'"');for(var l=e,s=0,d=o;s<d.length;s++){var u=d[s];if(!mi(l,u)){if(n.propsExist)throw new ReferenceError(u+' is not defined in the scope at path: "'+r()+'"');return}l=l[u]}return l}yi.cached=function(e){var t=hi.get(e);return void 0===t&&(t=yi(e),hi.set(e,t)),t};var xi=function(){function e(e,t){var n=this;if(void 0===t&&(t={}),this.tokens=e,this.options=t,this.render=function(e){void 0===e&&(e={});var t=n.tokens.varNames.length;n.cacheParsedPaths();for(var a=new Array(t),i=0;i<t;i++)a[i]=wi(e,n.toPathCache[i],n.options);return n.stringify(a)},this.renderFn=function(e,t){void 0===t&&(t={});var a=n.resolveVarNames(e,t);return n.stringify(a)},this.renderFnAsync=function(e,t){return void 0===t&&(t={}),Promise.all(n.resolveVarNames(e,t)).then((function(e){return n.stringify(e)}))},!ci(e)||!vi(e.strings)||!vi(e.varNames)||e.strings.length!==e.varNames.length+1)throw new TypeError("Invalid tokens object");if(!ci(t))throw new TypeError("Options should be an object. Got a "+typeof t);t.validateVarNames&&this.cacheParsedPaths()}return e.prototype.cacheParsedPaths=function(){var e=this.tokens.varNames;if(void 0===this.toPathCache){this.toPathCache=new Array(e.length);for(var t=0;t<e.length;t++)this.toPathCache[t]=yi.cached(e[t])}},e.prototype.resolveVarNames=function(e,t){void 0===t&&(t={});var n=this.tokens.varNames;if("function"!=typeof e)throw new TypeError("Expected a resolver function. Got "+String(e));for(var a=n.length,i=new Array(a),o=0;o<a;o++)i[o]=e.call(null,n[o],t);return i},e.prototype.stringify=function(e){for(var t=this.tokens.strings,n=this.options.explicit,a=e.length,i="",o=0;o<a;o++){i+=t[o];var r=e[o];(n||null!=r)&&(i+=r)}return i+=t[a]},e}();function _i(e,t){void 0===t&&(t={});var n=function(e,t){if(void 0===t&&(t={}),!pi(e))throw new TypeError("The template parameter must be a string. Got a "+typeof e);if(!ci(t))throw new TypeError("Options should be an object. Got a "+typeof t);var n=t.tags,a=void 0===n?["{{","}}"]:n,i=t.maxVarNameLength,o=void 0===i?1e3:i;if(!vi(a)||2!==a.length)throw TypeError("tags should be an array of two elements. Got "+String(a));var r=a[0],l=a[1];if(!pi(r,1)||!pi(l,1)||r===l)throw new TypeError('The open and close symbols should be two distinct non-empty strings. Got "'+r+'" and "'+l+'"');if(!fi(o)||o<=0)throw new Error("Expected a positive number for maxVarNameLength. Got "+o);for(var s,d,u=r.length,c=l.length,p=0,f=[],v=[],m=0;m<e.length&&-1!==(s=e.indexOf(r,m));){var h=s+u;if(-1===(p=e.substr(h,o+c).indexOf(l)))throw new SyntaxError('Missing "'+l+'" in the template for the "'+r+'" at position '+s+" within "+o+" characters");if(p+=h,0===(d=e.substring(h,p).trim()).length)throw new SyntaxError('Unexpected "'+l+'" tag found at position '+s);if(d.includes(r))throw new SyntaxError('Variable names cannot have "'+r+'". But at position '+s+'. Got "'+d+'"');v.push(d),p+=c,f.push(e.substring(m,s)),m=p}return f.push(e.substring(p)),{strings:f,varNames:v}}(e,t);return new xi(n,t)}const ki={missing:{icon:{name:"error",class:"error"},message:"Missing"},"too-long":{icon:{name:"warning",class:"warning"},message:"Too long"},"too-short":{icon:{name:"warning",class:"warning"},message:"Too short"},ideal:{icon:{name:"check",class:"success"},message:"Ideal length"}};function Ai(e,t,n=x({})){const a=x(!1),i=x(!1),o=y((()=>{var t,n;return null!=(n=null==(t=e.value)?void 0:t.length)?n:0})),r=y((()=>e.value&&t?Math.min(o.value/t.maxLength*100,100):0)),l=y((()=>e.value&&t?o.value>t.maxLength?"too-long":o.value<t.minLength?"too-short":"ideal":"missing"));return{isTouched:a,isTemplateUpdate:i,state:y((()=>{const e=l.value,t=ki[e];return{length:o.value,progress:r.value,status:e,message:t.message,icon:t.icon}})),transform:function(e){if(!e)return"";try{return t=e,a=n.value,_i(t,i).render(a)}catch(t){return console.warn("Template rendering error:",t),e}var t,a,i}}}const $i={class:"field"},Si={class:"label field-label type-label"},Ci={class:"hint"};var Oi=o({__name:"SeoFieldWrapper",props:{label:{},state:{},rule:{}},setup:e=>(e,t)=>{var n;return u(),P("div",$i,[B("label",Si,z(e.label),1),v(e.$slots,"default",{},void 0,!0),T(Ba,{progress:e.state.progress,status:e.state.status},null,8,["progress","status"]),B("small",Ci,[B("span",null,z(e.state.message),1),R(" "+z(e.rule.minLength)+"-"+z(e.rule.maxLength)+" characters recommended. (Current: "+z(null!=(n=e.state.length)?n:"?")+") ",1)])])}});pa("\n.field[data-v-54ed0d08] {\n\tdisplay: flex;\n\tflex-direction: column;\n}\n.label[data-v-54ed0d08] {\n\tmargin-bottom: 0.5rem;\n}\n.hint[data-v-54ed0d08] {\n\tfont-size: 0.875rem;\n\tcolor: var(--theme--foreground-subdued);\n}\n",{});var Ti=fa(Oi,[["__scopeId","data-v-54ed0d08"],["__file","SeoFieldWrapper.vue"]]),Ii=o({__name:"MetaDescriptionField",props:{modelValue:{},template:{},disabled:{type:Boolean}},emits:["update:modelValue"],setup(e,{emit:t}){const n=e,a=t,i=A("values"),{modelValue:o}=S(n),{isTouched:r,isTemplateUpdate:l,state:s,transform:d}=Ai(o,Ta.meta_description,i);function v(e){l.value||(r.value=!0),a("update:modelValue",e),l.value=!1}return(e,t)=>{const n=j("v-icon"),a=j("v-button"),i=j("v-textarea"),r=D("tooltip");return u(),c(Ti,{label:"Meta Description",state:p(s),rule:p(Ta).meta_description},{default:f((()=>[T(i,{"model-value":p(o),placeholder:"Enter your meta description","onUpdate:modelValue":v},K({_:2},[e.template&&!e.disabled?{name:"append",fn:f((()=>[U((u(),c(a,{"x-small":"",secondary:"",icon:"",class:"template-button",onClick:t[0]||(t[0]=t=>v(p(d)(e.template)))},{default:f((()=>[T(n,{name:"auto_fix_high"})])),_:1})),[[r,"Generate from template"]])])),key:"0"}:void 0]),1032,["model-value"])])),_:1},8,["state","rule"])}}});pa("\n.field[data-v-e0aba83f] {\n\tdisplay: flex;\n\tflex-direction: column;\n}\n.label[data-v-e0aba83f] {\n\tfont-weight: bold;\n\tmargin-bottom: 0.5rem;\n}\n.hint[data-v-e0aba83f] {\n\tfont-size: 0.875rem;\n\tcolor: var(--theme--foreground-subdued);\n}\n.textarea-wrapper[data-v-e0aba83f] {\n\tposition: relative;\n}\n.template-button[data-v-e0aba83f] {\n\tposition: absolute;\n\tz-index: 10;\n\tright: 8px;\n\ttop: 8px;\n}\n",{});var Ei=fa(Ii,[["__scopeId","data-v-e0aba83f"],["__file","MetaDescriptionField.vue"]]);const Fi={key:2,class:"image-preview"},Pi={key:0,class:"image-error"},Li={class:"message"},Mi=["src","alt"],Vi={key:2,class:"fallback"},ji={key:3,class:"actions"},Bi={class:"info"},Ni={class:"title"},zi={class:"meta"};var Ri=o({__name:"OgImage",props:{value:{default:null},disabled:{type:Boolean,default:!1},folder:{default:void 0},fileKeyToGet:{default:"filename_disk"},crop:{type:Boolean,default:!1},letterbox:{type:Boolean,default:!1},width:{default:"auto"},inOgPreview:{type:Boolean,default:!1}},emits:["input"],setup(t,{emit:n}){const a=t,i=n,o=x(!1),r=x(null),l=x(!1),s=x(null),d=x(!0),v=e(),{t:m,te:h}=q();function g(e,t,n){const a=n||function(e){const t=e.defaults.headers.common.Authorization;return"string"==typeof t&&t.split(" ")[1]||null}(e);return a?function(e,t){const n=[];for(const[e,a]of Object.entries(t))n.push(`${e}=${a}`);return e.includes("?")?`${e}&${n.join("&")}`:`${e}?${n.join("&")}`}(t,{access_token:a}):t}const w=y((()=>{var e;if(!(null==(e=r.value)?void 0:e.type))return null;if(r.value.type.includes("svg"))return g(v,`${jn.getRootPath()}assets/${r.value.id}`);if(r.value.type.includes("image")){const e=a.crop?"cover":"contain",t=`${jn.getRootPath()}assets/${r.value.id}?key=system-large-${e}&cache-buster=${r.value.modified_on}`;return g(v,t)}return null})),_=y((()=>{if(!r.value)return null;const{filesize:e,width:t,height:n,type:a}=r.value;return t&&n?`${t}x${n} • ${e} • ${a}`:`${e} • ${a}`})),k=y((()=>a.value&&"object"==typeof a.value?a.value:{}));async function A(){var e,t,n,a,i;if(d.value=!1,w.value)try{await v.get(w.value)}catch(o){const r=null==(i=null==(a=null==(n=null==(t=null==(e=o.response)?void 0:e.data)?void 0:t.errors)?void 0:n[0])?void 0:a.extensions)?void 0:i.code;s.value=r||"UNKNOWN",h(`errors.${s.value}`)||(s.value="UNKNOWN")}}function $(e){r.value=e;const t=e[a.fileKeyToGet];i("input","string"==typeof t?t:null)}function S(){i("input",null),o.value=!1,r.value=null,l.value=!1}function C(){if(!r.value)return;const e=r.value[a.fileKeyToGet];i("input","string"==typeof e?e:null)}return b((()=>a.value),((e,t)=>{e!==t&&(e&&async function(){var e;o.value=!0;try{let t="string"==typeof a.value?a.value:null==(e=a.value)?void 0:e.id;if(t){t=t.split(".").slice(0,-1).join(".");const e=await v.get(`/files/${t}`,{params:{fields:["id","title","width","height","filesize","type","filename_download"]}});r.value=null!==a.value&&"object"==typeof a.value?{...e.data.data,...a.value}:e.data.data}}finally{o.value=!1}}(),t&&null===e&&S())}),{immediate:!0}),(e,t)=>{const n=j("v-skeleton-loader"),a=j("v-notice"),i=j("v-icon"),v=j("v-button"),h=j("drawer-item"),g=j("v-upload"),b=D("tooltip");return u(),P("div",{class:N(["image",[e.width,{crop:e.crop,"og-preview-mode":e.inOgPreview}]])},[o.value?(u(),c(n,{key:0,type:"input-tall"})):e.disabled&&!r.value?(u(),c(a,{key:1,class:"disabled-placeholder",center:"",icon:"hide_image"},{default:f((()=>[R(z(p(m)("no_image_selected")),1)])),_:1})):r.value?(u(),P("div",Fi,[s.value||!w.value?(u(),P("div",Pi,[T(i,{large:"",name:"UNKNOWN"===s.value?"error":"info"},null,8,["name"]),B("span",Li,z(w.value?p(m)(`errors.${s.value}`):p(m)("errors.UNSUPPORTED_MEDIA_TYPE")),1)])):d.value?(u(),P("img",{key:1,src:w.value,class:N({"is-letterbox":e.letterbox}),alt:r.value.title||"",role:"presentation",onError:A},null,42,Mi)):(u(),P("div",Vi,[T(i,{name:"description"})])),t[2]||(t[2]=B("div",{class:"shadow"},null,-1)),e.disabled?I("v-if",!0):(u(),P("div",ji,[I(' <v-button v-tooltip="t(\'zoom\')" icon rounded @click="lightboxActive = true">\n\t\t\t\t\t\t<v-icon name="zoom_in" />\n\t\t\t\t\t</v-button> '),U((u(),c(v,{icon:"",rounded:"",href:w.value,download:r.value.filename_download},{default:f((()=>[T(i,{name:"download"})])),_:1},8,["href","download"])),[[b,p(m)("download")]]),U((u(),c(v,{icon:"",rounded:"",onClick:t[0]||(t[0]=e=>l.value=!0)},{default:f((()=>[T(i,{name:"open_in_new"})])),_:1})),[[b,p(m)("edit_item")]]),U((u(),c(v,{icon:"",rounded:"",onClick:S},{default:f((()=>[T(i,{name:"close"})])),_:1})),[[b,p(m)("deselect")]])])),B("div",Bi,[B("div",Ni,z(r.value.title),1),B("div",zi,z(_.value),1)]),!e.disabled&&r.value?(u(),c(h,{key:4,active:l.value,"onUpdate:active":t[1]||(t[1]=e=>l.value=e),collection:"directus_files","primary-key":r.value.id,edits:k.value,onInput:C},null,8,["active","primary-key","edits"])):I("v-if",!0),I(" TODO: Add lightbox functionality "),I(' <file-lightbox\n\t\t\t\t\tv-if="image"\n\t\t\t\t\tv-model="lightboxActive"\n\t\t\t\t\t:id="image.id"\n\t\t\t\t\t:title="image.title"\n\t\t\t\t/> ')])):(u(),c(g,{key:3,"from-library":"","from-url":"",folder:e.folder,onInput:$},null,8,["folder"]))],2)}}});pa(".label[data-v-7ffc177f] {\n  margin-bottom: 0.5rem;\n}\n\n.image-preview[data-v-7ffc177f] {\n  position: relative;\n  width: 100%;\n  height: var(--input-height-tall);\n  overflow: hidden;\n  background-color: var(--theme--background);\n  border-radius: var(--theme--border-radius);\n}\n.image-preview img[data-v-7ffc177f] {\n  z-index: 1;\n  width: 100%;\n  height: 100%;\n  max-height: inherit;\n  object-fit: contain;\n}\n.image-preview img.is-letterbox[data-v-7ffc177f] {\n  padding: 32px;\n}\n\n.image[data-v-7ffc177f] {\n  /* Styles for when used within OG Preview */\n}\n.image.full .image-preview[data-v-7ffc177f], .image.fill .image-preview[data-v-7ffc177f] {\n  height: auto;\n  max-height: 400px;\n}\n.image.crop .image-preview img[data-v-7ffc177f] {\n  object-fit: cover;\n}\n.image.og-preview-mode[data-v-7ffc177f] {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n}\n.image.og-preview-mode .image-preview[data-v-7ffc177f] {\n  height: 100%;\n  border-radius: 0;\n}\n.image.og-preview-mode .v-upload[data-v-7ffc177f] {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background-color: var(--theme--background);\n}\n.image.og-preview-mode img[data-v-7ffc177f] {\n  object-fit: cover;\n}\n.image.og-preview-mode .actions[data-v-7ffc177f] {\n  z-index: 10;\n  top: 50%;\n  transform: translateY(-50%);\n}\n.image.og-preview-mode .info[data-v-7ffc177f] {\n  z-index: 10;\n  opacity: 0;\n  transform: translateY(10px);\n  transition: opacity var(--fast) var(--transition), transform var(--fast) var(--transition);\n}\n.image.og-preview-mode .shadow[data-v-7ffc177f] {\n  z-index: 5;\n  opacity: 0;\n  transition: opacity var(--fast) var(--transition), height var(--fast) var(--transition);\n}\n.image.og-preview-mode .image-preview:hover .shadow[data-v-7ffc177f] {\n  opacity: 1;\n  height: 100%;\n  background: linear-gradient(180deg, rgba(38, 50, 56, 0) 0%, rgba(38, 50, 56, 0.5) 100%);\n}\n.image.og-preview-mode .image-preview:hover .info[data-v-7ffc177f] {\n  opacity: 1;\n  transform: translateY(0);\n}\n\n.fallback[data-v-7ffc177f] {\n  background-color: var(--theme--background);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  height: var(--input-height-tall);\n  border-radius: var(--theme--border-radius);\n}\n\n.image-error[data-v-7ffc177f] {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  width: 100%;\n  height: 100%;\n  color: var(--theme--foreground-subdued);\n  background-color: var(--theme--background-normal);\n}\n.image-error .v-icon[data-v-7ffc177f] {\n  margin-bottom: 6px;\n}\n.image-error .message[data-v-7ffc177f] {\n  max-width: 300px;\n  padding: 0 16px;\n  text-align: center;\n}\n\n.shadow[data-v-7ffc177f] {\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  z-index: 2;\n  width: 100%;\n  height: 40px;\n  overflow: hidden;\n  line-height: 1;\n  white-space: nowrap;\n  text-overflow: ellipsis;\n  background: linear-gradient(180deg, rgba(38, 50, 56, 0) 0%, rgba(38, 50, 56, 0.25) 100%);\n  transition: height var(--fast) var(--transition);\n}\n\n.actions[data-v-7ffc177f] {\n  --v-button-color: var(--theme--form--field--input--foreground-subdued);\n  --v-button-background-color: var(--white);\n  --v-button-color-hover: var(--theme--form--field--input--foreground);\n  --v-button-background-color-hover: var(--white);\n  position: absolute;\n  top: 30%;\n  left: 0;\n  z-index: 3;\n  display: flex;\n  justify-content: center;\n  width: 100%;\n}\n.actions .v-button[data-v-7ffc177f] {\n  margin-right: 12px;\n  transform: translateY(10px);\n  opacity: 0;\n  transition: var(--medium) var(--transition);\n  transition-property: opacity transform;\n}\n.actions .v-button[data-v-7ffc177f][data-v-7ffc177f]:nth-of-type(1) {\n  transition-delay: 0ms;\n}\n.actions .v-button[data-v-7ffc177f][data-v-7ffc177f]:nth-of-type(2) {\n  transition-delay: 25ms;\n}\n.actions .v-button[data-v-7ffc177f][data-v-7ffc177f]:nth-of-type(3) {\n  transition-delay: 50ms;\n}\n.actions .v-button[data-v-7ffc177f][data-v-7ffc177f]:nth-of-type(4) {\n  transition-delay: 75ms;\n}\n.actions .v-button[data-v-7ffc177f][data-v-7ffc177f]:nth-of-type(5) {\n  transition-delay: 100ms;\n}\n.actions .v-button[data-v-7ffc177f]:last-child {\n  margin-right: 0px;\n}\n\n.info[data-v-7ffc177f] {\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  z-index: 3;\n  width: 100%;\n  padding: 8px 12px;\n  line-height: 1.2;\n}\n\n.title[data-v-7ffc177f] {\n  color: var(--white);\n}\n\n.meta[data-v-7ffc177f] {\n  height: 17px;\n  max-height: 0;\n  overflow: hidden;\n  color: rgba(255, 255, 255, 0.75);\n  transition: max-height var(--fast) var(--transition);\n}\n\n/* General hover styles */\n.image-preview:hover .shadow[data-v-7ffc177f] {\n  height: 100%;\n  background: linear-gradient(180deg, rgba(38, 50, 56, 0) 0%, rgba(38, 50, 56, 0.5) 100%);\n}\n.image-preview:hover .actions .v-button[data-v-7ffc177f] {\n  transform: translateY(0px);\n  opacity: 1;\n}\n.image-preview:hover .meta[data-v-7ffc177f] {\n  max-height: 17px;\n}\n\n.disabled-placeholder[data-v-7ffc177f] {\n  height: var(--theme--input-height-tall);\n}",{});var Di=fa(Ri,[["__scopeId","data-v-7ffc177f"],["__file","OgImage.vue"]]),Ui=o({__name:"TitleField",props:{modelValue:{},template:{},disabled:{type:Boolean}},emits:["update:modelValue"],setup(e,{emit:t}){const n=e,a=t,i=A("values"),{modelValue:o}=S(n),{isTouched:r,isTemplateUpdate:l,state:s,transform:d}=Ai(o,Ta.title,i);function v(e){l.value||(r.value=!0),a("update:modelValue",e||""),l.value=!1}return(e,t)=>{const n=j("v-icon"),a=j("v-button"),i=j("v-input"),r=D("tooltip");return u(),c(Ti,{label:"Page Title",state:p(s),rule:p(Ta).title},{default:f((()=>[T(i,{"model-value":p(o),placeholder:"Enter your SEO title","onUpdate:modelValue":v},K({_:2},[e.template&&!e.disabled?{name:"append",fn:f((()=>[U((u(),c(a,{"x-small":"",secondary:"",icon:"",class:"template-button",onClick:t[0]||(t[0]=t=>v(p(d)(e.template)))},{default:f((()=>[T(n,{name:"auto_fix_high"})])),_:1})),[[r,"Generate from template"]])])),key:"0"}:void 0]),1032,["model-value"])])),_:1},8,["state","rule"])}}});pa("\n.field[data-v-3e32d82b] {\n\tdisplay: flex;\n\tflex-direction: column;\n}\n.label[data-v-3e32d82b] {\n\tmargin-bottom: 0.5rem;\n}\n.hint[data-v-3e32d82b] {\n\tfont-size: 0.875rem;\n\tcolor: var(--theme--foreground-subdued);\n}\n.template-button[data-v-3e32d82b] {\n\t--v-icon-color: var(--theme--foreground);\n}\n",{});var Ki=fa(Ui,[["__scopeId","data-v-3e32d82b"],["__file","TitleField.vue"]]);const qi=[{key:"no_index",label:"No Index",icon:"visibility_off",tooltip:"Prevents search engines from indexing this page"},{key:"no_follow",label:"No Follow",icon:"link_off",tooltip:"Prevents search engines from following links on this page"}],Gi=[{key:"change_frequency",label:"Change Frequency",icon:"update",tooltip:"How frequently this page is likely to change",options:[{text:"Always",value:"always"},{text:"Hourly",value:"hourly"},{text:"Daily",value:"daily"},{text:"Weekly",value:"weekly"},{text:"Monthly",value:"monthly"},{text:"Yearly",value:"yearly"},{text:"Never",value:"never"}]},{key:"priority",label:"Priority",icon:"signal_cellular_alt",tooltip:"Priority indicates the importance of this page relative to other pages (1.0 highest)",options:[{text:"Very High (1.0)",value:"1.0"},{text:"High (0.8)",value:"0.8"},{text:"Medium (0.5)",value:"0.5"},{text:"Low (0.3)",value:"0.3"},{text:"Very Low (0.1)",value:"0.1"}]}],Hi={key:0,class:"field"},Wi={class:"label field-label type-label"},Yi={class:"form-grid"},Ji={key:0,class:"hint"},Xi={key:1,class:"field"},Qi={class:"label field-label type-label"},Zi={class:"form-grid"},eo={class:"field"};var to=o({__name:"interface",props:{collection:{},field:{},value:{},disabled:{type:Boolean},values:{},canonicalUrl:{},titleTemplate:{},descriptionTemplate:{},showOgImage:{type:Boolean},showSearchControls:{type:Boolean},showSitemap:{type:Boolean},defaultChangeFrequency:{},defaultPriority:{},additionalFields:{},showFocusKeyphrase:{type:Boolean},contentFields:{},slugField:{}},emits:["input"],setup(e,{emit:t}){const n=e,a=t,{value:o}=S(n),r=y({get:()=>o.value?{...o.value,sitemap:{priority:n.defaultPriority||"0.5",change_frequency:n.defaultChangeFrequency||"weekly",...o.value.sitemap}}:{title:"",meta_description:"",focus_keyphrase:"",og_image:"",sitemap:{priority:n.defaultPriority||"0.5",change_frequency:n.defaultChangeFrequency||"weekly"},no_index:!1,no_follow:!1,additional_fields:{}},set(e){o.value=e}});function l(e,t){if(!n.value){const n=tt({},e,t);return void a("input",n)}const i={...n.value};tt(i,e,t),a("input",i)}const s=y((()=>{var e,t;return null!=(t=null==(e=n.additionalFields)?void 0:e.map((e=>{if(!e||!e.field)return console.warn("Invalid field definition:",e),null;const{field:t,type:n,...a}=e;return{field:t,name:ee(e.name||t),type:n,meta:{...a}}})).filter(Boolean))?t:[]})),d=y((()=>n.showSitemap||n.showSearchControls)),v=y((()=>{var e;return(null==(e=s.value)?void 0:e.length)>0})),m=y((()=>o.value&&n.showFocusKeyphrase)),h=y((()=>d.value||v.value||m.value));return(e,t)=>{const a=j("v-divider"),o=j("v-icon"),g=j("v-select"),b=j("interface-boolean"),y=j("v-notice"),w=j("v-form"),x=D("tooltip");return u(),c(p(Tn),{class:"seo-tabs","default-value":"metadata","unmount-on-hide":!1},{default:f((()=>[h.value?(u(),c(p(In),{key:0,class:"tabs-list","aria-label":"SEO Configuration"},{default:f((()=>[T(p(Mn),{class:"tabs-indicator"},{default:f((()=>t[5]||(t[5]=[B("div",{class:"indicator-bar"},null,-1)]))),_:1}),T(p(Ln),{class:"tab-trigger",value:"metadata"},{default:f((()=>t[6]||(t[6]=[B("span",null,"Basic",-1)]))),_:1}),d.value?(u(),c(p(Ln),{key:0,class:"tab-trigger",value:"advanced-settings"},{default:f((()=>t[7]||(t[7]=[B("span",null,"Advanced",-1)]))),_:1})):I("v-if",!0),v.value?(u(),c(p(Ln),{key:1,class:"tab-trigger",value:"custom-fields"},{default:f((()=>t[8]||(t[8]=[B("span",null,"Custom Fields",-1)]))),_:1})):I("v-if",!0),m.value?(u(),c(p(Ln),{key:2,class:"tab-trigger",value:"analysis"},{default:f((()=>t[9]||(t[9]=[B("span",null,"Keyphrase",-1)]))),_:1})):I("v-if",!0)])),_:1})):I("v-if",!0),T(p(Pn),{value:"metadata",class:"tab-content form-grid"},{default:f((()=>[I(" Title "),T(Ki,{"model-value":r.value.title,template:n.titleTemplate,disabled:n.disabled,"onUpdate:modelValue":t[0]||(t[0]=e=>l("title",e))},null,8,["model-value","template","disabled"]),I(" Meta Description "),T(Ei,{"model-value":r.value.meta_description,template:n.descriptionTemplate,disabled:n.disabled,"onUpdate:modelValue":t[1]||(t[1]=e=>l("meta_description",e))},null,8,["model-value","template","disabled"]),I(" Search Preview "),T(Oa,{title:r.value.title,"meta-description":r.value.meta_description,collection:n.collection||""},null,8,["title","meta-description","collection"]),T(a,{class:"field"}),I(" OG Image "),n.showOgImage?(u(),c(va,{key:0,title:r.value.title,description:r.value.meta_description,"og-image":r.value.og_image,url:n.canonicalUrl,class:"field"},{default:f((()=>[n.showOgImage?(u(),c(Di,{key:0,value:r.value.og_image,disabled:n.disabled,"in-og-preview":"",crop:"",onInput:t[2]||(t[2]=e=>l("og_image",e))},null,8,["value","disabled"])):I("v-if",!0)])),_:1},8,["title","description","og-image","url"])):I("v-if",!0)])),_:1}),I(" Use new computed prop for condition "),d.value?(u(),c(p(Pn),{key:1,value:"advanced-settings",class:"tab-content form-grid"},{default:f((()=>[I(" Sitemap Fields "),n.showSitemap?(u(),P("div",Hi,[B("label",Wi,[t[10]||(t[10]=R(" Sitemap Settings ")),U(T(o,{name:"info",small:"",class:"info-icon"},null,512),[[x,"Control how search engines crawl and index your site"]])]),B("div",Yi,[(u(!0),P(i,null,L(p(Gi),(e=>{var t;return u(),P("div",{key:e.key,class:"field half"},[T(g,{"model-value":(null==(t=r.value.sitemap)?void 0:t[e.key])||"",items:e.options,disabled:n.disabled,placeholder:e.label,"onUpdate:modelValue":t=>l(`sitemap.${e.key}`,t)},{prepend:f((()=>[T(o,{name:e.icon},null,8,["name"])])),_:2},1032,["model-value","items","disabled","placeholder","onUpdate:modelValue"]),e.tooltip?(u(),P("small",Ji,z(e.tooltip),1)):I("v-if",!0)])})),128))])])):I("v-if",!0),I(" Search Engine Controls "),n.showSearchControls?(u(),P("div",Xi,[B("label",Qi,[t[11]||(t[11]=R(" Search Engine Controls ")),U(T(o,{name:"info",small:"",class:"info-icon"},null,512),[[x,"Control how search engines interact with this page"]])]),B("div",Zi,[(u(!0),P(i,null,L(p(qi),(e=>(u(),c(b,{key:e.key,class:"field half","model-value":r.value[e.key],disabled:n.disabled,label:e.label,"onUpdate:modelValue":t=>l(e.key,t)},{append:f((()=>[U(T(o,{name:"visibility_off",small:"",class:"info-icon"},null,512),[[x,"Prevents search engines from indexing this page"]])])),_:2},1032,["model-value","disabled","label","onUpdate:modelValue"])))),128)),I(" Warning message when either option is enabled "),r.value.no_index||r.value.no_follow?(u(),c(y,{key:0,type:"warning",class:"field full"},{default:f((()=>[B("span",null,z(r.value.no_index&&r.value.no_follow?"This page will be hidden from search engines and its links won't be followed":r.value.no_index?"This page will be hidden from search engines":"Links on this page won't be followed by search engines"),1)])),_:1})):I("v-if",!0)])])):I("v-if",!0)])),_:1})):I("v-if",!0),v.value?(u(),c(p(Pn),{key:2,value:"custom-fields",class:"tab-content form-grid"},{default:f((()=>[I(" Custom Fields "),B("div",eo,[T(w,{fields:s.value,"model-value":r.value.additional_fields,"onUpdate:modelValue":t[3]||(t[3]=e=>l("additional_fields",e))},null,8,["fields","model-value"])])])),_:1})):I("v-if",!0),m.value?(u(),c(p(Pn),{key:3,value:"analysis",class:"tab-content"},{default:f((()=>[I(" Focus Keyphrase "),e.showFocusKeyphrase?(u(),c(li,{key:0,"model-value":r.value.focus_keyphrase||"",disabled:n.disabled,"onUpdate:modelValue":t[4]||(t[4]=e=>l("focus_keyphrase",e))},null,8,["model-value","disabled"])):I("v-if",!0),I(" Analysis "),T(ni,{"focus-keyphrase":r.value.focus_keyphrase||"",title:r.value.title||"",description:r.value.meta_description||"","slug-field":e.slugField,"content-fields":e.contentFields},null,8,["focus-keyphrase","title","description","slug-field","content-fields"])])),_:1})):I("v-if",!0)])),_:1})}}});pa(".form-grid {\n  display: grid;\n  grid-template-columns: [start] minmax(0, 1fr) [half] minmax(0, 1fr) [full];\n  gap: var(--theme--form--row-gap) var(--theme--form--column-gap);\n}\n.form-grid.with-fill {\n  grid-template-columns: [start] minmax(0, var(--form-column-max-width)) [half] minmax(0, var(--form-column-max-width)) [full] 1fr [fill];\n}\n.form-grid .field {\n  grid-column: start/fill;\n}\n@media (min-width: 960px) {\n  .form-grid .field {\n    grid-column: start/full;\n  }\n}\n.form-grid .half,\n.form-grid .half-left,\n.form-grid .half-space {\n  grid-column: start/fill;\n}\n@media (min-width: 960px) {\n  .form-grid .half,\n  .form-grid .half-left,\n  .form-grid .half-space {\n    grid-column: start/half;\n  }\n}\n.form-grid .half + .half,\n.form-grid .half-right {\n  grid-column: start/fill;\n}\n@media (min-width: 960px) {\n  .form-grid .half + .half,\n  .form-grid .half-right {\n    grid-column: half/full;\n  }\n}\n.form-grid .full {\n  grid-column: start/fill;\n}\n@media (min-width: 960px) {\n  .form-grid .full {\n    grid-column: start/full;\n  }\n}\n.form-grid .fill {\n  grid-column: start/fill;\n}\n.form-grid .first-visible-field :deep(.presentation-divider) {\n  margin-top: 0;\n}\n.form-grid.inline > .no-fields-info {\n  grid-column: 1/-1;\n}\n\n.hint {\n  font-size: 0.875rem;\n  line-height: 0.875rem;\n  color: var(--theme--foreground-subdued);\n}\n\n.tabs-list[data-v-133c521f] {\n  position: relative;\n  display: flex;\n  flex-shrink: 0;\n  border-bottom: var(--theme--border-width) solid var(--theme--border-color);\n  color: var(--header-color, var(--theme--foreground));\n  margin-bottom: 16px;\n}\n\n.tabs-indicator[data-v-133c521f] {\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  height: 2px;\n  width: var(--reka-tabs-indicator-size);\n  transform: translateX(var(--reka-tabs-indicator-position)) translateY(1px);\n  transition: width 0.3s ease, transform 0.3s ease;\n  will-change: width, transform;\n}\n.tabs-indicator .indicator-bar[data-v-133c521f] {\n  background-color: var(--theme--primary);\n  width: 100%;\n  height: 100%;\n  border-radius: 9999px;\n}\n\n.tab-trigger[data-v-133c521f] {\n  background-color: transparent;\n  padding: 10px 16px;\n  min-width: fit-content;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 15px;\n  color: var(--theme--foreground-normal);\n  user-select: none;\n  border: none;\n  border-radius: var(--theme--border-radius, 4px) var(--theme--border-radius, 4px) 0 0;\n  cursor: pointer;\n  outline: none;\n  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out;\n}\n.tab-trigger[data-v-133c521f]:hover {\n  color: var(--theme--primary);\n  background-color: var(--theme--background-subdued);\n}\n.tab-trigger[data-state=active][data-v-133c521f] {\n  color: var(--theme--primary);\n}\n.tab-trigger[data-v-133c521f]:focus-visible {\n  position: relative;\n  box-shadow: 0 0 0 var(--theme--border-width) var(--theme--primary);\n}\n\n.tab-content[data-v-133c521f] {\n  flex-grow: 1;\n  outline: none;\n}\n.tab-content[data-state=inactive][data-v-133c521f] {\n  display: none;\n}\n\n.mb-8[data-v-133c521f] {\n  margin-bottom: 1rem;\n}\n\n.info-icon[data-v-133c521f] {\n  --v-icon-color: var(--theme--foreground-subdued);\n}\n\n.label[data-v-133c521f] {\n  display: flex;\n  align-items: center;\n  gap: 4px;\n}",{});var no=t({id:"seo-interface",name:"SEO Interface",description:"Comprehensive SEO metadata management interface",icon:"search",component:fa(to,[["__scopeId","data-v-133c521f"],["__file","interface.vue"]]),types:["json"],group:"standard",options:({collection:e})=>[{field:"titleTemplate",name:"SEO Title Template",type:"string",meta:{width:"full",interface:"system-display-template",required:!1,options:{collectionName:e,font:"monospace",placeholder:"{{title}} | My Website"},note:"Define how page titles should be formatted. Use {{field}} to include dynamic content."}},{field:"descriptionTemplate",name:"Meta Description Template",type:"string",meta:{width:"full",interface:"system-display-template",required:!1,options:{collectionName:e,font:"monospace",placeholder:"{{description}}"},note:"Template for meta descriptions. Use {{field}} to include dynamic content."}},{field:"showOgImage",name:"Social Media Image",type:"boolean",meta:{note:"Adds an Open Graph image field for social media sharing",width:"full"},schema:{default_value:!1}},{name:"Additional SEO Fields",field:"divider-meta",type:"alias",meta:{interface:"presentation-divider",width:"full",options:{title:"Additional SEO Fields",inlineTitle:!0}}},{field:"showFocusKeyphrase",name:"Focus Keyphrase",type:"boolean",meta:{note:"Adds a focus keyphrase field for SEO optimization",width:"half"},schema:{default_value:!1}},{field:"showSearchControls",name:"Search Engine Controls",type:"boolean",meta:{note:"Adds controls for no-index and no-follow tags",width:"half"},schema:{default_value:!1}},{field:"showSitemap",name:"Sitemap Controls",type:"boolean",meta:{note:"Adds fields for sitemap configuration",width:"half"},schema:{default_value:!1}},{field:"divider-focus-keyphrase",type:"alias",meta:{interface:"presentation-divider",width:"full",options:{title:"Focus Keyphrase",inlineTitle:!0},hidden:!0,conditions:[{hidden:!1,rule:{_and:[{showFocusKeyphrase:{_eq:!0}}]}}]}},{field:"slugField",name:"Slug Field",type:"string",meta:{width:"half",note:"What field contains the slug for this collection?",hidden:!0,interface:"system-field",options:{collectionName:e},conditions:[{hidden:!1,rule:{_and:[{showFocusKeyphrase:{_eq:!0}}]}}]}},{field:"contentFields",name:"Content Fields",type:"csv",meta:{width:"half",interface:"system-fields",hidden:!0,note:"What fields contain the content that you want to analyze for the focus keyphrase?",options:{collectionName:e},conditions:[{hidden:!1,rule:{_and:[{showFocusKeyphrase:{_eq:!0}}]}}]}},{field:"divider-sitemap",type:"alias",meta:{interface:"presentation-divider",width:"full",options:{title:"Sitemap Defaults",inlineTitle:!0},hidden:!0,conditions:[{hidden:!1,rule:{_and:[{showSitemap:{_eq:!0}}]}}]}},{field:"defaultChangeFrequency",name:"Default Change Frequency",type:"string",meta:{note:"Default change frequency for items in this collection",width:"half",interface:"select-dropdown",hidden:!0,options:{choices:[{text:"Always",value:"always"},{text:"Hourly",value:"hourly"},{text:"Daily",value:"daily"},{text:"Weekly",value:"weekly"},{text:"Monthly",value:"monthly"},{text:"Yearly",value:"yearly"},{text:"Never",value:"never"}]},conditions:[{hidden:!1,rule:{_and:[{showSitemap:{_eq:!0}}]}}]},schema:{default_value:"weekly"}},{field:"defaultPriority",name:"Default Priority",type:"string",meta:{note:"Default priority for items in this collection (0.0 to 1.0)",width:"half",interface:"select-dropdown",hidden:!0,options:{choices:[{text:"Very High (1.0)",value:"1.0"},{text:"High (0.8)",value:"0.8"},{text:"Medium (0.5)",value:"0.5"},{text:"Low (0.3)",value:"0.3"},{text:"Very Low (0.1)",value:"0.1"}]},conditions:[{hidden:!1,rule:{_and:[{showSitemap:{_eq:!0}}]}}]},schema:{default_value:"0.5"}},{meta:{interface:"presentation-divider",width:"full",hidden:!0,conditions:[{hidden:!1,rule:{_and:[{showSitemap:{_eq:!0}}]}}]}},{field:"additionalFields",name:"Custom SEO Fields",type:"json",meta:{interface:"list",note:"Define additional custom SEO fields",options:{collection:e,fields:ao({collection:e||""})},width:"full",required:!0}}]});function ao({collection:e}){return[{name:"$t:field",field:"field",type:"string",meta:{interface:"input",width:"half",sort:2,required:!0,options:{dbSafe:!0,font:"monospace",placeholder:"$t:field_name_placeholder"}}},{name:"$t:field_width",field:"width",type:"string",meta:{interface:"select-dropdown",width:"half",sort:3,options:{choices:[{value:"half",text:"$t:half_width"},{value:"full",text:"$t:full_width"}]}}},{name:"$t:type",field:"type",type:"string",meta:{interface:"select-dropdown",width:"half",sort:4,options:{choices:[{text:"$t:string",value:"string"},{text:"$t:text",value:"text"},{divider:!0},{text:"$t:boolean",value:"boolean"},{divider:!0},{text:"$t:integer",value:"integer"},{text:"$t:bigInteger",value:"bigInteger"},{text:"$t:float",value:"float"},{text:"$t:decimal",value:"decimal"},{divider:!0},{text:"$t:geometry.All",value:"geometry"},{divider:!0},{text:"$t:timestamp",value:"timestamp"},{text:"$t:datetime",value:"dateTime"},{text:"$t:date",value:"date"},{text:"$t:time",value:"time"},{divider:!0},{text:"$t:json",value:"json"},{text:"$t:csv",value:"csv"},{text:"$t:uuid",value:"uuid"},{text:"$t:hash",value:"hash"}]}}},{name:"$t:required",field:"required",type:"boolean",meta:{interface:"boolean",sort:5,options:{label:"$t:requires_value"},width:"half"}},{name:"$t:note",field:"note",type:"string",meta:{interface:"system-input-translated-string",width:"full",sort:6,options:{placeholder:"$t:interfaces.list.field_note_placeholder"}}},{name:"$t:interfaces.list.interface_group",field:"group-interface",type:"alias",meta:{interface:"group-detail",field:"group-interface",width:"full",sort:7,options:{start:"open"},collection:e,special:["group","no-data","alias"]}},{name:"$t:interface_label",field:"interface",type:"string",meta:{interface:"system-interface",width:"half",sort:8,group:"group-interface",options:{typeField:"type"}}},{name:"$t:interface_options",field:"options",type:"string",meta:{interface:"system-interface-options",width:"full",sort:9,group:"group-interface",options:{interfaceField:"interface"}}},{name:"$t:interfaces.list.display_group",field:"group-display",type:"alias",meta:{interface:"group-detail",field:"group-display",width:"full",sort:10,options:{start:"closed"},collection:e,special:["group","no-data","alias"]}},{name:"$t:display_label",field:"display",type:"string",meta:{interface:"system-display",width:"half",group:"group-display",sort:11,options:{typeField:"type"}}},{name:"$t:display_options",field:"display_options",type:"string",meta:{interface:"system-display-options",width:"full",group:"group-display",sort:12,options:{displayField:"display"}}}]}const io={class:"seo-display"},oo={class:"preview-box"},ro={class:"header-label field-label type-label"},lo={key:0,class:"preview-box-item"},so={class:"preview-box-item border-bottom-dash"},uo={key:0},co={class:"preview-box-item border-bottom-dash"},po={key:0},fo={key:1,class:"preview-box-item border-bottom-dash"},vo={key:2,class:"preview-box-item border-bottom-dash"},mo=["textContent"];var ho=o({__name:"display",props:{value:{},collection:{},interface:{},interfaceOptions:{},type:{},field:{},showSearchPreview:{type:Boolean}},setup(e){const t=e,{state:n}=Ai(y((()=>{var e;return null==(e=t.value)?void 0:e.title})),Ta.title),{state:a}=Ai(y((()=>{var e;return null==(e=t.value)?void 0:e.meta_description})),Ta.meta_description),i=y((()=>{const e=t.interfaceOptions.additionalFields;if(!(null==e?void 0:e.length))return null;const n=t.value.additional_fields,a=e=>null!=(null==n?void 0:n[e.field])&&""!==n[e.field],i=e.length,o=e.filter(a).length,r=e.filter((e=>e.required)).filter((e=>!a(e))).length;return r>0?{icon:"error",class:"error",text:`${r} required`}:o===i?{icon:"check",class:"success"}:{icon:"warning",class:"warning",text:i-o+" optional"}})),o=y((()=>{var e,i,o,r,l,s,d;if(null==(e=t.value)?void 0:e.no_index)return{icon:"visibility_off",color:"neutral"};if(!(null==(i=t.value)?void 0:i.title)||!(null==(o=t.value)?void 0:o.meta_description))return{icon:"error",color:"error"};return(null==(r=t.interfaceOptions.additionalFields)?void 0:r.some((e=>{var n;return e.required&&(!(null==(n=t.value.additional_fields)?void 0:n[e.field])||""===t.value.additional_fields[e.field])})))?{icon:"error",color:"error"}:"ideal"!==(null==(l=n.value)?void 0:l.status)||"ideal"!==(null==(s=a.value)?void 0:s.status)||t.interfaceOptions.showOgImage&&!(null==(d=t.value)?void 0:d.og_image)?{icon:"warning",color:"warning"}:{icon:"check",color:"success"}}));return(e,t)=>{const r=j("v-icon"),l=j("v-chip"),s=j("v-menu"),d=D("tooltip");return u(),P("div",io,[T(s,{trigger:"hover",delay:300,"show-arrow":""},{activator:f((()=>[T(l,{outlined:""},{default:f((()=>[T(r,{name:o.value.icon,class:N(o.value.color)},null,8,["name","class"])])),_:1})])),default:f((()=>[B("div",oo,[B("label",ro,[t[0]||(t[0]=B("span",null,"SEO Status",-1)),U(T(r,{name:"info",small:"",class:"info-icon neutral"},null,512),[[d,"Quick checks to see if your SEO is optimized."]])]),I(" No Index "),e.interfaceOptions.showSearchControls?(u(),P("div",lo,[e.value.no_index?(u(),c(l,{key:0,small:"",label:""},{default:f((()=>[T(r,{name:"visibility_off",class:"chip-icon",small:""}),t[1]||(t[1]=R(" "+z("Hidden from search")))])),_:1})):I("v-if",!0),e.value.no_follow?(u(),c(l,{key:1,small:"",label:""},{default:f((()=>[T(r,{name:"link_off",class:"chip-icon",small:""}),t[2]||(t[2]=R(" "+z("Links not followed")))])),_:1})):I("v-if",!0)])):I("v-if",!0),I(" Title "),B("div",so,[t[3]||(t[3]=B("div",{class:"left"},[B("h3",null,"Title")],-1)),B("div",null,[T(l,{small:"",label:""},{default:f((()=>[T(r,{small:"",name:p(n).icon.name,class:N([p(n).icon.class,"chip-icon"])},null,8,["name","class"]),"ideal"!==p(n).status?(u(),P("span",uo,z(p(ee)(p(n).status)),1)):I("v-if",!0)])),_:1})])]),B("div",co,[t[4]||(t[4]=B("div",{class:"left"},[B("h3",null,"Meta Description")],-1)),B("div",null,[T(l,{small:"",label:""},{default:f((()=>[T(r,{small:"",name:p(a).icon.name,class:N([p(a).icon.class,"chip-icon"])},null,8,["name","class"]),"ideal"!==p(a).status?(u(),P("span",po,z(p(ee)(p(a).status)),1)):I("v-if",!0)])),_:1})])]),I(" OG Image "),e.interfaceOptions.showOgImage?(u(),P("div",fo,[t[5]||(t[5]=B("div",{class:"left"},[B("h3",null,"OG Image")],-1)),B("div",null,[T(l,{small:"",label:""},{default:f((()=>[T(r,{name:e.value.og_image?"check":"error",class:N([e.value.og_image?"success":"error","chip-icon"]),small:""},null,8,["name","class"]),R(" "+z(e.value.og_image?"":"Missing"),1)])),_:1})])])):I("v-if",!0),I(" Additional Fields "),i.value?(u(),P("div",vo,[t[6]||(t[6]=B("div",{class:"left"},[B("h3",null,"Additional Fields")],-1)),B("div",null,[T(l,{small:"",label:""},{default:f((()=>[T(r,{name:i.value.icon,class:N([i.value.class,"chip-icon"]),small:""},null,8,["name","class"]),i.value.text?(u(),P("span",{key:0,textContent:z(i.value.text)},null,8,mo)):I("v-if",!0)])),_:1})])])):I("v-if",!0),I(" Omitting the sitemap settings here on purpose. @TODO: Is it worth adding? "),I(" Search Preview "),e.showSearchPreview?(u(),c(Oa,{key:3,class:"full-width",title:e.value.title,"meta-description":e.value.meta_description,collection:e.collection},null,8,["title","meta-description","collection"])):I("v-if",!0)])])),_:1})])}}});pa(".seo-display[data-v-bc3389e4] {\n  display: flex;\n}\n\n.neutral[data-v-bc3389e4] {\n  --v-icon-color: var(--theme--foreground-subdued);\n}\n\n.warning[data-v-bc3389e4] {\n  --v-icon-color: var(--theme--warning);\n}\n\n.success[data-v-bc3389e4] {\n  --v-icon-color: var(--theme--success);\n}\n\n.error[data-v-bc3389e4] {\n  --v-icon-color: var(--theme--danger);\n}\n\n.header-label[data-v-bc3389e4] {\n  display: flex;\n  align-items: center;\n  gap: 4px;\n}\n\n.preview-box[data-v-bc3389e4] {\n  display: flex;\n  flex-direction: column;\n  min-width: 300px;\n  padding: 12px;\n  max-width: 400px;\n  width: 100%;\n  gap: 12px;\n  align-items: flex-start;\n}\n.preview-box-item[data-v-bc3389e4] {\n  display: flex;\n  justify-content: space-between;\n  width: 100%;\n  gap: 4px;\n}\n.preview-box-item .left[data-v-bc3389e4] {\n  display: flex;\n  align-items: center;\n  gap: 4px;\n}\n.preview-box-item-progress[data-v-bc3389e4] {\n  width: 50px;\n}\n.preview-box-item.border-top[data-v-bc3389e4] {\n  border-top: 1px dashed var(--theme--border-color);\n}\n.preview-box-item.border-bottom-dash[data-v-bc3389e4] {\n  border-bottom: 1px dashed var(--theme--border-color);\n}\n\n.full-width[data-v-bc3389e4] {\n  width: 100%;\n}\n\n.chip-icon[data-v-bc3389e4] {\n  margin-right: 4px;\n}",{});const go=[no],bo=[n({id:"seo-display",name:"SEO Display",icon:"search",description:"Display SEO metadata checks. Only use with SEO Interface.",component:fa(ho,[["__scopeId","data-v-bc3389e4"],["__file","display.vue"]]),options:[{field:"showSearchPreview",name:"Show Search Preview",type:"boolean",meta:{interface:"boolean",note:"Show a search preview of the current page when hovering over the SEO status chip."},schema:{default_value:!1}}],types:["json"]})],yo=[],wo=[],xo=[],_o=[],ko=[];export{bo as displays,go as interfaces,yo as layouts,wo as modules,ko as operations,xo as panels,_o as themes};
