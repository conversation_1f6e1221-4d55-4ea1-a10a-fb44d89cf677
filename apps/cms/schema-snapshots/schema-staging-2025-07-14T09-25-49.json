{"version": 1, "directus": "11.7.2", "vendor": "postgres", "collections": [{"collection": "Website", "meta": {"accountability": "all", "archive_app_filter": true, "archive_field": null, "archive_value": null, "collapse": "open", "collection": "Website", "color": "#3399FF", "display_template": null, "group": null, "hidden": false, "icon": "web", "item_duplication_fields": null, "note": null, "preview_url": null, "singleton": false, "sort": 1, "sort_field": null, "translations": null, "unarchive_value": null, "versioning": false}}, {"collection": "announcement_categories", "meta": {"accountability": "all", "archive_app_filter": true, "archive_field": "status", "archive_value": "archived", "collapse": "open", "collection": "announcement_categories", "color": null, "display_template": null, "group": "announcements", "hidden": false, "icon": null, "item_duplication_fields": null, "note": null, "preview_url": null, "singleton": false, "sort": 1, "sort_field": "sort", "translations": null, "unarchive_value": "draft", "versioning": false}, "schema": {"name": "announcement_categories"}}, {"collection": "announcement_categories_translations", "meta": {"accountability": "all", "archive_app_filter": true, "archive_field": null, "archive_value": null, "collapse": "open", "collection": "announcement_categories_translations", "color": null, "display_template": null, "group": "announcement_categories", "hidden": true, "icon": "import_export", "item_duplication_fields": null, "note": null, "preview_url": null, "singleton": false, "sort": 1, "sort_field": null, "translations": null, "unarchive_value": null, "versioning": false}, "schema": {"name": "announcement_categories_translations"}}, {"collection": "announcements", "meta": {"accountability": "all", "archive_app_filter": true, "archive_field": "status", "archive_value": "archived", "collapse": "open", "collection": "announcements", "color": null, "display_template": null, "group": "Website", "hidden": false, "icon": "campaign", "item_duplication_fields": null, "note": null, "preview_url": null, "singleton": false, "sort": 4, "sort_field": null, "translations": null, "unarchive_value": "draft", "versioning": false}, "schema": {"name": "announcements"}}, {"collection": "announcements_translations", "meta": {"accountability": "all", "archive_app_filter": true, "archive_field": null, "archive_value": null, "collapse": "open", "collection": "announcements_translations", "color": null, "display_template": null, "group": "announcements", "hidden": true, "icon": "import_export", "item_duplication_fields": null, "note": null, "preview_url": null, "singleton": false, "sort": 2, "sort_field": null, "translations": null, "unarchive_value": null, "versioning": false}, "schema": {"name": "announcements_translations"}}, {"collection": "blog", "meta": {"accountability": "all", "archive_app_filter": true, "archive_field": "status", "archive_value": "archived", "collapse": "open", "collection": "blog", "color": null, "display_template": "{{translations.title}}", "group": "Website", "hidden": false, "icon": "newspaper", "item_duplication_fields": null, "note": null, "preview_url": null, "singleton": false, "sort": 3, "sort_field": null, "translations": null, "unarchive_value": "draft", "versioning": false}, "schema": {"name": "blog"}}, {"collection": "blog_categories", "meta": {"accountability": "all", "archive_app_filter": true, "archive_field": "status", "archive_value": "archived", "collapse": "open", "collection": "blog_categories", "color": null, "display_template": "{{title}}", "group": "blog", "hidden": false, "icon": null, "item_duplication_fields": null, "note": null, "preview_url": null, "singleton": false, "sort": 1, "sort_field": "sort", "translations": null, "unarchive_value": "draft", "versioning": false}, "schema": {"name": "blog_categories"}}, {"collection": "blog_categories_translations", "meta": {"accountability": "all", "archive_app_filter": true, "archive_field": null, "archive_value": null, "collapse": "open", "collection": "blog_categories_translations", "color": null, "display_template": null, "group": "blog_categories", "hidden": true, "icon": "import_export", "item_duplication_fields": null, "note": null, "preview_url": null, "singleton": false, "sort": 1, "sort_field": null, "translations": null, "unarchive_value": null, "versioning": false}, "schema": {"name": "blog_categories_translations"}}, {"collection": "blog_translations", "meta": {"accountability": "all", "archive_app_filter": true, "archive_field": null, "archive_value": null, "collapse": "open", "collection": "blog_translations", "color": null, "display_template": null, "group": "blog", "hidden": true, "icon": "import_export", "item_duplication_fields": null, "note": null, "preview_url": null, "singleton": false, "sort": 3, "sort_field": null, "translations": null, "unarchive_value": null, "versioning": false}, "schema": {"name": "blog_translations"}}, {"collection": "contact", "meta": {"accountability": "all", "archive_app_filter": true, "archive_field": null, "archive_value": null, "collapse": "open", "collection": "contact", "color": null, "display_template": null, "group": "Website", "hidden": false, "icon": null, "item_duplication_fields": null, "note": null, "preview_url": null, "singleton": true, "sort": 2, "sort_field": null, "translations": null, "unarchive_value": null, "versioning": false}, "schema": {"name": "contact"}}, {"collection": "contact_translations", "meta": {"accountability": "all", "archive_app_filter": true, "archive_field": null, "archive_value": null, "collapse": "open", "collection": "contact_translations", "color": null, "display_template": null, "group": "contact", "hidden": true, "icon": "import_export", "item_duplication_fields": null, "note": null, "preview_url": null, "singleton": false, "sort": 2, "sort_field": null, "translations": null, "unarchive_value": null, "versioning": false}, "schema": {"name": "contact_translations"}}, {"collection": "faq", "meta": {"accountability": "all", "archive_app_filter": true, "archive_field": "status", "archive_value": "archived", "collapse": "open", "collection": "faq", "color": "#6366F1", "display_template": "{{translations.title}}", "group": "Website", "hidden": false, "icon": "help_outline", "item_duplication_fields": null, "note": "FAQ collection with multilingual support", "preview_url": null, "singleton": false, "sort": 5, "sort_field": null, "translations": null, "unarchive_value": "draft", "versioning": false}, "schema": {"name": "faq"}}, {"collection": "faq_categories", "meta": {"accountability": "all", "archive_app_filter": true, "archive_field": "status", "archive_value": "archived", "collapse": "open", "collection": "faq_categories", "color": null, "display_template": "{{translations.name}}", "group": "faq", "hidden": false, "icon": "folder", "item_duplication_fields": null, "note": "FAQ categories for organizing questions", "preview_url": null, "singleton": false, "sort": 2, "sort_field": "sort", "translations": null, "unarchive_value": "draft", "versioning": false}, "schema": {"name": "faq_categories"}}, {"collection": "faq_categories_translations", "meta": {"accountability": "all", "archive_app_filter": true, "archive_field": null, "archive_value": null, "collapse": "open", "collection": "faq_categories_translations", "color": null, "display_template": null, "group": "faq_categories", "hidden": true, "icon": "import_export", "item_duplication_fields": null, "note": null, "preview_url": null, "singleton": false, "sort": 1, "sort_field": null, "translations": null, "unarchive_value": null, "versioning": false}, "schema": {"name": "faq_categories_translations"}}, {"collection": "faq_translations", "meta": {"accountability": "all", "archive_app_filter": true, "archive_field": null, "archive_value": null, "collapse": "open", "collection": "faq_translations", "color": null, "display_template": "{{title}} ({{languages_code}})", "group": "faq", "hidden": true, "icon": "import_export", "item_duplication_fields": null, "note": "FAQ translations for multilingual support", "preview_url": null, "singleton": false, "sort": 1, "sort_field": null, "translations": null, "unarchive_value": null, "versioning": false}, "schema": {"name": "faq_translations"}}, {"collection": "global", "meta": {"accountability": "all", "archive_app_filter": true, "archive_field": "status", "archive_value": "archived", "collapse": "open", "collection": "global", "color": null, "display_template": null, "group": null, "hidden": false, "icon": null, "item_duplication_fields": null, "note": null, "preview_url": null, "singleton": true, "sort": 3, "sort_field": null, "translations": null, "unarchive_value": "draft", "versioning": false}, "schema": {"name": "global"}}, {"collection": "global_translations", "meta": {"accountability": "all", "archive_app_filter": true, "archive_field": null, "archive_value": null, "collapse": "open", "collection": "global_translations", "color": null, "display_template": null, "group": "global", "hidden": true, "icon": "import_export", "item_duplication_fields": null, "note": null, "preview_url": null, "singleton": false, "sort": 1, "sort_field": null, "translations": null, "unarchive_value": null, "versioning": false}, "schema": {"name": "global_translations"}}, {"collection": "homepage", "meta": {"accountability": "all", "archive_app_filter": true, "archive_field": "status", "archive_value": "archived", "collapse": "open", "collection": "homepage", "color": null, "display_template": null, "group": "Website", "hidden": false, "icon": null, "item_duplication_fields": null, "note": null, "preview_url": null, "singleton": true, "sort": 1, "sort_field": null, "translations": null, "unarchive_value": "draft", "versioning": false}, "schema": {"name": "homepage"}}, {"collection": "homepage_translations", "meta": {"accountability": "all", "archive_app_filter": true, "archive_field": null, "archive_value": null, "collapse": "open", "collection": "homepage_translations", "color": null, "display_template": null, "group": "homepage", "hidden": true, "icon": "import_export", "item_duplication_fields": null, "note": null, "preview_url": null, "singleton": false, "sort": 1, "sort_field": null, "translations": null, "unarchive_value": null, "versioning": false}, "schema": {"name": "homepage_translations"}}, {"collection": "junction_announcements_categories", "meta": {"accountability": "all", "archive_app_filter": true, "archive_field": null, "archive_value": null, "collapse": "open", "collection": "junction_announcements_categories", "color": null, "display_template": null, "group": "announcement_categories", "hidden": true, "icon": "import_export", "item_duplication_fields": null, "note": null, "preview_url": null, "singleton": false, "sort": 2, "sort_field": null, "translations": null, "unarchive_value": null, "versioning": false}, "schema": {"name": "junction_announcements_categories"}}, {"collection": "junction_blog_categories", "meta": {"accountability": "all", "archive_app_filter": true, "archive_field": null, "archive_value": null, "collapse": "open", "collection": "junction_blog_categories", "color": null, "display_template": null, "group": "blog", "hidden": true, "icon": "import_export", "item_duplication_fields": null, "note": null, "preview_url": null, "singleton": false, "sort": 2, "sort_field": null, "translations": null, "unarchive_value": null, "versioning": false}, "schema": {"name": "junction_blog_categories"}}, {"collection": "junction_contact_blog", "meta": {"accountability": "all", "archive_app_filter": true, "archive_field": null, "archive_value": null, "collapse": "open", "collection": "junction_contact_blog", "color": null, "display_template": null, "group": "contact", "hidden": true, "icon": "import_export", "item_duplication_fields": null, "note": null, "preview_url": null, "singleton": false, "sort": 1, "sort_field": null, "translations": null, "unarchive_value": null, "versioning": false}, "schema": {"name": "junction_contact_blog"}}, {"collection": "junction_faq_categories", "meta": {"accountability": "all", "archive_app_filter": true, "archive_field": null, "archive_value": null, "collapse": "open", "collection": "junction_faq_categories", "color": null, "display_template": null, "group": "faq", "hidden": true, "icon": "import_export", "item_duplication_fields": null, "note": null, "preview_url": null, "singleton": false, "sort": 3, "sort_field": null, "translations": null, "unarchive_value": null, "versioning": false}, "schema": {"name": "junction_faq_categories"}}, {"collection": "languages", "meta": {"accountability": "all", "archive_app_filter": true, "archive_field": null, "archive_value": null, "collapse": "open", "collection": "languages", "color": null, "display_template": "{{name}}", "group": null, "hidden": false, "icon": null, "item_duplication_fields": null, "note": null, "preview_url": null, "singleton": false, "sort": 2, "sort_field": null, "translations": null, "unarchive_value": null, "versioning": false}, "schema": {"name": "languages"}}], "fields": [{"collection": "announcement_categories", "field": "id", "type": "integer", "meta": {"collection": "announcement_categories", "conditions": null, "display": null, "display_options": null, "field": "id", "group": null, "hidden": true, "interface": "input", "note": null, "options": null, "readonly": true, "required": false, "sort": 1, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "id", "table": "announcement_categories", "data_type": "integer", "default_value": "nextval('announcement_categories_id_seq'::regclass)", "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_nullable": false, "is_unique": true, "is_indexed": false, "is_primary_key": true, "is_generated": false, "generation_expression": null, "has_auto_increment": true, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "announcement_categories", "field": "status", "type": "string", "meta": {"collection": "announcement_categories", "conditions": null, "display": "labels", "display_options": {"choices": [{"background": "#00C897", "foreground": "#FFFFFF", "text": "Published", "value": "published"}, {"background": "#FFC23B", "foreground": "#FFFFFF", "text": "Draft", "value": "draft"}, {"background": "#A2B5CD", "foreground": "#FFFFFF", "text": "Archived", "value": "archived"}], "showAsDot": true}, "field": "status", "group": null, "hidden": false, "interface": "select-dropdown", "note": null, "options": {"choices": [{"color": "var(--theme--primary)", "text": "$t:published", "value": "published"}, {"color": "var(--theme--foreground)", "text": "$t:draft", "value": "draft"}, {"color": "var(--theme--warning)", "text": "$t:archived", "value": "archived"}]}, "readonly": false, "required": false, "sort": 2, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "status", "table": "announcement_categories", "data_type": "character varying", "default_value": "draft", "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": false, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "announcement_categories", "field": "sort", "type": "integer", "meta": {"collection": "announcement_categories", "conditions": null, "display": "badge", "display_options": {"color": "#6644FF"}, "field": "sort", "group": null, "hidden": true, "interface": "input", "note": null, "options": null, "readonly": false, "required": false, "sort": 3, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "sort", "table": "announcement_categories", "data_type": "integer", "default_value": null, "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "announcement_categories", "field": "translations", "type": "alias", "meta": {"collection": "announcement_categories", "conditions": null, "display": "translations", "display_options": {"defaultLanguage": "en-US", "template": "{{title}}"}, "field": "translations", "group": null, "hidden": false, "interface": "translations", "note": null, "options": {"defaultLanguage": "en-US", "languageField": "name"}, "readonly": false, "required": false, "sort": 5, "special": ["translations"], "translations": null, "validation": null, "validation_message": null, "width": "full"}}, {"collection": "announcement_categories", "field": "icon", "type": "uuid", "meta": {"collection": "announcement_categories", "conditions": null, "display": null, "display_options": null, "field": "icon", "group": null, "hidden": false, "interface": "file-image", "note": null, "options": null, "readonly": false, "required": false, "sort": 4, "special": ["file"], "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "icon", "table": "announcement_categories", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "directus_files", "foreign_key_column": "id"}}, {"collection": "announcement_categories_translations", "field": "id", "type": "integer", "meta": {"collection": "announcement_categories_translations", "conditions": null, "display": null, "display_options": null, "field": "id", "group": null, "hidden": true, "interface": null, "note": null, "options": null, "readonly": false, "required": false, "sort": 1, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "id", "table": "announcement_categories_translations", "data_type": "integer", "default_value": "nextval('announcement_categories_translations_id_seq'::regclass)", "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_nullable": false, "is_unique": true, "is_indexed": false, "is_primary_key": true, "is_generated": false, "generation_expression": null, "has_auto_increment": true, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "announcement_categories_translations", "field": "announcement_categories_id", "type": "integer", "meta": {"collection": "announcement_categories_translations", "conditions": null, "display": null, "display_options": null, "field": "announcement_categories_id", "group": null, "hidden": true, "interface": null, "note": null, "options": null, "readonly": false, "required": false, "sort": 2, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "announcement_categories_id", "table": "announcement_categories_translations", "data_type": "integer", "default_value": null, "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "announcement_categories", "foreign_key_column": "id"}}, {"collection": "announcement_categories_translations", "field": "languages_code", "type": "string", "meta": {"collection": "announcement_categories_translations", "conditions": null, "display": null, "display_options": null, "field": "languages_code", "group": null, "hidden": true, "interface": null, "note": null, "options": null, "readonly": false, "required": false, "sort": 3, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "languages_code", "table": "announcement_categories_translations", "data_type": "character varying", "default_value": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "languages", "foreign_key_column": "code"}}, {"collection": "announcement_categories_translations", "field": "title", "type": "string", "meta": {"collection": "announcement_categories_translations", "conditions": null, "display": null, "display_options": null, "field": "title", "group": null, "hidden": false, "interface": "input", "note": null, "options": null, "readonly": false, "required": false, "sort": 4, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "title", "table": "announcement_categories_translations", "data_type": "character varying", "default_value": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "announcements", "field": "id", "type": "integer", "meta": {"collection": "announcements", "conditions": null, "display": null, "display_options": null, "field": "id", "group": null, "hidden": true, "interface": "input", "note": null, "options": null, "readonly": true, "required": false, "sort": 1, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "id", "table": "announcements", "data_type": "integer", "default_value": "nextval('announcements_id_seq'::regclass)", "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_nullable": false, "is_unique": true, "is_indexed": false, "is_primary_key": true, "is_generated": false, "generation_expression": null, "has_auto_increment": true, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "announcements", "field": "status", "type": "string", "meta": {"collection": "announcements", "conditions": null, "display": "labels", "display_options": {"choices": [{"background": "#00C897", "foreground": "#FFFFFF", "text": "Published", "value": "published"}, {"background": "#FFC23B", "foreground": "#FFFFFF", "text": "Draft", "value": "draft"}, {"background": "#A2B5CD", "foreground": "#FFFFFF", "text": "Archived", "value": "archived"}], "showAsDot": true}, "field": "status", "group": null, "hidden": false, "interface": "select-dropdown", "note": null, "options": {"choices": [{"color": "var(--theme--primary)", "text": "$t:published", "value": "published"}, {"color": "var(--theme--foreground)", "text": "$t:draft", "value": "draft"}, {"color": "var(--theme--warning)", "text": "$t:archived", "value": "archived"}]}, "readonly": false, "required": false, "sort": 2, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "status", "table": "announcements", "data_type": "character varying", "default_value": "draft", "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": false, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "announcements", "field": "user_created", "type": "uuid", "meta": {"collection": "announcements", "conditions": null, "display": "user", "display_options": null, "field": "user_created", "group": null, "hidden": true, "interface": "select-dropdown-m2o", "note": null, "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "readonly": true, "required": false, "sort": 3, "special": ["user-created"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "user_created", "table": "announcements", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "directus_users", "foreign_key_column": "id"}}, {"collection": "announcements", "field": "date_created", "type": "timestamp", "meta": {"collection": "announcements", "conditions": null, "display": "datetime", "display_options": {"relative": true}, "field": "date_created", "group": null, "hidden": true, "interface": "datetime", "note": null, "options": null, "readonly": true, "required": false, "sort": 4, "special": ["date-created"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "date_created", "table": "announcements", "data_type": "timestamp with time zone", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "announcements", "field": "user_updated", "type": "uuid", "meta": {"collection": "announcements", "conditions": null, "display": "user", "display_options": null, "field": "user_updated", "group": null, "hidden": true, "interface": "select-dropdown-m2o", "note": null, "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "readonly": true, "required": false, "sort": 5, "special": ["user-updated"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "user_updated", "table": "announcements", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "directus_users", "foreign_key_column": "id"}}, {"collection": "announcements", "field": "date_updated", "type": "timestamp", "meta": {"collection": "announcements", "conditions": null, "display": "datetime", "display_options": {"relative": true}, "field": "date_updated", "group": null, "hidden": true, "interface": "datetime", "note": null, "options": null, "readonly": true, "required": false, "sort": 6, "special": ["date-updated"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "date_updated", "table": "announcements", "data_type": "timestamp with time zone", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "announcements", "field": "translations", "type": "alias", "meta": {"collection": "announcements", "conditions": null, "display": "translations", "display_options": {"defaultLanguage": "en-US", "template": "{{title}} ({{languages_code}})"}, "field": "translations", "group": null, "hidden": false, "interface": "translations", "note": null, "options": {"defaultLanguage": "en-US", "languageField": "name", "userLanguage": true}, "readonly": false, "required": false, "sort": 8, "special": ["translations"], "translations": null, "validation": null, "validation_message": null, "width": "full"}}, {"collection": "announcements", "field": "categories", "type": "alias", "meta": {"collection": "announcements", "conditions": null, "display": "related-values", "display_options": {"template": "{{announcement_categories_id.translations.title}}"}, "field": "categories", "group": null, "hidden": false, "interface": "list-m2m", "note": null, "options": {"template": "{{announcement_categories_id.translations}}"}, "readonly": false, "required": false, "sort": 7, "special": ["m2m"], "translations": null, "validation": null, "validation_message": null, "width": "full"}}, {"collection": "announcements_translations", "field": "id", "type": "integer", "meta": {"collection": "announcements_translations", "conditions": null, "display": null, "display_options": null, "field": "id", "group": null, "hidden": true, "interface": null, "note": null, "options": null, "readonly": false, "required": false, "sort": 1, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "id", "table": "announcements_translations", "data_type": "integer", "default_value": "nextval('announcements_translations_id_seq'::regclass)", "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_nullable": false, "is_unique": true, "is_indexed": false, "is_primary_key": true, "is_generated": false, "generation_expression": null, "has_auto_increment": true, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "announcements_translations", "field": "announcements_id", "type": "integer", "meta": {"collection": "announcements_translations", "conditions": null, "display": null, "display_options": null, "field": "announcements_id", "group": null, "hidden": true, "interface": null, "note": null, "options": null, "readonly": false, "required": false, "sort": 2, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "announcements_id", "table": "announcements_translations", "data_type": "integer", "default_value": null, "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "announcements", "foreign_key_column": "id"}}, {"collection": "announcements_translations", "field": "languages_code", "type": "string", "meta": {"collection": "announcements_translations", "conditions": null, "display": "related-values", "display_options": {"template": "{{name}}"}, "field": "languages_code", "group": null, "hidden": true, "interface": null, "note": null, "options": null, "readonly": false, "required": false, "sort": 3, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "languages_code", "table": "announcements_translations", "data_type": "character varying", "default_value": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "languages", "foreign_key_column": "code"}}, {"collection": "announcements_translations", "field": "title", "type": "string", "meta": {"collection": "announcements_translations", "conditions": null, "display": null, "display_options": null, "field": "title", "group": null, "hidden": false, "interface": "input", "note": "<PERSON>i<PERSON><PERSON> đề của announcement cho từng ngôn ngữ. Nên ngắn gọn và mô tả rõ nội dung.", "options": null, "readonly": false, "required": true, "sort": 5, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "title", "table": "announcements_translations", "data_type": "character varying", "default_value": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "announcements_translations", "field": "content", "type": "text", "meta": {"collection": "announcements_translations", "conditions": null, "display": null, "display_options": null, "field": "content", "group": null, "hidden": false, "interface": "input-rich-text-html", "note": "<PERSON>ội dung ch<PERSON>h của announcement. Hỗ trợ HTML và formatting.", "options": {"toolbar": ["bold", "italic", "underline", "strikethrough", "subscript", "superscript", "fontselect", "fontsizeselect", "h1", "h2", "h3", "h4", "h5", "h6", "alignleft", "aligncenter", "alignright", "alignjustify", "numlist", "bullist", "outdent", "indent", "forecolor", "backcolor", "removeformat", "link", "image", "media", "table", "hr", "code", "blockquote", "undo", "redo", "fullscreen"]}, "readonly": false, "required": false, "sort": 6, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "content", "table": "announcements_translations", "data_type": "text", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "announcements_translations", "field": "slug", "type": "string", "meta": {"collection": "announcements_translations", "conditions": null, "display": null, "display_options": null, "field": "slug", "group": null, "hidden": false, "interface": "extension-wpslug", "note": null, "options": null, "readonly": false, "required": true, "sort": 4, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "slug", "table": "announcements_translations", "data_type": "character varying", "default_value": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": true, "is_indexed": true, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "blog", "field": "id", "type": "integer", "meta": {"collection": "blog", "conditions": null, "display": null, "display_options": null, "field": "id", "group": null, "hidden": true, "interface": "input", "note": null, "options": null, "readonly": true, "required": false, "sort": 1, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "id", "table": "blog", "data_type": "integer", "default_value": "nextval('blog_id_seq'::regclass)", "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_nullable": false, "is_unique": true, "is_indexed": false, "is_primary_key": true, "is_generated": false, "generation_expression": null, "has_auto_increment": true, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "blog", "field": "status", "type": "string", "meta": {"collection": "blog", "conditions": null, "display": "labels", "display_options": {"choices": [{"background": "var(--theme--primary-background)", "color": "var(--theme--primary)", "foreground": "var(--theme--primary)", "text": "$t:published", "value": "published"}, {"background": "var(--theme--background-normal)", "color": "var(--theme--foreground)", "foreground": "var(--theme--foreground)", "text": "$t:draft", "value": "draft"}, {"background": "var(--theme--warning-background)", "color": "var(--theme--warning)", "foreground": "var(--theme--warning)", "text": "$t:archived", "value": "archived"}], "showAsDot": true}, "field": "status", "group": null, "hidden": false, "interface": "select-dropdown", "note": null, "options": {"choices": [{"color": "var(--theme--primary)", "text": "$t:published", "value": "published"}, {"color": "var(--theme--foreground)", "text": "$t:draft", "value": "draft"}, {"color": "var(--theme--warning)", "text": "$t:archived", "value": "archived"}]}, "readonly": false, "required": false, "sort": 2, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "status", "table": "blog", "data_type": "character varying", "default_value": "draft", "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": false, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "blog", "field": "date_created", "type": "timestamp", "meta": {"collection": "blog", "conditions": null, "display": "datetime", "display_options": {"relative": true}, "field": "date_created", "group": null, "hidden": true, "interface": "datetime", "note": null, "options": null, "readonly": true, "required": false, "sort": 4, "special": ["date-created"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "date_created", "table": "blog", "data_type": "timestamp with time zone", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "blog", "field": "date_updated", "type": "timestamp", "meta": {"collection": "blog", "conditions": null, "display": "datetime", "display_options": {"relative": true}, "field": "date_updated", "group": null, "hidden": true, "interface": "datetime", "note": null, "options": null, "readonly": true, "required": false, "sort": 5, "special": ["date-updated"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "date_updated", "table": "blog", "data_type": "timestamp with time zone", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "blog", "field": "image", "type": "uuid", "meta": {"collection": "blog", "conditions": null, "display": null, "display_options": null, "field": "image", "group": null, "hidden": false, "interface": "file-image", "note": null, "options": null, "readonly": false, "required": false, "sort": 6, "special": ["file"], "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "image", "table": "blog", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "directus_files", "foreign_key_column": "id"}}, {"collection": "blog", "field": "translations", "type": "alias", "meta": {"collection": "blog", "conditions": null, "display": "translations", "display_options": {"defaultLanguage": "en-US", "template": "{{title}}"}, "field": "translations", "group": null, "hidden": false, "interface": "translations", "note": null, "options": {"defaultLanguage": "en-US", "languageField": "name"}, "readonly": false, "required": false, "sort": 8, "special": ["translations"], "translations": null, "validation": null, "validation_message": null, "width": "full"}}, {"collection": "blog", "field": "categories", "type": "alias", "meta": {"collection": "blog", "conditions": null, "display": "related-values", "display_options": {"template": "{{blog_categories_id.title}}"}, "field": "categories", "group": null, "hidden": false, "interface": "list-m2m", "note": null, "options": null, "readonly": false, "required": false, "sort": 7, "special": ["m2m"], "translations": null, "validation": null, "validation_message": null, "width": "full"}}, {"collection": "blog", "field": "is_featured", "type": "boolean", "meta": {"collection": "blog", "conditions": null, "display": null, "display_options": null, "field": "is_featured", "group": null, "hidden": false, "interface": "boolean", "note": null, "options": {"choices": null}, "readonly": false, "required": false, "sort": 3, "special": ["cast-boolean"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "is_featured", "table": "blog", "data_type": "boolean", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "blog_categories", "field": "id", "type": "integer", "meta": {"collection": "blog_categories", "conditions": null, "display": null, "display_options": null, "field": "id", "group": null, "hidden": true, "interface": "input", "note": null, "options": null, "readonly": true, "required": false, "sort": 1, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "id", "table": "blog_categories", "data_type": "integer", "default_value": "nextval('blog_categories_id_seq'::regclass)", "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_nullable": false, "is_unique": true, "is_indexed": false, "is_primary_key": true, "is_generated": false, "generation_expression": null, "has_auto_increment": true, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "blog_categories", "field": "status", "type": "string", "meta": {"collection": "blog_categories", "conditions": null, "display": "labels", "display_options": {"choices": [{"background": "var(--theme--primary-background)", "color": "var(--theme--primary)", "foreground": "var(--theme--primary)", "text": "$t:published", "value": "published"}, {"background": "var(--theme--background-normal)", "color": "var(--theme--foreground)", "foreground": "var(--theme--foreground)", "text": "$t:draft", "value": "draft"}, {"background": "var(--theme--warning-background)", "color": "var(--theme--warning)", "foreground": "var(--theme--warning)", "text": "$t:archived", "value": "archived"}], "showAsDot": true}, "field": "status", "group": null, "hidden": false, "interface": "select-dropdown", "note": null, "options": {"choices": [{"color": "var(--theme--primary)", "text": "$t:published", "value": "published"}, {"color": "var(--theme--foreground)", "text": "$t:draft", "value": "draft"}, {"color": "var(--theme--warning)", "text": "$t:archived", "value": "archived"}]}, "readonly": false, "required": false, "sort": 2, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "status", "table": "blog_categories", "data_type": "character varying", "default_value": "draft", "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": false, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "blog_categories", "field": "sort", "type": "integer", "meta": {"collection": "blog_categories", "conditions": null, "display": null, "display_options": null, "field": "sort", "group": null, "hidden": true, "interface": "input", "note": null, "options": null, "readonly": false, "required": false, "sort": 3, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "sort", "table": "blog_categories", "data_type": "integer", "default_value": null, "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "blog_categories", "field": "translations", "type": "alias", "meta": {"collection": "blog_categories", "conditions": null, "display": "translations", "display_options": {"template": "{{title}}"}, "field": "translations", "group": null, "hidden": false, "interface": "translations", "note": null, "options": {"languageField": "name"}, "readonly": false, "required": false, "sort": 5, "special": ["translations"], "translations": null, "validation": null, "validation_message": null, "width": "full"}}, {"collection": "blog_categories", "field": "title", "type": "string", "meta": {"collection": "blog_categories", "conditions": null, "display": "raw", "display_options": null, "field": "title", "group": null, "hidden": false, "interface": "input", "note": "Auto-generated from translations", "options": null, "readonly": false, "required": false, "sort": 1, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "title", "table": "blog_categories", "data_type": "character varying", "default_value": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "blog_categories_translations", "field": "id", "type": "integer", "meta": {"collection": "blog_categories_translations", "conditions": null, "display": null, "display_options": null, "field": "id", "group": null, "hidden": true, "interface": null, "note": null, "options": null, "readonly": false, "required": false, "sort": 1, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "id", "table": "blog_categories_translations", "data_type": "integer", "default_value": "nextval('blog_categories_translations_id_seq'::regclass)", "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_nullable": false, "is_unique": true, "is_indexed": false, "is_primary_key": true, "is_generated": false, "generation_expression": null, "has_auto_increment": true, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "blog_categories_translations", "field": "blog_categories_id", "type": "integer", "meta": {"collection": "blog_categories_translations", "conditions": null, "display": null, "display_options": null, "field": "blog_categories_id", "group": null, "hidden": true, "interface": null, "note": null, "options": null, "readonly": false, "required": false, "sort": 2, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "blog_categories_id", "table": "blog_categories_translations", "data_type": "integer", "default_value": null, "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "blog_categories", "foreign_key_column": "id"}}, {"collection": "blog_categories_translations", "field": "languages_code", "type": "string", "meta": {"collection": "blog_categories_translations", "conditions": null, "display": null, "display_options": null, "field": "languages_code", "group": null, "hidden": true, "interface": null, "note": null, "options": null, "readonly": false, "required": false, "sort": 3, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "languages_code", "table": "blog_categories_translations", "data_type": "character varying", "default_value": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "languages", "foreign_key_column": "code"}}, {"collection": "blog_categories_translations", "field": "title", "type": "string", "meta": {"collection": "blog_categories_translations", "conditions": null, "display": null, "display_options": null, "field": "title", "group": null, "hidden": false, "interface": "input", "note": null, "options": null, "readonly": false, "required": false, "sort": 4, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "title", "table": "blog_categories_translations", "data_type": "character varying", "default_value": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "blog_translations", "field": "id", "type": "integer", "meta": {"collection": "blog_translations", "conditions": null, "display": null, "display_options": null, "field": "id", "group": null, "hidden": true, "interface": null, "note": null, "options": null, "readonly": false, "required": false, "sort": 1, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "id", "table": "blog_translations", "data_type": "integer", "default_value": "nextval('blog_translations_id_seq'::regclass)", "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_nullable": false, "is_unique": true, "is_indexed": false, "is_primary_key": true, "is_generated": false, "generation_expression": null, "has_auto_increment": true, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "blog_translations", "field": "blog_id", "type": "integer", "meta": {"collection": "blog_translations", "conditions": null, "display": null, "display_options": null, "field": "blog_id", "group": null, "hidden": true, "interface": null, "note": null, "options": null, "readonly": false, "required": false, "sort": 2, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "blog_id", "table": "blog_translations", "data_type": "integer", "default_value": null, "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "blog", "foreign_key_column": "id"}}, {"collection": "blog_translations", "field": "languages_code", "type": "string", "meta": {"collection": "blog_translations", "conditions": null, "display": null, "display_options": null, "field": "languages_code", "group": null, "hidden": true, "interface": null, "note": null, "options": null, "readonly": false, "required": false, "sort": 3, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "languages_code", "table": "blog_translations", "data_type": "character varying", "default_value": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "languages", "foreign_key_column": "code"}}, {"collection": "blog_translations", "field": "title", "type": "string", "meta": {"collection": "blog_translations", "conditions": null, "display": null, "display_options": null, "field": "title", "group": null, "hidden": false, "interface": "input", "note": null, "options": null, "readonly": false, "required": false, "sort": 4, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "title", "table": "blog_translations", "data_type": "character varying", "default_value": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "blog_translations", "field": "intro", "type": "text", "meta": {"collection": "blog_translations", "conditions": null, "display": null, "display_options": null, "field": "intro", "group": null, "hidden": false, "interface": "input-multiline", "note": null, "options": null, "readonly": false, "required": false, "sort": 6, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "intro", "table": "blog_translations", "data_type": "text", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "blog_translations", "field": "content", "type": "text", "meta": {"collection": "blog_translations", "conditions": null, "display": null, "display_options": null, "field": "content", "group": null, "hidden": false, "interface": "input-rich-text-html", "note": null, "options": {"toolbar": ["undo", "redo", "bold", "italic", "underline", "strikethrough", "subscript", "superscript", "fontfamily", "fontsize", "h1", "h2", "h3", "h4", "h5", "h6", "alignleft", "aligncenter", "alignright", "alignjustify", "alignnone", "indent", "outdent", "numlist", "bullist", "forecolor", "backcolor", "removeformat", "cut", "copy", "paste", "remove", "selectall", "blockquote", "customLink", "unlink", "customImage", "customMedia", "table", "hr", "code", "fullscreen", "visualaid", "ltr rtl"]}, "readonly": false, "required": false, "sort": 7, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "content", "table": "blog_translations", "data_type": "text", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "blog_translations", "field": "slug", "type": "string", "meta": {"collection": "blog_translations", "conditions": null, "display": null, "display_options": null, "field": "slug", "group": null, "hidden": false, "interface": "extension-wpslug", "note": null, "options": null, "readonly": false, "required": true, "sort": 5, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "slug", "table": "blog_translations", "data_type": "character varying", "default_value": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": true, "is_indexed": true, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "blog_translations", "field": "seo", "type": "json", "meta": {"collection": "blog_translations", "conditions": null, "display": null, "display_options": null, "field": "seo", "group": null, "hidden": false, "interface": "seo-interface", "note": null, "options": {"showFocusKeyphrase": true, "showOgImage": true, "showSearchControls": true, "showSitemap": true}, "readonly": false, "required": false, "sort": 8, "special": ["cast-json"], "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "seo", "table": "blog_translations", "data_type": "json", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "contact", "field": "id", "type": "integer", "meta": {"collection": "contact", "conditions": null, "display": null, "display_options": null, "field": "id", "group": null, "hidden": true, "interface": "input", "note": null, "options": null, "readonly": true, "required": false, "sort": 1, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "id", "table": "contact", "data_type": "integer", "default_value": "nextval('contact_id_seq'::regclass)", "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_nullable": false, "is_unique": true, "is_indexed": false, "is_primary_key": true, "is_generated": false, "generation_expression": null, "has_auto_increment": true, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "contact", "field": "translations", "type": "alias", "meta": {"collection": "contact", "conditions": null, "display": null, "display_options": null, "field": "translations", "group": null, "hidden": false, "interface": "translations", "note": null, "options": {"defaultLanguage": "en-US", "languageDirectionField": "name", "languageField": "name", "userLanguage": true}, "readonly": false, "required": false, "sort": 3, "special": ["translations"], "translations": null, "validation": null, "validation_message": null, "width": "full"}}, {"collection": "contact", "field": "blogs", "type": "alias", "meta": {"collection": "contact", "conditions": null, "display": "related-values", "display_options": {"template": "{{blog_id.translations.title}}"}, "field": "blogs", "group": null, "hidden": false, "interface": "list-m2m", "note": null, "options": null, "readonly": false, "required": false, "sort": 2, "special": ["m2m"], "translations": null, "validation": null, "validation_message": null, "width": "full"}}, {"collection": "contact", "field": "social_networks", "type": "json", "meta": {"collection": "contact", "conditions": null, "display": "formatted-json-value", "display_options": null, "field": "social_networks", "group": null, "hidden": false, "interface": "list", "note": "<PERSON><PERSON> s<PERSON>ch các mạng xã hội và liên kết của SafeTrade", "options": {"fields": [{"field": "icon", "meta": {"interface": "input", "options": {"placeholder": "i-custom-logo-telegram"}, "width": "half"}, "name": "Icon Class", "type": "string"}, {"field": "title", "meta": {"interface": "input", "options": {"placeholder": "Telegram"}, "width": "half"}, "name": "Title", "type": "string"}, {"field": "url", "meta": {"interface": "input", "options": {"placeholder": "https://t.me/SafeTradeEx"}, "width": "full"}, "name": "URL", "type": "string"}, {"field": "order", "meta": {"interface": "input", "options": {"placeholder": "1"}, "width": "half"}, "name": "Order", "type": "integer"}], "template": "{{icon}} - {{title}}: {{url}}"}, "readonly": false, "required": false, "sort": 10, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "social_networks", "table": "contact", "data_type": "json", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "contact_translations", "field": "id", "type": "integer", "meta": {"collection": "contact_translations", "conditions": null, "display": null, "display_options": null, "field": "id", "group": null, "hidden": true, "interface": null, "note": null, "options": null, "readonly": false, "required": false, "sort": 1, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "id", "table": "contact_translations", "data_type": "integer", "default_value": "nextval('contact_translations_id_seq'::regclass)", "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_nullable": false, "is_unique": true, "is_indexed": false, "is_primary_key": true, "is_generated": false, "generation_expression": null, "has_auto_increment": true, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "contact_translations", "field": "contact_id", "type": "integer", "meta": {"collection": "contact_translations", "conditions": null, "display": null, "display_options": null, "field": "contact_id", "group": null, "hidden": true, "interface": null, "note": null, "options": null, "readonly": false, "required": false, "sort": 2, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "contact_id", "table": "contact_translations", "data_type": "integer", "default_value": null, "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "contact", "foreign_key_column": "id"}}, {"collection": "contact_translations", "field": "languages_code", "type": "string", "meta": {"collection": "contact_translations", "conditions": null, "display": null, "display_options": null, "field": "languages_code", "group": null, "hidden": true, "interface": null, "note": null, "options": null, "readonly": false, "required": false, "sort": 3, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "languages_code", "table": "contact_translations", "data_type": "character varying", "default_value": "en-US", "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "languages", "foreign_key_column": "code"}}, {"collection": "contact_translations", "field": "title", "type": "string", "meta": {"collection": "contact_translations", "conditions": null, "display": null, "display_options": null, "field": "title", "group": null, "hidden": false, "interface": "input", "note": null, "options": null, "readonly": false, "required": false, "sort": 4, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "title", "table": "contact_translations", "data_type": "character varying", "default_value": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "contact_translations", "field": "intro", "type": "text", "meta": {"collection": "contact_translations", "conditions": null, "display": null, "display_options": null, "field": "intro", "group": null, "hidden": false, "interface": "input-multiline", "note": null, "options": null, "readonly": false, "required": false, "sort": 5, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "intro", "table": "contact_translations", "data_type": "text", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "contact_translations", "field": "media_items", "type": "json", "meta": {"collection": "contact_translations", "conditions": null, "display": null, "display_options": null, "field": "media_items", "group": null, "hidden": false, "interface": "list", "note": null, "options": {"fields": [{"field": "label", "meta": {"field": "label"}, "name": "label"}, {"field": "value", "meta": {"field": "value"}, "name": "value"}]}, "readonly": false, "required": false, "sort": 6, "special": ["cast-json"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "media_items", "table": "contact_translations", "data_type": "json", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "contact_translations", "field": "support_items", "type": "json", "meta": {"collection": "contact_translations", "conditions": null, "display": null, "display_options": null, "field": "support_items", "group": null, "hidden": false, "interface": "list", "note": null, "options": {"fields": [{"field": "label", "meta": {"field": "label"}, "name": "label"}, {"field": "value", "meta": {"field": "value"}, "name": "value"}], "template": null}, "readonly": false, "required": false, "sort": 7, "special": ["cast-json"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "support_items", "table": "contact_translations", "data_type": "json", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "contact_translations", "field": "report_items", "type": "json", "meta": {"collection": "contact_translations", "conditions": null, "display": null, "display_options": null, "field": "report_items", "group": null, "hidden": false, "interface": "list", "note": null, "options": {"fields": [{"field": "label", "meta": {"field": "label"}, "name": "label"}, {"field": "value", "meta": {"field": "value"}, "name": "value"}]}, "readonly": false, "required": false, "sort": 8, "special": ["cast-json"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "report_items", "table": "contact_translations", "data_type": "json", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "contact_translations", "field": "law_items", "type": "json", "meta": {"collection": "contact_translations", "conditions": null, "display": null, "display_options": null, "field": "law_items", "group": null, "hidden": false, "interface": "list", "note": null, "options": {"fields": [{"field": "label", "meta": {"field": "label"}, "name": "label"}, {"field": "value", "meta": {"field": "value"}, "name": "value"}]}, "readonly": false, "required": false, "sort": 9, "special": ["cast-json"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "law_items", "table": "contact_translations", "data_type": "json", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "contact_translations", "field": "seo", "type": "json", "meta": {"collection": "contact_translations", "conditions": null, "display": null, "display_options": null, "field": "seo", "group": null, "hidden": false, "interface": "seo-interface", "note": null, "options": {"showFocusKeyphrase": true, "showOgImage": true, "showSearchControls": true, "showSitemap": true}, "readonly": false, "required": false, "sort": 10, "special": ["cast-json"], "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "seo", "table": "contact_translations", "data_type": "json", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "faq", "field": "id", "type": "integer", "meta": {"collection": "faq", "conditions": null, "display": null, "display_options": null, "field": "id", "group": null, "hidden": true, "interface": "input", "note": null, "options": null, "readonly": true, "required": false, "sort": 1, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "id", "table": "faq", "data_type": "integer", "default_value": "nextval('faq_id_seq'::regclass)", "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_nullable": false, "is_unique": true, "is_indexed": false, "is_primary_key": true, "is_generated": false, "generation_expression": null, "has_auto_increment": true, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "faq", "field": "status", "type": "string", "meta": {"collection": "faq", "conditions": null, "display": "labels", "display_options": {"choices": [{"background": "#00C897", "foreground": "#FFFFFF", "text": "Published", "value": "published"}, {"background": "#FFC23B", "foreground": "#FFFFFF", "text": "Draft", "value": "draft"}, {"background": "#A2B5CD", "foreground": "#FFFFFF", "text": "Archived", "value": "archived"}], "showAsDot": true}, "field": "status", "group": null, "hidden": false, "interface": "select-dropdown", "note": null, "options": {"choices": [{"color": "var(--theme--primary)", "text": "$t:published", "value": "published"}, {"color": "var(--theme--foreground)", "text": "$t:draft", "value": "draft"}, {"color": "var(--theme--warning)", "text": "$t:archived", "value": "archived"}]}, "readonly": false, "required": false, "sort": 2, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "status", "table": "faq", "data_type": "character varying", "default_value": "draft", "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": false, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "faq", "field": "user_created", "type": "uuid", "meta": {"collection": "faq", "conditions": null, "display": "user", "display_options": null, "field": "user_created", "group": null, "hidden": true, "interface": "select-dropdown-m2o", "note": null, "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "readonly": true, "required": false, "sort": 3, "special": ["user-created"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "user_created", "table": "faq", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "directus_users", "foreign_key_column": "id"}}, {"collection": "faq", "field": "date_created", "type": "timestamp", "meta": {"collection": "faq", "conditions": null, "display": "datetime", "display_options": {"relative": true}, "field": "date_created", "group": null, "hidden": true, "interface": "datetime", "note": null, "options": null, "readonly": true, "required": false, "sort": 4, "special": ["date-created"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "date_created", "table": "faq", "data_type": "timestamp with time zone", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "faq", "field": "user_updated", "type": "uuid", "meta": {"collection": "faq", "conditions": null, "display": "user", "display_options": null, "field": "user_updated", "group": null, "hidden": true, "interface": "select-dropdown-m2o", "note": null, "options": {"template": "{{avatar}} {{first_name}} {{last_name}}"}, "readonly": true, "required": false, "sort": 5, "special": ["user-updated"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "user_updated", "table": "faq", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "directus_users", "foreign_key_column": "id"}}, {"collection": "faq", "field": "date_updated", "type": "timestamp", "meta": {"collection": "faq", "conditions": null, "display": "datetime", "display_options": {"relative": true}, "field": "date_updated", "group": null, "hidden": true, "interface": "datetime", "note": null, "options": null, "readonly": true, "required": false, "sort": 6, "special": ["date-updated"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "date_updated", "table": "faq", "data_type": "timestamp with time zone", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "faq", "field": "translations", "type": "alias", "meta": {"collection": "faq", "conditions": null, "display": "translations", "display_options": {"defaultLanguage": "en-US", "template": "{{title}}"}, "field": "translations", "group": null, "hidden": false, "interface": "translations", "note": null, "options": {"defaultLanguage": "en-US", "languageField": "name"}, "readonly": false, "required": false, "sort": 8, "special": ["translations"], "translations": null, "validation": null, "validation_message": null, "width": "full"}}, {"collection": "faq", "field": "categories", "type": "alias", "meta": {"collection": "faq", "conditions": null, "display": "related-values", "display_options": {"template": "{{faq_categories_id.translations.title}}"}, "field": "categories", "group": null, "hidden": false, "interface": "list-m2m", "note": null, "options": {"template": "{{faq_categories_id.translations.title}}"}, "readonly": false, "required": false, "sort": 7, "special": ["m2m"], "translations": null, "validation": null, "validation_message": null, "width": "full"}}, {"collection": "faq_categories", "field": "id", "type": "integer", "meta": {"collection": "faq_categories", "conditions": null, "display": null, "display_options": null, "field": "id", "group": null, "hidden": true, "interface": "input", "note": null, "options": null, "readonly": true, "required": false, "sort": 1, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "id", "table": "faq_categories", "data_type": "integer", "default_value": "nextval('faq_categories_id_seq'::regclass)", "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_nullable": false, "is_unique": true, "is_indexed": false, "is_primary_key": true, "is_generated": false, "generation_expression": null, "has_auto_increment": true, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "faq_categories", "field": "status", "type": "string", "meta": {"collection": "faq_categories", "conditions": null, "display": "labels", "display_options": {"choices": [{"background": "#00C897", "foreground": "#FFFFFF", "text": "Published", "value": "published"}, {"background": "#FFC23B", "foreground": "#FFFFFF", "text": "Draft", "value": "draft"}, {"background": "#A2B5CD", "foreground": "#FFFFFF", "text": "Archived", "value": "archived"}], "showAsDot": true}, "field": "status", "group": null, "hidden": false, "interface": "select-dropdown", "note": null, "options": {"choices": [{"color": "var(--theme--primary)", "text": "$t:published", "value": "published"}, {"color": "var(--theme--foreground)", "text": "$t:draft", "value": "draft"}, {"color": "var(--theme--warning)", "text": "$t:archived", "value": "archived"}]}, "readonly": false, "required": false, "sort": 2, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "status", "table": "faq_categories", "data_type": "character varying", "default_value": "draft", "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": false, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "faq_categories", "field": "sort", "type": "integer", "meta": {"collection": "faq_categories", "conditions": null, "display": null, "display_options": null, "field": "sort", "group": null, "hidden": true, "interface": "input", "note": null, "options": null, "readonly": false, "required": false, "sort": 3, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "sort", "table": "faq_categories", "data_type": "integer", "default_value": null, "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "faq_categories", "field": "date_created", "type": "timestamp", "meta": {"collection": "faq_categories", "conditions": null, "display": "datetime", "display_options": {"relative": true}, "field": "date_created", "group": null, "hidden": true, "interface": "datetime", "note": null, "options": null, "readonly": true, "required": false, "sort": 5, "special": ["date-created"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "date_created", "table": "faq_categories", "data_type": "timestamp with time zone", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "faq_categories", "field": "date_updated", "type": "timestamp", "meta": {"collection": "faq_categories", "conditions": null, "display": "datetime", "display_options": {"relative": true}, "field": "date_updated", "group": null, "hidden": true, "interface": "datetime", "note": null, "options": null, "readonly": true, "required": false, "sort": 7, "special": ["date-updated"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "date_updated", "table": "faq_categories", "data_type": "timestamp with time zone", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "faq_categories", "field": "translations", "type": "alias", "meta": {"collection": "faq_categories", "conditions": null, "display": "translations", "display_options": {"defaultLanguage": "en-US", "languageField": "name", "template": "{{title}}"}, "field": "translations", "group": null, "hidden": false, "interface": "translations", "note": null, "options": {"defaultLanguage": "en-US", "languageField": "name", "userLanguage": true}, "readonly": false, "required": false, "sort": 8, "special": ["translations"], "translations": null, "validation": null, "validation_message": null, "width": "full"}}, {"collection": "faq_categories_translations", "field": "id", "type": "integer", "meta": {"collection": "faq_categories_translations", "conditions": null, "display": null, "display_options": null, "field": "id", "group": null, "hidden": true, "interface": null, "note": null, "options": null, "readonly": false, "required": false, "sort": 1, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "id", "table": "faq_categories_translations", "data_type": "integer", "default_value": "nextval('faq_categories_translations_id_seq'::regclass)", "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_nullable": false, "is_unique": true, "is_indexed": false, "is_primary_key": true, "is_generated": false, "generation_expression": null, "has_auto_increment": true, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "faq_categories_translations", "field": "faq_categories_id", "type": "integer", "meta": {"collection": "faq_categories_translations", "conditions": null, "display": null, "display_options": null, "field": "faq_categories_id", "group": null, "hidden": true, "interface": null, "note": null, "options": null, "readonly": false, "required": false, "sort": 2, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "faq_categories_id", "table": "faq_categories_translations", "data_type": "integer", "default_value": null, "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "faq_categories", "foreign_key_column": "id"}}, {"collection": "faq_categories_translations", "field": "languages_code", "type": "string", "meta": {"collection": "faq_categories_translations", "conditions": null, "display": null, "display_options": null, "field": "languages_code", "group": null, "hidden": true, "interface": null, "note": null, "options": null, "readonly": false, "required": false, "sort": 3, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "languages_code", "table": "faq_categories_translations", "data_type": "character varying", "default_value": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "languages", "foreign_key_column": "code"}}, {"collection": "faq_categories_translations", "field": "title", "type": "string", "meta": {"collection": "faq_categories_translations", "conditions": null, "display": null, "display_options": null, "field": "title", "group": null, "hidden": false, "interface": "input", "note": null, "options": null, "readonly": false, "required": false, "sort": 4, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "title", "table": "faq_categories_translations", "data_type": "character varying", "default_value": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "faq_translations", "field": "id", "type": "integer", "meta": {"collection": "faq_translations", "conditions": null, "display": null, "display_options": null, "field": "id", "group": null, "hidden": true, "interface": "input", "note": null, "options": null, "readonly": true, "required": false, "sort": 1, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "id", "table": "faq_translations", "data_type": "integer", "default_value": "nextval('faq_translations_id_seq'::regclass)", "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_nullable": false, "is_unique": true, "is_indexed": false, "is_primary_key": true, "is_generated": false, "generation_expression": null, "has_auto_increment": true, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "faq_translations", "field": "faq_id", "type": "integer", "meta": {"collection": "faq_translations", "conditions": null, "display": null, "display_options": null, "field": "faq_id", "group": null, "hidden": true, "interface": "select-dropdown-m2o", "note": null, "options": {"template": "{{id}}"}, "readonly": false, "required": false, "sort": 2, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "faq_id", "table": "faq_translations", "data_type": "integer", "default_value": null, "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "faq", "foreign_key_column": "id"}}, {"collection": "faq_translations", "field": "languages_code", "type": "string", "meta": {"collection": "faq_translations", "conditions": null, "display": null, "display_options": null, "field": "languages_code", "group": null, "hidden": true, "interface": "select-dropdown-m2o", "note": null, "options": {"template": "{{name}}"}, "readonly": false, "required": false, "sort": 3, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "languages_code", "table": "faq_translations", "data_type": "character varying", "default_value": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "languages", "foreign_key_column": "code"}}, {"collection": "faq_translations", "field": "title", "type": "string", "meta": {"collection": "faq_translations", "conditions": null, "display": null, "display_options": null, "field": "title", "group": null, "hidden": false, "interface": "input", "note": null, "options": null, "readonly": false, "required": false, "sort": 5, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "title", "table": "faq_translations", "data_type": "character varying", "default_value": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "faq_translations", "field": "content", "type": "text", "meta": {"collection": "faq_translations", "conditions": null, "display": null, "display_options": null, "field": "content", "group": null, "hidden": false, "interface": "input-rich-text-html", "note": null, "options": null, "readonly": false, "required": false, "sort": 6, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "content", "table": "faq_translations", "data_type": "text", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "faq_translations", "field": "slug", "type": "string", "meta": {"collection": "faq_translations", "conditions": null, "display": null, "display_options": null, "field": "slug", "group": null, "hidden": false, "interface": "input", "note": null, "options": {"slug": true}, "readonly": false, "required": false, "sort": 4, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "slug", "table": "faq_translations", "data_type": "character varying", "default_value": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "global", "field": "id", "type": "integer", "meta": {"collection": "global", "conditions": null, "display": null, "display_options": null, "field": "id", "group": null, "hidden": true, "interface": "input", "note": null, "options": null, "readonly": true, "required": false, "sort": 1, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "id", "table": "global", "data_type": "integer", "default_value": "nextval('global_id_seq'::regclass)", "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_nullable": false, "is_unique": true, "is_indexed": false, "is_primary_key": true, "is_generated": false, "generation_expression": null, "has_auto_increment": true, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "global", "field": "status", "type": "string", "meta": {"collection": "global", "conditions": null, "display": "labels", "display_options": {"choices": [{"background": "var(--theme--primary-background)", "color": "var(--theme--primary)", "foreground": "var(--theme--primary)", "text": "$t:published", "value": "published"}, {"background": "var(--theme--background-normal)", "color": "var(--theme--foreground)", "foreground": "var(--theme--foreground)", "text": "$t:draft", "value": "draft"}, {"background": "var(--theme--warning-background)", "color": "var(--theme--warning)", "foreground": "var(--theme--warning)", "text": "$t:archived", "value": "archived"}], "showAsDot": true}, "field": "status", "group": null, "hidden": false, "interface": "select-dropdown", "note": null, "options": {"choices": [{"color": "var(--theme--primary)", "text": "$t:published", "value": "published"}, {"color": "var(--theme--foreground)", "text": "$t:draft", "value": "draft"}, {"color": "var(--theme--warning)", "text": "$t:archived", "value": "archived"}]}, "readonly": false, "required": false, "sort": 2, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "status", "table": "global", "data_type": "character varying", "default_value": "draft", "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": false, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "global", "field": "socials", "type": "json", "meta": {"collection": "global", "conditions": null, "display": null, "display_options": null, "field": "socials", "group": null, "hidden": false, "interface": "list", "note": null, "options": {"fields": [{"field": "social", "meta": {"field": "social", "interface": "select-dropdown", "options": {"choices": [{"icon": "mail", "text": "Mail", "value": "mail"}, {"icon": "telegram", "text": "Telegram", "value": "telegram"}, {"icon": "square_x_twitter", "text": "X", "value": "x"}, {"icon": "facebook", "text": "Facebook", "value": "facebook"}, {"icon": "medium_m", "text": "Medium", "value": "medium"}, {"icon": "reddit_alien", "text": "Reddit", "value": "reddit"}, {"icon": "instagram", "text": "Instagram", "value": "instagram"}, {"icon": "youtube", "text": "Youtube", "value": "youtube"}, {"icon": "discord", "text": "Discord", "value": "discord"}]}, "type": "string", "width": "full"}, "name": "social", "type": "string"}, {"field": "url", "meta": {"field": "url", "interface": null, "type": "string"}, "name": "url", "type": "string"}]}, "readonly": false, "required": false, "sort": 3, "special": ["cast-json"], "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "socials", "table": "global", "data_type": "json", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "global", "field": "translations", "type": "alias", "meta": {"collection": "global", "conditions": null, "display": null, "display_options": null, "field": "translations", "group": null, "hidden": false, "interface": "translations", "note": null, "options": {"defaultLanguage": "en-US", "languageDirectionField": "name", "languageField": "name", "userLanguage": true}, "readonly": false, "required": false, "sort": 4, "special": ["translations"], "translations": null, "validation": null, "validation_message": null, "width": "full"}}, {"collection": "global_translations", "field": "id", "type": "integer", "meta": {"collection": "global_translations", "conditions": null, "display": null, "display_options": null, "field": "id", "group": null, "hidden": true, "interface": null, "note": null, "options": null, "readonly": false, "required": false, "sort": 1, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "id", "table": "global_translations", "data_type": "integer", "default_value": "nextval('global_translations_id_seq'::regclass)", "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_nullable": false, "is_unique": true, "is_indexed": false, "is_primary_key": true, "is_generated": false, "generation_expression": null, "has_auto_increment": true, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "global_translations", "field": "global_id", "type": "integer", "meta": {"collection": "global_translations", "conditions": null, "display": null, "display_options": null, "field": "global_id", "group": null, "hidden": true, "interface": null, "note": null, "options": null, "readonly": false, "required": false, "sort": 2, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "global_id", "table": "global_translations", "data_type": "integer", "default_value": null, "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "global", "foreign_key_column": "id"}}, {"collection": "global_translations", "field": "languages_code", "type": "string", "meta": {"collection": "global_translations", "conditions": null, "display": null, "display_options": null, "field": "languages_code", "group": null, "hidden": true, "interface": null, "note": null, "options": null, "readonly": false, "required": false, "sort": 3, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "languages_code", "table": "global_translations", "data_type": "character varying", "default_value": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "languages", "foreign_key_column": "code"}}, {"collection": "global_translations", "field": "title", "type": "string", "meta": {"collection": "global_translations", "conditions": null, "display": null, "display_options": null, "field": "title", "group": null, "hidden": false, "interface": "input", "note": null, "options": null, "readonly": false, "required": false, "sort": 4, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "title", "table": "global_translations", "data_type": "character varying", "default_value": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "global_translations", "field": "intro", "type": "text", "meta": {"collection": "global_translations", "conditions": null, "display": null, "display_options": null, "field": "intro", "group": null, "hidden": false, "interface": "input-multiline", "note": null, "options": null, "readonly": false, "required": false, "sort": 5, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "intro", "table": "global_translations", "data_type": "text", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "global_translations", "field": "copyright", "type": "string", "meta": {"collection": "global_translations", "conditions": null, "display": null, "display_options": null, "field": "copyright", "group": null, "hidden": false, "interface": "input", "note": null, "options": null, "readonly": false, "required": false, "sort": 6, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "copyright", "table": "global_translations", "data_type": "character varying", "default_value": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "global_translations", "field": "seo", "type": "json", "meta": {"collection": "global_translations", "conditions": null, "display": null, "display_options": null, "field": "seo", "group": null, "hidden": false, "interface": "seo-interface", "note": null, "options": null, "readonly": false, "required": false, "sort": 7, "special": ["cast-json"], "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "seo", "table": "global_translations", "data_type": "json", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "homepage", "field": "id", "type": "integer", "meta": {"collection": "homepage", "conditions": null, "display": null, "display_options": null, "field": "id", "group": null, "hidden": true, "interface": "input", "note": null, "options": null, "readonly": true, "required": false, "sort": 1, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "id", "table": "homepage", "data_type": "integer", "default_value": "nextval('homepage_id_seq'::regclass)", "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_nullable": false, "is_unique": true, "is_indexed": false, "is_primary_key": true, "is_generated": false, "generation_expression": null, "has_auto_increment": true, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "homepage", "field": "status", "type": "string", "meta": {"collection": "homepage", "conditions": null, "display": "labels", "display_options": {"choices": [{"background": "var(--theme--primary-background)", "color": "var(--theme--primary)", "foreground": "var(--theme--primary)", "text": "$t:published", "value": "published"}, {"background": "var(--theme--background-normal)", "color": "var(--theme--foreground)", "foreground": "var(--theme--foreground)", "text": "$t:draft", "value": "draft"}, {"background": "var(--theme--warning-background)", "color": "var(--theme--warning)", "foreground": "var(--theme--warning)", "text": "$t:archived", "value": "archived"}], "showAsDot": true}, "field": "status", "group": null, "hidden": false, "interface": "select-dropdown", "note": null, "options": {"choices": [{"color": "var(--theme--primary)", "text": "$t:published", "value": "published"}, {"color": "var(--theme--foreground)", "text": "$t:draft", "value": "draft"}, {"color": "var(--theme--warning)", "text": "$t:archived", "value": "archived"}]}, "readonly": false, "required": false, "sort": 2, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "status", "table": "homepage", "data_type": "character varying", "default_value": "draft", "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": false, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "homepage", "field": "banner_image_light", "type": "uuid", "meta": {"collection": "homepage", "conditions": null, "display": null, "display_options": null, "field": "banner_image_light", "group": null, "hidden": false, "interface": "file-image", "note": null, "options": null, "readonly": false, "required": false, "sort": 3, "special": ["file"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "banner_image_light", "table": "homepage", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "directus_files", "foreign_key_column": "id"}}, {"collection": "homepage", "field": "banner_image_dark", "type": "uuid", "meta": {"collection": "homepage", "conditions": null, "display": null, "display_options": null, "field": "banner_image_dark", "group": null, "hidden": false, "interface": "file-image", "note": null, "options": null, "readonly": false, "required": false, "sort": 4, "special": ["file"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "banner_image_dark", "table": "homepage", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "directus_files", "foreign_key_column": "id"}}, {"collection": "homepage", "field": "banner_download_light", "type": "uuid", "meta": {"collection": "homepage", "conditions": null, "display": null, "display_options": null, "field": "banner_download_light", "group": null, "hidden": false, "interface": "file-image", "note": null, "options": null, "readonly": false, "required": false, "sort": 5, "special": ["file"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "banner_download_light", "table": "homepage", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "directus_files", "foreign_key_column": "id"}}, {"collection": "homepage", "field": "banner_download_dark", "type": "uuid", "meta": {"collection": "homepage", "conditions": null, "display": null, "display_options": null, "field": "banner_download_dark", "group": null, "hidden": false, "interface": "file-image", "note": null, "options": null, "readonly": false, "required": false, "sort": 6, "special": ["file"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "banner_download_dark", "table": "homepage", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "directus_files", "foreign_key_column": "id"}}, {"collection": "homepage", "field": "translations", "type": "alias", "meta": {"collection": "homepage", "conditions": null, "display": null, "display_options": null, "field": "translations", "group": null, "hidden": false, "interface": "translations", "note": null, "options": {"defaultLanguage": "en-US", "languageField": "name", "userLanguage": true}, "readonly": false, "required": false, "sort": 8, "special": ["translations"], "translations": null, "validation": null, "validation_message": null, "width": "full"}}, {"collection": "homepage", "field": "image_qr_download", "type": "uuid", "meta": {"collection": "homepage", "conditions": null, "display": null, "display_options": null, "field": "image_qr_download", "group": null, "hidden": false, "interface": "file-image", "note": null, "options": null, "readonly": false, "required": false, "sort": 7, "special": ["file"], "translations": null, "validation": null, "validation_message": null, "width": "half"}, "schema": {"name": "image_qr_download", "table": "homepage", "data_type": "uuid", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "directus_files", "foreign_key_column": "id"}}, {"collection": "homepage_translations", "field": "id", "type": "integer", "meta": {"collection": "homepage_translations", "conditions": null, "display": null, "display_options": null, "field": "id", "group": null, "hidden": true, "interface": null, "note": null, "options": null, "readonly": false, "required": false, "sort": 1, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "id", "table": "homepage_translations", "data_type": "integer", "default_value": "nextval('homepage_translations_id_seq'::regclass)", "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_nullable": false, "is_unique": true, "is_indexed": false, "is_primary_key": true, "is_generated": false, "generation_expression": null, "has_auto_increment": true, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "homepage_translations", "field": "homepage_id", "type": "integer", "meta": {"collection": "homepage_translations", "conditions": null, "display": null, "display_options": null, "field": "homepage_id", "group": null, "hidden": true, "interface": null, "note": null, "options": null, "readonly": false, "required": false, "sort": 2, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "homepage_id", "table": "homepage_translations", "data_type": "integer", "default_value": null, "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "homepage", "foreign_key_column": "id"}}, {"collection": "homepage_translations", "field": "languages_code", "type": "string", "meta": {"collection": "homepage_translations", "conditions": null, "display": null, "display_options": null, "field": "languages_code", "group": null, "hidden": true, "interface": null, "note": null, "options": null, "readonly": false, "required": false, "sort": 3, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "languages_code", "table": "homepage_translations", "data_type": "character varying", "default_value": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "languages", "foreign_key_column": "code"}}, {"collection": "homepage_translations", "field": "hero_banner_title", "type": "string", "meta": {"collection": "homepage_translations", "conditions": null, "display": null, "display_options": null, "field": "hero_banner_title", "group": "hero_banner", "hidden": false, "interface": "input", "note": null, "options": null, "readonly": false, "required": false, "sort": 1, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "hero_banner_title", "table": "homepage_translations", "data_type": "character varying", "default_value": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "homepage_translations", "field": "hero_banner_intro", "type": "string", "meta": {"collection": "homepage_translations", "conditions": null, "display": null, "display_options": null, "field": "hero_banner_intro", "group": "hero_banner", "hidden": false, "interface": "input", "note": null, "options": null, "readonly": false, "required": false, "sort": 2, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "hero_banner_intro", "table": "homepage_translations", "data_type": "character varying", "default_value": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "homepage_translations", "field": "banner_download_title", "type": "string", "meta": {"collection": "homepage_translations", "conditions": null, "display": null, "display_options": null, "field": "banner_download_title", "group": "banner_download", "hidden": false, "interface": "input", "note": null, "options": null, "readonly": false, "required": false, "sort": 1, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "banner_download_title", "table": "homepage_translations", "data_type": "character varying", "default_value": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "homepage_translations", "field": "banner_download_intro", "type": "text", "meta": {"collection": "homepage_translations", "conditions": null, "display": null, "display_options": null, "field": "banner_download_intro", "group": "banner_download", "hidden": false, "interface": "input-rich-text-html", "note": null, "options": null, "readonly": false, "required": false, "sort": 2, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "banner_download_intro", "table": "homepage_translations", "data_type": "text", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "homepage_translations", "field": "link_download_ios", "type": "string", "meta": {"collection": "homepage_translations", "conditions": null, "display": null, "display_options": null, "field": "link_download_ios", "group": "banner_download", "hidden": false, "interface": "input", "note": null, "options": null, "readonly": false, "required": false, "sort": 5, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "link_download_ios", "table": "homepage_translations", "data_type": "character varying", "default_value": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "homepage_translations", "field": "link_download_google", "type": "string", "meta": {"collection": "homepage_translations", "conditions": null, "display": null, "display_options": null, "field": "link_download_google", "group": "banner_download", "hidden": false, "interface": "input", "note": null, "options": null, "readonly": false, "required": false, "sort": 3, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "link_download_google", "table": "homepage_translations", "data_type": "character varying", "default_value": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "homepage_translations", "field": "link_download_apk", "type": "string", "meta": {"collection": "homepage_translations", "conditions": null, "display": null, "display_options": null, "field": "link_download_apk", "group": "banner_download", "hidden": false, "interface": "input", "note": null, "options": null, "readonly": false, "required": false, "sort": 4, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "link_download_apk", "table": "homepage_translations", "data_type": "character varying", "default_value": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "homepage_translations", "field": "services_title", "type": "string", "meta": {"collection": "homepage_translations", "conditions": null, "display": null, "display_options": null, "field": "services_title", "group": "services", "hidden": false, "interface": "input", "note": null, "options": null, "readonly": false, "required": false, "sort": 1, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "services_title", "table": "homepage_translations", "data_type": "character varying", "default_value": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "homepage_translations", "field": "services_intro", "type": "text", "meta": {"collection": "homepage_translations", "conditions": null, "display": null, "display_options": null, "field": "services_intro", "group": "services", "hidden": false, "interface": "input-multiline", "note": null, "options": null, "readonly": false, "required": false, "sort": 2, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "services_intro", "table": "homepage_translations", "data_type": "text", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "homepage_translations", "field": "services_items", "type": "json", "meta": {"collection": "homepage_translations", "conditions": null, "display": null, "display_options": null, "field": "services_items", "group": "services", "hidden": false, "interface": "list", "note": null, "options": {"fields": [{"field": "title", "meta": {"field": "title", "interface": null, "type": "string", "width": "full"}, "name": "title", "type": "string"}, {"field": "intro", "meta": {"field": "intro", "interface": null, "type": "text", "width": "full"}, "name": "intro", "type": "text"}, {"field": "id_image", "meta": {"field": "id_image", "interface": null, "type": "string", "width": "full"}, "name": "id_image", "type": "string"}, {"field": "url", "meta": {"field": "url", "interface": null, "type": "string", "width": "full"}, "name": "url", "type": "string"}]}, "readonly": false, "required": false, "sort": 3, "special": ["cast-json"], "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "services_items", "table": "homepage_translations", "data_type": "json", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "homepage_translations", "field": "statistics_items", "type": "json", "meta": {"collection": "homepage_translations", "conditions": null, "display": null, "display_options": null, "field": "statistics_items", "group": null, "hidden": false, "interface": "list", "note": null, "options": {"fields": [{"field": "label", "meta": {"field": "label", "interface": null, "type": "string"}, "name": "label", "type": "string"}, {"field": "value", "meta": {"field": "value", "interface": null, "type": "string", "width": "full"}, "name": "value", "type": "string"}]}, "readonly": false, "required": false, "sort": 5, "special": ["cast-json"], "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "statistics_items", "table": "homepage_translations", "data_type": "json", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "homepage_translations", "field": "cet_title", "type": "text", "meta": {"collection": "homepage_translations", "conditions": null, "display": null, "display_options": null, "field": "cet_title", "group": "cet", "hidden": false, "interface": "input-rich-text-html", "note": null, "options": {"toolbar": ["bold", "italic", "underline", "numlist", "bullist", "customLink"]}, "readonly": false, "required": false, "sort": 1, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "cet_title", "table": "homepage_translations", "data_type": "text", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "homepage_translations", "field": "cet_intro", "type": "text", "meta": {"collection": "homepage_translations", "conditions": null, "display": null, "display_options": null, "field": "cet_intro", "group": "cet", "hidden": false, "interface": "input-multiline", "note": null, "options": null, "readonly": false, "required": false, "sort": 2, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "cet_intro", "table": "homepage_translations", "data_type": "text", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "homepage_translations", "field": "cet_items", "type": "json", "meta": {"collection": "homepage_translations", "conditions": null, "display": null, "display_options": null, "field": "cet_items", "group": "cet", "hidden": false, "interface": "list", "note": null, "options": {"fields": [{"field": "label", "meta": {"field": "label", "interface": "input", "type": "string", "width": "full"}, "name": "label", "type": "string"}, {"field": "value", "meta": {"field": "value", "interface": "input", "type": "string"}, "name": "value", "type": "string"}]}, "readonly": false, "required": false, "sort": 3, "special": ["cast-json"], "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "cet_items", "table": "homepage_translations", "data_type": "json", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "homepage_translations", "field": "hero_banner", "type": "alias", "meta": {"collection": "homepage_translations", "conditions": null, "display": null, "display_options": null, "field": "hero_banner", "group": null, "hidden": false, "interface": "group-detail", "note": null, "options": null, "readonly": false, "required": false, "sort": 4, "special": ["alias", "no-data", "group"], "translations": null, "validation": null, "validation_message": null, "width": "full"}}, {"collection": "homepage_translations", "field": "banner_download", "type": "alias", "meta": {"collection": "homepage_translations", "conditions": null, "display": null, "display_options": null, "field": "banner_download", "group": null, "hidden": false, "interface": "group-detail", "note": null, "options": null, "readonly": false, "required": false, "sort": 6, "special": ["alias", "no-data", "group"], "translations": null, "validation": null, "validation_message": null, "width": "full"}}, {"collection": "homepage_translations", "field": "services", "type": "alias", "meta": {"collection": "homepage_translations", "conditions": null, "display": null, "display_options": null, "field": "services", "group": null, "hidden": false, "interface": "group-detail", "note": null, "options": null, "readonly": false, "required": false, "sort": 7, "special": ["alias", "no-data", "group"], "translations": null, "validation": null, "validation_message": null, "width": "full"}}, {"collection": "homepage_translations", "field": "cet", "type": "alias", "meta": {"collection": "homepage_translations", "conditions": null, "display": null, "display_options": null, "field": "cet", "group": null, "hidden": false, "interface": "group-detail", "note": null, "options": null, "readonly": false, "required": false, "sort": 8, "special": ["alias", "no-data", "group"], "translations": null, "validation": null, "validation_message": null, "width": "full"}}, {"collection": "homepage_translations", "field": "seo", "type": "json", "meta": {"collection": "homepage_translations", "conditions": null, "display": null, "display_options": null, "field": "seo", "group": null, "hidden": false, "interface": "seo-interface", "note": null, "options": {"showFocusKeyphrase": true, "showOgImage": true, "showSearchControls": true, "showSitemap": true}, "readonly": false, "required": false, "sort": 9, "special": ["cast-json"], "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "seo", "table": "homepage_translations", "data_type": "json", "default_value": null, "max_length": null, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "junction_announcements_categories", "field": "id", "type": "integer", "meta": {"collection": "junction_announcements_categories", "conditions": null, "display": null, "display_options": null, "field": "id", "group": null, "hidden": true, "interface": null, "note": null, "options": null, "readonly": false, "required": false, "sort": 1, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "id", "table": "junction_announcements_categories", "data_type": "integer", "default_value": "nextval('junction_announcements_categories_id_seq'::regclass)", "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_nullable": false, "is_unique": true, "is_indexed": false, "is_primary_key": true, "is_generated": false, "generation_expression": null, "has_auto_increment": true, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "junction_announcements_categories", "field": "announcements_id", "type": "integer", "meta": {"collection": "junction_announcements_categories", "conditions": null, "display": null, "display_options": null, "field": "announcements_id", "group": null, "hidden": true, "interface": null, "note": null, "options": null, "readonly": false, "required": false, "sort": 2, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "announcements_id", "table": "junction_announcements_categories", "data_type": "integer", "default_value": null, "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "announcements", "foreign_key_column": "id"}}, {"collection": "junction_announcements_categories", "field": "announcement_categories_id", "type": "integer", "meta": {"collection": "junction_announcements_categories", "conditions": null, "display": null, "display_options": null, "field": "announcement_categories_id", "group": null, "hidden": true, "interface": null, "note": null, "options": null, "readonly": false, "required": false, "sort": 3, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "announcement_categories_id", "table": "junction_announcements_categories", "data_type": "integer", "default_value": null, "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "announcement_categories", "foreign_key_column": "id"}}, {"collection": "junction_blog_categories", "field": "id", "type": "integer", "meta": {"collection": "junction_blog_categories", "conditions": null, "display": null, "display_options": null, "field": "id", "group": null, "hidden": true, "interface": null, "note": null, "options": null, "readonly": false, "required": false, "sort": 1, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "id", "table": "junction_blog_categories", "data_type": "integer", "default_value": "nextval('junction_blog_categories_id_seq'::regclass)", "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_nullable": false, "is_unique": true, "is_indexed": false, "is_primary_key": true, "is_generated": false, "generation_expression": null, "has_auto_increment": true, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "junction_blog_categories", "field": "blog_id", "type": "integer", "meta": {"collection": "junction_blog_categories", "conditions": null, "display": null, "display_options": null, "field": "blog_id", "group": null, "hidden": true, "interface": null, "note": null, "options": null, "readonly": false, "required": false, "sort": 2, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "blog_id", "table": "junction_blog_categories", "data_type": "integer", "default_value": null, "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "blog", "foreign_key_column": "id"}}, {"collection": "junction_blog_categories", "field": "blog_categories_id", "type": "integer", "meta": {"collection": "junction_blog_categories", "conditions": null, "display": null, "display_options": null, "field": "blog_categories_id", "group": null, "hidden": true, "interface": null, "note": null, "options": null, "readonly": false, "required": false, "sort": 3, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "blog_categories_id", "table": "junction_blog_categories", "data_type": "integer", "default_value": null, "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "blog_categories", "foreign_key_column": "id"}}, {"collection": "junction_contact_blog", "field": "id", "type": "integer", "meta": {"collection": "junction_contact_blog", "conditions": null, "display": null, "display_options": null, "field": "id", "group": null, "hidden": true, "interface": null, "note": null, "options": null, "readonly": false, "required": false, "sort": 1, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "id", "table": "junction_contact_blog", "data_type": "integer", "default_value": "nextval('junction_contact_blog_id_seq'::regclass)", "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_nullable": false, "is_unique": true, "is_indexed": false, "is_primary_key": true, "is_generated": false, "generation_expression": null, "has_auto_increment": true, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "junction_contact_blog", "field": "contact_id", "type": "integer", "meta": {"collection": "junction_contact_blog", "conditions": null, "display": null, "display_options": null, "field": "contact_id", "group": null, "hidden": true, "interface": null, "note": null, "options": null, "readonly": false, "required": false, "sort": 2, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "contact_id", "table": "junction_contact_blog", "data_type": "integer", "default_value": null, "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "contact", "foreign_key_column": "id"}}, {"collection": "junction_contact_blog", "field": "blog_id", "type": "integer", "meta": {"collection": "junction_contact_blog", "conditions": null, "display": null, "display_options": null, "field": "blog_id", "group": null, "hidden": true, "interface": null, "note": null, "options": null, "readonly": false, "required": false, "sort": 3, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "blog_id", "table": "junction_contact_blog", "data_type": "integer", "default_value": null, "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "blog", "foreign_key_column": "id"}}, {"collection": "junction_faq_categories", "field": "id", "type": "integer", "meta": {"collection": "junction_faq_categories", "conditions": null, "display": null, "display_options": null, "field": "id", "group": null, "hidden": true, "interface": null, "note": null, "options": null, "readonly": false, "required": false, "sort": 1, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "id", "table": "junction_faq_categories", "data_type": "integer", "default_value": "nextval('junction_faq_categories_id_seq'::regclass)", "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_nullable": false, "is_unique": true, "is_indexed": false, "is_primary_key": true, "is_generated": false, "generation_expression": null, "has_auto_increment": true, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "junction_faq_categories", "field": "faq_id", "type": "integer", "meta": {"collection": "junction_faq_categories", "conditions": null, "display": null, "display_options": null, "field": "faq_id", "group": null, "hidden": true, "interface": null, "note": null, "options": null, "readonly": false, "required": false, "sort": 2, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "faq_id", "table": "junction_faq_categories", "data_type": "integer", "default_value": null, "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "faq", "foreign_key_column": "id"}}, {"collection": "junction_faq_categories", "field": "faq_categories_id", "type": "integer", "meta": {"collection": "junction_faq_categories", "conditions": null, "display": null, "display_options": null, "field": "faq_categories_id", "group": null, "hidden": true, "interface": null, "note": null, "options": null, "readonly": false, "required": false, "sort": 3, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "faq_categories_id", "table": "junction_faq_categories", "data_type": "integer", "default_value": null, "max_length": null, "numeric_precision": 32, "numeric_scale": 0, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": "faq_categories", "foreign_key_column": "id"}}, {"collection": "languages", "field": "code", "type": "string", "meta": {"collection": "languages", "conditions": null, "display": null, "display_options": null, "field": "code", "group": null, "hidden": false, "interface": "input", "note": null, "options": null, "readonly": false, "required": false, "sort": 1, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "code", "table": "languages", "data_type": "character varying", "default_value": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": false, "is_unique": true, "is_indexed": false, "is_primary_key": true, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "languages", "field": "name", "type": "string", "meta": {"collection": "languages", "conditions": null, "display": null, "display_options": null, "field": "name", "group": null, "hidden": false, "interface": "input", "note": null, "options": null, "readonly": false, "required": false, "sort": 2, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "name", "table": "languages", "data_type": "character varying", "default_value": null, "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}, {"collection": "languages", "field": "direction", "type": "string", "meta": {"collection": "languages", "conditions": null, "display": null, "display_options": null, "field": "direction", "group": null, "hidden": false, "interface": "select-dropdown", "note": null, "options": {"choices": [{"text": "Left to right", "value": "ltr"}, {"text": "Right to left", "value": "rtl"}]}, "readonly": false, "required": false, "sort": 3, "special": null, "translations": null, "validation": null, "validation_message": null, "width": "full"}, "schema": {"name": "direction", "table": "languages", "data_type": "character varying", "default_value": "ltr", "max_length": 255, "numeric_precision": null, "numeric_scale": null, "is_nullable": true, "is_unique": false, "is_indexed": false, "is_primary_key": false, "is_generated": false, "generation_expression": null, "has_auto_increment": false, "foreign_key_table": null, "foreign_key_column": null}}], "relations": [{"collection": "announcement_categories", "field": "icon", "related_collection": "directus_files", "meta": {"junction_field": null, "many_collection": "announcement_categories", "many_field": "icon", "one_allowed_collections": null, "one_collection": "directus_files", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": null, "sort_field": null}, "schema": {"table": "announcement_categories", "column": "icon", "foreign_key_table": "directus_files", "foreign_key_column": "id", "constraint_name": "announcement_categories_icon_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "announcement_categories_translations", "field": "languages_code", "related_collection": "languages", "meta": {"junction_field": "announcement_categories_id", "many_collection": "announcement_categories_translations", "many_field": "languages_code", "one_allowed_collections": null, "one_collection": "languages", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": null, "sort_field": null}, "schema": {"table": "announcement_categories_translations", "column": "languages_code", "foreign_key_table": "languages", "foreign_key_column": "code", "constraint_name": "announcement_categories_translations_languages_code_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "announcement_categories_translations", "field": "announcement_categories_id", "related_collection": "announcement_categories", "meta": {"junction_field": "languages_code", "many_collection": "announcement_categories_translations", "many_field": "announcement_categories_id", "one_allowed_collections": null, "one_collection": "announcement_categories", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": "translations", "sort_field": null}, "schema": {"table": "announcement_categories_translations", "column": "announcement_categories_id", "foreign_key_table": "announcement_categories", "foreign_key_column": "id", "constraint_name": "announcement_categories_translations_annou__34b891f3_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "announcements", "field": "user_created", "related_collection": "directus_users", "meta": {"junction_field": null, "many_collection": "announcements", "many_field": "user_created", "one_allowed_collections": null, "one_collection": "directus_users", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": null, "sort_field": null}, "schema": {"table": "announcements", "column": "user_created", "foreign_key_table": "directus_users", "foreign_key_column": "id", "constraint_name": "announcements_user_created_foreign", "on_update": "NO ACTION", "on_delete": "NO ACTION"}}, {"collection": "announcements", "field": "user_updated", "related_collection": "directus_users", "meta": {"junction_field": null, "many_collection": "announcements", "many_field": "user_updated", "one_allowed_collections": null, "one_collection": "directus_users", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": null, "sort_field": null}, "schema": {"table": "announcements", "column": "user_updated", "foreign_key_table": "directus_users", "foreign_key_column": "id", "constraint_name": "announcements_user_updated_foreign", "on_update": "NO ACTION", "on_delete": "NO ACTION"}}, {"collection": "announcements_translations", "field": "languages_code", "related_collection": "languages", "meta": {"junction_field": "announcements_id", "many_collection": "announcements_translations", "many_field": "languages_code", "one_allowed_collections": null, "one_collection": "languages", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": null, "sort_field": null}, "schema": {"table": "announcements_translations", "column": "languages_code", "foreign_key_table": "languages", "foreign_key_column": "code", "constraint_name": "announcements_translations_languages_code_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "announcements_translations", "field": "announcements_id", "related_collection": "announcements", "meta": {"junction_field": "languages_code", "many_collection": "announcements_translations", "many_field": "announcements_id", "one_allowed_collections": null, "one_collection": "announcements", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": "translations", "sort_field": null}, "schema": {"table": "announcements_translations", "column": "announcements_id", "foreign_key_table": "announcements", "foreign_key_column": "id", "constraint_name": "announcements_translations_announcements_id_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "blog", "field": "image", "related_collection": "directus_files", "meta": {"junction_field": null, "many_collection": "blog", "many_field": "image", "one_allowed_collections": null, "one_collection": "directus_files", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": null, "sort_field": null}, "schema": {"table": "blog", "column": "image", "foreign_key_table": "directus_files", "foreign_key_column": "id", "constraint_name": "blog_image_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "blog_categories_translations", "field": "languages_code", "related_collection": "languages", "meta": {"junction_field": "blog_categories_id", "many_collection": "blog_categories_translations", "many_field": "languages_code", "one_allowed_collections": null, "one_collection": "languages", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": null, "sort_field": null}, "schema": {"table": "blog_categories_translations", "column": "languages_code", "foreign_key_table": "languages", "foreign_key_column": "code", "constraint_name": "blog_categories_translations_languages_code_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "blog_categories_translations", "field": "blog_categories_id", "related_collection": "blog_categories", "meta": {"junction_field": "languages_code", "many_collection": "blog_categories_translations", "many_field": "blog_categories_id", "one_allowed_collections": null, "one_collection": "blog_categories", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": "translations", "sort_field": null}, "schema": {"table": "blog_categories_translations", "column": "blog_categories_id", "foreign_key_table": "blog_categories", "foreign_key_column": "id", "constraint_name": "blog_categories_translations_blog_categories_id_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "blog_translations", "field": "languages_code", "related_collection": "languages", "meta": {"junction_field": "blog_id", "many_collection": "blog_translations", "many_field": "languages_code", "one_allowed_collections": null, "one_collection": "languages", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": null, "sort_field": null}, "schema": {"table": "blog_translations", "column": "languages_code", "foreign_key_table": "languages", "foreign_key_column": "code", "constraint_name": "blog_translations_languages_code_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "blog_translations", "field": "blog_id", "related_collection": "blog", "meta": {"junction_field": "languages_code", "many_collection": "blog_translations", "many_field": "blog_id", "one_allowed_collections": null, "one_collection": "blog", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": "translations", "sort_field": null}, "schema": {"table": "blog_translations", "column": "blog_id", "foreign_key_table": "blog", "foreign_key_column": "id", "constraint_name": "blog_translations_blog_id_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "contact_translations", "field": "languages_code", "related_collection": "languages", "meta": {"junction_field": "contact_id", "many_collection": "contact_translations", "many_field": "languages_code", "one_allowed_collections": null, "one_collection": "languages", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": null, "sort_field": null}, "schema": {"table": "contact_translations", "column": "languages_code", "foreign_key_table": "languages", "foreign_key_column": "code", "constraint_name": "contact_translations_languages_code_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "contact_translations", "field": "contact_id", "related_collection": "contact", "meta": {"junction_field": "languages_code", "many_collection": "contact_translations", "many_field": "contact_id", "one_allowed_collections": null, "one_collection": "contact", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": "translations", "sort_field": null}, "schema": {"table": "contact_translations", "column": "contact_id", "foreign_key_table": "contact", "foreign_key_column": "id", "constraint_name": "contact_translations_contact_id_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "faq", "field": "user_created", "related_collection": "directus_users", "meta": {"junction_field": null, "many_collection": "faq", "many_field": "user_created", "one_allowed_collections": null, "one_collection": "directus_users", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": null, "sort_field": null}, "schema": {"table": "faq", "column": "user_created", "foreign_key_table": "directus_users", "foreign_key_column": "id", "constraint_name": "faq_user_created_foreign", "on_update": "NO ACTION", "on_delete": "NO ACTION"}}, {"collection": "faq", "field": "user_updated", "related_collection": "directus_users", "meta": {"junction_field": null, "many_collection": "faq", "many_field": "user_updated", "one_allowed_collections": null, "one_collection": "directus_users", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": null, "sort_field": null}, "schema": {"table": "faq", "column": "user_updated", "foreign_key_table": "directus_users", "foreign_key_column": "id", "constraint_name": "faq_user_updated_foreign", "on_update": "NO ACTION", "on_delete": "NO ACTION"}}, {"collection": "faq_categories_translations", "field": "languages_code", "related_collection": "languages", "meta": {"junction_field": "faq_categories_id", "many_collection": "faq_categories_translations", "many_field": "languages_code", "one_allowed_collections": null, "one_collection": "languages", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": null, "sort_field": null}, "schema": {"table": "faq_categories_translations", "column": "languages_code", "foreign_key_table": "languages", "foreign_key_column": "code", "constraint_name": "faq_categories_translations_languages_code_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "faq_categories_translations", "field": "faq_categories_id", "related_collection": "faq_categories", "meta": {"junction_field": "languages_code", "many_collection": "faq_categories_translations", "many_field": "faq_categories_id", "one_allowed_collections": null, "one_collection": "faq_categories", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": "translations", "sort_field": null}, "schema": {"table": "faq_categories_translations", "column": "faq_categories_id", "foreign_key_table": "faq_categories", "foreign_key_column": "id", "constraint_name": "faq_categories_translations_faq_categories_id_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "faq_translations", "field": "faq_id", "related_collection": "faq", "meta": {"junction_field": "languages_code", "many_collection": "faq_translations", "many_field": "faq_id", "one_allowed_collections": null, "one_collection": "faq", "one_collection_field": null, "one_deselect_action": "delete", "one_field": "translations", "sort_field": null}, "schema": {"table": "faq_translations", "column": "faq_id", "foreign_key_table": "faq", "foreign_key_column": "id", "constraint_name": "faq_translations_faq_id_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "faq_translations", "field": "languages_code", "related_collection": "languages", "meta": {"junction_field": "faq_id", "many_collection": "faq_translations", "many_field": "languages_code", "one_allowed_collections": null, "one_collection": "languages", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": null, "sort_field": null}, "schema": {"table": "faq_translations", "column": "languages_code", "foreign_key_table": "languages", "foreign_key_column": "code", "constraint_name": "faq_translations_languages_code_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "global_translations", "field": "languages_code", "related_collection": "languages", "meta": {"junction_field": "global_id", "many_collection": "global_translations", "many_field": "languages_code", "one_allowed_collections": null, "one_collection": "languages", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": null, "sort_field": null}, "schema": {"table": "global_translations", "column": "languages_code", "foreign_key_table": "languages", "foreign_key_column": "code", "constraint_name": "global_translations_languages_code_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "global_translations", "field": "global_id", "related_collection": "global", "meta": {"junction_field": "languages_code", "many_collection": "global_translations", "many_field": "global_id", "one_allowed_collections": null, "one_collection": "global", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": "translations", "sort_field": null}, "schema": {"table": "global_translations", "column": "global_id", "foreign_key_table": "global", "foreign_key_column": "id", "constraint_name": "global_translations_global_id_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "homepage", "field": "banner_image_light", "related_collection": "directus_files", "meta": {"junction_field": null, "many_collection": "homepage", "many_field": "banner_image_light", "one_allowed_collections": null, "one_collection": "directus_files", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": null, "sort_field": null}, "schema": {"table": "homepage", "column": "banner_image_light", "foreign_key_table": "directus_files", "foreign_key_column": "id", "constraint_name": "homepage_banner_image_light_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "homepage", "field": "banner_image_dark", "related_collection": "directus_files", "meta": {"junction_field": null, "many_collection": "homepage", "many_field": "banner_image_dark", "one_allowed_collections": null, "one_collection": "directus_files", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": null, "sort_field": null}, "schema": {"table": "homepage", "column": "banner_image_dark", "foreign_key_table": "directus_files", "foreign_key_column": "id", "constraint_name": "homepage_banner_image_dark_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "homepage", "field": "banner_download_light", "related_collection": "directus_files", "meta": {"junction_field": null, "many_collection": "homepage", "many_field": "banner_download_light", "one_allowed_collections": null, "one_collection": "directus_files", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": null, "sort_field": null}, "schema": {"table": "homepage", "column": "banner_download_light", "foreign_key_table": "directus_files", "foreign_key_column": "id", "constraint_name": "homepage_banner_download_light_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "homepage", "field": "banner_download_dark", "related_collection": "directus_files", "meta": {"junction_field": null, "many_collection": "homepage", "many_field": "banner_download_dark", "one_allowed_collections": null, "one_collection": "directus_files", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": null, "sort_field": null}, "schema": {"table": "homepage", "column": "banner_download_dark", "foreign_key_table": "directus_files", "foreign_key_column": "id", "constraint_name": "homepage_banner_download_dark_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "homepage", "field": "image_qr_download", "related_collection": "directus_files", "meta": {"junction_field": null, "many_collection": "homepage", "many_field": "image_qr_download", "one_allowed_collections": null, "one_collection": "directus_files", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": null, "sort_field": null}, "schema": {"table": "homepage", "column": "image_qr_download", "foreign_key_table": "directus_files", "foreign_key_column": "id", "constraint_name": "homepage_image_qr_download_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "homepage_translations", "field": "languages_code", "related_collection": "languages", "meta": {"junction_field": "homepage_id", "many_collection": "homepage_translations", "many_field": "languages_code", "one_allowed_collections": null, "one_collection": "languages", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": null, "sort_field": null}, "schema": {"table": "homepage_translations", "column": "languages_code", "foreign_key_table": "languages", "foreign_key_column": "code", "constraint_name": "homepage_translations_languages_code_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "homepage_translations", "field": "homepage_id", "related_collection": "homepage", "meta": {"junction_field": "languages_code", "many_collection": "homepage_translations", "many_field": "homepage_id", "one_allowed_collections": null, "one_collection": "homepage", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": "translations", "sort_field": null}, "schema": {"table": "homepage_translations", "column": "homepage_id", "foreign_key_table": "homepage", "foreign_key_column": "id", "constraint_name": "homepage_translations_homepage_id_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "junction_announcements_categories", "field": "announcement_categories_id", "related_collection": "announcement_categories", "meta": {"junction_field": "announcements_id", "many_collection": "junction_announcements_categories", "many_field": "announcement_categories_id", "one_allowed_collections": null, "one_collection": "announcement_categories", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": null, "sort_field": null}, "schema": {"table": "junction_announcements_categories", "column": "announcement_categories_id", "foreign_key_table": "announcement_categories", "foreign_key_column": "id", "constraint_name": "junction_announcements_categories_announce__1ee26a60_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "junction_announcements_categories", "field": "announcements_id", "related_collection": "announcements", "meta": {"junction_field": "announcement_categories_id", "many_collection": "junction_announcements_categories", "many_field": "announcements_id", "one_allowed_collections": null, "one_collection": "announcements", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": "categories", "sort_field": null}, "schema": {"table": "junction_announcements_categories", "column": "announcements_id", "foreign_key_table": "announcements", "foreign_key_column": "id", "constraint_name": "junction_announcements_categories_announcements_id_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "junction_blog_categories", "field": "blog_categories_id", "related_collection": "blog_categories", "meta": {"junction_field": "blog_id", "many_collection": "junction_blog_categories", "many_field": "blog_categories_id", "one_allowed_collections": null, "one_collection": "blog_categories", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": null, "sort_field": null}, "schema": {"table": "junction_blog_categories", "column": "blog_categories_id", "foreign_key_table": "blog_categories", "foreign_key_column": "id", "constraint_name": "junction_blog_categories_blog_categories_id_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "junction_blog_categories", "field": "blog_id", "related_collection": "blog", "meta": {"junction_field": "blog_categories_id", "many_collection": "junction_blog_categories", "many_field": "blog_id", "one_allowed_collections": null, "one_collection": "blog", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": "categories", "sort_field": null}, "schema": {"table": "junction_blog_categories", "column": "blog_id", "foreign_key_table": "blog", "foreign_key_column": "id", "constraint_name": "junction_blog_categories_blog_id_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "junction_contact_blog", "field": "blog_id", "related_collection": "blog", "meta": {"junction_field": "contact_id", "many_collection": "junction_contact_blog", "many_field": "blog_id", "one_allowed_collections": null, "one_collection": "blog", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": null, "sort_field": null}, "schema": {"table": "junction_contact_blog", "column": "blog_id", "foreign_key_table": "blog", "foreign_key_column": "id", "constraint_name": "junction_contact_blog_blog_id_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "junction_contact_blog", "field": "contact_id", "related_collection": "contact", "meta": {"junction_field": "blog_id", "many_collection": "junction_contact_blog", "many_field": "contact_id", "one_allowed_collections": null, "one_collection": "contact", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": "blogs", "sort_field": null}, "schema": {"table": "junction_contact_blog", "column": "contact_id", "foreign_key_table": "contact", "foreign_key_column": "id", "constraint_name": "junction_contact_blog_contact_id_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "junction_faq_categories", "field": "faq_categories_id", "related_collection": "faq_categories", "meta": {"junction_field": "faq_id", "many_collection": "junction_faq_categories", "many_field": "faq_categories_id", "one_allowed_collections": null, "one_collection": "faq_categories", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": null, "sort_field": null}, "schema": {"table": "junction_faq_categories", "column": "faq_categories_id", "foreign_key_table": "faq_categories", "foreign_key_column": "id", "constraint_name": "junction_faq_categories_faq_categories_id_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}, {"collection": "junction_faq_categories", "field": "faq_id", "related_collection": "faq", "meta": {"junction_field": "faq_categories_id", "many_collection": "junction_faq_categories", "many_field": "faq_id", "one_allowed_collections": null, "one_collection": "faq", "one_collection_field": null, "one_deselect_action": "nullify", "one_field": "categories", "sort_field": null}, "schema": {"table": "junction_faq_categories", "column": "faq_id", "foreign_key_table": "faq", "foreign_key_column": "id", "constraint_name": "junction_faq_categories_faq_id_foreign", "on_update": "NO ACTION", "on_delete": "SET NULL"}}], "metadata": {"environment": "staging", "timestamp": "2025-07-14T09:25:49.448Z", "generatedBy": "schema-manager", "version": "1.0.0"}}