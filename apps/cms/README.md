# SafeTrade CMS Development Setup Guide

This guide will help you set up the SafeTrade CMS development environment using Directus, PostgreSQL, and Redis with Docker Compose.

---

## Prerequisites

- [Docker](https://docs.docker.com/get-docker/) installed
- [Docker Compose](https://docs.docker.com/compose/install/) installed
- [Node.js](https://nodejs.org/) (for schema management tools)
- [pnpm](https://pnpm.io/) (package manager)

---

## Step 1: Configure Environment Variables

Copy the example environment file and adjust values as needed:

```bash
cp .env.example .env
# Edit .env with your preferred values
```

### Environment Variables Reference:

- `DB_USER`: PostgreSQL username (default: user_safetrade)
- `DB_PASSWORD`: PostgreSQL password
- `DB_DATABASE`: Database name (default: db_safetrade)
- `DIRECTUS_SECRET`: Secret key for Directus (generate a secure random string)
- `DIRECTUS_PORT`: Port for Directus admin interface (default: 8055)
- `ADMIN_EMAIL`: Initial admin user email
- `ADMIN_PASSWORD`: Initial admin user password
- `PUBLIC_URL`: Public URL for the CMS

---

## Step 2: Install Dependencies

Install Node.js dependencies for schema management:

```bash
pnpm install
```

---

## Step 3: Start Services

Start all services using Docker Compose:

```bash
docker compose up -d
```

This will start:

- **PostgreSQL Database** (PostGIS enabled) with persistent storage
- **Redis Cache** for performance optimization
- **Directus CMS** with admin interface

---

## Step 4: Verify Services are Running

Check that all services are healthy:

```bash
docker compose ps
```

Wait for all services to show "healthy" status before proceeding.

---

## Step 5: Access the Application

- **Directus Admin Interface:** [http://localhost:8055](http://localhost:8055)
- **Login with:** admin credentials from your `.env` file

---

## Step 6: Schema Management

This CMS includes advanced schema management tools:

### Sync Schema (Dry Run):

```bash
pnpm run schema:sync:dry
```

### Apply Schema Changes:

```bash
pnpm run schema:sync
```

### Create Schema Snapshot:

```bash
pnpm run schema:snapshot
```

### Update Schema from Remote:

```bash
pnpm run schema:update
```

---

## Step 7: Common Docker Commands

- **View logs:**

    ```bash
    docker compose logs -f
    ```

- **View specific service logs:**

    ```bash
    docker compose logs -f directus
    docker compose logs -f database
    docker compose logs -f cache
    ```

- **Stop all services:**

    ```bash
    docker compose down
    ```

- **Restart services:**

    ```bash
    docker compose restart
    ```

- **Rebuild and restart:**
    ```bash
    docker compose up -d --build
    ```

---

## Data Persistence

- **Database data:** Stored in `./data/database/`
- **File uploads:** Stored in `./uploads/`
- **Extensions:** Stored in `./extensions/`
- **Schema snapshots:** Stored in `./schema-snapshots/`

---

## Troubleshooting

### Database Connection Issues:

- Ensure PostgreSQL container is healthy: `docker compose ps`
- Check database logs: `docker compose logs database`
- Verify environment variables in `.env`

### Directus Not Starting:

- Check if all dependencies are healthy
- Verify `DIRECTUS_SECRET` is properly set
- Check Directus logs: `docker compose logs directus`

### Schema Management Issues:

- Ensure Node.js dependencies are installed: `pnpm install`
- Check API tokens in `.env` file
- Run schema sync in dry-run mode first: `pnpm run schema:sync:dry`

### Reset Database:

```bash
docker compose down
sudo rm -rf ./data/database
docker compose up -d
```

### Cache Issues:

```bash
docker compose restart cache
```

---

## Development Workflow

1. **Start development environment:**

    ```bash
    docker compose up -d
    ```

2. **Make schema changes in Directus admin interface**

3. **Create schema snapshot:**

    ```bash
    pnpm run schema:snapshot
    ```

4. **Sync changes to staging/production:**
    ```bash
    pnpm run schema:sync
    ```

---

## Backup & Restore

### Full CMS Backup

To create a complete backup of your running CMS, you need to backup both the database and file assets.

#### 1. Database Backup

Create a PostgreSQL dump while the database is running:

```bash
# Create backup directory
mkdir -p ./backups

# Generate backup with timestamp
BACKUP_DATE=$(date +"%Y%m%d_%H%M%S")
docker compose exec database pg_dump -U user_safetrade -d db_safetrade > ./backups/db_backup_${BACKUP_DATE}.sql
```

Or use the docker exec method:

```bash
BACKUP_DATE=$(date +"%Y%m%d_%H%M%S")
docker compose exec database pg_dump -U user_safetrade -d db_safetrade -f /tmp/backup.sql
docker compose cp database:/tmp/backup.sql ./backups/db_backup_${BACKUP_DATE}.sql
```

#### 2. Files & Assets Backup

Backup all uploaded files and extensions:

```bash
BACKUP_DATE=$(date +"%Y%m%d_%H%M%S")

# Create full backup archive
tar -czf ./backups/cms_full_backup_${BACKUP_DATE}.tar.gz \
  ./uploads \
  ./extensions \
  ./schema-snapshots \
  ./.env \
  ./docker-compose.yml
```

### Restore from Backup

#### 1. Stop Services

```bash
docker compose down
```

#### 2. Extract Backup

```bash
# Extract complete backup
tar -xzf ./backups/cms_complete_backup_YYYYMMDD_HHMMSS.tar.gz -C ./backups/

# Extract files backup
tar -xzf ./backups/files_backup_YYYYMMDD_HHMMSS.tar.gz
```

#### 3. Restore Database

```bash
# Start only database service
docker compose up -d database

# Wait for database to be ready
sleep 10

# Restore database
docker compose exec -T database psql -U user_safetrade -d db_safetrade < ./backups/db_backup_YYYYMMDD_HHMMSS.sql
```

#### 4. Start All Services

```bash
docker compose up -d
```

### Automated Backup with Cron

Set up automatic daily backups:

```bash
# Open crontab
crontab -e

# Add daily backup at 2 AM
0 2 * * * cd /path/to/your/cms && ./backup.sh >> ./backups/backup.log 2>&1
```

### Backup Best Practices

- **Regular Backups**: Schedule daily automated backups
- **Multiple Locations**: Store backups in different locations (local, cloud)
- **Test Restores**: Regularly test your backup restoration process
- **Retention Policy**: Keep backups for 30 days, monthly for 1 year
- **Monitoring**: Monitor backup script execution and file sizes

### Backup Cleanup Script

Automatically clean old backups:

```bash
#!/bin/bash
# File: cleanup_backups.sh

BACKUP_DIR="./backups"
DAYS_TO_KEEP=30

echo "Cleaning backups older than $DAYS_TO_KEEP days..."

find $BACKUP_DIR -name "cms_complete_backup_*.tar.gz" -mtime +$DAYS_TO_KEEP -delete

echo "Cleanup completed"
```

---

## Additional Notes

- The CMS uses Directus 11.7.2 with PostgreSQL (PostGIS enabled) and Redis caching
- Extensions auto-reload is enabled for development
- CORS is configured for local development
- Schema management supports staging and production environments
- All data is persisted in local volumes for development

---

For further help, contact the project maintainer or refer to the official [Directus Documentation](https://docs.directus.io/).
