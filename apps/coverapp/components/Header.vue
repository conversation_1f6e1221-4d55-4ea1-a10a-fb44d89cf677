<script setup lang="ts">
import { encryptEmail, encryptPhone, screenDevice } from '@zsmartex/utils'
import { format as formatDate } from 'date-fns'
import type { Notify } from '@zsmartex/types'
import { ExchangeLayout } from '@zsmartex/types'
import AcceptModal from '~/layouts/p2p/AcceptModal.vue'

const config = useRuntimeConfig()
const userStore = useUserStore()
const publicStore = usePublicStore()
const tradeStore = useTradeStore()
const i18n = useI18n()
const staticID = ref('')

const device = screenDevice()

const acceptModal = ref<InstanceType<typeof AcceptModal>>()

const pendingNotify = computed(() => {
  return userStore.notifies.filter(n => !n.seen).length
})

async function fetchP2PTrade() {
  await useAsyncData(async () => await tradeStore.FetchP2PTrade(staticID.value))
}

async function notifyClick(notify: Notify) {
  // if (notify.seen) return

  notify.seen = true
  userStore.SeenNotify(notify.id)

  if (notify.type === 'trade') {
    if (notify.title === 'page.global.notify.p2p.trade_accepted.title') {
      navigateTo(`/p2p/trade/${notify.data.static_id}`)
    }
    if (notify.title === 'page.global.notify.p2p.request_p2p.title') {
      staticID.value = notify.data.static_id
      await fetchP2PTrade()
      if (new Date(tradeStore.p2p_trade.expired_at) < new Date()) return
      acceptModal.value?.openModal()
    }
  }
}

function exchangeUrl(type: ExchangeLayout) {
  if (process.client) return `/exchange/${tradeStore.market.name.replace('/', '-')}?type=${type}`
}
</script>

<template>
  <header class="z-header">
    <div class="z-header-nav">
      <NuxtLink to="/" class="z-header-nav-item z-header-logo">
        <img src="@/assets/img/rectangular_logo.png">
      </NuxtLink>

      <NuxtLink v-show="device === ScreenDevice.Desktop || device === ScreenDevice.LargeDesktop" to="/markets" class="z-header-nav-item bold-text">
        {{ $t('page.global.header.link.markets') }}
      </NuxtLink>

      <ZHeaderDropdown v-show="device === ScreenDevice.Desktop || device === ScreenDevice.LargeDesktop" class="z-header-nav-item z-header-nav-item-trade" :placement="Placement.BottomLeft">
        <span class="bold-text">{{ $t('page.global.header.link.trades') }}</span> <ZIcon type="arrow-down" />
        <template #overlay>
          <ZMenu :value="tradeStore.exchange_layout">
            <ZMenuItem key="basic" :is-router-link="true" :to="exchangeUrl(ExchangeLayout.Basic)" :selected="$route.path.startsWith('/exchange/') && tradeStore.exchange_layout === 'basic'">
              <ZIcon type="shujufenxi" />
              <div class="z-menu-item-content">
                <div class="z-menu-item-title">
                  {{ $t('page.global.header.classic') }}
                </div>
                <div class="z-menu-item-desc">
                  {{ $t('page.global.header.classic_desc') }}
                </div>
              </div>
            </ZMenuItem>
            <ZMenuItem v-show="device !== ScreenDevice.Desktop" key="pro" :is-router-link="true" :to="exchangeUrl(ExchangeLayout.Pro)" :selected="$route.path.startsWith('/exchange/') && tradeStore.exchange_layout === 'pro'">
              <ZIcon type="chuangkou" />
              <div class="z-menu-item-content">
                <div class="z-menu-item-title">
                  {{ $t('page.global.header.advanced') }}
                </div>
                <div class="z-menu-item-desc">
                  {{ $t('page.global.header.advanced_desc') }}
                </div>
              </div>
            </ZMenuItem>
          </ZMenu>
        </template>
      </ZHeaderDropdown>

      <!-- <NuxtLink v-show="device === ScreenDevice.Desktop || device === ScreenDevice.LargeDesktop && config.public.p2p" to="/p2p" class="z-header-nav-item bold-text">
        {{ $t('page.global.header.p2p') }}
      </NuxtLink> -->
    </div>
    <div class="z-header-nav">
      <template v-if="userStore.isAuthenticated">
        <NuxtLink v-show="device === ScreenDevice.Desktop || device === ScreenDevice.LargeDesktop" to="/my/wallet/assets/trading_account/spot" class="z-header-nav-item bold-text">
          {{ $t('page.global.header.link.assets') }}
        </NuxtLink>
        <NuxtLink v-show="device === ScreenDevice.Desktop || device === ScreenDevice.LargeDesktop" to="/my/orders/open" class="z-header-nav-item bold-text">
          {{ $t('page.global.header.link.orders') }}
        </NuxtLink>
        <ZHeaderDropdown v-show="device === ScreenDevice.Desktop || device === ScreenDevice.LargeDesktop" class="z-header-nav-item z-header-nav-item-profile" :placement="Placement.BottomRight">
          <ZIconUserDuotone />
          <template #overlay>
            <ZMenu>
              <div key="info" class="z-menu-item dropdown-user-info">
                <div v-if="userStore.email" class="email">
                  {{ encryptEmail(userStore.email) }}
                </div>
                <div v-else class="phone">
                  {{ encryptPhone(userStore.phone!.number) }}
                </div>
                <div class="vip">
                  <ZIcon type="vip" /> <span>{{ $t('page.global.header.vip_0') }}</span>
                </div>
              </div>
              <ZMenuItem key="dashboard" :is-router-link="true" to="/my/dashboard" :selected="$route.path.startsWith('/my/dashboard')">
                {{ $t('page.global.header.dashboard') }}
              </ZMenuItem>
              <ZMenuItem key="security" :is-router-link="true" to="/my/security" :selected="$route.path.startsWith('/my/security')">
                {{ $t('page.global.header.security_center') }}
              </ZMenuItem>
              <!-- <ZMenuItem key="referral" :is-router-link="true" to="/my/invite" :selected="$route.path.startsWith('/my/invite')">
                {{ $t('page.global.header.invite') }}
              </ZMenuItem> -->
              <ZMenuItem key="api" :is-router-link="true" to="/my/api" :selected="$route.path.startsWith('/my/api')">
                {{ $t('page.global.header.api') }}
              </ZMenuItem>
              <ZMenuItem key="logout" class="z-menu-item-logout" @click="userStore.Logout()">
                {{ $t('page.global.action.log_out') }}
              </ZMenuItem>
            </ZMenu>
          </template>
        </ZHeaderDropdown>
        <ZHeaderDropdown v-show="device === ScreenDevice.Desktop || device === ScreenDevice.LargeDesktop && config.public.p2p" class="z-header-nav-item z-header-nav-item-notify" :placement="Placement.BottomRight">
          <div class="z-header-nav-item-notify-icon">
            <ZIconCommentFilled />
            <span>{{ pendingNotify }}</span>
          </div>
          <template #overlay>
            <div class="z-header-nav-item-notify-head pb-[20px]">
              <div>
                <span>{{ `${pendingNotify} ` }} </span>
                <span class="text-gray">{{ $t('page.global.header.pending_notifications') }}</span>
              </div>
              <div class="cursor-pointer" @click="navigateTo('/my/notifies')">
                {{ $t('page.global.action.all') }}
              </div>
            </div>
            <div v-if="userStore.notifies.length === 0" class="z-header-nav-item-notify-table-empty mb-[20px] h-[160px] w-full flex flex-col items-center justify-center">
              <ZIconClipboardTimesDuotone />
              <span class="text-gray">{{ $t('page.global.table.empty') }}</span>
            </div>
            <div v-else class="z-header-nav-item-notify-table mb-2">
              <div
                v-for="notify in userStore.notifies.slice(0, 5)"
                :key="notify.id"
                class="z-header-nav-item-notify-table-row"
                @click="notifyClick(notify)"
              >
                <div
                  class="z-header-nav-item-notify-table-row-status"
                  :class="{ seen: notify.seen }"
                />
                <div class="z-header-nav-item-notify-table-row-icon">
                  <ZIconDesktopDuotone />
                </div>
                <div class="z-header-nav-item-notify-table-row-content flex-1">
                  <div>
                    {{ $t(notify.title) }}
                  </div>
                  <div class="text-gray z-header-nav-item-notify-table-row-desc">
                    {{ $t(notify.desc, {
                      ...notify.data,
                    }) }}
                  </div>
                </div>
                <div class="z-header-nav-item-notify-table-row-date text-[12px]">
                  {{ formatDate(new Date(notify.created_at), "dd-MM HH:mm") }}
                </div>
              </div>
            </div>
          </template>
        </ZHeaderDropdown>
      </template>
      <template v-else>
        <NuxtLink v-show="device === ScreenDevice.Desktop || device === ScreenDevice.LargeDesktop" class="z-header-nav-item login bold-text" to="/login">
          <ZButton type="primary" :selected="$route.path.includes('/login')">
            {{ $t('page.global.action.login') }}
          </ZButton>
        </NuxtLink>
        <NuxtLink class="z-header-nav-item register bold-text" to="/register">
          <ZButton type="primary" to="/register" :selected="$route.path.includes('/register')">
            {{ $t('page.global.action.register') }}
          </ZButton>
        </NuxtLink>
      </template>
      <!-- <ZHeaderDropdown v-show="device === ScreenDevice.Desktop || device === ScreenDevice.LargeDesktop" class="z-header-nav-item" :placement="Placement.BottomRight">
        <span class="bold-text">{{ $t('page.global.language') }}</span>
        <template #overlay>
          <ZMenu v-model="i18n.locale">
            <ZMenuItem v-for="(locale, key) in i18n.locales" :key="key" :value="key">
              {{ locale['page.global.language'] }}
            </ZMenuItem>
          </ZMenu>
        </template>
      </ZHeaderDropdown>
      <ZHeaderDropdown v-show="device === ScreenDevice.Desktop || device === ScreenDevice.LargeDesktop" class="z-header-nav-item" :placement="Placement.BottomRight">
        <span class="bold-text">{{ publicStore.convert_currency.toUpperCase() }}</span>
        <template #overlay>
          <ZMenu v-model="publicStore.convert_currency">
            <ZMenuItem v-for="(name, key) in publicStore.global_price" :key="key" :value="name">
              {{ key.toUpperCase() }}
            </ZMenuItem>
          </ZMenu>
        </template>
      </ZHeaderDropdown> -->
      <div v-show="device !== ScreenDevice.Desktop && device !== ScreenDevice.LargeDesktop" class="z-header-mobile-menu" @click="publicStore.showDrawer = true">
        <ZIconMenuFilled />
      </div>
    </div>
    <AcceptModal ref="acceptModal" />
  </header>
</template>

<style lang="less">
.z-header {
  display: flex;
  height: 50px;
  justify-content: space-between;
  padding: 0 12px;
  background-color: @layout-header-background-color;
  z-index: 1;
  font-size: 14px;
  font-weight: 500;

  @media @tablet {
    padding-right: 24px;
    padding-left: 12px;
  }

  &-mobile {
    &-menu {
      display: flex;
      align-items: center;

      svg {
        width: 32px;
        height: 32px;

        .cls-1 {
          fill: white;
        }
      }

      .z-icon-person {
        font-size: 24px;
        color: white;
      }
    }
  }

  .z-dropdown {
    .z-menu {
      min-width: 150px;
      padding: 8px 0;
      background-color: @layout-header-background-color;

      &-item {
        cursor: pointer;
        line-height: 36px;
        padding: 0 16px;
        color: @white-color;

        &-selected, &:hover {
          background-color: rgba(255, 255, 255, 0.05);
        }
      }
    }
  }

  &-nav {
    display: flex;
    align-items: center;
    line-height: 50px;

    &-item {
      margin: 0 12px;
      color: @white-color;
      user-select: none;
      cursor: pointer;

      a {
        color: @white-color;
      }

      i {
        font-size: 12px;
        vertical-align: middle;
      }

      &-trade {
        .z-menu {
          width: 300px;

          &-item {
            position: relative;
            display: flex;
            align-items: center;
            padding: 6px 16px !important;

            &:last-child::after {
              display: none;
            }

            i {
              font-size: 20px;
              margin-right: 16px;
              color: @blue-color;
            }

            &-title {
              font-size: 14px;
              line-height: 1.5;
            }

            &-desc {
              font-size: 12px;
              margin-top: 2px;
              line-height: 1.5;
              color: @gray-color;
            }

            &:hover &-title {
              color: @primary-color;
            }

            &::after {
              position: absolute;
              content: "";
              bottom: -1px;
              background-color: @base-border-color;
              width: 280px;
              height: 1px;
              left: 50%;
              transform: translateX(-50%);
            }
          }
        }
      }

      &-notify {
        line-height: normal;
        cursor: auto;

        .z-dropdown-bottomRight-overlay {
          top: 125%;
        }

        svg {
          width: 32px;
          height: 32px;

          .cls-1 {
            fill: white;
          }
        }

        &-icon {
          position: relative;

          svg {
            width: 28px;
            height: 28px;

            .cls-1, .cls-2 {
              fill: white;
            }
          }

          & > span {
            position: absolute;
            top: 2px;
            right: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            width: 12px;
            height: 12px;
            font-size: 10px;
            color: white;
            border-radius: 50%;
            background-color: red;
          }
        }

        i {
          font-size: 24px;
        }

        &-more {
          padding-bottom: 20px;
          color: @primary-color;
          cursor: pointer;
        }

        &-head {
          display: flex;
          justify-content: space-between;
          margin-bottom: 20px;
          padding: 20px;
          border-bottom: 1px solid rgba(@gray-color, 0.3);
        }

        &-table {
          &-empty {
            svg {
              width: 80px;
              height: 80px;

              .cls-1 {
                fill: rgba(@gray-color, 0.3);
              }

              .cls-2 {
                fill: @gray-color;
              }
            }
          }

          &-row {
            position: relative;
            display: flex;
            align-items: flex-start;
            padding: 8px 20px;
            color: @white-color;
            cursor: pointer;

            &-icon {
              margin-top: 2px;
              margin-right: 8px;

              svg {
                width: 16px;
                height: 16px;

                .cls-1, .cls-2 {
                  fill: @gray-color;
                }
              }
            }

            &-date {
              position: absolute;
              right: 20px;
              top: 8px;
            }

            &-status {
              margin-right: 8px;
              margin-top: 5px;
              width: 8px;
              height: 8px;
              background-color: @up-color;
              border-radius: 50%;

              &.seen {
                background-color: @gray-color !important;
              }
            }

            &-content {
              max-width: 380px;
            }

            &-desc {
              width: 100%;
              display: inline-block;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }

            &:hover {
              background-color: rgba(255, 255, 255, 0.05) !important;
            }
          }
        }

        .z-overlay {
          width: 500px;
          background-color: @layout-header-background-color;
        }
      }

      &-profile {
        .z-dropdown-bottomRight-overlay {
          top: 125%;
        }

        svg {
          width: 32px;
          height: 32px;

          .cls-1, .cls-2 {
            fill: white;
          }
        }

        .z-dropdown-trigger {
          display: flex;
          align-items: center;
        }

        i {
          font-size: 24px;
        }

        .z-menu {
          width: 210px;

          &-item-logout {
            border-top: 1px solid @base-border-color;
          }

          .dropdown-user-info {
            height: 84px;
            padding-top: 12px;
            padding-bottom: 12px;
            border-bottom: 1px solid @base-border-color;

            .email {
              font-size: 18px;
              line-height: 20px;
            }

            .phone {
              font-size: 18px;
              line-height: 20px;
            }

            .vip {
              margin-top: 8px;
              line-height: 1.5;
              vertical-align: middle;
              display: flex;
              align-items: center;
              padding: 0 12px;
              background-color: @btn-primary-background;
              width: max-content;
              border-radius: 19px;
              font-size: 14px;
              font-weight: normal;
              color: @blue-color;

              i {
                font-size: 16px;
                line-height: 1.5;
              }

              span {
                margin-left: 6px;
              }
            }

            &:hover {
              background-color: transparent;
              color: @white-color;
            }
          }
        }
      }

      &:hover {
        color: @white-color;
      }

      &:first-child {
        margin-left: 0;
      }

      &:last-child {
        margin-right: 0;
      }
    }
  }

  &-logo {
    display: flex;
    align-items: center;

    img {
      height: 40px;
    }
  }

  .z-button {
    background-color: transparent !important;
    border-color: @white-color !important;
    height: 32px !important;
    line-height: 32px !important;
  }

  .login .z-button {
    border: none !important;
  }

  .z-menu-item {
    &:hover {
      background-color: rgba(255, 255, 255, 0.05) !important;
    }
  }
}
</style>
