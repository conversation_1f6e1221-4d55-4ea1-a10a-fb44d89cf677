<script setup lang="ts">
const props = defineProps<{
  title?: string
  to: string
}>()

const router = useRouter()
const publicStore = usePublicStore()

const onClick = () => {
  if (props.to) {
    publicStore.showDrawer = false
    router.push(props.to)
  }
}
</script>

<template>
  <NuxtLink :to="to" class="menu-item" @click="onClick">
    <div class="menu-item-link text-[14px]">
      <div class="">
        {{ title }}
      </div>
    </div>
  </NuxtLink>
</template>

<style lang="less">
.menu-item {
  &-link {
    display: flex;
    align-items: center;
  }

  &.menu-item-active {
    color: @white-color !important;
    border-radius: 4px;
  }
}
</style>
