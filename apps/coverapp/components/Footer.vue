<script setup lang="ts">
const runtimeConfig = useRuntimeConfig()
const tradeStore = useTradeStore()

const exchangeName = computed(() => runtimeConfig.public.exchangeName)

const fields = computed(() => [
  {
    key: 'example1',
    title: 'Example',
    icon: 'plus',
    child: [
      {
        to: `/exchange/${tradeStore.market.name.replace('/', '-')}?type=basic`,
        title: 'Exchange',
      },
      {
        to: '/my/wallet/assets/trading_account/spot',
        title: 'Wallets',
      },
      {
        to: '/fees',
        title: 'Fees',
      },
    ],
  },
  {
    key: 'example2',
    title: 'Example',
    icon: 'plus',
    child: [
      {
        to: '/terms',
        title: 'Terms',
      },
      {
        to: '/status',
        title: 'Status',
      },
      {
        to: 'https://support.safetrade.com/',
        title: 'Support',
        target: '_blank',
      },
    ],
  },
  {
    key: 'example3',
    title: 'Example',
    icon: 'plus',
    child: [
      {
        to: 'https://bitcointalk.org/index.php?topic=3240246.0',
        title: 'About us',
        target: '_blank',
      },
      {
        to: 'https://discord.gg/4szJzdR',
        title: 'Discord',
        target: '_blank',
      },
      {
        to: '/api',
        title: 'API',
      },
    ],
  },
])

const values = ref<Record<string, boolean>>({})

onMounted(() => {
  fields.value.forEach((f) => {
    values.value[f.key] = false
  })
})

function onClick(key: string) {
  values.value[key] = !values.value[key]
}
</script>

<template>
  <footer class="z-footer">
    <ZContainer>
      <div class="z-footer-menu flex">
        <div class="z-footer-nav z-footer-nav-img">
          <a href="#" class="z-footer-logo">
            <img src="@/assets/img/rectangular_logo.png">
          </a>
        </div>
        <div class="z-footer-nav-links">
          <div v-for="(field, index) in fields" :key="index" class="z-footer-nav" :class="{ 'z-footer-nav-active': values[field.key] }">
            <NuxtLink v-for="(item, i) in field.child" :key="i" :target="item.target" :to="item.to" class="z-footer-nav-item">
              {{ item.title }}
            </NuxtLink>
          </div>
        </div>
        <div class="z-footer-nav-icons">
          <div class="flex">
            <NuxtLink to="https://t.me/SafeTradeEx" target="_blank">
              <img class="w-[25px] m-[12px]" src="../assets/img/telegram.svg">
            </NuxtLink>
            <NuxtLink to="https://twitter.com/safetradeex" target="_blank">
              <img class="w-[25px] m-[12px]" src="../assets/img/twitter.svg">
            </NuxtLink>
            <NuxtLink to="https://t.me/SafeTradeEx" target="_blank">
              <img class="w-[25px] m-[12px]" src="../assets/img/youtube.svg">
            </NuxtLink>
          </div>
          <div class="flex">
            <NuxtLink to="https://www.reddit.com/r/SafeTradex" target="_blank">
              <img class="w-[25px] m-[12px]" src="../assets/img/reddit.svg">
            </NuxtLink>
            <NuxtLink to="https://medium.com/@safetrade" target="_blank">
              <img class="w-[25px] m-[12px]" src="../assets/img/medium.svg">
            </NuxtLink>
            <NuxtLink to="https://coinmarketcap.com/exchanges/safetrade/" target="_blank">
              <img class="w-[25px] m-[12px]" src="../assets/img/marketcap.svg">
            </NuxtLink>
          </div>
        </div>
      </div>
    </ZContainer>
    <div class="z-footer-bottom">
      <I18n path="page.global.footer.copyright">
        <template #year>
          2019-{{ new Date().getFullYear() }}
        </template>
        <template #provider>
          <a href="#">
            {{ exchangeName }}
          </a>
        </template>
      </I18n>
    </div>
  </footer>
</template>

<style lang="less">
.z-footer {
  width: 100%;
  padding: 0 16px;
  background-color: @layout-footer-background-color;
  color: @white-color;
  font-weight: 500;

  &-mobile {
    display: none;

    @media @mobile {
      display: block;
    }
  }

  .z-container {
    @media @mobile, @tablet {
      width: 100%;
    }
  }

  &-logo {
    display: block;
    width: 150px;

    img {
      width: 100%;
    }
  }

  &-menu {
    display: flex;
    padding-top: 30px;
    padding-bottom: 30px;

    @media @large-desktop, @desktop {
      margin-left: auto;
      margin-right: auto;
      height: auto;
    }

    @media @mobile {
      display: block;
    }
  }

  &-nav {
    flex: 1;
    font-size: 12px;

    @media @mobile {
      margin-bottom: 12px;
      // max-height: 33px;
      overflow: hidden;

      &-active {
        max-height: fit-content;
      }
    }

    @media @tablet {
      padding-left: 12px;
    }

    &-img {
      display: flex;
      width: calc(100% / 12 * 3);
      align-items: center;
      padding: 0 !important;

      @media @mobile {
        width: 100%;
      }
    }

    &-links {
      // w-6/12 flex items-center
      display: flex;
      width: calc(100% / 12 * 6);
      align-items: center;

      @media @mobile {
        margin: 24px 0;
        width: 100%;

        .z-footer-nav {
          padding-left: 60px;
          font-size: 14px;
        }
      }
    }

    &-icons {
      width: calc(100% / 12 * 3);
      display: flex;
      flex-direction: column;
      align-items: flex-end;

      @media @mobile {
        width: 100%;
        align-items: center;
      }
    }

    &-social-network {
      display: flex;
      font-size: 32px;
      margin-top: 16px;

      a {
        margin-left: 16px;

        &:first-child {
          margin-left: 0;
        }
      }
    }

    &-desc {
      margin-top: 16px;
      margin-left: 8px;
      font-size: 14px;
    }

    &-title {
      margin-bottom: 24px;
      font-size: 16px;

      @media @mobile {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;
      }
    }

    a {
      color: @action-color;
    }

    &-item {
      display: block;
      color: @action-color;
      line-height: 25px;
    }
  }

  &-bottom {
    text-align: center;
    font-size: 14px;
    padding: 16px 0;
    border-top: 1px solid @dropdown-border-color;
    color: @action-color;
  }
}
</style>
