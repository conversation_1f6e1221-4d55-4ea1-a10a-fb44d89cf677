<script setup lang="ts">
import { randomString } from '@zsmartex/utils'

const props = withDefaults(defineProps<{
  title: string
  countItem: number
  defaultVisible: boolean
  to?: string
  decsription?: string
  onClick?: () => void
}>(), {
  countItem: 0,
  defaultVisible: false,
})

const router = useRouter()
const publicStore = usePublicStore()
const visible = useState(`components_menu-item_visible-${randomString()}`, () => false)
visible.value = props.defaultVisible
const origin = ref('')

onMounted(() => {
  origin.value = location.origin
})

const onClick = () => {
  if (props.onClick && typeof (props.onClick) === 'function') {
    props.onClick()
  } else if (props.to && props.countItem === 0) {
    publicStore.showDrawer = false
    router.push(props.to)
  } else {
    visible.value = !visible.value
  }
}
</script>

<template>
  <div>
    <div class="menu-item bold-text flex" :class="[{ 'menu-item-selected': visible }]" @click="onClick">
      <div class="flex-1 flex justify-between">
        <div class="menu-item-link">
          <div v-if="!$slots.title">
            {{ title }}
          </div>
          <slot name="title" />
        </div>
        <div class="menu-item-description">
          {{ decsription }}
        </div>
      </div>
      <div v-if="countItem > 0">
        <ZIcon v-if="!visible" type="arrow" />
        <ZIcon v-else type="arrow-down" />
      </div>
    </div>
    <Transition name="menu-show">
      <div
        v-if="visible" class="menu-sub" :class="[
          { [`menu-sub-${countItem}`]: !!countItem },
        ]"
      >
        <slot />
      </div>
    </Transition>
  </div>
</template>

<style lang="less">
@height_menu_sub_item: 80px;
.menu {
  user-select: none;

  &-sub {
    opacity: 1;
    transition: all 0.3s ease;
    overflow: hidden;
    position: relative;
    background-color: white;

    &:first-child {
      padding-top: 2px;
    }

    .menu-item {
      margin: 0;
      padding-left: 50px;
      transition: all .4s;

      &-selected {
        background-color: @gray-color;
      }

      &::before {
        content: "";
        position: absolute;
        left: 22px;
        width: 0.375rem;
        height: 0.375rem;
        border-radius: 50%;
        background-color: #bec5cc;
      }
    }

    &-1 {
      max-height: 50px;

      @media @tablet {
        max-height: 56px;
      }
    }
    &-2 {
      max-height: 100px;

      @media @tablet {
        max-height: 112px;
      }
    }
    &-3 {
      max-height: 150px;

      @media @tablet {
        max-height: 168px;
      }
    }
    &-4 {
      max-height: 200px;

      @media @tablet {
        max-height: 224px;
      }
    }
    &-5 {
      max-height: 250px;

      @media @tablet {
        max-height: 280px;
      }
    }
    &-6 {
      max-height: 300px;

      @media @tablet {
        max-height: 336px;
      }
    }
    &-7 {
      max-height: 350px;

      @media @tablet {
        max-height: 392px;
      }
    }
    &-8 {
      max-height: 400px;

      @media @tablet {
        max-height: 448px;
      }
    }
  }

  &-item {
    display: flex;
    padding: 2px 24px;
    height: 48px;
    background-color: white;
    align-items: center;
    justify-content: space-between;
    color: @text-color;
    transition: all .4s;

    @media @tablet {
      height: 56px;
    }

    &-link {
      @media @mobile {
        font-size: 14px;
      }

      @media @tablet {
        font-size: 16px;
      }
    }

    &-description {
      color: @gray-color;

      @media @mobile {
        font-size: 14px;
      }

      @media @tablet {
        font-size: 16px;
      }
    }

    &:hover {
      background-color: rgba(@gray-color, 0.15);
      color: @text-color !important;
    }
  }

  .menu-show {

    &-enter-from, &-leave-to {
      max-height: 0;
      opacity: 0.5;
    }
  }
}
</style>
