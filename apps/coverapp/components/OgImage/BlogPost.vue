<script lang="ts" setup>
/**
 * @credits NuxtLabs <https://nuxtlabs.com/>
 * @see https://github.com/nuxt/nuxt.com/blob/main/components/OgImage/OgImageDocs.vue
 */

const props = withDefaults(defineProps<{ title?: string; description?: string; headline?: string }>(), {
  title: 'SafeTrade',
  description: 'description',
  headline: 'headline',
})

const title = 'SafeTrade'
</script>

<template>
  <div class="w-full h-full flex flex-col justify-center bg-[#16171a]">
    <svg
      class="absolute right-0 top-0"
      width="1000"
      height="593"
      viewBox="0 0 629 593"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g filter="url(#filter0_f_199_94966)">
        <path d="M628.5 -578L639.334 -94.4223L806.598 -548.281L659.827 -87.387L965.396 -462.344L676.925 -74.0787L1087.69 -329.501L688.776 -55.9396L1160.22 -164.149L694.095 -34.9354L1175.13 15.7948L692.306 -13.3422L1130.8 190.83L683.602 6.50012L1032.04 341.989L668.927 22.4412L889.557 452.891L649.872 32.7537L718.78 511.519L628.5 36.32L538.22 511.519L607.128 32.7537L367.443 452.891L588.073 22.4412L224.955 341.989L573.398 6.50012L126.198 190.83L564.694 -13.3422L81.8734 15.7948L562.905 -34.9354L96.7839 -164.149L568.224 -55.9396L169.314 -329.501L580.075 -74.0787L291.604 -462.344L597.173 -87.387L450.402 -548.281L617.666 -94.4223L628.5 -578Z" fill="#00DC82" />
      </g>
      <defs>
        <filter
          id="filter0_f_199_94966"
          x="0.873535"
          y="-659"
          width="1255.25"
          height="1251.52"
          filterUnits="userSpaceOnUse"
          color-interpolation-filters="sRGB"
        >
          <feFlood flood-opacity="0" result="BackgroundImageFix" />
          <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape" />
          <feGaussianBlur stdDeviation="40.5" result="effect1_foregroundBlur_199_94966" />
        </filter>
      </defs>
    </svg>

    <div class="w-[600px] pl-[100px]">
      <h1 class="w-[600px] m-0 text-[75px] font-semibold mb-4 text-white flex items-center">
        <span>{{ title }}</span>
      </h1>
      <p class="text-[40px] text-[#E4E4E7]">
        Safe - Low Fees
      </p>
      <!-- <p class="text-[40px] text-[#E4E4E7]">
        Low Fees
      </p> -->
    </div>

    <svg class="absolute top-[130px] right-[90px] w-[360px] h-[360px]" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 4034.5 3982.4">
      <!-- <switch>
      </switch> -->
      <g>
        <g id="Layer_2">
          <g>
            <path class="fill-white" d="M2026,1220.4l-678.4,391l1,784l677.3,390.7l674-388.8l1-786.5L2026,1220.4z M2517.1,2296L2026,2580.7      l-494.5-286.7l-0.7-575.6l495.2-286.9l491.9,286L2517.1,2296z" />
            <polygon class="fill-white" points="2025.9,1431.6 2517.8,1717.6 2700.9,1611.7 2700.9,1610.7 2026,1220.4 1347.6,1611.4       1530.7,1718.5     " />
            <path class="fill-white" d="M2201.5,1836.8c0-96.1-77.9-174-174-174c-96.1,0-174,77.9-174,174c0,72.9,44.8,135.3,108.3,161.2      l-73.9,344.7l283-0.2l-72.6-346.8C2159.1,1968.7,2201.5,1907.7,2201.5,1836.8z" />
            <path class="fill-white" d="M2193.7,1888.7l-166.2,89.8c0,0-140.2-78-166.7-92.1c15.1,50.7,52.6,91.8,101.2,111.6l-73.9,344.7l283-0.2      l-72.6-346.8C2143.7,1975.6,2178.7,1936.6,2193.7,1888.7z" />
            <path class="fill-white" d="M2034.9,2342.7l136-0.1l-72.6-346.8c45.3-20.2,80.4-59.2,95.3-107.1l-159.6,86.2L2034.9,2342.7z" />
          </g>
        </g>
        <g id="Layer_3">
          <path class="fill-white" d="M2704.5,486c-204.7-91.2-431.4-141.8-669.9-141.8c-310.9,0-601.7,86.1-849.8,235.7L2704.5,1509V486z" />
          <path class="fill-white" d="M2034.6,296.2c237.9,0,464.4,49,669.9,137.4V278c-207.6-81.2-433.5-125.8-669.9-125.8     c-382.4,0-737.6,116.7-1031.9,316.3l136.1,83.2C1398.8,389.8,1705.8,296.2,2034.6,296.2z" />
          <path class="fill-white" d="M1093.8,639c-427.4,297.7-707.2,792.8-707.2,1353.3c0,12.2,0.1,24.3,0.4,36.4l1547.7-871.4L1093.8,639z" />
          <path class="fill-white" d="M338.6,1992.3c0-569.3,280.5-1073,710.7-1380.6l-132-81.4h0c-439.3,336.3-722.7,866-722.7,1462     c0,47.7,1.8,94.9,5.4,141.7l139.7-78.7C339,2034.4,338.6,2013.4,338.6,1992.3z" />
          <path class="fill-white" d="M1243.4,3438.3l9-1794.2l-859.9,488C439.8,2696.2,771.3,3179.5,1243.4,3438.3z" />
          <path class="fill-white" d="M346.6,2158.1l-136.1,77.2c82.8,627.5,481.8,1155.4,1031.8,1418.1l0.8-160.7     C751.4,3232.7,402.9,2738.2,346.6,2158.1z" />
          <path class="fill-white" d="M2870.4,3413l-1525.9-919.8v996.1c209.9,96.9,443.7,151,690.1,151C2339.7,3640.3,2625.4,3557.4,2870.4,3413z     " />
          <path class="fill-white" d="M2034.6,3688.3c-245.8,0-479.3-52.3-690.1-146.3v156.4c213.1,86.3,446.1,133.8,690.1,133.8     c377.3,0,728-113.5,1019.9-308.3l-137.6-82.9C2659.9,3597.9,2357.8,3688.3,2034.6,3688.3z" />
          <path class="fill-white" d="M2961.4,3355.2c435.4-296.6,721.3-796.4,721.3-1363c0-7.1,0-14.1-0.1-21.1l-1560.1,873.1L2961.4,3355.2z" />
          <path class="fill-white" d="M3870.4,1866l-140.4,78.5c0.4,15.8,0.7,31.7,0.7,47.7c0,575.3-286.4,1083.6-724.4,1390.3l133.2,81.2     c446.4-335.7,735.1-869.9,735.1-1471.5C3874.6,1949.8,3873.2,1907.7,3870.4,1866z" />
          <path class="fill-white" d="M3723.6,1836l136.5-76.1c-80.8-641.5-492-1180.2-1057.7-1440.3v159.9     C3309.3,737.3,3669.4,1242.5,3723.6,1836z" />
          <path class="fill-white" d="M2802.4,533.6v1815.9l875.2-487.9C3632.3,1284.3,3289.5,790.5,2802.4,533.6z" />
        </g>
      </g>
    </svg>
  </div>
</template>
