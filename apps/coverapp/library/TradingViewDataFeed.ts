import { randomString } from '@zsmartex/utils'
import type { Market } from '@zsmartex/types'
import type TradingView from '@/public/charting_library/charting_library'
import type { TradingViewResolution, TradingViewStream } from '~/types'

const history: Record<string, any> = reactive({})

const supportedResolutions = [
  '1',
  '3',
  '5',
  '15',
  '30',
  '60',
  '120',
  '240',
  '360',
  '480',
  '720',
  '1440',
  '4320',
  '10080',
  '43200',
] as TradingView.ResolutionString[]

const createChannelString = (name: string) => {
  const channel = name.split(/[:/]/)
  return `${channel[0]}/${channel[1]}`
}

export default class TradingViewDataFeed implements TradingView.IBasicDataFeed {
  market: Market

  key = randomString()

  constructor(market: Market) {
    this.market = market
  }

  onReady(cb: TradingView.OnReadyCallback) {
    cb({ supported_resolutions: supportedResolutions })
  }

  resolveSymbol(symbolName: string, onSymbolResolvedCallback: TradingView.ResolveCallback) {
    const symbolStub = {
      name: symbolName,
      description: '',
      type: 'crypto',
      session: '24x7',
      timezone: 'exchange',
      ticker: symbolName,
      minmov: 1,
      pricescale: 10 ** this.market.price_precision,
      has_intraday: true,
      intraday_multipliers: supportedResolutions as string[],
      supported_resolutions: supportedResolutions,
    } as TradingView.LibrarySymbolInfo

    onSymbolResolvedCallback(symbolStub)
  }

  searchSymbols() {}

  async getBars(symbolInfo: TradingView.LibrarySymbolInfo, resolution: TradingView.ResolutionString, periodParams: TradingView.PeriodParams, onResult: TradingView.HistoryCallback, onError: TradingView.ErrorCallback) {
    try {
      const timeFrom = Math.min(periodParams.to - 24 * 60 * 60, periodParams.from) // query at least one day

      const { data } = await useBetterFetch<number[][]>(`trade/public/markets/${this.market.id}/k-line?period=${resolution}&time_from=${timeFrom}&time_to=${periodParams.to}&limit=${periodParams.countBack}`)
      const bars = data.map(el => ({
        time: el[0] * 1000,
        open: Number(el[1]),
        high: Number(el[2]),
        low: Number(el[3]),
        close: Number(el[4]),
        volume: Number(el[5]),
      }))

      if (periodParams.firstDataRequest) {
        history[symbolInfo.name] = { lastBar: bars.length ? bars[bars.length - 1] : null }
      }

      onResult(bars, { noData: !bars.length })
    }
    catch (error: any) {
      onError(error.response)
      return error
    }
  }

  subscribeBars(symbolInfo: TradingView.LibrarySymbolInfo, resolution: TradingView.ResolutionString, onRealtimeCallback: TradingView.SubscribeBarsCallback, subscribeUID: string) {
    const tradingViewStore = useTradingViewStore()
    const channelString = createChannelString(symbolInfo.name)

    const stream: TradingViewStream = {
      key: this.key,
      market: this.market,
      channelString,
      subscribeUID,
      resolution: resolution as TradingViewResolution,
      symbolInfo,
      lastBar: history[symbolInfo.name].lastBar,
      listener: onRealtimeCallback,
    }

    tradingViewStore.streams.push(stream)
  }

  unsubscribeBars() {
    const tradingViewStore = useTradingViewStore()

    const index = tradingViewStore.streams.findIndex(stream => stream.key === this.key)

    if (index === -1) return

    tradingViewStore.streams.splice(index, 1)
  }

  calculateHistoryDepth(resolution: any) {
    return resolution < 60
      ? { resolutionBack: 'D', intervalBack: '1' }
      : undefined
  }
}
