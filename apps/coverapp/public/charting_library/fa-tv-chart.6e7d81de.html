<!DOCTYPE html><html lang="fa" dir="rtl"><head><meta charset="utf-8"><meta http-equiv="X-UA-Compatible" content="IE=Edge"><script>window===window.parent&&(location.href="about:blank")</script><script defer="defer" crossorigin="anonymous" src="bundles/runtime.bb849b185e08d58830ae.js"></script><script defer="defer" crossorigin="anonymous" src="bundles/vendors.9a9929f9ead8552d3e88.js"></script><script defer="defer" crossorigin="anonymous" src="bundles/library.b7e61e095e1ec3d88b97.js"></script><link type="text/css" href="bundles/library.901313069720368f8d7f.rtl.css" rel="stylesheet"></head><body class="chart-page unselectable on-widget"><div class="loading-indicator" id="loading-indicator"></div><script>var JSServer={},__initialEnabledFeaturesets=["charting_library"]</script><script>!function(){window.urlParams=function(){function n(n){return decodeURIComponent(n.replace(t," ")).replace(/<\/?[^>]+(>|$)/g,"")}for(var e,t=/\+/g,r=/([^&=]+)=?([^&]*)/g,i=function(){var n=location.href,e=n.indexOf("#");if(0<=e)return n.substring(e+1);throw"Unexpected use of this page"}(),o={};e=r.exec(i);)o[n(e[1])]=n(e[2]);var s,a=window.parent[o.uid],l=["datafeed","customFormatters","brokerFactory","save_load_adapter"];for(s in a)-1===l.indexOf(s)&&(o[s]=JSON.stringify(a[s]));return o}(),window.locale=urlParams.locale,window.language=urlParams.locale,window.addCustomCSSFile=function(n){var e=document.createElement("link");e.setAttribute("type","text/css"),e.setAttribute("rel","stylesheet"),e.setAttribute("href",n),document.body.appendChild(e)},urlParams.customCSS&&window.addCustomCSSFile(urlParams.customCSS);var n={};if("string"==typeof urlParams.loading_screen)try{n=JSON.parse(urlParams.loading_screen)}catch(n){}var e=document.getElementById("loading-indicator");n.backgroundColor&&(e.style="background-color: "+n.backgroundColor),function(){"use strict";var n,e,t;i="\n/* Thanks to google guys for the original <paper-spinner> =)\n * https://github.com/PolymerElements/paper-spinner */\n.tv-spinner {\n  display: none;\n  position: absolute;\n  width: 1em;\n  height: 1em;\n  top: calc(50% - 0.5em);\n  left: calc(50% - 0.5em);\n  margin: 0 auto;\n  color: #37a6ef;\n  animation: tv-spinner__container-rotate 0.9s linear infinite;\n  will-change: transform;\n  /* The spinner does not have any contents that would have to be\n\t * flipped if the direction changes. Always use ltr so that the\n\t * style works out correctly in both cases. */\n  direction: ltr;\n}\n.tv-spinner--size_mini {\n  font-size: 16px;\n}\n.tv-spinner--size_medium {\n  font-size: 32px;\n}\n.tv-spinner--size_large {\n  font-size: 56px;\n}\n.tv-spinner--size_mini .tv-spinner__width_element:after {\n  border-width: 2px;\n}\n.tv-spinner--size_medium .tv-spinner__width_element:after {\n  border-width: 3px;\n}\n.tv-spinner--size_large .tv-spinner__width_element:after {\n  border-width: 4px;\n}\n.tv-spinner--shown {\n  display: block;\n}\n.tv-spinner__spinner-layer {\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  white-space: nowrap;\n  color: currentColor;\n  transform: rotate(90deg);\n  /**\n\t\t * Patch the gap that appear between the two adjacent div.circle-clipper while the\n\t\t * spinner is rotating (appears on Chrome 50, Safari 9.1.1, and Edge).\n\t\t */\n}\n.tv-spinner__spinner-layer::after {\n  content: '';\n  position: absolute;\n  box-sizing: border-box;\n  top: 0;\n  border-width: 0.07em;\n  border-radius: 50%;\n  left: 45%;\n  width: 10%;\n  border-top-style: solid;\n}\n.tv-spinner__background {\n  display: inline-block;\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  overflow: hidden;\n}\n.tv-spinner__background::after {\n  content: '';\n  position: absolute;\n  box-sizing: border-box;\n  top: 0;\n  left: 0;\n  border-radius: 50%;\n  bottom: 0;\n  width: 100%;\n  border-color: rgba(135, 151, 165, 0.2);\n  border-style: solid;\n}\n.tv-spinner__circle-clipper {\n  display: inline-block;\n  position: relative;\n  width: 50%;\n  height: 100%;\n  overflow: hidden;\n}\n.tv-spinner__circle-clipper::after {\n  content: '';\n  position: absolute;\n  box-sizing: border-box;\n  top: 0;\n  border-radius: 50%;\n  bottom: 0;\n  width: 200%;\n  border-style: solid;\n  border-bottom-color: transparent;\n  animation-duration: 1.4s;\n  animation-timing-function: cubic-bezier(0.36, 0, 0.37, 0.99);\n  animation-iteration-count: 1;\n  will-change: transform;\n}\n.tv-spinner__circle-clipper--left::after {\n  left: 0;\n  border-right-color: transparent;\n  transform: rotate(0deg);\n  animation-name: tv-spinner__left-spin;\n}\n.tv-spinner__circle-clipper--right::after {\n  left: -100%;\n  border-left-color: transparent;\n  transform: rotate(-124deg);\n  animation-name: tv-spinner__right-spin;\n}\n@keyframes tv-spinner__container-rotate {\n  100% {\n    transform: rotate(360deg);\n  }\n}\n@keyframes tv-spinner__left-spin {\n  0% {\n    transform: rotate(130deg);\n  }\n  to {\n    transform: rotate(0deg);\n  }\n}\n@keyframes tv-spinner__right-spin {\n  0% {\n    transform: rotate(-130deg);\n  }\n  to {\n    transform: rotate(-124deg);\n  }\n}\n",t=(n=void 0===n?{}:n).insertAt,"undefined"!=typeof document&&(e=document.head||document.getElementsByTagName("head")[0],(n=document.createElement("style")).type="text/css","top"===t&&e.firstChild?e.insertBefore(n,e.firstChild):e.appendChild(n),n.styleSheet?n.styleSheet.cssText=i:n.appendChild(document.createTextNode(i)));var s,a=new WeakMap;(i=s=s||{})[i.Element=1]="Element",i[i.Document=9]="Document";var r=function(){var n,e,t=(e=document.documentElement,(n=a?a.get(e):n)||((n=e.ownerDocument.createRange()).selectNodeContents(e),a&&a.set(e,n)),n.createContextualFragment('\n\t\t<div class="tv-spinner" role="progressbar">\n\t\t\t<div class="tv-spinner__spinner-layer">\n\t\t\t\t<div class="tv-spinner__background tv-spinner__width_element"></div>\n\t\t\t\t<div class="tv-spinner__circle-clipper tv-spinner__width_element tv-spinner__circle-clipper--left"></div>\x3c!--\n\t\t\t\t--\x3e<div class="tv-spinner__circle-clipper tv-spinner__width_element tv-spinner__circle-clipper--right"></div>\n\t\t\t</div>\n\t\t</div>\n\t'));if("firstElementChild"in t)r=t.firstElementChild;else for(var r=null,i=0;i<t.childNodes.length;i++){var o=t.childNodes[i];if(o.nodeType===s.Element){r=o;break}}return null!==r&&t.removeChild(r),r}(),i=(o.prototype.spin=function(n){return this._el.classList.add("tv-spinner--shown"),void 0===this._container&&(void 0!==(this._container=n)&&n.appendChild(this._el)),this._shown=!0,this},o.prototype.stop=function(n){return n&&void 0!==this._container&&this._container.removeChild(this._el),this._el.classList.remove("tv-spinner--shown"),this._shown=!1,this},o.prototype.setStyle=function(t){var r=this;return Object.keys(t).forEach(function(n){var e=t[n];void 0!==e&&r._el.style.setProperty(n,e)}),this},o.prototype.setSize=function(n){return this._el.className="tv-spinner "+(void 0!==n?"tv-spinner--size_"+n:"")+" "+(this._shown?"tv-spinner--shown":""),this},o.prototype.getEl=function(){return this._el},o.prototype.destroy=function(){this.stop(),delete this._el,delete this._container},o);function o(n){this._shown=!1,this._el=r.cloneNode(!0),this.setSize(n||"large")}window.Spinner=i}();n=n.foregroundColor||"auto",n=new Spinner("large").setStyle({color:n,zIndex:String(2e9)});n.getEl().classList.add("spinner"),n.spin(e)}()</script><script>var _tv_languages=_tv_languages||{};_tv_languages.fa={Cancel:"لغو",Price:"قیمت",Open:"باز",Comment:"توضیحات",Delete:"حذف",Interval:"بازه زمانی",Search:"جستجو",Date:"تاریخ",Description:"شرح",Settings:"تنظیمات",Width:"عرض",Background:"پس‌زمینه",Border:"حاشیه",Apply:"اعمال",Symbol:"نماد","Color Theme":"شمای رنگی","Bar's Style":"نحوه نمایش",Bars:"میله‌ای",Candles:"شمعی","Hollow Candles":"شمعی توخالی",Line:"خط",Area:"ناحیه",Grid:"شبکه",Add:"افزودن",Show:"نمایش",Industry:"صنعت",Remove:"حذف",April:"آوریل",May:"می",August:"آگوست",December:"دسامبر",Prices:"قیمت‌ها",Change:"تغییر","Hong Kong":"هنگ کنگ",Type:"نوع",Error:"خطا","Invalid Symbol":"نماد غیر معتبر",More:"بیشتر",Singapore:"سنگاپور",day_0:"days",hour_0:"hours",Reverse:"معکوس",OK:"تایید",Compare:"مقایسه",Close:"پایانی",Save:"ذخیره",month_0:"months",Hide:"عدم نمایش",m_interval_short:"m",h_interval_short:"h",D_interval_short:"D",W_interval_short:"W",M_interval_short:"M","Remove from favorites":"حذف از موارد مورد علاقه","Add to favorites":"افزودن به موارد مورد علاقه","Time Interval":"دوره زمانی",Copy:"کپی",Drawings:"ترسیم",Indicators:"اندیکاتورها","Price Line":"خط قیمت","Buenos Aires":"بوینس آیرس",Bogota:"بوگوتا",Chicago:"شیکاگو","Los Angeles":"لس آنجلس","New York":"نیویورک","Sao Paulo":"سائوپلو",Toronto:"تورنتو",Vancouver:"ونکوور",Bangkok:"بانگوک",Kolkata:"کلکته",Seoul:"سئول",Shanghai:"شانگهای",Taipei:"چین تایپه",Tehran:"تهران",Tokyo:"توکیو",Sydney:"سیدنی",Athens:"آتن",Berlin:"برلین",London:"لندن",Madrid:"مادرید",Moscow:"مسکو",Paris:"پاریس",Warsaw:"ورشو",Coordinates:"مختصات",Events:"وقایع",Inputs:"ورودی‌ها",Properties:"تنظیمات",Scales:"محورها",Style:"نحوه نمایش","Color bars based on previous close":"نمایش رنگ کندل‌ها بر اساس قیمت پایانی روز قبل",Borders:"حاشیه",Wick:"سایه بیشترین و کمترین","Accumulation/Distribution_study":"Accumulation/Distribution","Accumulative Swing Index_study":"Accumulative Swing Index","Advance/Decline_study":"Advance/Decline","Arnaud Legoux Moving Average_study":"Arnaud Legoux Moving Average",Aroon_study:"Aroon",ASI_study:"ASI","Average Directional Index_study":"Average Directional Index","Average True Range_study":"Average True Range","Awesome Oscillator_study":"Awesome Oscillator","Balance of Power_study":"Balance of Power","Bollinger Bands %B_study":"نوارهای بولینگر %B","Bollinger Bands Width_study":"Bollinger Bands Width","Bollinger Bands_study":"نوارهای بولینگر","Chaikin Money Flow_study":"Chaikin Money Flow","Chaikin Oscillator_study":"Chaikin Oscillator","Chande Kroll Stop_study":"Chande Kroll Stop","Chande Momentum Oscillator_study":"Chande Momentum Oscillator","Chop Zone_study":"Chop Zone","Choppiness Index_study":"Choppiness Index","Commodity Channel Index_study":"Commodity Channel Index","Connors RSI_study":"Connors RSI","Coppock Curve_study":"Coppock Curve","Correlation Coefficient_study":"Correlation Coefficient",CRSI_study:"CRSI","Detrended Price Oscillator_study":"Detrended Price Oscillator","Directional Movement_study":"Directional Movement","Donchian Channels_study":"Donchian Channels","Double EMA_study":"Double EMA","Ease Of Movement_study":"Ease Of Movement","Elder's Force Index_study":"Elder's Force Index","EMA Cross_study":"EMA Cross",Envelopes_study:"Envelopes","Fisher Transform_study":"Fisher Transform","Fixed Range_study":"Fixed Range","Historical Volatility_study":"Historical Volatility","Hull Moving Average_study":"Hull Moving Average","Ichimoku Cloud_study":"Ichimoku Cloud","Keltner Channels_study":"Keltner Channels","Klinger Oscillator_study":"Klinger Oscillator","Know Sure Thing_study":"Know Sure Thing","Least Squares Moving Average_study":"Least Squares Moving Average","Linear Regression Curve_study":"Linear Regression Curve","MA Cross_study":"تقاطع میانگین متحرک","MA with EMA Cross_study":"MA with EMA Cross","MA/EMA Cross_study":"MA/EMA Cross",MACD_study:"MACD","Mass Index_study":"شاخص انبوه","McGinley Dynamic_study":"McGinley Dynamic",Momentum_study:"Momentum","Money Flow_study":"گردش پول","Moving Average Channel_study":"Moving Average Channel","Moving Average Exponential_study":"نمایی میانگین متحرک","Moving Average Weighted_study":"Moving Average Weighted","Moving Average_study":"میانگین متحرک","Net Volume_study":"حجم خالص","On Balance Volume_study":"On Balance Volume","Parabolic SAR_study":"Parabolic SAR","Pivot Points Standard_study":"Pivot Points Standard","Price Channel_study":"Price Channel","Price Oscillator_study":"Price Oscillator","Price Volume Trend_study":"Price Volume Trend","Rate Of Change_study":"Rate Of Change","Relative Strength Index_study":"Relative Strength Index","Relative Vigor Index_study":"Relative Vigor Index","Relative Volatility Index_study":"Relative Volatility Index","Session Volume_study":"Session Volume","Session Volume HD_study":"Session Volume","SMI Ergodic Indicator/Oscillator_study":"SMI Ergodic Indicator/Oscillator","Smoothed Moving Average_study":"Smoothed Moving Average","Stochastic RSI_study":"Stochastic RSI",Stochastic_study:"Stochastic","Triple EMA_study":"Triple EMA",TRIX_study:"TRIX","True Strength Indicator_study":"True Strength Indicator","Ultimate Oscillator_study":"Ultimate Oscillator","Visible Range_study":"Visible Range","Volume Oscillator_study":"Volume Oscillator",Volume_study:"حجم","Vortex Indicator_study":"Vortex Indicator",VWAP_study:"VWAP",VWMA_study:"VWMA","Williams %R_study":"Williams %R","Williams Alligator_study":"Williams Alligator","Williams Fractal_study":"Williams Fractal","Zig Zag_study":"Zig Zag","Plots Background_study":"Plots Background",SuperTrend_study:"SuperTrend","Average Price_study":"Average Price","Typical Price_study":"Typical Price","Median Price_study":"Median Price","Money Flow Index_study":"Money Flow Index","Moving Average Double_study":"Moving Average Double","Moving Average Triple_study":"Moving Average Triple","Moving Average Adaptive_study":"Moving Average Adaptive","Moving Average Hamming_study":"Moving Average Hamming","Moving Average Modified_study":"Moving Average Modified","Moving Average Multiple_study":"Moving Average Multiple","Linear Regression Slope_study":"Linear Regression Slope","Standard Error_study":"Standard Error","Standard Error Bands_study":"Standard Error Bands","Correlation - Log_study":"Correlation - Log","Standard Deviation_study":"Standard Deviation","Chaikin Volatility_study":"Chaikin Volatility","Volatility Close-to-Close_study":"Volatility Close-to-Close","Volatility Zero Trend Close-to-Close_study":"Volatility Zero Trend Close-to-Close","Volatility O-H-L-C_study":"Volatility O-H-L-C","Volatility Index_study":"Volatility Index","Trend Strength Index_study":"Trend Strength Index","Majority Rule_study":"Majority Rule",Length_input:"Length",Plot_input:"Plot",Zero_input:"Zero",Signal_input:"Signal",Long_input:"Long",Short_input:"Short",UpperLimit_input:"UpperLimit",LowerLimit_input:"LowerLimit",Offset_input:"Offset",length_input:"length",mult_input:"mult",short_input:"short",long_input:"long",Limit_input:"Limit",Move_input:"Move",Value_input:"Value",Method_input:"Method","Accumulation/Distribution_input":"Accumulation/Distribution",ADR_B_input:"ADR_B","Equality Line_input":"Equality Line","Window Size_input":"Window Size",Sigma_input:"Sigma","Aroon Up_input":"Aroon Up","Aroon Down_input":"Aroon Down",Upper_input:"Upper",Lower_input:"Lower",Deviation_input:"Deviation","Levels Format_input":"Levels Format","Labels Position_input":"Labels Position","0 Level Color_input":"0 Level Color","0.236 Level Color_input":"0.236 Level Color","0.382 Level Color_input":"0.382 Level Color","0.5 Level Color_input":"0.5 Level Color","0.618 Level Color_input":"0.618 Level Color","0.65 Level Color_input":"0.65 Level Color","0.786 Level Color_input":"0.786 Level Color","1 Level Color_input":"1 Level Color","1.272 Level Color_input":"1.272 Level Color","1.414 Level Color_input":"1.414 Level Color","1.618 Level Color_input":"1.618 Level Color","1.65 Level Color_input":"1.65 Level Color","2.618 Level Color_input":"2.618 Level Color","2.65 Level Color_input":"2.65 Level Color","3.618 Level Color_input":"3.618 Level Color","3.65 Level Color_input":"3.65 Level Color","4.236 Level Color_input":"4.236 Level Color","-0.236 Level Color_input":"-0.236 Level Color","-0.382 Level Color_input":"-0.382 Level Color","-0.618 Level Color_input":"-0.618 Level Color","-0.65 Level Color_input":"-0.65 Level Color",ADX_input:"ADX","ADX Smoothing_input":"ADX Smoothing","DI Length_input":"DI Length",Smoothing_input:"Smoothing",ATR_input:"ATR",Growing_input:"Growing",Falling_input:"Falling","Color 0_input":"Color 0","Color 1_input":"Color 1",Source_input:"Source",StdDev_input:"StdDev",Basis_input:"Basis",Median_input:"Median","Bollinger Bands %B_input":"Bollinger Bands %B",Overbought_input:"Overbought",Oversold_input:"Oversold","Bollinger Bands Width_input":"Bollinger Bands Width","RSI Length_input":"RSI Length","UpDown Length_input":"UpDown Length","ROC Length_input":"ROC Length",MF_input:"MF",resolution_input:"resolution","Fast Length_input":"Fast Length","Slow Length_input":"Slow Length","Chaikin Oscillator_input":"Chaikin Oscillator",P_input:"P",X_input:"X",Q_input:"Q",p_input:"p",x_input:"x",q_input:"q",Price_input:"Price","Chande MO_input":"Chande MO","Zero Line_input":"Zero Line","Color 2_input":"Color 2","Color 3_input":"Color 3","Color 4_input":"Color 4","Color 5_input":"Color 5","Color 6_input":"Color 6","Color 7_input":"Color 7","Color 8_input":"Color 8",CHOP_input:"CHOP","Upper Band_input":"Upper Band","Lower Band_input":"Lower Band",CCI_input:"CCI","WMA Length_input":"WMA Length","Long RoC Length_input":"Long RoC Length","Short RoC Length_input":"Short RoC Length",sym_input:"sym",Symbol_input:"Symbol",Correlation_input:"Correlation",Period_input:"Period",Centered_input:"Centered","Detrended Price Oscillator_input":"Detrended Price Oscillator",isCentered_input:"isCentered",DPO_input:"DPO","ADX smoothing_input":"ADX smoothing","+DI_input":"+DI","-DI_input":"-DI",DEMA_input:"DEMA",Divisor_input:"Divisor",EOM_input:"EOM","Elder's Force Index_input":"Elder's Force Index",Percent_input:"Percent",Exponential_input:"Exponential",Average_input:"Average","Upper Percentage_input":"Upper Percentage","Lower Percentage_input":"Lower Percentage",Fisher_input:"Fisher",Trigger_input:"Trigger",Level_input:"Level",HV_input:"HV","Hull MA_input":"Hull MA","Conversion Line Periods_input":"Conversion Line Periods","Base Line Periods_input":"Base Line Periods","Lagging Span 2 Periods_input":"Lagging Span 2 Periods",Displacement_input:"Displacement","Conversion Line_input":"Conversion Line","Base Line_input":"Base Line","Lagging Span_input":"Lagging Span","Lead 1_input":"Lead 1","Lead 2_input":"Lead 2","yay Color 0_input":"yay Color 0","yay Color 1_input":"yay Color 1",Multiplier_input:"Multiplier","Bands style_input":"Bands style",Middle_input:"Middle",useTrueRange_input:"useTrueRange",ROCLen1_input:"ROCLen1",ROCLen2_input:"ROCLen2",ROCLen3_input:"ROCLen3",ROCLen4_input:"ROCLen4",SMALen1_input:"SMALen1",SMALen2_input:"SMALen2",SMALen3_input:"SMALen3",SMALen4_input:"SMALen4",SigLen_input:"SigLen",KST_input:"KST",Sig_input:"Sig",roclen1_input:"roclen1",roclen2_input:"roclen2",roclen3_input:"roclen3",roclen4_input:"roclen4",smalen1_input:"smalen1",smalen2_input:"smalen2",smalen3_input:"smalen3",smalen4_input:"smalen4",siglen_input:"siglen","Upper Deviation_input":"Upper Deviation","Lower Deviation_input":"Lower Deviation","Use Upper Deviation_input":"Use Upper Deviation","Use Lower Deviation_input":"Use Lower Deviation",Count_input:"Count",Crosses_input:"Crosses",MOM_input:"MOM",MA_input:"MA","Length EMA_input":"Length EMA","Length MA_input":"Length MA","Fast length_input":"Fast length","Slow length_input":"Slow length","Signal smoothing_input":"Signal smoothing","Simple ma(oscillator)_input":"Simple ma(oscillator)","Simple ma(signal line)_input":"Simple ma(signal line)",Histogram_input:"Histogram",MACD_input:"MACD",fastLength_input:"fastLength",slowLength_input:"slowLength",signalLength_input:"signalLength",NV_input:"NV",OnBalanceVolume_input:"OnBalanceVolume",Start_input:"شروع",Increment_input:"Increment","Max value_input":"Max value",ParabolicSAR_input:"ParabolicSAR",start_input:"start",increment_input:"increment",maximum_input:"maximum","Short length_input":"Short length","Long length_input":"Long length",OSC_input:"OSC",shortlen_input:"shortlen",longlen_input:"longlen",PVT_input:"PVT",ROC_input:"ROC",RSI_input:"RSI",RVGI_input:"RVGI",RVI_input:"RVI","Long period_input":"Long period","Short period_input":"Short period","Signal line period_input":"Signal line period",SMI_input:"SMI","SMI Ergodic Oscillator_input":"SMI Ergodic Oscillator",Indicator_input:"Indicator",Oscillator_input:"Oscillator",K_input:"K",D_input:"D",smoothK_input:"smoothK",smoothD_input:"smoothD","%K_input":"%K","%D_input":"%D","Stochastic Length_input":"Stochastic Length","RSI Source_input":"RSI Source",lengthRSI_input:"lengthRSI",lengthStoch_input:"lengthStoch",TRIX_input:"TRIX",TEMA_input:"TEMA","Long Length_input":"Long Length","Short Length_input":"Short Length","Signal Length_input":"Signal Length",Length1_input:"Length1",Length2_input:"Length2",Length3_input:"Length3",length7_input:"length7",length14_input:"length14",length28_input:"length28",UO_input:"UO",VWMA_input:"VWMA",len_input:"len","VI +_input":"VI +","VI -_input":"VI -","%R_input":"%R","Jaw Length_input":"Jaw Length","Teeth Length_input":"Teeth Length","Lips Length_input":"Lips Length",Jaw_input:"Jaw",Teeth_input:"Teeth",Lips_input:"Lips",jawLength_input:"jawLength",teethLength_input:"teethLength",lipsLength_input:"lipsLength","Down fractals_input":"Down fractals","Up fractals_input":"Up fractals",Periods_input:"Periods",Shapes_input:"Shapes","show MA_input":"show MA","MA Length_input":"MA Length","Color based on previous close_input":"Color based on previous close","Rows Layout_input":"Rows Layout","Row Size_input":"Row Size",Volume_input:"Volume","Value Area volume_input":"Value Area volume","Extend POC Right_input":"Extend POC Right","Value Area Volume_input":"Value Area Volume",Placement_input:"Placement",POC_input:"POC","Developing Poc_input":"Developing Poc","Up Volume_input":"Up Volume","Down Volume_input":"Down Volume","Value Area_input":"Value Area","Histogram Box_input":"Histogram Box","Value Area Up_input":"Value Area Up","Value Area Down_input":"Value Area Down","Number Of Rows_input":"Number Of Rows","Ticks Per Row_input":"Ticks Per Row","Up/Down_input":"Up/Down",Total_input:"Total","Deviation (%)_input":"Deviation (%)",Depth_input:"Depth","Extend to last bar_input":"Extend to last bar",Simple_input:"Simple",Weighted_input:"Weighted","Wilder's Smoothing_input":"Wilder's Smoothing","1st Period_input":"1st Period","2nd Period_input":"2nd Period","3rd Period_input":"3rd Period","4th Period_input":"4th Period","5th Period_input":"5th Period","6th Period_input":"6th Period","Rate of Change Lookback_input":"Rate of Change Lookback","Instrument 1_input":"Instrument 1","Instrument 2_input":"Instrument 2","Rolling Period_input":"Rolling Period","Standard Errors_input":"Standard Errors","Averaging Periods_input":"Averaging Periods","Days Per Year_input":"Days Per Year","Market Closed Percentage_input":"Market Closed Percentage","ATR Mult_input":"ATR Mult",VWAP_input:"VWAP","Anchor Period_input":"Anchor Period",Session_input:"Session",Week_input:"Week",Month_input:"Month",Year_input:"Year",Decade_input:"Decade",Century_input:"Century","Go to":"برو به","Image URL":"مسیر عکس","Save image":"ذخیره عکس",Dot_hotkey:"Dot",minutes_interval:"minutes",hours_interval:"hours",days_interval:"days",weeks_interval:"weeks",months_interval:"months",log_scale:"log",auto_scale:"خودکار",adj_adjustments:"adj","Date Range":"بازه زمانی",Icon:"شمایل","Compare or Add Symbol":"مقایسه یا افزودن نماد","Fullscreen mode":"حالت تمام صفحه",Ticks_interval_group_name:"Ticks",Seconds_interval_group_name:"Seconds",Minutes_interval_group_name:"Minutes",Hours_interval_group_name:"Hours",Days_interval_group_name:"Days",Weeks_interval_group_name:"Weeks",Months_interval_group_name:"Months",Ranges_interval_group_name:"Ranges",Crosshair:"نشانه‌گر","Chart Properties":"تنظیمات نمودار","Undo {0}":"حالت قبلی {0}","Redo {0}":"حالت بعدی {0}","Add Alert":"افزودن هشدار","Apply Elliott Wave":"اجرای امواج الیوت","Bring to Front":"اولین","Send to Back":"آخرین","Bring Forward":"جلو","Send Backward":"عقب","Visual Order":"ترتیب نمایش","Save As":"ذخیره به عنوان ",Clone:"تکثیر",Lock:"قفل","Add Symbol_compare_or_add_symbol_dialog":"Add Symbol","Overlay the main chart":"نمایش بر روی ناحیه اصلی","Change Resolution":"تغییر رزولوشن","Add to Watchlist {0}":"افزودن {0} به دیده بان",Eraser:"پاک‌کن","Horizontal Line":"خط افقی",Arrow:"پیکان",Extended:"تمدید شده","Fib Retracement":"اصلاحی فیبوناچی","Fib Time Zone":"منطقه زمانی فیبوناچی","Circle Lines":"خطوط منحنی","Fib Circles":"دایره های فیبوناچی",Rectangle:"مستطیل",Arc:"کمان",Text_tool:"Text","Anchored Text":"متن ثابت",Balloon:"بالون",Brush:"قلم",Forecast:"پیش بینی","Reset Chart":"تنظیمات پیش‌فرض نمودار",Undo:"حالت قبلی",Redo:"حالت بعدی","Time Zone":"منطقه زمانی","Change Symbol":"تغییر نماد","Change Interval":"تغییر بازه","Add To Watchlist":"اضافه کردن به دیده بان","Insert Drawing Tool":"افزودن شکل","Insert Indicator":"افزودن اندیکاتور","Lock/Unlock":"قفل/باز","Scale Price Chart Only":"فقط نمودار مقیاس قیمت","Stay in Drawing Mode":"ماندن در حالت ترسیم","Stay In Drawing Mode":"ماندن در حالت رسم","Lock All Drawing Tools":"قفل کردن ابزار های رسم","Hide All Drawing Tools":"عدم نمایش اشکال","Symbol Last Price Label":"Symbol Last Value Label","Go to Date":"برو به تاریخ","Session Breaks":"تنفس معاملاتی","Company Comparison":"مقایسه شرکت","Zoom Out":"کوچک نمایی","Zoom In":"بزرگ نمایی",Defaults:"پیش‌فرض‌ها",Help:"کمک",Cross:"مکان‌نما",Dot:"نقطه","Bar #":"شماره میله",Color:"رنگ","Background Color":"رنگ پس‌زمینه","Text color":"رنگ متن","Left End":"ابتدا باز","Right End":"انتها باز","Border color":"رنگ حاشیه","Border Color":"رنگ حاشیه","Font Size":"اندازه قلم","Marker Color":"رنگ نشانگر","Background color 1":"رنگ پس‌زمینه ۱","Background color 2":"رنگ پس‌زمینه ۲",Actual:"واقعی","Create Vertical Line":"ایجاد خط عمودی","Create Horizontal Line":"ایجاد خط افقی","Lock Scale":"قفل کردن محور",Percent_scale_menu:"Percent","Indexed to 100_scale_menu":"Indexed to 100",Logarithmic_scale_menu:"Logarithmic",Regular_scale_menu:"Regular","No Overlapping Labels_scale_menu":"No Overlapping Labels","Invert Scale_scale_menu":"Invert Scale",Labels:"برچسب‌ها",Watermark:"رنگ نماد پس زمینه","Top Margin":"حاشیه از بالا","Bottom Margin":"حاشیه از پایین","Right Margin":"حاشیه از راست",bars_unit:"bars","Indicator Titles":"عنوان اندیکاتور","Indicator Values":"مقادیر اندیکاتور","Show Price":"نمایش قیمت",Extend:"بسط","Color Bars Based on Previous Close":"نمایش رنگ کندل‌ها بر اساس قیمت پایانی روز قبل","Show Price Range":"نمایش فاصله قیمتی","Show Bars Range":"نمایش فاصله روزها","Show Date/Time Range":"نمایش فاصله تاریخی","Show Distance":"نمایش فاصله مختصات","Show Angle":"نمایش زاویه","Always Show Stats":"نمایش همیشگی آمار","Text Wrap":"شکستن خودکار خطوط",Text:"متن",Mirrored:"جرخش افقی",Flipped:"چرخش عمودی",Levels:"سطوح","Extend Right":"امتداد از راست","Extend Left":"امتداد از چپ",Bottom:"پایین","Price Levels":"سطوح قیمت","Time Levels":"سطوح تاریخ","Left Labels":"برچسب‌های چپ","Top Labels":"برچسب‌های بالا",Fans:"بادبزن‌ها",Arcs:"کمان‌ها","Extend left":"امتداد از چپ","Extend right":"امتداد از راست",Label:"برچسب","Label background":"برچسب پس‌زمینه",Transparency:"شفافیت","#1 (price)_linetool point":"#1 (price)","#1 (price, bar)_linetool point":"#1 (price, bar)","#{0} (price, bar)_linetool point":"#{0} (price, bar)",Channel:"کانال",Median:"خط میانی","Extend Lines":"امتداد خطوط",Original:"اصلی",Schiff:"شیف","Modified Schiff":"شیف تغییر داده‌شد",Inside:"داخلی","Label Background":"برچسب پس‌زمینه","Stop color":"رنگ محدوده زیان","Target color":"رنگ محدوده سود","Entry price":"قیمت ورود","#1 (vertical position %, bar)_linetool point":"#1 (vertical position %, bar)","#1 (bar)_linetool point":"#1 (bar)",Precision:"دقت",High:"بیشترین",Low:"کمترین","(H + L)/2":"‎(H + L)/2","(H + L + C)/3":"‎(H + L + C)/3","(O + H + L + C)/4":"‎(O + H + L + C)/4",Simple:"ساده","With Markers":"نقاط قیمت",Step:"پله‌ای",Default:"پیش‌فرض","Override Min Tick":"حداقل مقیاس قیمت","Line With Breaks":"خط",Histogram:"میله‌ای",Cross_chart_type:"به‌علاوه","Area With Breaks":"ناحیه",Columns:"ستونی",Circles:"دایره","Above Bar":"نمودار بالا","orders_Pyramiding: count orders":"orders","ticks_slippage ... ticks":"ticks","% of equity":"درصد از سهم",Offset:"فاصله","Main chart symbol_input":"Main chart symbol","Another symbol_input":"Another symbol","Text Color":"رنگ متن","Show Labels":"نمایش برچسب‌ها","Copy Chart Layout":"کپی طرح نمودار","{0} copy_ex: AAPL chart copy":"{0} copy","No symbols matched your criteria":"هیچ نمادی با شرط شما مطابقت ندارد","Always Visible":"همواره آشکار","Always Invisible":"همواره مخفی","{0} bars":"{0} میله","n/a":"هیچ کدام",Jan:"ژانویه",Feb:"فوریه",Mar:"مارس",Apr:"آوریل",Jun:"ژوئن",Aug:"آگوست",Dec:"دسامبر",d_dates:"روز",h_dates:"ساعت",m_dates:"ماه",s_dates:"s",Cycle:"دوره",Primary:"اصلی",Intermediate:"میانروز",Minor_wave:"Minor",Minute_wave:"دقیقه",Minuette:"دقیقه",Subminuette:"کمتر از دقیقه","ABCD Pattern":"الگوی ABCD","Arrow Mark Down":"پیکان رو به پایین","Arrow Mark Left":"پیکان رو به چپ","Arrow Mark Right":"پیکان رو به راست","Arrow Mark Up":"پیکان رو به بالا","Bars Pattern":"الگوی داده ها","Double Curve":"منحنی دوگانه",Curve:"منحنی","Cyclic Lines":"خطوط دایره ای","Cypher Pattern":"الگوی Cypher","Date and Price Range":"محدوده تاریخ و قیمت","Fib Channel":"کانال فیبوناچی","Fib Wedge":"گوه فیبوناچی","Flag Mark":"علامت گذاری",Note:"یادداشت","Anchored Note":"یادداشت ثابت","Price Range":"محدوده قیمتی","Regression Trend":"روند رگراسیون","Long Position":"وضعیت خرید",SUCCESS:"موفقیت",FAILURE:"شکست",in_dates:"in","{0} P&L: {1}":"‎{0} P&L {1}‎",Open_line_tool_position:"سود/زیان",Closed_line_tool_position:"سود/زیان","Risk/Reward Ratio: {0}":"‫نسبت ریسک به سود: {0}","distance: {0} px":"{0} px :فاصله مختصات",T_interval_short:"T",s_interval_short:"s",R_interval_short:"R",tick_0:"ticks",week_0:"weeks",second_0:"seconds",minute_0:"minutes",range_0:"ranges",ext_shorthand_for_extended_session:"ext",O_in_legend:"باز",H_in_legend:"بیشترین",L_in_legend:"کمترین",C_in_legend:"پایانی",HL2_in_legend:"HL2",HLC3_in_legend:"HLC3",OHLC4_in_legend:"OHLC4","loading...":"در حال بارگزاری ...",Circle:"دایره","Custom color...":"رنگ دلخواه...","Not applicable":"غیر قابل قبول",Confirmation:"تاییدیه","No indicators matched your criteria.":"هیچ اندیکاتوری با شرط شما مطابقت ندارد.",Su_day_of_week:"Su",Mo_day_of_week:"Mo",Tu_day_of_week:"Tu",We_day_of_week:"We",Th_day_of_week:"Th",Fr_day_of_week:"Fr",Sa_day_of_week:"Sa","in %s_time_range":"in %s","%s ago_time_range":"%s ago","%d minute_0":"%d minutes","%d hour_0":"%d hours","%d day_0":"%d days","%d month_0":"%d months","%d year_0":"%d years",Light_colorThemeName:"Light",Dark_colorThemeName:"Dark",Normal:"خط","{symbolsCount} symbol_symbols_and_drawings_count_0":"{symbolsCount} symbols","with {drawingsCount} drawing_symbols_and_drawings_count_0":"with {drawingsCount} drawings","{drawingsCount} drawing_0":"{drawingsCount} drawings","No drawings yet":"شکلی رسم نشده است","Add Symbol":"افزودن نماد","Confirm Inputs":"تایید ورودی‏ ها","Add Custom Color_Color Picker":"Add Custom Color","Opacity_Color Picker":"Opacity","Add_Color Picker":"Add"}</script></body></html>