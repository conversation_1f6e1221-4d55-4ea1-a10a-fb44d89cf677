<script setup lang="ts">
import type { Advertisement, Payment } from '@zsmartex/types'
import getSymbolFromCurrency from 'currency-symbol-map'
import { roundNumber } from '@zsmartex/utils'

const props = defineProps<{
  advertisement: Advertisement
  side: string
}>()

const tradeStore = useTradeStore()
const userStore = useUserStore()
const visible = ref(false)
const loading = ref(false)
const pay = ref('')
const receive = ref('')
const takerPaymentID = ref('')
const drawer = ref(false)

const errorPay = computed(() => {
  if (props.side === 'buy' && pay.value && (Number(pay.value) > props.advertisement.max || Number(pay.value) < props.advertisement.min)) return 'error.range.pay'
  return ''
})

const errorReceive = computed(() => {
  if (props.side === 'sell' && receive.value && (Number(receive.value) > props.advertisement.max || Number(receive.value) < props.advertisement.min)) return 'error.range.receive'
  return ''
})

const types = computed(() => {
  const result: string[] = []
  for (const payment of props.advertisement.payments) {
    result.push(payment.type)
  }
  return result
})

const payments = computed(() => {
  return userStore.payments.filter(p => types.value.includes(p.type))
})

const disabledButton = computed(() => {
  if (!pay.value || errorPay.value) return true
  if (!receive.value || errorReceive.value) return true
  if (props.side === 'sell' && !takerPaymentID.value) return true
  return false
})

// onMounted(() => {
//   const element = useCurrentElement()
//   onClickOutside(element.value, () => {
//     visible.value = false
//   })
// })

const onChangePay = () => {
  let result = ''
  if (props.side === 'buy') {
    result = (Number(pay.value) / props.advertisement.price).toFixed(2)
  } else {
    result = (Number(pay.value) * props.advertisement.price).toFixed(2)
  }

  while (result.includes('.') && (result[result.length - 1] === '.' || result[result.length - 1] === '0')) {
    result = result.slice(0, result.length - 1)
  }

  receive.value = result
}

const onChangeReceive = () => {
  let result = ''
  if (props.side === 'buy') {
    result = (Number(receive.value) * props.advertisement.price).toFixed(2)
  } else {
    result = (Number(receive.value) / props.advertisement.price).toFixed(2)
  }

  while (result.includes('.') && (result[result.length - 1] === '.' || result[result.length - 1] === '0')) {
    result = result.slice(0, result.length - 1)
  }

  pay.value = result
}

const createP2PTrade = async () => {
  loading.value = true

  await tradeStore.CreateP2PTrade({
    advertisement_id: props.advertisement.id,
    amount: props.side === 'buy' ? Number(receive.value) : Number(pay.value),
    payment_id: props.side === 'buy' ? props.advertisement.payments[0].id : Number(takerPaymentID.value),
  }, (staticID: string) => {
    visible.value = false
    drawer.value = false
    navigateTo(`/p2p/trade/${staticID}`)
  })

  loading.value = false
}

const TypeAdvertisement = (payment: string) => {
  switch (payment) {
    case 'bank':
      return 'Bank Account'
    case 'momo':
      return 'MoMo'
    case 'zalo':
      return 'Zalo Pay'
    default:
      return payment
  }
}

const formatPaymentData = (payment: Payment) => {
  if (payment.type === 'bank') {
    let result = `${payment.name_account} - ${payment.data.number_account} - ${payment.data.bank_name}`
    if (payment.data.bank_address) result += ` - ${payment.data.bank_address}`
    return result
  } else if (payment.type === 'momo') {
    return `${payment.name_account} - ${payment.data.phone}`
  }

  return ''
}
</script>

<template>
  <div class="layout-advertisement">
    <div v-if="!visible" class="layout-advertisement-row">
      <div class="layout-advertisement-row-member">
        <div class="flex h-full">
          <div class="mr-3 rounded-full bg-blue-600 h-[20px] w-[20px] flex justify-center items-center text-white">
            {{ advertisement.username ? advertisement.username[0].toUpperCase() : advertisement.username }}
          </div>
          <div class="flex flex-col justify-between">
            <div class="text-blue-500 bold-text cursor-pointer" @click="navigateTo(`/p2p/profiles/${advertisement.member_id}`)">
              {{ advertisement.username }}
            </div>
            <div class="flex layout-advertisement-row-information">
              <div>
                <span class="bold-text">{{ advertisement.total_trade }}</span> {{ $t('layout.p2p.order') }}
              </div>
              <div>
                <span class="bold-text">{{ roundNumber(advertisement.success_rate * 100, 2) }}%</span> {{ $t('layout.p2p.success') }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="layout-advertisement-row-price flex items-center">
        <span class="text-xl bold-text mr-2">{{ roundNumber(advertisement.price, 2) }}</span>
        <span class="text-[12px] bold-text mt-[6px]">{{ advertisement.fiat_currency.toUpperCase() }}</span>
      </div>
      <div class="layout-advertisement-row-limit flex flex-col justify-between">
        <div>
          <span class="text-gray mr-2">{{ $t('page.global.table.available') }}</span>
          <span class="bold-text">{{ `${roundNumber(advertisement.available_amount, 2)} ${advertisement.coin_currency.toUpperCase()}` }}</span>
        </div>
        <div>
          <span class="text-gray mr-2">{{ $t('page.global.placeholder.limit') }}</span>
          <span class="bold-text">{{ `${roundNumber(advertisement.min, 2)} ${getSymbolFromCurrency(advertisement.fiat_currency.toUpperCase())}` }} - {{ `${roundNumber(advertisement.max, 2)} ${getSymbolFromCurrency(advertisement.fiat_currency.toUpperCase())}` }}</span>
        </div>
      </div>
      <div class="layout-advertisement-row-payments">
        <div v-for="payment in advertisement.payments" :key="payment.id">
          <ZTooltip :title="TypeAdvertisement(payment.type)">
            {{ TypeAdvertisement(payment.type) }}
          </ZTooltip>
        </div>
      </div>
      <div class="layout-advertisement-row-trade flex items-center">
        <ZButton
          :class="[
            { 'layout-advertisement-row-trade-button-up': side === 'buy' },
            { 'layout-advertisement-row-trade-button-down': side === 'sell' },
          ]"
          @click="userStore.isAuthenticated ? visible = true : navigateTo('/login')"
        >
          {{ `${side.toUpperCase()} ${advertisement.coin_currency.toUpperCase()}` }}
        </ZButton>
      </div>
    </div>
    <div v-else class="layout-advertisement-row-action shadow-lg">
      <div class="w-7/12">
        <div class="flex">
          <div class="mr-3 rounded-full bg-blue-600 h-[20px] w-[20px] flex justify-center items-center text-white">
            {{ advertisement.username ? advertisement.username[0].toUpperCase() : advertisement.username }}
          </div>
          <div class="mr-4 text-blue-500 bold-text">
            {{ advertisement.username }}
          </div>
          <div class="flex">
            <div>
              <span class="bold-text">{{ advertisement.total_trade }}</span> {{ $t('layout.p2p.order') }}
            </div>
            <div>
              <span class="bold-text">{{ roundNumber(advertisement.success_rate * 100, 2) }}%</span> {{ $t('layout.p2p.success') }}
            </div>
          </div>
        </div>
        <div class="px-[32px] py-[24px]">
          <div class="flex mb-4">
            <div class="flex-1 flex items-center">
              <div class="mr-3 text-gray">
                {{ $t('page.global.placeholder.price') }}
              </div>
              <div
                class="bold-text"
                :class="[
                  { 'text-up': side === 'buy' },
                  { 'text-down': side === 'sell' },
                ]"
              >
                {{ `${roundNumber(advertisement.price, 2)} ${advertisement.fiat_currency.toUpperCase()}` }}
              </div>
            </div>
            <div class="flex-1 flex items-center">
              <div class="mr-3 text-gray">
                {{ $t('page.global.table.available') }}
              </div>
              <div class="bold-text">
                {{ `${roundNumber(advertisement.available_amount, 2)} ${advertisement.coin_currency.toUpperCase()}` }}
              </div>
            </div>
          </div>
          <div class="flex mb-4">
            <div class="flex-1 flex items-center">
              <div class="mr-3 text-gray">
                {{ $t('page.global.placeholder.min') }}
              </div>
              <div class="bold-text">
                {{ `${roundNumber(advertisement.min, 2)} ${getSymbolFromCurrency(advertisement.fiat_currency.toUpperCase())}` }}
              </div>
            </div>
            <div class="flex-1 flex items-center">
              <div class="mr-3 text-gray">
                {{ $t('page.global.placeholder.max') }}
              </div>
              <div class="bold-text">
                {{ `${roundNumber(advertisement.max, 2)} ${getSymbolFromCurrency(advertisement.fiat_currency.toUpperCase())}` }}
              </div>
            </div>
          </div>
          <div class="flex">
            <div class="flex-1 flex items-center">
              <div class="mr-3 text-gray">
                {{ $t('layout.p2p.time_limit') }}
              </div>
              <div class="bold-text">
                {{ $t('layout.p2p.15m') }}
              </div>
            </div>
            <div class="flex-1 flex items-center">
              <div class="mr-3 text-gray">
                {{ $t('page.p2p.payments') }}
              </div>
              <div class="layout-advertisement-row-payments">
                <div v-for="payment in advertisement.payments" :key="payment.id">
                  <ZTooltip :title="TypeAdvertisement(payment.type)">
                    {{ TypeAdvertisement(payment.type) }}
                  </ZTooltip>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="w-5/12 pl-[24px] h-[336px] layout-advertisement-row-action-right">
        <div class="mb-8">
          <div class="mb-3">
            {{ $t('layout.p2p.i_want_pay') }}
          </div>
          <ZInput v-model="pay" :type="InputType.Decimal" :error="errorPay" class="w-full" @input="onChangePay">
            <template #suffix>
              <div class="flex">
                <div class="mr-3 text-primary bold-text">
                  {{ $t('page.global.action.all') }}
                </div>
                <div class="text-gray">
                  {{ side === 'buy' ? advertisement.fiat_currency.toUpperCase() : advertisement.coin_currency.toUpperCase() }}
                </div>
              </div>
            </template>
          </ZInput>
        </div>
        <div class="mb-8">
          <div class="mb-3">
            {{ $t('layout.p2p.i_will_receive') }}
          </div>
          <ZInput v-model="receive" :type="InputType.Decimal" :error="errorReceive" class="w-full" @input="onChangeReceive">
            <template #suffix>
              <div class="text-gray">
                {{ side === 'buy' ? advertisement.coin_currency.toUpperCase() : advertisement.fiat_currency.toUpperCase() }}
              </div>
            </template>
          </ZInput>
        </div>
        <div v-if="side === 'sell'" class="mb-8">
          <div class="mb-3">
            {{ $t('page.p2p.number_account') }}
          </div>
          <ZSelect
            v-model="takerPaymentID"
            :data-source="payments"
            :search="true"
            :scroll="true"
            :show-clear="false"
            :columns="[
              {
                key: 'data',
                scopedSlots: true,
              },
            ]"
            :find-by="['id']"
            value-key="id"
            label-key="data"
            :label-format-func="formatPaymentData"
            placeholder="Number account"
          />
        </div>
        <div class="flex mb-24">
          <div class="w-4/12">
            <ZButton
              class="!w-full h-[40px] layout-advertisement-row-action-cancel"
              @click="visible = false"
            >
              {{ $t('page.global.action.cancel') }}
            </ZButton>
          </div>
          <div class="w-8/12 ml-[16px]">
            <ZButton
              class="!w-full h-[40px]"
              :class="[
                { 'layout-advertisement-row-action-buy': side === 'buy' },
                { 'layout-advertisement-row-action-sell': side === 'sell' },
              ]"
              :loading="loading"
              :disabled="disabledButton"
              @click="createP2PTrade"
            >
              <span class="capitalize">{{ `${side} ${advertisement.coin_currency.toUpperCase()}` }}</span>
            </ZButton>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="layout-advertisement-mobile">
    <div class="layout-advertisement-mobile-row mb-3 justify-between">
      <div class="flex">
        <div class="mr-1 rounded-full bg-blue-600 h-[18px] w-[18px] text-[12px] flex justify-center items-center text-white">
          {{ advertisement.username ? advertisement.username[0].toUpperCase() : advertisement.username }}
        </div>
        <div class="text-blue-500 bold-text cursor-pointer" @click="navigateTo(`/p2p/profiles/${advertisement.member_id}`)">
          {{ advertisement.username }}
        </div>
      </div>
      <div class="flex layout-advertisement-row-information">
        <div class=" text-[12px] text-gray">
          <span class="bold-text">{{ advertisement.total_trade }}</span> {{ $t('layout.p2p.order') }}
        </div>
        <div class=" text-[12px] text-gray">
          <span class="bold-text">{{ roundNumber(advertisement.success_rate * 100, 2) }}%</span> {{ $t('layout.p2p.success') }}
        </div>
      </div>
    </div>
    <div class="layout-advertisement-mobile-row mb-4">
      <div class="w-8/12">
        <div class="text-[12px] text-gray mb-2">
          {{ $t('page.p2p.price') }}
        </div>
        <div class="bold-text mb-1">
          <span class="text-[22px]">{{ `${roundNumber(advertisement.price, 2)} ` }}</span>{{ advertisement.fiat_currency.toUpperCase() }}
        </div>
        <div>
          <span class="text-gray mr-2 text-[12px]">{{ $t('page.global.table.available') }}</span>
          <span class="bold-text text-[12px]">{{ `${roundNumber(advertisement.available_amount, 2)} ${advertisement.coin_currency.toUpperCase()}` }}</span>
        </div>
        <div>
          <span class="text-gray mr-2 text-[12px]">{{ $t('page.p2p.limit') }}</span>
          <span class="bold-text text-[12px]">{{ `${roundNumber(advertisement.min, 2)} ${getSymbolFromCurrency(advertisement.fiat_currency.toUpperCase())}` }} - {{ `${roundNumber(advertisement.max, 2)} ${getSymbolFromCurrency(advertisement.fiat_currency.toUpperCase())}` }}</span>
        </div>
      </div>
      <div class="w-4/12 h-[90px] flex items-end">
        <div class="layout-advertisement-row-trade w-full flex items-center">
          <ZButton
            :class="[
              { 'layout-advertisement-row-trade-button-up': side === 'buy' },
              { 'layout-advertisement-row-trade-button-down': side === 'sell' },
            ]"
            @click="userStore.isAuthenticated ? drawer = true : navigateTo('/login')"
          >
            {{ `${side.toUpperCase()} ${advertisement.coin_currency.toUpperCase()}` }}
          </ZButton>
        </div>
      </div>
    </div>
    <div class="layout-advertisement-row-payments text-[12px]">
      <div v-for="payment in advertisement.payments" :key="payment.id">
        <ZTooltip :title="payment.type">
          {{ payment.type }}
        </ZTooltip>
      </div>
    </div>
    <ZDrawer v-model="drawer" :title="`${side[0].toUpperCase() + side.slice(1)} ${advertisement.coin_currency.toUpperCase()}`" height="58 0" :footer="false" :position="Position.Bottom" @close="drawer = false">
      <div class="p-[16px]">
        <div class="flex mb-2">
          <div class="mr-1 rounded-full bg-blue-600 h-[18px] w-[18px] flex justify-center items-center text-white">
            {{ advertisement.username ? advertisement.username[0].toUpperCase() : advertisement.username }}
          </div>
          <div class="text-blue-500 bold-text cursor-pointer" @click="navigateTo(`/p2p/profiles/${advertisement.member_id}`)">
            {{ advertisement.username }}
          </div>
        </div>
        <div class="flex ml-[20px] mb-6 layout-advertisement-row-information">
          <div class=" text-[12px] text-gray">
            <span class="bold-text">{{ advertisement.total_trade }}</span> {{ $t('layout.p2p.order') }}
          </div>
          <div class=" text-[12px] text-gray">
            <span class="bold-text">{{ roundNumber(advertisement.success_rate * 100, 2) }}%</span> {{ $t('layout.p2p.success') }}
          </div>
        </div>
        <div class="mb-2">
          <span class="text-gray mr-2">{{ $t('page.p2p.price') }}</span>
          <span
            class="bold-text"
            :class="[
              { 'text-up': side === 'buy' },
              { 'text-down': side === 'sell' },
            ]"
          >
            {{ `${roundNumber(advertisement.price, 2)} ${advertisement.fiat_currency.toUpperCase()}` }}
          </span>
        </div>
        <div class="mb-6">
          <span class="text-gray mr-2">{{ $t('page.global.table.available') }}</span>
          <span class="bold-text">{{ `${roundNumber(advertisement.available_amount, 2)} ${advertisement.coin_currency.toUpperCase()}` }}</span>
        </div>
        <div class="mb-8">
          <div class="mb-2 bold-text">
            {{ $t('layout.p2p.i_want_pay') }}
          </div>
          <ZInput v-model="pay" :type="InputType.Decimal" :error="errorPay" class="w-full" @input="onChangePay">
            <template #suffix>
              <div class="flex">
                <div class="mr-3 text-primary bold-text">
                  {{ $t('page.global.action.all') }}
                </div>
                <div class="text-gray">
                  {{ side === 'buy' ? advertisement.fiat_currency.toUpperCase() : advertisement.coin_currency.toUpperCase() }}
                </div>
              </div>
            </template>
          </ZInput>
        </div>
        <div class="mb-16">
          <div class="mb-2 bold-text">
            {{ $t('layout.p2p.i_will_receive') }}
          </div>
          <ZInput v-model="receive" :type="InputType.Decimal" :error="errorReceive" class="w-full" @input="onChangeReceive">
            <template #suffix>
              <div class="text-gray">
                {{ side === 'buy' ? advertisement.coin_currency.toUpperCase() : advertisement.fiat_currency.toUpperCase() }}
              </div>
            </template>
          </ZInput>
        </div>
        <ZButton
          class="!w-full h-[40px] mb-4"
          :class="[
            { 'layout-advertisement-row-action-buy': side === 'buy' },
            { 'layout-advertisement-row-action-sell': side === 'sell' },
          ]"
          :loading="loading"
          :disabled="disabledButton"
          @click="createP2PTrade"
        >
          <span class="capitalize">{{ `${side} ${advertisement.coin_currency.toUpperCase()}` }}</span>
        </ZButton>
        <div class="flex-1 flex justify-between items-center mb-2">
          <div class="mr-3 text-gray">
            {{ $t('layout.p2p.time_limit') }}
          </div>
          <div class="bold-text">
            {{ $t('layout.p2p.15m') }}
          </div>
        </div>
        <div class="flex-1">
          <div class="mr-3 mb-2 text-gray">
            {{ $t('page.p2p.payments') }}
          </div>
          <div class="layout-advertisement-row-payments">
            <div v-for="payment in advertisement.payments" :key="payment.id">
              <ZTooltip :title="payment.type">
                {{ payment.type }}
              </ZTooltip>
            </div>
          </div>
        </div>
      </div>
    </ZDrawer>
  </div>
</template>

<style lang="less">
// .layout-advertisement {
//   border-top: 1px solid @base-border-color;

//   @media @mobile {
//     display: none;
//   }

//   &-mobile {
//     padding: 16px;
//     border-top: 1px solid @base-border-color;

//     &-row {
//       display: flex;
//       align-items: center;
//     }
//   }

//   &-visible + & {
//     border-top: none;
//   }
// }

.layout-advertisement {
  @media @mobile {
    display: none;
  }

  &-mobile {
    display: none;
    padding: 16px;
    border-top: 1px solid @base-border-color;

    @media @mobile {
      display: block;
    }

    &-row {
      display: flex;
      align-items: center;
    }
  }

  &-row {
    display: flex;
    padding: 24px 0;
    height: 92px;
    border-top: 1px solid @base-border-color;

    &-information {
      @media @tablet {
        display: block;
      }
    }

    &-action {
      display: flex;
      padding: 24px;
      border-radius: 8px;
      box-shadow: 0 3px 6px -4px #0000001f, 0 6px 16px #00000014, 0 9px 28px 8px #0000000d;

      &-right {
        border-left: 1px solid @base-border-color;
      }

      &-cancel {
        background-color: white !important;
        border-color: white !important;
        color: @gray-color !important;
      }

      &-buy {
        background-color: @up-color !important;
        border-color: @up-color !important;
        color: white !important;
      }

      &-sell {
        background-color: @down-color !important;
        border-color: @down-color !important;
        color: white !important;
      }

      .z-select {
        .z-dropdown {
          &-trigger {
            background-color: white !important;
          }
        }
      }
    }

    & > div {
      flex: 1;
    }

    &-payments {
      display: flex;
      flex-wrap: wrap;
      max-width: 280px;

      & > div {
        margin-bottom: 6px;
        padding: 2px 6px;
        height: fit-content;
        line-height: normal;
        background-color: rgba(@gray-color, 0.1);
        border-radius: 4px;
        color: @primary-color;
        cursor: pointer;

        & ~ div {
          margin-left: 6px;
        }
      }
    }

    &-trade {
      max-width: 130px;

      .z-button {
        width: 100%;
        height: 32px;
        line-height: 32px;
        color: white;
        border-radius: 4px;
      }

      &-button {
        &-up {
          border: 1px solid @up-color !important;
          background-color: @up-color !important;
        }
        &-down {
          border: 1px solid @down-color !important;
          background-color: @down-color !important;
        }
      }
    }
  }
}
</style>
