<script setup lang="ts">
defineProps<{
  loading: boolean
}>()

const emit = defineEmits<{
  (event: 'click'): void
}>()

const visible = ref(false)
const check = ref(false)

function openModal() {
  visible.value = true
}

function onClick() {
  emit('click')
  visible.value = false
}

defineExpose({
  openModal,
})
</script>

<template>
  <ZModal
    v-model="visible"
    class="layout-p2p-payment-confirm-modal"
    :title="$t('page.p2p.confirm_unlock.title')"
    class-modal="!w-[400px]"
  >
    <div class="text-center mb-6 text-[#ffc107]">
      <ZIconWarnDuotone />
    </div>
    <div class="mb-4">
      {{ $t('page.p2p.confirm_unlock.notice') }}
    </div>
    <div class="mb-4">
      <ZCheckbox v-model="check" />
      {{ $t('page.p2p.confirm_unlock.confirm') }}
    </div>
    <div class="flex mt-4">
      <div class="flex-1 text-center text-gray cursor-pointer h-[40px] leading-[40px]" @click="visible = false">
        {{ $t('page.global.action.cancel') }}
      </div>
      <ZButton class="flex-1" :loading="loading" :disabled="!check" @click="onClick">
        {{ $t('page.global.action.accept') }}
      </ZButton>
    </div>
  </ZModal>
</template>

<style lang="less">
.layout-p2p-payment-confirm-modal {
  svg {
    width: 100px;
    height: 100px;

    .cls-1 {
      fill: @warn-color;
    }

    .cls-2 {
      fill: white;
    }
  }

  .z-checkbox {
    svg {
      width: 14px;
      height: 14px;

      .cls-1 {
        fill: white;
      }
    }
  }

  &-info {
    border-bottom: 1px solid @base-border-color;
  }

  .z-button {
    border: 1px solid @primary-color;
    background-color: @primary-color;
    color: white !important;
  }
}
</style>
