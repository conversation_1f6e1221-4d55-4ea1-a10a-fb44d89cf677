<script setup lang="ts">
import { InputType } from '@zsmartex/components/types'
import type But<PERSON> from '#components/Button.vue'

const visible = ref(false)

function openModal() {
  visible.value = true
}

const delayButtonEmail = ref<InstanceType<typeof Button>>()
const delayButtonPhone = ref<InstanceType<typeof Button>>()

const tradeStore = useTradeStore()

const loading = ref(false)

const emailCode = ref('')
const phoneCode = ref('')
const OTPCode = ref('')

function GenerateCode(type: 'email' | 'phone') {
  loading.value = true
  const button = type === 'email' ? delayButtonEmail : delayButtonPhone

  tradeStore.GenerateCodeP2PTrade(tradeStore.p2p_trade.static_id, type, button.value?.StartDelay)
  loading.value = false
}

const disabledButton = computed(() => {
  if (emailCode.value.length !== 6) return true
  if (phoneCode.value.length < 6) return true
  if (OTPCode.value.length < 6) return true
  return false
})

async function CompleteP2PTrade() {
  loading.value = true

  await tradeStore.CompleteP2PTrade(
    tradeStore.p2p_trade.static_id,
    {
      email_code: emailCode.value,
      phone_code: phoneCode.value,
      otp_code: OTPCode.value,
    },
    () => {
      visible.value = false
    },
  )
  loading.value = false
}

defineExpose({
  openModal,
})
</script>

<template>
  <ZModal
    v-model="visible"
    class="layout-p2p-complete-modal"
    :title="$t('layout.my.security.unbind.title')"
  >
    <ZForm autocomplete="off" @submit="CompleteP2PTrade">
      <div class="rounded bg-gray-100 mt-4 px-4 py-4">
        <ZFormRow label="Email Code" :required="true">
          <ZInput
            v-model="emailCode"
            name="email_code"
            :type="InputType.Number"
            :placeholder="$t('page.global.placeholder.e-confirmation_code')"
            :max-length="6"
          >
            <template #suffix>
              <ZButton
                ref="delayButtonEmail"
                :delay="{
                  time: 60,
                  content: 'Get [#{time}] again',
                }"
                @click="GenerateCode('email')"
              >
                {{ $t('page.global.action.get_code') }}
              </ZButton>
            </template>
          </ZInput>
        </ZFormRow>
        <ZFormRow label="Phone Code" :required="true">
          <ZInput
            v-model="phoneCode"
            name="phone_code"
            :type="InputType.Number"
            :placeholder="$t('page.global.placeholder.phone_confirmation_code')"
            :max-length="6"
            class="px-2 py-8 h-32 bg-gray-100"
            :required="true"
          >
            <template #suffix>
              <ZButton
                ref="delayButtonPhone"
                :delay="{
                  time: 60,
                  content: 'Get [#{time}] again',
                }"
                @click="GenerateCode('phone')"
              >
                {{ $t('page.global.action.get_code') }}
              </ZButton>
            </template>
          </ZInput>
        </ZFormRow>
        <ZFormRow label="OTP Code" :required="true">
          <ZCodeInput v-model="OTPCode" class="relative" :length="6" />
        </ZFormRow>
      </div>
      <ZButton
        class="!w-full mt-4 mb-2"
        type="primary"
        html-type="submit"
        :loading="loading"
        :disabled="disabledButton"
      >
        {{ $t('page.global.action.save') }}
      </ZButton>
    </ZForm>
  </ZModal>
</template>

<style lang="less">
.layout-p2p-complete-modal {
  .z-overlay {
    width: 450px;
  }

  .phone-number {
    height: 40px;
    line-height: 40px;
    border: none;

    &.z-input-focused {
      box-shadow: none !important;
      outline: 0;
    }
  }

  .z-dropdown {
    &-trigger {
      border-radius: 4px;
      background-color: rgba(@gray-color, 0.1);
      padding: 8px 12px;
      align-items: center;
      cursor: pointer;
    }

    &-overlay {
      width: 250px;
    }
  }

  .z-input {
    background-color: #fff;
    height: 40px;
    line-height: 40px;
  }

  .z-table-row-col {
    display: block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .z-button {
    height: 40px;
    font-size: 18px;
    color: #fff;
  }
}
</style>
