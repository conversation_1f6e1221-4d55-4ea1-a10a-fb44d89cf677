<script setup lang="ts">
const visible = ref(false)
const image = ref('')

function openModal(img?: string) {
  visible.value = true

  if (img) {
    image.value = img
  }
}

defineExpose({
  openModal,
})
</script>

<template>
  <ZModal
    v-model="visible"
    class="layout-p2p-preview-modal"
    :title="$t('page.p2p.preview_image')"
  >
    <img class="w-full" :src="image">
  </ZModal>
</template>

<style lang="less">
.layout-p2p-preview-modal {
  // font-size: 16px;
  .img {
    width: 100%;
  }
}
</style>
