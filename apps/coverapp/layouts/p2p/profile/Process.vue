<script setup lang="ts">
defineProps<{
  type: string
  value: number
  total: number
}>()
</script>

<template>
  <div class="layouts-p2p-profile-process">
    <div class="layouts-p2p-profile-process-item" :style="`width: ${value / total * 100}%`" :class="`layouts-p2p-profile-process-item-${type}`" />
  </div>
</template>

<style lang="less">
.layouts-p2p-profile-process {
  position: relative;
  width: 160px;
  height: 8px;
  border-radius: 8px;
  overflow: hidden;
  background-color: @base-border-color;

  &-item {
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;

    &-positive {
      background-color: @up-color;
    }

    &-negative {
      background-color: @down-color;
    }
  }

}
</style>
