<script setup lang="ts">
import { encryptEmail, roundNumber } from '@zsmartex/utils'
import type { ZTabItem } from '@zsmartex/components/types'
import { format as formatDate } from 'date-fns'
import Process from './Process.vue'

const props = defineProps<{
  id: number
}>()

const total = ref(0)

const publicStore = usePublicStore()

const { query, callbacks } = useQuery()

const tabs: ZTabItem[] = [
  {
    key: 'all',
    text: 'All',
  },
  {
    key: 'positive',
    text: 'Positive',
  },
  {
    key: 'negative',
    text: 'Negative',
  },
]

const tagsExample = computed(() => {
  if (query.value.type === 'all') return ['Fast transaction', 'Polite and friendly', 'Patient', 'Safe and trustworthy', 'Good price', 'Slow transaction', 'Impatient', 'Suspicious/Scam']
  if (query.value.type === P2PFeedbackType.Positive) return ['Fast transaction', 'Polite and friendly', 'Patient', 'Safe and trustworthy', 'Good price']
  return ['Slow transaction', 'Impatient', 'Suspicious/Scam']
})

const { data: feedbacks, pending, refresh } = await useAsyncData(async () => {
  const { headers, data } = await publicStore.FetchP2PFeedbacks(props.id, query.value)

  total.value = Number(headers.total)

  return data
}, { default: () => ([]) })
callbacks.push(refresh)

const numberPositive = feedbacks.value?.filter(f => f.type === P2PFeedbackType.Positive) ? feedbacks.value?.filter(f => f.type === P2PFeedbackType.Positive).length : 0
const numberNegative = feedbacks.value?.filter(f => f.type === P2PFeedbackType.Negative) ? feedbacks.value?.filter(f => f.type === P2PFeedbackType.Negative).length : 0
const rate = (!feedbacks.value || feedbacks.value.length === 0) ? 0 : roundNumber((numberPositive / feedbacks.value.length), 2)
const length = feedbacks.value!.length || 0
</script>

<template>
  <div class="page-p2p-profile-feedback">
    <div class="page-p2p-profile-feedback-head flex mb-4">
      <div class="mr-12">
        <div class="bold-text text-2xl">
          {{ `${rate}%` }}
        </div>
        <div class="text-gray">
          {{ `${length} Reviews` }}
        </div>
      </div>
      <div class="flex flex-col justify-center">
        <div class="text-[12px] flex items-center bold-text">
          <ZIconLikeFilled class="like mr-3" />
          <Process class="mr-3" :type="P2PFeedbackType.Positive" :total="length" :value="numberPositive" />
          {{ numberPositive }}
        </div>
        <div class="text-[12px] flex items-center bold-text">
          <ZIconDislikeFilled class="dislike mr-3" />
          <Process class="mr-3" :type="P2PFeedbackType.Negative" :total="length" :value="numberNegative" />
          {{ numberNegative }}
        </div>
      </div>
    </div>
    <ZTab v-model="query.type" class="page-p2p-profile-feedback-tab mb-6" :tabs="tabs" />
    <div class="page-p2p-profile-feedback-tag mb-4">
      <div
        v-for="Tag in tagsExample"
        :key="Tag"
        class="page-p2p-profile-feedback-tag-item"
        :class="{ 'page-p2p-profile-feedback-tag-item-active': Tag === tag }"
        @click="tag === Tag ? tag = 'all' : tag = Tag"
      >
        {{ Tag }}
      </div>
    </div>
    <div class="relative">
      <ZLoading v-if="pending" />
      <div v-else-if="feedbacks!.length === 0" class="h-[300px] w-full flex flex-col items-center justify-center empty">
        <ZIconClipboardTimesDuotone />
        <span class="text-gray">{{ $t('page.global.table.empty') }}</span>
      </div>
      <div v-for="feedback in feedbacks" :key="feedback.id" class="page-p2p-profile-feedback-item">
        <div class="flex items-center mb-4">
          <div class="mr-3 rounded-full bg-blue-600 h-[20px] w-[20px] text-[12px] flex justify-center items-center text-white">
            {{ feedback.email ? feedback.email[0].toUpperCase() : feedback.email }}
          </div>
          <div class="bold-text mr-3">
            {{ encryptEmail(feedback.email) }}
          </div>
          <span class="text-gray">
            {{ formatDate(new Date(feedback.created_at), "yyyy-MM-dd") }}
          </span>
        </div>
        <div class="flex items-center">
          <div class="mr-4">
            <ZIconLikeFilled v-if="feedback.type === P2PFeedbackType.Positive" class="like" />
            <ZIconDislikeFilled v-else class="dislike" />
          </div>
          <div>
            {{ feedback.content ? feedback.content : feedback.tags.join(', ') }}
          </div>
        </div>
      </div>
      <ZPagination v-model="query.page" v-model:page-size="query.limit" :is-loading="pending" :total="total" />
    </div>
  </div>
</template>

<style lang="less">
.page-p2p-profile-feedback {
  background-color: white;
  padding: 16px;
  border-radius: 4px;

  .z-tab {
    &-item {
      font-size: 16px;
    }
  }

  svg {
    width: 18px;
    height: 18px;
  }

  .like {
    fill: @up-color;
  }

  .dislike {
    fill: @down-color;
  }

  &-item {
    padding: 16px 0;
    border-bottom: 1px solid rgba(@base-border-color, 0.08);
  }

  &-tag {
    display: flex;

    &-item {
      margin-right: 12px;
      margin-bottom: 8px;
      padding: 4px 16px;
      color: @gray-color;
      border: 1px solid @base-border-color;
      border-radius: 4px;
      cursor: pointer;
      user-select: none;

      &-active {
        border-color: @primary-color;
        color: @primary-color;
      }
    }
  }
}
</style>
