<script setup lang="ts">
import { roundNumber } from '@zsmartex/utils'
const visible = ref(false)

const tradeStore = useTradeStore()
const loading = ref(false)

function openModal() {
  visible.value = true
}

const AcceptP2PRequest = async () => {
  loading.value = true
  await tradeStore.AcceptP2PRequest(tradeStore.p2p_trade.static_id, () => {
    visible.value = false
    navigateTo(`/p2p/trade/${tradeStore.p2p_trade.static_id}`)
  })
  loading.value = false
}

defineExpose({
  openModal,
})
</script>

<template>
  <ZModal
    v-model="visible"
    class="layout-p2p-accept-modal"
    :title="$t('layout.p2p.accept_modal.title')"
  >
    <div class="flex mb-5 pt-4">
      <div class="flex flex-1">
        <div class="text-gray mr-3">
          {{ $t('page.p2p.static_id') }}
        </div>
        <div class="bold-text">
          {{ tradeStore.p2p_trade.static_id }}
        </div>
      </div>
      <div class="flex">
        <div class="text-gray mr-3">
          {{ $t('page.global.placeholder.side') }}
        </div>
        <div
          class="capitalize bold-text"
          :class="[
            { buy: tradeStore.p2p_trade.side === 'buy' },
            { sell: tradeStore.p2p_trade.side === 'sell' },
          ]"
        >
          {{ tradeStore.p2p_trade.side }}
        </div>
      </div>
    </div>
    <div class="flex mb-5">
      <div class="flex flex-1">
        <div class="text-gray mr-3">
          {{ $t('page.global.placeholder.price') }}
        </div>
        <div class="bold-text">
          {{ `${roundNumber(tradeStore.p2p_trade.price, 0)} ${tradeStore.p2p_trade.fiat_currency_id.toUpperCase()}` }}
        </div>
      </div>
      <div class="flex flex-1">
        <div class="text-gray mr-3">
          {{ $t('page.global.placeholder.amount') }}
        </div>
        <div class="bold-text">
          {{ `${roundNumber(tradeStore.p2p_trade.amount, 0)} ${tradeStore.p2p_trade.coin_currency_id.toUpperCase()}` }}
        </div>
      </div>
    </div>
    <div class="flex mb-5">
      <div class="text-gray mr-3">
        {{ $t('page.global.placeholder.total') }}
      </div>
      <div
        class="bold-text"
        :class="[
          { buy: tradeStore.p2p_trade.side === 'buy' },
          { sell: tradeStore.p2p_trade.side === 'sell' },
        ]"
      >
        {{ `${roundNumber(tradeStore.p2p_trade.total, 0)} ${tradeStore.p2p_trade.fiat_currency_id.toUpperCase()}` }}
      </div>
    </div>
    <div class="flex">
      <div class="flex-1 text-center text-gray cursor-pointer h-[40px] leading-[40px]" @click="visible = false">
        {{ $t('page.global.action.cancel') }}
      </div>
      <ZButton class="flex-1" :loading="loading" @click="AcceptP2PRequest">
        {{ $t('page.global.action.accept') }}
      </ZButton>
    </div>
  </ZModal>
</template>

<style lang="less">
.layout-p2p-accept-modal {
  // font-size: 16px;

  .buy {
    color: @up-color;
  }

  .sell {
    color: @down-color;
  }

  .z-button {
    border: 1px solid @primary-color !important;
    background-color: @primary-color;
    color: white !important;
  }
}
</style>
