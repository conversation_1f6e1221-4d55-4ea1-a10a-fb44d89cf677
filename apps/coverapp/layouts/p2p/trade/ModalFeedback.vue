<script setup lang="ts">
import type { ZTableColumn } from '@zsmartex/components/types'
import { P2PFeedbackType } from '@zsmartex/types'
import type { P2PTrade } from '@zsmartex/types'

defineProps<{
  loading: boolean
}>()

const emit = defineEmits<{
  (event: 'click', type: P2PFeedbackType, tags: string[], content: string): void
}>()

const visible = ref(false)
const p2pTrade = ref<P2PTrade>({} as P2PTrade)

const type = ref(P2PFeedbackType.Positive)
const tags = ref<string[]>([])
const content = ref('')

function openModal(data: P2PTrade) {
  visible.value = true
  p2pTrade.value = data
}

const tagsExample = computed(() => {
  if (type.value === P2PFeedbackType.Positive) return ['Fast transaction', 'Polite and friendly', 'Patient', 'Safe and trustworthy', 'Good price']
  return ['Slow transaction', 'Impatient', 'Suspicious/Scam']
})

const disabledButton = computed(() => {
  if (tags.value.length <= 0) return true
  return false
})

const typeColumn: ZTableColumn[] = [
  {
    key: 'value',
    scopedSlots: true,
  },
]

const CreateP2PFeedback = async () => {
  emit('click', type.value, tags.value, content.value)
  visible.value = false
}

const handleTag = (tag: string) => {
  const index = tags.value.indexOf(tag)
  if (index !== -1) {
    tags.value.splice(index, 1)
  } else {
    tags.value.push(tag)
  }
}

defineExpose({
  openModal,
})
</script>

<template>
  <ZModal
    v-model="visible"
    class="layout-p2p-trade-feedback-modal"
    :title="$t('layout.p2p.trade.create_feedback.title')"
  >
    <div class="mb-4">
      <div class="mb-2">
        {{ $t('layout.p2p.trade.create_feedback.type') }}
      </div>
      <div class="flex">
        <ZSelect
          v-model="type"
          :data-source="[
            {
              value: P2PFeedbackType.Positive,
            },
            {
              value: P2PFeedbackType.Negative,
            },
          ]"
          :scroll="true"
          :show-clear="false"
          :columns="typeColumn"
          :find-by="['value']"
          value-key="value"
          label-key="value"
          placeholder="Type"
          :replace-func="(text: string) => text[0].toUpperCase() + text.slice(1)"
          class="w-full"
          @change="tags = []"
        />
      </div>
    </div>
    <div class="mb-4">
      <div class="mb-2">
        {{ $t('layout.p2p.trade.create_feedback.tags') }}
      </div>
      <div class="flex flex-wrap">
        <div
          v-for="tag in tagsExample"
          :key="tag"
          class="layout-p2p-trade-feedback-modal-tag"
          :class="{ 'layout-p2p-trade-feedback-modal-tag-active': tags.includes(tag) }"
          @click="handleTag(tag)"
        >
          {{ tag }}
        </div>
      </div>
    </div>
    <ZFormRow class="mb-6" label="Content">
      <textarea v-model="content" class="layout-p2p-trade-feedback-modal-area" />
    </ZFormRow>
    <div class="flex">
      <div class="flex-1 text-center text-gray cursor-pointer h-[40px] leading-[40px]" @click="visible = false">
        {{ $t('page.global.action.cancel') }}
      </div>
      <ZButton class="flex-1 h-[40px]! leading-[40px]" :loading="loading" :disabled="disabledButton" @click="CreateP2PFeedback">
        {{ $t('page.global.action.create') }}
      </ZButton>
    </div>
  </ZModal>
</template>

<style lang="less">
.layout-p2p-trade-feedback-modal {
  .z-button {
    border: 1px solid @primary-color;
    background-color: @primary-color;
    color: white !important;
    border-radius: 4px;
  }

  &-tag {
    margin-right: 12px;
    margin-bottom: 8px;
    padding: 2px 8px;
    background-color: rgba(@base-border-color, 0.1);
    color: @gray-color;
    border-radius: 4px;
    cursor: pointer;
    user-select: none;

    &-active {
      background-color: rgba(@primary-color, 0.2);
      color: @primary-color;
    }
  }

  &-area {
    width: 100%;
    min-height: 100px;
    padding: 12px;
    font-size: 14px;
    font-family: 'URWDIN-Regular';
    outline: none;
    border-radius: 4px;
    border: 1px solid @base-border-color;
    transition: 0.3s all;

    &:hover {
      border: 1px solid @primary-color;
    }

    &:focus {
      border: 1px solid @primary-color;
      box-shadow: 0 0 0 2px rgba(@primary-color, 0.2);
    }
  }
}
</style>
