<script setup lang="ts">
import type { ZTableColumn } from '@zsmartex/components/types'
import getSymbolFromCurrency from 'currency-symbol-map'

const emit = defineEmits<{
  (event: 'click'): void
}>()
const visible = ref(false)

function openModal() {
  visible.value = true
}

const publicStore = usePublicStore()
const userStore = useUserStore()
const side = ref('buy')
const fiatCurrency = ref('')
const coinCurrency = ref('')
const price = ref('')
const amount = ref('')
const min = ref('')
const max = ref('')
const paymentID = ref('')
const loading = ref(false)

const coinColumn: ZTableColumn[] = [
  {
    key: 'id',
    scopedSlots: true,
  },
]

const fiatColumn: ZTableColumn[] = [
  {
    key: 'id',
    scopedSlots: true,
  },
]

const symbol = computed(() => {
  if (side.value === 'buy' && fiatCurrency.value) {
    return getSymbolFromCurrency(fiatCurrency.value.toUpperCase())
  }

  if (side.value === 'sell' && coinCurrency.value) {
    return coinCurrency.value.toUpperCase()
  }

  return ''
})

const CreateAdvertisement = async () => {
  loading.value = true

  const params: Record<string, any> = {
    coin_currency: coinCurrency.value,
    fiat_currency: fiatCurrency.value,
    origin_amount: amount.value,
    side: side.value,
    min: min.value,
    max: max.value,
    price: price.value,
    payments: [paymentID.value],
  }

  await userStore.CreateAdvertisement(params)
  loading.value = false
  visible.value = false
  emit('click')
}

defineExpose({
  openModal,
})
</script>

<template>
  <ZModal
    v-model="visible"
    class="layout-p2p-advertisement-modal"
    :title="$t('layout.p2p.advertisement_modal.title')"
  >
    <div class="layout-p2p-advertisement-modal-side">
      <div class="layout-p2p-advertisement-modal-side-item" :class="{ active: side === 'buy' }" @click="side = 'buy'">
        {{ $t('page.global.action.buy') }}
      </div>
      <div class="layout-p2p-advertisement-modal-side-item" :class="{ active: side === 'sell' }" @click="side = 'sell'">
        {{ $t('page.global.action.sell') }}
      </div>
    </div>
    <div class="layout-p2p-advertisement-modal-currency">
      <div class="flex-1 mr-4">
        <div class="mb-2">
          {{ side === 'sell' ? `With ${$t('layout.p2p.advertisement_modal.coin')}` : $t('layout.p2p.advertisement_modal.coin') }}
        </div>
        <div class="flex">
          <ZSelect
            v-model="coinCurrency"
            :data-source="publicStore.currencies"
            :search="true"
            :scroll="true"
            :show-clear="false"
            :columns="coinColumn"
            :find-by="['id']"
            value-key="id"
            label-key="id"
            placeholder="Coin"
            :replace-func="(text: string) => text.toUpperCase()"
            class="w-full"
          />
        </div>
      </div>
      <div class="flex-1 ml-4">
        <div class="mb-2">
          {{ side === 'buy' ? `With ${$t('layout.p2p.advertisement_modal.fiat')}` : $t('layout.p2p.advertisement_modal.fiat') }}
        </div>
        <div class="flex">
          <ZSelect
            v-model="fiatCurrency"
            :data-source="publicStore.fiatCurrencies"
            :search="true"
            :scroll="true"
            :show-clear="false"
            :columns="fiatColumn"
            :find-by="['id']"
            value-key="id"
            label-key="id"
            placeholder="Fiat"
            :replace-func="(text: string) => text.toUpperCase()"
            class="w-full"
          />
        </div>
      </div>
    </div>
    <div class="flex mb-[16px]">
      <div class="flex-1 mr-4">
        <div class="mb-2">
          {{ $t('page.global.placeholder.price') }}
        </div>
        <div class="flex w-full">
          <ZInput v-model="price" class="w-full" :placeholder="$t('page.global.placeholder.price')" :type="InputType.Decimal" />
        </div>
      </div>
      <div class="flex-1 ml-4">
        <div class="mb-2">
          {{ $t('page.global.placeholder.amount') }}
        </div>
        <div class="flex w-full">
          <ZInput v-model="amount" class="w-full" :placeholder="$t('page.global.placeholder.amount')" :type="InputType.Decimal" />
        </div>
      </div>
    </div>
    <div class="flex mb-[16px]">
      <div class="flex-1 mr-4">
        <div class="mb-2">
          {{ `Min ${symbol ? `(${symbol})` : ''}` }}
        </div>
        <div class="flex w-full">
          <ZInput v-model="min" class="w-full" :placeholder="$t('page.global.placeholder.min')" :type="InputType.Decimal" />
        </div>
      </div>
      <div class="flex-1 ml-4">
        <div class="mb-2">
          {{ `Max ${symbol ? `(${symbol})` : ''}` }}
        </div>
        <div class="flex w-full">
          <ZInput v-model="max" class="w-full" :placeholder="$t('page.global.placeholder.max')" :type="InputType.Decimal" />
        </div>
      </div>
    </div>
    <div class="mb-[16px]">
      <div class="mb-2">
        {{ $t('layout.p2p.advertisement_modal.payment') }}
      </div>
      <div>
        <ZSelect
          v-model="paymentID"
          :data-source="userStore.payments"
          :search="true"
          :scroll="true"
          :show-clear="false"
          :columns="[
            {
              key: 'number_account',
            },
          ]"
          :find-by="['number_account']"
          value-key="id"
          label-key="number_account"
          placeholder="Number account"
        />
      </div>
    </div>
    <div class="flex">
      <div class="flex-1 text-center text-gray cursor-pointer h-[40px] leading-[40px]" @click="visible = false">
        {{ $t('page.global.action.cancel') }}
      </div>
      <ZButton class="flex-1 h-[40px]! leading-[40px]" :loading="loading" @click="CreateAdvertisement">
        {{ $t('page.global.action.create') }}
      </ZButton>
    </div>
  </ZModal>
</template>

<style lang="less">
.layout-p2p-advertisement-modal {
  .z-button {
    border: 1px solid @primary-color !important;
    background-color: @primary-color;
    color: white !important;
  }

  &-side {
    display: flex;
    margin-bottom: 16px;
    padding: 4px;
    border: 1px solid @base-border-color;
    border-radius: 4px;

    &-item {
      padding: 6px 0;
      flex: 1;
      text-align: center;
      border-radius: 4px;
      color: @gray-color;
      font-size: 15px;
      cursor: pointer;

      &.active {
        background-color: @base-border-color;
        color: @text-color;
      }
    }
  }

  .z-button {
    border-radius: 4px;
  }

  &-currency {
    display: flex;
    margin-bottom: 16px;
  }
}
</style>
