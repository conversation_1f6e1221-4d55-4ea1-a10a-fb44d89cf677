<script setup lang="ts">
defineProps<{
  loading: boolean
}>()

const emit = defineEmits<{
  (event: 'click'): void
}>()

const runtimeConfig = useRuntimeConfig()
const visible = ref(false)

function openModal() {
  visible.value = true
}

const tradeStore = useTradeStore()

defineExpose({
  openModal,
})
</script>

<template>
  <ZModal
    v-model="visible"
    class="layout-p2p-payment-confirm-modal"
    :title="$t('page.p2p.payment_confirm.title')"
  >
    <div class="mb-4">
      {{ $t('page.p2p.payment_confirm.desc') }}
    </div>
    <div class="px-6 layout-p2p-payment-confirm-modal-info">
      <div class="mb-4">
        <div class="text-gray mb-2">
          {{ $t('page.p2p.name_account') }}
        </div>
        <div>
          {{ tradeStore.p2p_trade.name_account }}
        </div>
      </div>
      <div class="mb-4">
        <div class="text-gray mb-2">
          {{ $t('page.p2p.number_account') }}
        </div>
        <div>
          <!-- {{ tradeStore.p2p_trade.number_account }} -->
        </div>
      </div>
      <div class="mb-4">
        <div class="text-gray mb-2">
          {{ $t('page.p2p.bank_name') }}
        </div>
        <div>
          <!-- {{ tradeStore.p2p_trade.bank_name }} -->
        </div>
      </div>
      <div class="mb-4">
        <div class="text-gray mb-2">
          {{ $t('page.p2p.bank_address') }}
        </div>
        <div>
          <!-- {{ tradeStore.p2p_trade.bank_address }} -->
        </div>
      </div>
    </div>
    <div class="mt-4">
      {{ $t('page.p2p.payment_confirm.warning', { exchange_name: runtimeConfig.public.exchangeName }) }}
    </div>
    <div class="flex mt-4">
      <div class="flex-1 text-center text-gray cursor-pointer h-[40px] leading-[40px]" @click="visible = false">
        {{ $t('page.global.action.cancel') }}
      </div>
      <ZButton class="flex-1" :loading="loading" @click="emit('click')">
        {{ $t('page.global.action.accept') }}
      </ZButton>
    </div>
  </ZModal>
</template>

<style lang="less">
.layout-p2p-payment-confirm-modal {
  &-info {
    border-bottom: 1px solid @base-border-color;
  }

  .z-button {
    border: 1px solid @primary-color !important;
    background-color: @primary-color;
    color: white !important;
  }
}
</style>
