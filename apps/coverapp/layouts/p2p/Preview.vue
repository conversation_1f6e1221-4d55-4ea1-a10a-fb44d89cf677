<script setup lang="ts">
</script>

<template>
  <div class="page-p2p-preview">
    <ZContainer class="!h-full flex justify-between items-center">
      <div class="page-p2p-preview-texts">
        <div class="page-p2p-preview-texts-title bold-text">
          {{ $t('page.p2p.advertisers.local_trading') }}
        </div>
        <div class="page-p2p-preview-texts-description">
          {{ $t('page.p2p.advertisers.free_trade') }}
        </div>
      </div>
      <div class="page-p2p-preview-img">
        <img src="../../assets/img/background-new.png">
      </div>
    </ZContainer>
    <!-- <Name /> -->
    <!-- <Broadcast /> -->
  </div>
</template>

<style lang="less">
.page-p2p-preview {
  position: relative;
  height: 380px;
  width: 100%;
  background-color: rgba(55, 114, 255, 0.1);;
  // background-image: url("@/assets/img/background.png");
  // background-position: 50%;
  // background-repeat: no-repeat;
  // background-size: cover;

  @media @mobile {
    padding-top: 50px;
    height: calc(100vh - 50px);
  }

  @media @tablet {
    padding-top: 50px;
    height: 1000px;
  }

  .z-container {
    @media @mobile, @tablet {
      display: block !important;
    }
  }

  &-texts {
    max-width: 640px;

    @media @mobile {
      padding: 0 16px;
      padding-top: 100px;
    }

    @media @tablet {
      padding: 0 24px;
      padding-top: 100px;
    }

    &-title {
      margin-bottom: 24px;
      font-size: 64px;

      @media @mobile {
        font-size: 40px;
      }
    }

    &-description {
      font-size: 24px;
      color: @gray-color;

      @media @mobile {
        max-width: 240px;
      }

      & > span {
        color: @text-color;
      }
    }

    .z-button {
      width: 180px;
      height: 48px;
      background-color: @primary-color;
      color: white;
      border-radius: 90px;
      font-size: 16px;
      font-weight: bold;
    }
  }

  &-img {
    @media @mobile {
      margin-top: 80px;
      padding: 0 16px;
      width: 100%;
    }

    @media @tablet {
      display: flex;
      justify-content: center;
    }

    img {
      @media @mobile {
        width: 100%;
      }
    }
  }

  // @media @mobile {
  //   height: 300px;
  // }

  // @media @tablet {
  //   height: 300px;
  // }
}
</style>
