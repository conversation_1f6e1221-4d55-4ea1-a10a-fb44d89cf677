<script setup lang="ts">
import FeatureMarketItem from './feature-market/Item.vue'

const publicStore = usePublicStore()
</script>

<template>
  <div class="page-markets-feature-markets">
    <ZContainer class=" grid gap-4 grid-cols-4">
      <FeatureMarketItem
        v-for="(market_id, index) in publicStore.market_feature_markets"
        :key="index"
        :market-id="market_id"
      />
    </ZContainer>
  </div>
</template>

<style lang="less" scoped>
.page-markets-feature-markets {
  @media @mobile {
    overflow-x: scroll;
  }
}
</style>
