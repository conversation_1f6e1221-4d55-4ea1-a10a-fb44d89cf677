<script setup lang="ts">
import { roundNumber } from '@zsmartex/utils'
import FeatureMarketSparkLine from './SparkLine.vue'

const props = defineProps<{
  marketId: string
}>()

const publicStore = usePublicStore()
const tradeStore = useTradeStore()

const market = computed(() => {
  return publicStore.markets.find(market => market.id === props.marketId)!
})

const ticker = computed(() => {
  return publicStore.enabledTickers.find(ticker => ticker.id === props.marketId)!
})

function onClick() {
  navigateTo(`/exchange/${market.value.base_unit.toUpperCase()}-${market.value.quote_unit.toUpperCase()}?type=${tradeStore.exchange_layout}`)
}
</script>

<template>
  <ZCol class="page-markets-feature-markets-item" @click="onClick">
    <ZCard v-if="market">
      <ZRow class="items-center">
        <ZCol class="page-markets-feature-markets-item-market-name bold-text">
          {{ market.name }}
        </ZCol>
        <div class="page-markets-feature-markets-item-market-change bold-text" :class="[parseFloat(ticker.price_change_percent) >= 0 ? 'text-up' : 'text-down']">
          {{ ticker.price_change_percent }}
        </div>
      </ZRow>
      <ZRow class="items-center">
        <ZCol>
          <div class="page-markets-feature-markets-item-price" :class="[parseFloat(ticker.price_change_percent) >= 0 ? 'text-up' : 'text-down']">
            {{ roundNumber(ticker.last, market.price_precision) }}
          </div>
          <div class="page-markets-feature-markets-item-24h-volume">
            {{ $t('page.markets.feature_market.24h_vol') }} {{ roundNumber(ticker.volume, 2) }} {{ market.quote_unit.toUpperCase() }}
          </div>
        </ZCol>
        <FeatureMarketSparkLine :market-id="market.id" />
      </ZRow>
    </ZCard>
  </ZCol>
</template>

<style lang="less" scoped>
.page-markets-feature-markets-item {
  cursor: pointer;

  .z-row + .z-row {
    margin-top: 12px;
  }

  &-market-name {
    font-size: 16px;
  }

  &-price {
    font-size: 20px;
  }

  &-24h-volume {
    font-size: 12px;
    color: @gray-color;
  }

  &-sparkline {
    width: 100px;

    canvas {
      cursor: pointer !important;
    }
  }
}
</style>
