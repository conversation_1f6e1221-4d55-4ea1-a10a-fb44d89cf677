<script setup lang="ts">
import type { ZTabItem, ZTableColumn } from '@zsmartex/components/types'
import type { Ticker } from '@zsmartex/types'
import { Align, Format, ParseType, SortBy } from '@zsmartex/types'

const publicStore = usePublicStore()
const tradeStore = useTradeStore()
const tabSelected = ref(publicStore.quote_list[0])
const { query } = useQuery()

const extraQuoteList = computed(() => {
  const extraQuoteList: string[] = []

  for (const market of publicStore.enabledMarkets) {
    if (!publicStore.quote_list.includes(market.quote_unit) && !extraQuoteList.includes(market.quote_unit)) {
      extraQuoteList.push(market.quote_unit)
    }
  }

  return extraQuoteList
})

const quoteCurrency = computed(() => {
  return publicStore.currencies.find(currency => currency.id === tabSelected.value)
})

const columns = computed(() => {
  const result: ZTableColumn[] = [
    {
      title: $t('page.global.table.market'),
      sort: true,
      sortBy: SortBy.String,
      key: 'market.name',
      class: 'items-center',
      scopedSlots: true,
    },
    {
      title: $t('page.global.table.last_price'),
      key: 'last',
      sort: true,
      sortBy: SortBy.Number,
      parse: ParseType.Decimal,
      precision: (ticker: Ticker) => ticker.market.price_precision,
    },
    {
      title: $t('page.global.table.24h_change'),
      key: 'price_change_percent',
      formatBy: Format.Change,
      sort: true,
      sortBy: SortBy.Number,
      sideKey: 'price_change_percent',
    },
    {
      title: $t('page.global.table.24h_high'),
      key: 'high',
      sort: true,
      sortBy: SortBy.Number,
      parse: ParseType.Decimal,
      precision: (ticker: Ticker) => ticker.market.price_precision,
    },
    {
      title: $t('page.global.table.24h_low'),
      key: 'low',
      sort: true,
      sortBy: SortBy.Number,
      parse: ParseType.Decimal,
      precision: (ticker: Ticker) => ticker.market.price_precision,
    },
    {
      title: $t('page.global.table.24h_amount'),
      key: 'amount',
      sort: true,
      sortBy: SortBy.Number,
      parse: ParseType.Decimal,
      precision: 2,
    },
    {
      title: $t('page.global.table.24h_volume'),
      key: 'volume',
      sort: true,
      sortBy: SortBy.Number,
      parse: ParseType.Decimal,
      precision: (item: Ticker) => ['btc', 'eth'].includes(item.market.quote_unit) ? 6 : 2,
    },
    {
      title: $t('page.global.table.action'),
      key: 'action',
      scopedSlots: true,
      align: Align.Right,
    },
    {
      title: $t('page.global.table.last_price_24h_change'),
      key: 'price_change',
      scopedSlots: true,
      align: Align.Right,
    },
  ]

  return result
})

const tickers = computed(() => {
  return publicStore.enabledTickers.filter((ticker) => {
    if (tabSelected.value === 'favorites') {
      return tradeStore.favorites.includes(ticker.id)
    }

    if (tabSelected.value === 'all') {
      return true
    }

    return tabSelected.value === ticker.market.quote_unit
  })
})

const tabs = computed(() => {
  const result: ZTabItem[] = [
    {
      key: 'favorites',
      slotName: true,
    },
  ]

  publicStore.quote_list.forEach((quote) => {
    result.push({
      key: quote,
      slotName: true,
    })
  })

  extraQuoteList.value.forEach((quote) => {
    result.push({
      key: quote,
      slotName: true,
    })
  })

  return result
})
</script>

<template>
  <ZContainer class="page-markets-market-list mt-4">
    <ZTablePro
      :columns="columns"
      :hover="true"
      :data-source="tickers"
      :is-router-link="true"
      :search-enabled="true"
      :router-builder="`/exchange/#{market.base_unit.toUpper}-#{market.quote_unit.toUpper}?type=${tradeStore.exchange_layout}`"
      :find-by="['market.name', 'market.base_unit', 'market.base_currency.name']"
      :query="query"
      head-responsive
    >
      <template #head>
        <ZTab v-model="tabSelected" :tabs="tabs" :button="true">
          <template #favorites>
            <div class="flex items-center">
              <ZIconStarFilled /> <span>{{ $t("page.global.table.favorites") }}</span>
            </div>
          </template>
          <template v-for="quote in publicStore.quote_list" #[quote]>
            {{ quote.toUpperCase() }}
          </template>
          <template v-for="quote in extraQuoteList" #[quote]>
            {{ quote.toUpperCase() }}
          </template>
        </ZTab>
      </template>
      <template #[`market.name`]="{ item }">
        <ZIconStarFilled class="mr-1" :class="[{ active: tradeStore.favorites.includes(item.market.id) }]" @click.prevent="tradeStore.ChangeFavorite(item.id)" /> {{ item.market.name }}
      </template>
      <template #action>
        <ZButton>
          {{ $t('page.global.action.trade') }}
        </ZButton>
      </template>
      <template #price_change="{ item }">
        <div class="text-right">
          <div
            class="leading-normal text-[14px]"
            :class="[
              { 'text-up': parseFloat(item.price_change_percent) > 0 },
              { 'text-down': parseFloat(item.price_change_percent) < 0 },
            ]"
          >
            {{ item.price_change_percent }}
          </div>
          <div class="leading-normal text-[12px]">
            {{ `${item.last} ${item.market.quote_unit.toUpperCase()}` }}
          </div>
        </div>
      </template>
    </ZTablePro>
  </ZContainer>
</template>

<style lang="less">
.page-markets-market-list {
  .name, .z-table-pro-head-slot{
    svg {
      width: 18px;
      height: 18px;

      .cls-1 {
        fill: @gray-color;
      }

      &.active {
        .cls-1 {
          fill: @primary-color;
        }
      }
    }
  }

  .z-tab {
    width: 900px;
  }

  @media @mobile, @tablet {
    width: 100%;
  }

  .z-table {
    &-pro {
      &-head {
        @media @mobile {
          padding: 16px;
        }
      }
    }

    &-head {
      @media @mobile {
        padding: 0 16px;
      }
    }

    &-row {
      @media @mobile {
        padding: 0 16px;
        height: 48px !important;
      }
    }
  }

  .z-tab-item {
    i {
      color: @gray-color;
    }

    &-active i {
      color: inherit;
    }
  }

  .z-table-row i {
    color: @gray-color;

    &.active {
      color: @primary-color;
    }
  }

  .z-table {
    .last {
      @media @mobile {
        display: none !important;
      }
    }

    .price_change_percent {
      @media @mobile {
        display: none !important;
      }
    }

    .high {
      @media @mobile {
        display: none !important;
      }
    }

    .low {
      @media @mobile {
        display: none !important;
      }
    }

    .amount {
      @media @mobile {
        display: none !important;
      }
    }

    .volume {
      @media @mobile {
        display: none !important;
      }
    }

    .action {
      @media @mobile {
        display: none !important;
      }
    }

    .price_change {
      display: none !important;

      @media @mobile {
        display: flex !important;
        align-items: center;
        justify-content: flex-end;
      }
    }
  }
}
</style>
