<script setup lang="ts">
import { Line } from 'vue-chartjs'
import colors from '@zsmartex/colors'
import ColorConvert from 'color-convert'

const props = defineProps<{
  marketId: string
}>()

const publicStore = usePublicStore()

const sparkline = computed(() => {
  return publicStore.sparklines[props.marketId]
})

const ticker = computed(() => {
  return publicStore.enabledTickers!.find(ticker => ticker.id === props.marketId)!
})

const isUp = computed(() => {
  return Number.parseFloat(ticker.value.price_change_percent) >= 0
})

const chartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  animation: {
    duration: 0,
  },
  scales: {
    x: {
      display: false,
      grid: {
        display: false,
      },
      border: {
        display: false,
      },
      ticks: {
        display: false,
      },
    },
    y: {
      beginAtZero: false,
      min: Math.min(...sparkline.value),
      max: Math.max(...sparkline.value),
      grid: {
        display: false,
      },
      border: {
        display: false,
      },
      ticks: {
        display: false,
      },
    },
  },
  plugins: {
    legend: {
      display: false,
    },
    tooltip: {
      enabled: false,
    },
  },
  layout: {
    padding: 0,
  },
}

const chartData = computed(() => {
  const borderColor = isUp.value ? colors['up-color'] : colors['down-color']
  const rgbBg = ColorConvert.hex.rgb(borderColor)

  return {
    labels: Array.from({ length: sparkline.value.length }, (_, i) => i + 1),
    datasets: [
      {
        data: sparkline.value,
        pointRadius: 0,
        pointHoverRadius: 0,
        borderWidth: 1.5,
        borderColor,
        backgroundColor: (ctx: any) => {
          const canvas = ctx.chart.ctx
          const gradient = canvas.createLinearGradient(0, 0, 0, ctx.chart.height)

          gradient.addColorStop(0, `rgba(${rgbBg[0]}, ${rgbBg[1]}, ${rgbBg[2]}, 0.2)`)
          gradient.addColorStop(1, 'rgba(255, 255, 255, 0.3)')

          return gradient
        },
        fill: true,
      },
    ],
  }
})
</script>

<template>
  <div class="page-home-market-list-sparkline">
    <Line v-if="publicStore.sparklines[props.marketId].length > 0" height="40px" width="125px" :options="chartOptions" :data="chartData" />
  </div>
</template>
