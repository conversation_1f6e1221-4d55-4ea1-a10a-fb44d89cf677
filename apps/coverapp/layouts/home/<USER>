<script setup lang="ts">
import { screenDevice } from '@zsmartex/utils'

const props = defineProps<{
  mul: number
}>()

const publicStore = usePublicStore()
const config = useRuntimeConfig()

const bannerSize = computed(() => {
  return {
    width: (1288 * props.mul - 24) / 2,
    height: 170,
  }
})

const device = screenDevice()

const slidesPerView = computed(() => {
  if (!(device.value === ScreenDevice.Desktop || device.value === ScreenDevice.LargeDesktop)) return 'auto'
  else return 2
})
</script>

<template>
  <div class="page-home-banner">
    <ZSwiper
      :options="{
        slidesPerView,
        spaceBetween: 24,
        loop: true,
        setWrapperSize: true,
      }"
    >
      <ZSwiperSlide v-for="item in publicStore.banners" :key="item.uuid">
        <a :href="item.url" :style="`width: ${bannerSize.width}px; height: ${bannerSize.height}px`">
          <img :src="`/api/v2/kouda/public/banners/${item.uuid}`">
        </a>
      </ZSwiperSlide>
    </ZSwiper>
  </div>
</template>

<style lang="less">
.page-home-banner {
  position: relative;
  overflow: hidden;

  @media @mobile {
    padding-left: 16px;
    margin-left: 0;
    margin-right: 0;
  }

  @media @tablet {
    padding-left: 24px;
    margin-left: 0;
    margin-right: 0;
  }

  .z-swiper {
    position: relative;
    padding: 10px 0;
  }

  .z-swiper-slide {
    margin-right: 24px;

    @media @mobile {
      margin-right: 0;
    }

    @media @tablet {
      margin-right: 0;
    }
  }

  a {
    display: inline-block;
    // width: 100%;
    // height: 170px;

    @media @mobile {
      height: 130px;
    }

    @media @tablet {
      height: 160px;
    }

    img {
      @media @mobile {
        height: 100%;
      }

      @media @tablet {
        height: 100%;
      }
    }
  }

  img {
    width: 100%;
    height: 100%;
    border-radius: 6px;
  }
}
</style>
