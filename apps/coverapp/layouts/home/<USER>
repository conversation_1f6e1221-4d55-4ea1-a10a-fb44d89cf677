<script setup lang="ts">
import config from '@zsmartex/config'
import type { ZTableColumn } from '@zsmartex/components/types'
import type { Ticker } from '@zsmartex/types'
import SparkLine from './market-list/SparkLine.vue'

const publicStore = usePublicStore()
const columns: ZTableColumn[] = [
  {
    key: 'currency_icon',
    scopedSlots: true,
  },
  {
    title: $t('page.global.table.name'),
    key: 'market.base_unit',
    toUpper: true,
  },
  {
    title: $t('page.global.table.last_price'),
    key: 'last',
    sortBy: SortBy.Number,
    parse: ParseType.Decimal,
    precision: (ticker: Ticker) => ticker.market.price_precision,
    scopedSlots: true,
  },
  {
    title: $t('page.global.table.24h_change'),
    key: 'price_change_percent',
    formatBy: Format.Change,
    sideKey: 'price_change_percent',
    align: Align.Left,
  },
  {
    title: $t('page.global.table.markets'),
    key: 'sparkline',
    scopedSlots: true,
    align: Align.Right,
  },
]

const tickers = computed(() => {
  return publicStore.enabledTickers.filter((ticker) => {
    return publicStore.home_feature_markets.includes(ticker.id)
  })
})

const total = ref(tickers.value.length)
</script>

<template>
  <ZContainer class="page-home-market-list">
    <div class="page-home-market-list-title bold-text">
      {{ $t('layout.home.coin_list.title') }}
    </div>

    <ZTable
      :columns="columns"
      :hover="true"
      :data-source="tickers"
      :is-router-link="true"
      router-builder="/exchange/#{market.base_unit.toUpper}-#{market.quote_unit.toUpper}?type=pro"
    >
      <template #last="{ item }">
        {{ (Number(item.last) * publicStore.priceCurrency).toFixed(item.market.price_precision) }}
      </template>
      <template #currency_icon="{ item }">
        <span class="currency_icon">
          <img :src="item.market.base_currency.icon_url">
        </span>
      </template>
      <template #sparkline="{ item }">
        <span class="sparkline">
          <ClientOnly>
            <SparkLine :market-id="item.id" />
          </ClientOnly>
        </span>
      </template>
    </ZTable>
    <div class="mt-[40px] page-home-market-list-more flex justify-center items-center">
      <NuxtLink to="/markets" class="flex justify-center items-center cursor-pointer bold-text">
        {{ $t('page.home.market_list.more') }} <ZIconArrowLeftDuotone />
      </NuxtLink>
    </div>
  </ZContainer>
</template>

<style lang="less">
.page-home-market-list {
  margin: 12px auto;
  margin-top: 100px;
  padding-bottom: 80px;

  &-more {
    color: @primary-color;
    font-size: 600;

    svg {
      margin-left: 8px;
      width: 12px;
      height: 12px;
      transform: rotate(180deg);

      .cls-1, .cls-2 {
        fill: @primary-color;
      }
    }
  }

  &-title {
    margin-bottom: 40px;
    font-size: 40px;

    @media @mobile {
      padding-left: 16px;
    }

    @media @tablet {
      padding: 0 24px;
    }
  }

  .z-table {
    &-head {
      @media @tablet {
        padding: 0 24px;
      }
    }

    &-row {
      height: 70px !important;
      line-height: 70px !important;
      border-top: none !important;

      @media @tablet {
        padding: 0 24px;
      }

      &:hover {
        border-top: 1px solid @base-border-color !important;
        border-bottom: 1px solid @base-border-color !important;
      }
    }
  }

  .currency_icon {
    display: flex;
    align-items: center;
    height: 100%;
    flex: 0 0 40px;

    img {
      border-radius: 50%;
      width: 32px;
      height: 32px;
    }
  }

  .price_change_percent {
    @media @mobile {
      justify-content: flex-end;
      text-align: right;
    }
  }

  .sparkline {
    width: 125px;

    @media @mobile {
      display: none;
    }
  }

  &-sparkline {
    display: block;
    width: 100%;
    height: 30px;

    > * {
      display: flex;
    }

    canvas {
      cursor: pointer !important;
    }
  }
}
</style>
