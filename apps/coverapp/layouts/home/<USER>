<script setup lang="ts">
import { format as formatDate } from 'date-fns'

const publicStore = usePublicStore()
</script>

<template>
  <div class="page-home-annoucement">
    <div class="page-home-annoucement-title bold-text">
      Annoucements
    </div>

    <div class="page-home-annoucement-rows">
      <NuxtLink v-for="article in publicStore.announcements" :to="article.html_url"
        target="announcements" class="page-home-annoucement-rows-item flex">
        <div class="page-home-annoucement-rows-item-cicrle" />
        <div>
          <div class="page-home-annoucement-rows-item-title mb-2">
            {{ article.title }}
          </div>
          <div class="page-home-annoucement-rows-item-time text-[12px] text-gray-400">
            {{ formatDate(new Date(article.created_at), "yyyy-MM-dd HH:mm:ss") }}
          </div>
        </div>
      </NuxtLink>
    </div>
  </div>
</template>

<style lang="less">
.page-home-annoucement {
  margin: 12px auto;
  padding: 80px 0;

  &-rows {
    height: 180px;
    overflow-y: scroll;
    user-select: none;

    &-item {
      color: @text-color;

      &:hover {
        color: @text-color;
      }

      &+.page-home-annoucement-rows-item {
        margin-top: 24px;
      }

      &-title {
        font-size: 16px;
      }

      &-time {
        font-size: 14px;
      }

      &-cicrle {
        margin-right: 16px;
        margin-top: 7px;
        width: 6px;
        height: 6px;
        background-color: rgba(@gray-color, 0.5);
        border-radius: 50%;
      }
    }
  }

  &-title {
    margin-bottom: 40px;
    font-size: 40px;

    @media @mobile {
      padding-left: 16px;
    }

    @media @tablet {
      padding: 0 24px;
    }
  }
}
</style>
