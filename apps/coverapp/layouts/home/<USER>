<script setup lang="ts">
// import Name from './preview/Name.vue'
import { NuxtLink } from '#components';
import { ExchangeLayout } from '@zsmartex/types'

const userStore = useUserStore()
const tradeStore = useTradeStore()
const buttonText = computed(() => {
  if (userStore.isAuthenticated) return 'Trade now'
  else return 'Register now'
})

function exchangeUrl() {
  return `/exchange/${tradeStore.market.name.replace('/', '-')}?type=${ExchangeLayout.Basic}`
}
</script>

<template>
  <div class="page-home-preview">
    <ZContainer class='flex justify-center items-center'>
      <div class="page-home-preview-texts flex flex-col justify-center items-center text-center">
        <h1 class="page-home-preview-texts-title bold-text">
          {{ $t('page.home.preview.trade_with_us') }}
        </h1>
        <h2 class="page-home-preview-texts-description">
          {{ $t('page.home.preview.desc') }}
        </h2>
        <ZButton class="mt-[48px]">
          <NuxtLink :to="`${buttonText === 'Trade now' ? exchangeUrl() : '/register'}`" >
            {{ buttonText }}
          </NuxtLink>
        </ZButton>
      </div>
    </ZContainer>
  </div>
</template>

<style lang="less">
.page-home-preview {
  position: relative;
  width: 100%;
  padding: 90px 0;
  background-color: rgba(55, 114, 255, 0.1);;
  // background-image: url("@/assets/img/background.png");
  // background-position: 50%;
  // background-repeat: no-repeat;
  // background-size: cover;

  @media @mobile {
    padding-top: 50px;
    height: 540px;
  }

  @media @tablet {
    padding-top: 50px;
    height: 540px;
  }

  .z-container {
    @media @mobile, @tablet {
      display: block !important;
    }
  }

  &-texts {
    @media @large-desktop, @desktop {
      max-width: 640px;
    }

    @media @mobile {
      padding: 0 16px;
      padding-top: 100px;
    }

    @media @tablet {
      padding: 0 24px;
      padding-top: 100px;
    }

    &-title {
      margin-bottom: 24px;
      font-size: 64px;

      @media @mobile {
        font-size: 40px;
      }
    }

    &-description {
      font-size: 24px;
      color: @gray-color;

      @media @mobile {
        max-width: 240px;
      }

      & > span {
        color: @text-color;
      }
    }

    .z-button {
      width: 180px;
      height: 48px;
      background-color: @primary-color;
      color: white;
      border-radius: 90px;
      font-size: 16px;
      font-weight: bold;
    }
  }

  &-img {
    @media @mobile {
      margin-top: 80px;
      padding: 0 16px;
      width: 100%;
    }

    @media @tablet {
      display: flex;
      justify-content: center;
    }

    img {
      @media @mobile {
        width: 100%;
      }
    }
  }

  // @media @mobile {
  //   height: 300px;
  // }

  // @media @tablet {
  //   height: 300px;
  // }
}
</style>
