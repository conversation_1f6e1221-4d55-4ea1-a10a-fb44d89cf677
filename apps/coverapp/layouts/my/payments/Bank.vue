<script setup lang="ts">
import type { ZTableColumn } from '@zsmartex/components/types'
import type { Payment } from '@zsmartex/types'
import { Align } from '@zsmartex/types'

const props = defineProps<{
  tab: string
}>()

const emits = defineEmits<{
  (event: 'update', payment: Payment): void
}>()

const userStore = useUserStore()
const tempDelete = useState<Record<string, boolean>>(() => ({}))

const payments = computed(() => {
  return userStore.payments.filter(p => p.type === props.tab)
})

const columns = computed(() => {
  const columns: ZTableColumn[] = [
    {
      key: 'name_account',
      title: 'Name',
    },
  ]

  if (props.tab === 'bank') {
    columns.push(
      {
        key: 'data.number_account',
        title: 'Number Account',
      },
      {
        key: 'data.bank_name',
        title: 'Bank name',
      },
      {
        key: 'data.bank_address',
        title: 'Bank Address',
      },
      {
        key: 'action',
        align: Align.Right,
        scopedSlots: true,
      },
    )
  }

  if (props.tab === 'momo') {
    columns.push(
      {
        key: 'data.phone',
        title: 'Account Phone',
      },
      {
        key: 'action',
        align: Align.Right,
        scopedSlots: true,
      },
    )
  }

  return columns
})

const DeleteAddressBook = async (payment: Payment) => {
  await userStore.DeletePayment(payment.id)
}
</script>

<template>
  <div class="page-my-payments-bank">
    <ZTable
      :columns="columns"
      :data-source="payments"
    >
      <template #action="{ item }">
        <div class="page-my-payments-bank-action" @click="emits('update', item)">
          {{ $t('page.global.action.update') }}
        </div>
        <ZPopconfirm v-model="tempDelete[item.id]" :placement="Placement.TopRight" trigger="click" @click="DeleteAddressBook(item)">
          <div class="page-my-payments-bank-action ml-3">
            {{ $t('page.global.action.delete') }}
          </div>
        </ZPopconfirm>
      </template>
    </ZTable>
  </div>
</template>

<style lang="less">
.page-my-payments-bank {
  padding-top: 16px;

  &-action {
    color: @primary-color;
    cursor: pointer;
  }

  .name_account {
    @media @mobile {
      display: none;
    }
  }

  .bank_address {
    @media @mobile {
      display: none;
    }
  }
}
</style>
