<script setup lang="ts">
import type { Payment } from '@zsmartex/types'
const emit = defineEmits<{
  (event: 'update:modelValue', value: boolean): void
}>()
const visible = ref(false)
const action = ref('')
const form = ref<Record<string, any>>({})

const userStore = useUserStore()
const nameAccount = ref('')
const type = ref('bank')
const loading = ref(false)

const disabledButton = computed(() => {
  if (nameAccount.value.length == 0) return true
  if (type.value === 'bank' && Object.keys(form.value).length < 3) return true
  if (type.value === 'momo' && Object.keys(form.value).length < 1) return true
  return false
})

function openModal(typeModal: string, data?: Payment) {
  visible.value = true
  action.value = typeModal

  if (data) {
    nameAccount.value = data.name_account
    type.value = data.type
  
    if (type.value === 'bank') {
      form.value.number_account = data.data.number_account
      form.value.bank_name = data.data.bank_name
      form.value.bank_address = data.data.bank_address
    } else if (type.value === 'momo') {
      form.value.phone = data.data.phone
    }
  }
}

const ActionPayment = async () => {
  loading.value = true

  if (action.value === 'create') {
    await userStore.CreatePayment({
      name_account: nameAccount.value,
      type: type.value,
      data: form.value,
    })
  } else {
    await userStore.UpdatePayment(
      form.value.id,
      {
        data: form.value,
      },
    )
  }

  loading.value = false
  visible.value = false
}

defineExpose({
  openModal,
})
</script>

<template>
  <ZModal
    v-model="visible"
    class="layout-payments-payment-modal"
    :title="action === 'create' ? 'Create Payment' : 'Update Payment'"
  >
    <ZForm autocomplete="off" @submit="ActionPayment">
      <div class="mb-[16px]">
        <div class="mb-2">
          {{ `${'Type'}` }}
        </div>
        <ZSelect
          v-model="type"
          :data-source="[
            {
              value: 'bank',
              text: 'Bank Account',
            },
            {
              value: 'momo',
              text: 'MoMo',
            },
          ]"
          :search="true"
          :scroll="true"
          :show-clear="false"
          :columns="[
            {
              key: 'text',
            },
          ]"
          :find-by="['value', 'text']"
          value-key="value"
          label-key="text"
        />
      </div>
      <ZFormRow label="Name Account" class="mb-[16px]" required>
        <ZInput
          v-model="nameAccount"
          :type="InputType.Text"
          :placeholder="$t('page.p2p.name_account')"
          class="px-2 h-32"
          :required="true"
        />
      </ZFormRow>
      <div v-if="type === 'bank'">
        <ZFormRow label="Number Account" required>
          <ZInput
            v-model="form.number_account"
            :type="InputType.Number"
            :placeholder="$t('page.p2p.number_account')"
            class="px-2 h-32"
            :required="true"
          />
        </ZFormRow>
        <ZFormRow label="Bank Name" required>
          <ZInput
            v-model="form.bank_name"
            :type="InputType.Text"
            :placeholder="$t('page.p2p.bank_name')"
            class="px-2 h-32"
            :required="true"
          />
        </ZFormRow>
        <ZFormRow label="Bank Address">
          <ZInput
            v-model="form.bank_address"
            :type="InputType.Text"
            :placeholder="$t('page.p2p.bank_address')"
            class="px-2 h-32"
            :required="true"
          />
        </ZFormRow>
      </div>
      <div v-else-if="type === 'momo'">
        <ZFormRow label="Phone Account" required>
          <ZInput
            v-model="form.phone"
            :type="InputType.Number"
            placeholder="Phone Account"
            class="px-2 h-32"
            :required="true"
          />
        </ZFormRow>
      </div>
      <ZButton
        class="!w-full mt-4 mb-2"
        type="primary"
        html-type="submit"
        :loading="loading"
        :disabled="disabledButton"
      >
        {{ $t('page.global.action.save') }}
      </ZButton>
    </ZForm>
  </ZModal>
</template>

<style lang="less">
.layout-payments-payment-modal {
  .z-button {
    height: 36px;
  }

  .z-modal-layout {
    @media @mobile {
      width: 300px;
    }
  }
}
</style>
