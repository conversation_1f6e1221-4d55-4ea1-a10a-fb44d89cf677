<script setup lang="ts">
import type { Currency, Placement } from '@zsmartex/types'
import type { ZTableColumn } from '@zsmartex/components/types'

const props = withDefaults(defineProps<{
  modelValue?: boolean
  currencyId: string
  dataSource: Currency[]
  placement?: Placement
}>(), {
  modelValue: false,
})

const emit = defineEmits<{
  (event: 'update:modelValue', value: boolean): void
  (event: 'click', currency: Currency): void
}>()
const visible = useVModel(props, 'modelValue', emit)

const publicStore = usePublicStore()

const searchBoxColumns: ZTableColumn[] = [
  {
    key: 'name',
    scopedSlots: true,
  },
]

const currencies = computed(() => {
  return props.dataSource.filter(c => c.id !== props.currencyId)
})

const onSearchBoxClick = (currency: Currency) => {
  emit('click', currency)
}
</script>

<template>
  <ZSearchBox v-model="visible" :data-source="currencies" :columns="searchBoxColumns" :find-by="['id', 'name']" @click="onSearchBoxClick">
    <ZRow class="search-box-trigger rounded bg-gray-100 mt-4 py-3 px-4 items-center text-base">
      <ZCol>
        <ZRow class="items-center">
          <img :src="publicStore.getCurrencyByID(currencyId)?.icon_url" class="icon"> {{ currencyId.toUpperCase() }}
        </ZRow>
      </ZCol>
      <ZIconAngleDownFilled class="coin-search-icon" />
    </ZRow>
    <template #name="{ item }">
      <img :src="publicStore.getCurrencyByID(item.id)?.icon_url" class="icon">
      <span class="text-base mr-1">{{ item.id.toUpperCase() }}</span>
      <span class="text-gray-500">{{ item.name }}</span>
    </template>
  </ZSearchBox>
</template>

<style lang="less">
.z-search-box {
  .coin-search-icon {
    width: 20px;
    height: 20px;
    fill: @gray-color;
  }

  .z-table-row {
    height: 36px;
  }

  img {
    width: 24px;
    height: 24px;
  }
}
</style>
