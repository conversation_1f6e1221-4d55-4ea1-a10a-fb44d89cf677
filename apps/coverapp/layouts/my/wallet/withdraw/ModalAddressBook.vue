<script setup lang="ts">
import type { ZTableColumn } from '@zsmartex/components/types'
import { screenDevice } from '@zsmartex/utils'
import type { Beneficiary, Currency } from '@zsmartex/types'

const emit = defineEmits<{
  (event: 'click', id: number, label: string, address: string, blockchain_key: string, parent_name: string): void
}>()

const visible = ref(false)
const currency = ref<Currency>({} as Currency)
const userStore = useUserStore()
const publicStore = usePublicStore()

function openModal(data: Currency) {
  visible.value = true
  currency.value = data
}

const beneficiaries = computed(() => {
  return userStore.beneficiaries.filter(b => b.currency_id === currency.value.id && publicStore.getWithdrawalEnabledNetworks(currency.value.id).find(n => n.blockchain_key === b.blockchain_key))
})

const columns: ZTableColumn[] = [
  {
    key: 'adress_book',
    scopedSlots: true,
  },
]

function GetParrentCurrencyName(currencyID: string, blockchainKey: string) {
  const currency = publicStore.currencies.find(c => c.id === currencyID)!
  const network = currency.networks.find(c => c.blockchain_key === blockchainKey)!
  const parentID = network.parent_id ? network.parent_id : network.currency_id
  return `${publicStore.currencies.find(c => c.id === parentID)!.name} (${network.protocol})`
}

function onClick(item: Beneficiary): void {
  emit('click', item)
  visible.value = false
}

const device = screenDevice()

defineExpose({
  openModal,
})
</script>

<template>
  <ZModal
    v-model="visible"
    class="page-my-wallet-withdraw-address-book-modal"
    :title="$t('layout.my.wallet.withdraw.address_book.title')"
    :class-modal="`${device !== ScreenDevice.Mobile ? '!max-w-[720px]' : '!max-w-[360px]'}`"
  >
    <ZTable
      class="mt-4"
      :columns="columns"
      :data-source="beneficiaries"
      :head-enabled="false"
      :hover="true"
      responsive
      @click="(item: Beneficiary) => onClick(item)"
    >
      <template #adress_book="{ item }">
        <div class="w-full">
          <div class="flex justify-between">
            <div class="bold-text">
              {{ item.label }}
            </div>
            <div v-if="item.address.includes('?memo=')">
              <span class="text-gray-400 mr-2">{{ $t('layout.my.wallet.withdraw.address_book.memo') }}</span> {{ item.address.split('?memo=')[1] }}
            </div>
          </div>
          <div class="flex justify-between text-sm">
            <div>
              <span class="text-gray-400 mr-2">{{ $t('layout.my.wallet.withdraw.address_book.address') }}</span> {{ item.address.split('?memo=')[0].length > 50 ? `${item.address.split('?memo=')[0].slice(0, 22)}...${item.address.split('?memo=')[0].slice(-25)}` : item.address.split('?memo=')[0] }}
            </div>
            <div>
              <span class="text-gray-400 mr-2">{{ $t('layout.my.wallet.withdraw.address_book.network') }}</span> {{ GetParrentCurrencyName(item.currency_id, item.blockchain_key) }}
            </div>
          </div>
        </div>
      </template>
    </ZTable>
    <div class="flex justify-center my-6">
      <NuxtLink to="/my/address-book">
        {{ $t('layout.my.wallet.withdraw.address_book.add_withdraw_address') }}
      </NuxtLink>
    </div>
  </ZModal>
</template>

<style lang="less">
.page-my-wallet-withdraw-address-book-modal {
  &-select {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 8px;
    height: 40px;
    border: 1px solid @base-border-color;
    border-radius: 4px;
    cursor: pointer;
  }

  .z-overlay {
    width: 500px;
  }

  .z-input {
    height: 40px;
  }

  .z-dropdown-overlay {
    width: 250px;
  }

  .z-button {
    width: 280px;
    height: 40px;
    font-size: 18px;

    &:disabled {
      background-color: #e9eff3 !important;
      border: 1px solid #e9eff3 !important;
    }
  }

  .z-table {
    &-row {
      height: 64px;

      & + & {
        border-top: 1px solid @base-border-color;
      }
    }
  }

  .z-search-box {
    .z-dropdown {
      &-overlay {
        width: 100%;
      }

      &-trigger {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 8px;
        height: 38px;
        border: 1px solid @base-border-color;
        border-radius: 4px;
      }
    }
  }
}
</style>
