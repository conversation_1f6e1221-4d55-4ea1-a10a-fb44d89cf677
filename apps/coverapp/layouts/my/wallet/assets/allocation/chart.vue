<script setup lang="ts">
const assetsStore = useAssetsStore()
const publicStore = usePublicStore()

const assets = computed(() => {
  let total = 0

  assetsStore.assets.forEach((asset) => {
    const currency = publicStore.currencies.find(c => c.id === asset.currency)
    if (currency) total += (Number(asset.balance) + Number(asset.locked)) * Number(currency.price)
  })

  const result: {
    item: string
    value: number
    percent: number
  }[] = []
  const btc = publicStore.currencies.find(c => c.id === 'btc')
  if (btc) {
    assetsStore.assets.forEach((asset) => {
      const currency = publicStore.currencies.find(c => c.id === asset.currency)
      if (currency) {
        const value = ((Number(asset.balance) + Number(asset.locked)) * Number(currency.price))
        if (value > 0) {
          result.push({
            item: asset.currency.toUpperCase(),
            value,
            percent: value / total * 100,
          })
        }
      }
    })
  }

  result.sort((a, b) => b.value - a.value)
  if (result.length > 4) {
    const ot = result.reduce((a, b, i) => {
      if (i >= 4) return a
      return a + b.value
    }, 0)
    result.splice(4, 1, {
      item: $t('page.my.wallet.assets.allocation.other'),
      value: total - ot,
      percent: (total - ot) / total * 100,
    })
  }
  return result.slice(0, 5)
})
</script>

<template>
  <div class="page-my-wallet-overview-assets-allocation-chart">
    <div class="page-my-wallet-overview-assets-allocation-chart-title">
      <div class="bold-text">
        {{ $t('page.my.wallet.assets.allocation.title') }}
      </div>
    </div>
    <div ref="chart" :class="{ hidden: !assets.length }" />
    <div v-if="!assets.length" class="flex flex-col justify-center items-center h-[200px]">
      <ZIconClipboardTimesDuotone />
      <span class="text-xl">{{ $t('page.my.wallet.assets.allocation.no_data') }}</span>
    </div>
  </div>
</template>

<style lang="less">
.page-my-wallet-overview-assets-allocation-chart {
  svg {
    width: 80px;
    height: 80px;

    .cls-1 {
      fill: @base-border-color;
    }

    .cls-2 {
      fill: @gray-color;
    }
  }

  &-title {
    display: flex;
    align-items: center;
    padding-bottom: 12px;
    font-size: 18px;
  }
}
</style>
