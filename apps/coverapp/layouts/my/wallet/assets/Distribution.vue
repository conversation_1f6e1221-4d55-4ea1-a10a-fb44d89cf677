<script setup lang="ts">
import type { PNLDistribution } from '@zsmartex/types'
import getSymbolFromCurrency from 'currency-symbol-map'

const props = defineProps<{
  modelValue: boolean
  pnlYesterday: number
  changeYesterday: number
}>()
const emit = defineEmits<{
  (event: 'update:modelValue', value: boolean): void
}>()
const hidden = useVModel(props, 'modelValue', emit)
const assetsStore = useAssetsStore()
const publicStore = usePublicStore()

const { data: pnlDistribution } = await useAsyncData<PNLDistribution>(() => assetsStore.FetchPNLDistribution({}).then(res => res.data))

const surplus = computed(() => {
  let total = 0
  assetsStore.assets.forEach((asset) => {
    const currency = publicStore.currencies.find(c => c.id === asset.currency)
    if (currency) total += (Number(asset.balance) + Number(asset.locked)) * Number(currency.price) * publicStore.global_price[publicStore.convert_currency]
  })

  const btc = publicStore.currencies.find(c => c.id === 'btc')
  return {
    btc: (total / Number(btc?.price)) || 0,
    usdt: total,
  }
})
</script>

<template>
  <ZCard class="page-my-wallet-assets-distribution mt-2">
    <div class="page-my-wallet-assets-distribution-container">
      <div class="flex-1">
        <div class="pb-1 flex items-center">
          {{ $t('page.my.wallet.assets.distribution.estimated_balance') }}
          <ZIconEyeClosedFilled v-if="hidden" class="cursor-pointer opacity-60" @click="hidden = !hidden" />
          <ZIconEyeFilled v-else class="cursor-pointer opacity-60" @click="hidden = !hidden" />
        </div>
        <div class="inline-block bold-text text-2xl flex items-end leading-none">
          {{ hidden ? '****' : surplus.btc.toFixed(8) }}
          <span class="text-xs pl-2">{{ $t('page.my.wallet.assets.distribution.estimated_balance_btc', { value: hidden ? '****' : surplus.usdt.toFixed(2), symbol: getSymbolFromCurrency(publicStore.convert_currency) }) }}</span>
        </div>
      </div>
      <div class="flex-1 page-my-wallet-assets-distribution-child">
        <div class="page-my-wallet-assets-distribution-child-1">
          <div class="pb-1">
            {{ $t('page.my.wallet.assets.distribution.yesterday_pnl') }}
          </div>
          <div class="inline-block bold-text text-2xl flex items-end leading-none">
            {{ `${pnlYesterday >= 0 ? '+' : '-'}${hidden ? '****' : Math.abs(pnlYesterday)}${getSymbolFromCurrency(publicStore.convert_currency)}` }}
            <span
              class="text-xs pl-2"
              :class="[
                { green: changeYesterday && changeYesterday >= 0 },
                { red: changeYesterday && changeYesterday < 0 },
              ]"
            >
              {{ `${changeYesterday >= 0 ? '+' : '-'}${Math.abs(Number(Number(changeYesterday).toFixed(2)))}%` }}
            </span>
          </div>
        </div>
        <div class="page-my-wallet-assets-distribution-child-2">
          <div class="pb-1">
            {{ $t('page.my.wallet.assets.distribution.30_yesterday_pnl') }}
          </div>
          <div class="inline-block bold-text text-2xl flex items-end leading-none">
            {{ `${pnlDistribution!.pnl_month && pnlDistribution!.pnl_month < 0 ? '-' : '+'}${hidden ? '****' : pnlDistribution!.pnl_month ? Math.abs(pnlDistribution!.pnl_month).toFixed(2) : "0.00"}${getSymbolFromCurrency(publicStore.convert_currency)}` }}
            <span
              class="text-xs pl-2"
              :class="[
                { green: pnlDistribution!.trend_month && pnlDistribution!.trend_month >= 0 },
                { red: pnlDistribution!.trend_month && pnlDistribution!.trend_month < 0 },
              ]"
            >
              {{ `${pnlDistribution!.trend_month && pnlDistribution!.trend_month < 0 ? '-' : '+'}${pnlDistribution!.trend_month ? Math.abs(Number(Number(pnlDistribution!.trend_month).toFixed(2))) : "0.00"}%` }}
            </span>
          </div>
        </div>
      </div>
    </div>
  </ZCard>
</template>

<style lang="less">
.page-my-wallet-assets-distribution {
  &-container {
    display: flex;

    svg {
      width: 20px;
      height: 20px;
      fill: @gray-color;
    }

    @media @mobile {
      display: block;
    }
  }

  &-child {
    display: flex;

    @media @mobile {
      display: block;
    }

    &-1 {
      @media @mobile {
        margin-top: 12px;
      }
    }

    &-2 {
      margin-left: 128px;

      @media @mobile {
        margin-top: 12px;
        margin-left: 0;
      }
    }
  }

  span.green{
    color: @up-color;
  }

  span.red{
    color: @down-color;
  }
}
</style>
