<script setup lang="ts">
import type { ZTableColumn } from '@zsmartex/components/types'
import type { Currency } from '@zsmartex/types'
import { Align, SortBy } from '@zsmartex/types'
import { roundNumber } from '@zsmartex/utils'
import ModalConvert from '~/layouts/my/wallet/ModalConvert.vue'

defineProps<{
  hidden?: boolean
}>()

const drawer = ref(false)
const tradeDrawer = ref(false)
const runtimeConfig = useRuntimeConfig()
const assetsStore = useAssetsStore()
const publicStore = usePublicStore()
const tradeStore = useTradeStore()
const hiddenEmpty = useState(() => false)
const currencyID = ref('')
const { query } = useQuery()

const modalConvert = ref<InstanceType<typeof ModalConvert>>()

const columns = computed(() => {
  const result: ZTableColumn[] = [
    {
      title: $t('page.global.table.currency'),
      key: 'currency',
      scopedSlots: true,
      sort: true,
      sortBy: SortBy.String,
    },
    {
      title: $t('page.global.table.total'),
      key: 'total',
      align: Align.Right,
      sort: true,
      sortBy: SortBy.Number,
      scopedSlots: true,
    },
    {
      title: $t('page.global.table.available'),
      key: 'available',
      align: Align.Right,
      sort: true,
      sortBy: SortBy.Number,
      scopedSlots: true,
    },
    {
      title: $t('page.global.table.locked'),
      key: 'locked',
      align: Align.Right,
      sort: true,
      sortBy: SortBy.Number,
      scopedSlots: true,
    },
    {
      title: $t('page.global.table.btc_value'),
      key: 'btc_value',
      align: Align.Right,
      scopedSlots: true,
      sort: true,
      sortBy: SortBy.Number,
    },
    {
      title: $t('page.global.table.action'),
      key: 'action',
      align: Align.Right,
      scopedSlots: true,
    },
    {
      title: $t('page.global.table.action'),
      key: 'drawer',
      align: Align.Right,
      scopedSlots: true,
    },
  ]

  return result
})

const assets = computed(() => {
  const currency = publicStore.enabledCurrencies.find(c => c.id === 'btc')
  const btcPrice = currency && Number(currency.price) > 0 ? Number(currency.price) : 1
  const result = publicStore.enabledCurrencies.map((currency) => {
    const a = assetsStore.assets.find(a => a.currency === currency.id)

    if (!a) {
      return {
        currency: currency.id,
        name: currency.name,
        total: 0,
        available: 0,
        locked: 0,
        btc_value: 0,
      }
    }

    const totalAsset = Number(a.balance) + Number(a.locked)

    return {
      currency: a.currency,
      name: currency?.name,
      available: Number(a.balance),
      locked: Number(a.locked),
      total: totalAsset,
      btc_value: (Number(currency?.price) * totalAsset / btcPrice) || 0,
    }
  })

  if (hiddenEmpty.value) {
    return result.filter(r => r.total > 0)
  }

  return result
})

const balanceBTC = computed(() => {
  const result = assets.value.reduce((a, b) => {
    return a + b.btc_value
  }, 0)

  return `${roundNumber(result, 8)} BTC`
})

const balanceCurrency = computed(() => {
  const currency = publicStore.enabledCurrencies.find(c => c.id === 'btc')
  const btcPrice = currency && Number(currency.price) > 0 ? Number(currency.price) : 1

  const btc = assets.value.reduce((a, b) => {
    return a + b.btc_value
  }, 0)

  return `${roundNumber(btc * btcPrice * publicStore.global_price[publicStore.convert_currency], 2)} ${publicStore.convert_currency.toUpperCase()}`
})

function getCurrency(currencyID: string) {
  return publicStore.enabledCurrencies.find(c => c.id === currencyID && c.status === CurrencyStatus.Enabled) as Currency
}

function messageDeposit(currencyID: string) {
  const currency = getCurrency(currencyID)

  if (!currency) return $t('page.my.wallet.assets.balances.deposit_disabled')

  const network = currency.networks.find(network => network.options && network.options.message)
  if (!network) return $t('page.my.wallet.assets.balances.deposit_disabled')

  return `${$t('page.my.wallet.assets.balances.deposit_disabled')}: ${network.options.message}`
}

function messageWithdraw(currencyID: string) {
  const currency = getCurrency(currencyID)

  if (!currency) return $t('page.my.wallet.assets.balances.withdraw_disabled')

  const network = currency.networks.find(network => network.options && network.options.message)
  if (!network) return $t('page.my.wallet.assets.balances.withdraw_disabled')

  return `${$t('page.my.wallet.assets.balances.withdraw_disabled')}: ${network.options.message}`
}

function handleShowConvert(currency?: string) {
  if (currency) {
    currencyID.value = currency
  } else {
    currencyID.value = ''
  }
  drawer.value = false
  modalConvert.value?.openModal('spot', currencyID.value)
}

function handleShowDrawer(currency: string) {
  drawer.value = true
  currencyID.value = currency
}

function handleCloseDrawer() {
  if (tradeDrawer.value) {
    tradeDrawer.value = false
  } else {
    drawer.value = false
  }
}
</script>

<template>
  <div>
    <ZCard>
      <div class="flex justify-between">
        <div>
          <div class="flex-1">
            <div class="pb-1 flex items-center">
              {{ $t('page.my.wallet.assets.distribution.estimated_balance') }}
            </div>
            <div class="inline-block bold-text text-2xl flex items-end leading-none">
              {{ balanceBTC }}
              <span class="text-xs pl-2"> ~ {{ balanceCurrency }}</span>
            </div>
          </div>
        </div>
        <div class="flex items-start">
          <ZButton v-if="runtimeConfig.public.p2p" class="mr-3" @click="handleShowConvert()">
            Transfer
          </ZButton>
          <ZButton @click="navigateTo('/my/wallet/assets/history/deposits')">
            History
          </ZButton>
        </div>
      </div>
    </ZCard>
    <ZTablePro
      class="mt-5 mb-5"
      :title="$t('page.my.wallet.assets.balances.title_table')"
      :columns="columns"
      :data-source="assets"
      :search-enabled="true"
      :find-by="['currency', 'name']"
      :query="query"
      hover
    >
      <template #head>
        <div class="flex flex-1 justify-end mr-4">
          <div class="flex items-center  select-none">
            <div class="cursor-pointer flex items-center">
              <ZCheckbox v-model="hiddenEmpty" />
              <span @click="hiddenEmpty = !hiddenEmpty">{{ $t('page.global.hide') }}</span>
            </div>
          </div>
        </div>
      </template>
      <template #currency="{ item }">
        <img :src="publicStore.getCurrencyByID(item.currency)?.icon_url">
        <div>
          <div class="currency-code">
            {{ item.currency.toUpperCase() }}
          </div>
          <div class="currency-name">
            {{ item.name }}
          </div>
        </div>
      </template>
      <template #total="{ item }">
        {{ hidden ? '****' : item.total === 0 ? '--' : Number(item.total).toFixed(8) }}
      </template>
      <template #available="{ item }">
        {{ hidden ? '****' : item.available === 0 ? '--' : Number(item.available).toFixed(8) }}
      </template>
      <template #locked="{ item }">
        {{ hidden ? '****' : item.locked === 0 ? '--' : Number(item.locked).toFixed(8) }}
      </template>
      <template #btc_value="{ item }">
        {{ hidden ? '****' : item.btc_value === 0 ? '--' : Number(item.btc_value).toFixed(8) }}
      </template>
      <template #action="{ item }">
        <ZTooltip class="mr-4" :title="messageDeposit(item.currency)" :overlay-style="publicStore.getDepositEnabledNetworks(item.currency).length ? 'display: none' : 'display: initial'">
          <ZButton
            :is-router-link="true"
            :to="`/my/wallet/assets/deposit/${item.currency}`"
            :disabled="!publicStore.getDepositEnabledNetworks(item.currency).length"
          >
            {{ $t('page.global.action.deposit') }}
          </ZButton>
        </ZTooltip>
        <ZTooltip class="mr-4" :title="messageWithdraw(item.currency)" :overlay-style="publicStore.getWithdrawalEnabledNetworks(item.currency).length ? 'display: none' : 'display: initial'">
          <ZButton
            :is-router-link="true"
            :to="`/my/wallet/assets/withdraw/${item.currency}`"
            :disabled="!publicStore.getWithdrawalEnabledNetworks(item.currency).length"
          >
            {{ $t('page.global.action.withdraw') }}
          </ZButton>
        </ZTooltip>
        <ZButton
          v-if="runtimeConfig.public.p2p"
          class="mr-4"
          @click="handleShowConvert(item.currency)"
        >
          Transfer
        </ZButton>
        <ZDropdown trigger="click">
          <ZButton>{{ $t('page.global.action.trade') }}</ZButton>
          <template #overlay>
            <ZMenu>
              <ZMenuItem
                v-for="(market, index) in publicStore.enabledMarkets.filter(m => m.base_unit === item.currency)"
                :key="index"
                class="text-center"
                :is-router-link="true"
                :to="`/exchange/${market.name.replace('/', '-')}?type=${tradeStore.exchange_layout}`"
              >
                {{ market.name }}
              </ZMenuItem>
            </ZMenu>
          </template>
        </ZDropdown>
      </template>
      <template #drawer="{ item }">
        <div class="flex items-center bold-text text-[20px]" @click="handleShowDrawer(item.currency)">
          ...
        </div>
      </template>
    </ZTablePro>
    <ZDrawer v-model="drawer" :title="currencyID.toUpperCase()" height="340" :footer="false" :position="Position.Bottom" @close="handleCloseDrawer">
      <div v-if="!tradeDrawer" class="page-my-wallet-assets-drawer">
        <div
          v-show="publicStore.getDepositEnabledNetworks(currencyID).length"
          class="page-my-wallet-assets-drawer-item bold-text"
          @click="navigateTo(`/my/wallet/assets/deposit/${currencyID}`)"
        >
          <ZIcon type="person" class="mr-2" />
          {{ $t('page.my.wallet.assets.deposit') }}
        </div>
        <div
          v-show="publicStore.getWithdrawalEnabledNetworks(currencyID).length"
          class="page-my-wallet-assets-drawer-item bold-text"
          @click="navigateTo(`/my/wallet/assets/withdraw/${currencyID}`)"
        >
          <ZIcon type="person" class="mr-2" />
          {{ $t('page.my.wallet.assets.withdraw') }}
        </div>
        <div
          v-if="runtimeConfig.public.p2p"
          class="page-my-wallet-assets-drawer-item bold-text"
          @click="handleShowConvert(currencyID)"
        >
          <ZIcon type="person" class="mr-2" />
          {{ $t('page.my.wallet.assets.transfer') }}
        </div>
        <div class="page-my-wallet-assets-drawer-item bold-text" @click="tradeDrawer = true">
          <ZIcon type="person" class="mr-2" />
          {{ $t('page.my.wallet.assets.trade') }}
        </div>
      </div>
      <div v-else class="page-my-wallet-assets-drawer">
        <div
          v-for="(market, index) in publicStore.enabledMarkets.filter(m => m.base_unit === currencyID)"
          :key="index"
          class="page-my-wallet-assets-drawer-item bold-text"
          @click="navigateTo(`/exchange/${market.name.replace('/', '-')}?type=${tradeStore.exchange_layout}`)"
        >
          {{ market.name }}
        </div>
      </div>
    </ZDrawer>
    <ModalConvert ref="modalConvert" />
  </div>
</template>

<style lang="less">
.page-my-wallet-assets {
  .z-table-pro {
    &-head {
      @media @mobile {
        padding: 16px;
      }

      &-slot {
        flex: 1;
        display: flex;
        justify-items: flex-end;
      }
    }
  }

  .z-table {
    &-head {
      @media @mobile {
        padding: 0 16px;
      }
    }

    &-row {
      @media @mobile {
        padding: 0 16px;
      }
    }
  }

  &-drawer {
    margin-top: 8px;
    padding: 0 16px;

    &-item {
      padding: 16px 0;
    }
  }

  .drawer {
    display: none !important;

    @media @mobile {
      display: flex !important;
      justify-content: flex-end;
    }
  }

  .available {
    @media @mobile {
      display: none !important;
    }
  }

  .locked {
    @media @mobile {
      display: none !important;
    }
  }

  .btc_value {
    @media @mobile {
      display: none !important;
    }
  }

  .action {
    @media @mobile {
      display: none !important;
    }
  }
}
</style>
