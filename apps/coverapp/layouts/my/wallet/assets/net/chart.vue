<script setup lang="ts">
import { roundNumber } from '@zsmartex/utils'
import type { AssetStatistic } from '@zsmartex/types'

const props = defineProps<{
  data: AssetStatistic[]
  loading: boolean
  hiddenNumber?: boolean
}>()
</script>

<template>
  <div class="page-my-wallet-overview-assets-net-chart">
    <div class="page-my-wallet-overview-assets-net-chart-title">
      <div class="bold-text">
        {{ $t('page.my.wallet.assets.net.title') }}
      </div>
      <span v-if="data.length" class="ml-3">${{ hiddenNumber ? '****' : roundNumber(data[data.length - 1].total, 2) }}</span>
    </div>
    <ZLoading v-if="loading" />
    <div ref="chart" :class="{ hidden: !data.length }" />
    <div v-if="!data.length" class="flex flex-col justify-center items-center h-[200px]">
      <ZIconClipboardTimesDuotone />
      <span class="text-xl">{{ $t('page.my.wallet.assets.allocation.no_data') }}</span>
    </div>
  </div>
</template>

<style lang="less">
.page-my-wallet-overview-assets-net-chart {
  svg {
    width: 80px;
    height: 80px;

    .cls-1 {
      fill: @base-border-color;
    }

    .cls-2 {
      fill: @gray-color;
    }
  }

  &-title {
    display: flex;
    align-items: center;
    padding-bottom: 12px;
    font-size: 18px;

    span {
      font-size: 16px;
    }
  }
}
</style>
