<script setup lang="ts">
import type { PNLDaily } from '@zsmartex/types'

const props = defineProps<{
  data: PNLDaily[]
  loading: boolean
}>()
</script>

<template>
  <div class="page-my-wallet-overview-assets-daily-chart">
    <div class="page-my-wallet-overview-assets-daily-chart-title mb-2">
      <div class="bold-text">
        {{ $t('page.my.wallet.assets.daily.title') }}
      </div>
      <span
        v-if="data.length"
        class="ml-3"
        :class="[
          { green: data[data.length - 1].value >= 0 },
          { red: data[data.length - 1].value < 0 },
        ]"
      >
        {{ `${data[data.length - 1].value >= 0 ? '+' : '-'}$${Math.abs(Number(data[data.length - 1].value.toFixed(2)))}` }}
      </span>
    </div>
    <ZLoading v-if="loading" />
    <div ref="chart" class="mt-2" :class="{ hidden: !data.length }" />
    <div v-if="!data.length" class="flex flex-col justify-center items-center h-[200px]">
      <ZIconClipboardTimesDuotone />
      <span class="text-xl">{{ $t('page.my.wallet.assets.allocation.no_data') }}</span>
    </div>
  </div>
</template>

<style lang="less">
.page-my-wallet-overview-assets-daily-chart {
  svg {
    width: 80px;
    height: 80px;

    .cls-1 {
      fill: @base-border-color;
    }

    .cls-2 {
      fill: @gray-color;
    }
  }

  &-title {
    display: flex;
    align-items: center;
    top: 0;
    font-size: 18px;

    span {
      font-size: 16px;

      &.green {
        color: @up-color;
      }

      &.red {
        color: @down-color;
      }
    }
  }
}
</style>
