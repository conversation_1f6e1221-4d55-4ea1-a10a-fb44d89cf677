<script setup lang="ts">
const visible = ref(false)

const runtimeConfig = useRuntimeConfig()
const publicStore = usePublicStore()
const userStore = useUserStore()
const assetsStore = useAssetsStore()
const loading = useState(() => false)
const fromType = useState(() => 'spot')
const toType = useState(() => 'p2p')
const amount = ref('')
const coinCurrency = ref('')

const types = [
  {
    text: 'Spot',
    value: 'spot',
  },
  {
    text: 'P2P',
    value: 'p2p',
  },
]

const available = computed(() => {
  if (!coinCurrency.value) return '0'

  let assets = assetsStore.assets
  if (fromType.value === 'p2p') {
    assets = assetsStore.p2p_assets
  }

  const asset = assets.find(a => a.currency === coinCurrency.value)
  if (asset) {
    return asset.balance
  }

  return '0'
})

const disabledButton = computed(() => {
  if (Number(available.value) === 0) return true
  if (!amount.value) return true
  if (!coinCurrency.value) return true
  return false
})

function HandleConvertPositionCoin() {
  const tmp = fromType.value
  fromType.value = toType.value
  toType.value = tmp
}

async function ConvertCoin() {
  loading.value = true
  await userStore.ConvertCoin({
    from_type: fromType.value,
    to_type: toType.value,
    coin_currency: coinCurrency.value,
    amount: amount.value,
  })
  loading.value = false
  visible.value = false
}

function testFrom(type: string) {
  const currentFrom = fromType.value
  const currentTo = toType.value

  if (type === currentTo) {
    fromType.value = toType.value
    toType.value = currentFrom
  } else {
    fromType.value = type
  }
}

function testTo(type: string) {
  const currentFrom = fromType.value
  const currentTo = toType.value

  if (type === currentFrom) {
    toType.value = fromType.value
    fromType.value = currentTo
  } else {
    toType.value = type
  }
}

function openModal(fromValue?: string, currencyID?: string) {
  visible.value = true

  if (fromValue) {
    fromType.value = fromValue
    toType.value = types[0].value === fromValue ? types[1].value : types[0].value
  }

  if (currencyID) {
    coinCurrency.value = currencyID
  }
}

defineExpose({
  openModal,
})
</script>

<template>
  <ZModal
    v-model="visible"
    class="page-my-wallet-assets-convert"
    :title="$t('page.my.wallet.assets.convert_modal.title')"
  >
    <div class="pb-4">
      <span class="text-sm">{{ $t('page.my.wallet.assets.convert_modal.note', { exchange_name: runtimeConfig.public.exchangeName }) }}</span>
      <div class="flex items-center mt-3 page-my-wallet-assets-convert-currency">
        <div class="flex-1">
          <div class="mb-1 text-sm page-my-wallet-assets-convert-text">
            {{ $t('page.my.wallet.assets.convert_modal.from') }}
          </div>
          <ZSelect
            :model-value="fromType"
            :data-source="types"
            :columns="[
              {
                key: 'text',
              },
            ]"
            :search="false"
            :scroll="true"
            :find-by="['text']"
            value-key="value"
            label-key="text"
            @change="testFrom"
          />
        </div>
        <ZIconArrowRepeatDuotone class="cursor-pointer" @click="HandleConvertPositionCoin" />
        <div class="flex-1">
          <div class="mb-1 text-sm page-my-wallet-assets-convert-text">
            {{ $t('page.my.wallet.assets.convert_modal.to') }}
          </div>
          <ZSelect
            :model-value="toType"
            :data-source="types"
            :columns="[
              {
                key: 'text',
              },
            ]"
            :search="false"
            :scroll="true"
            :find-by="['text']"
            value-key="value"
            label-key="text"
            @change="testTo"
          />
        </div>
      </div>
    </div>
    <div class="flex-1 mb-4">
      <div class="mb-1 text-sm page-my-wallet-assets-convert-text">
        {{ $t('page.my.wallet.assets.convert_modal.coin') }}
      </div>
      <ZSelect
        v-model="coinCurrency"
        :data-source="publicStore.enabledCurrencies"
        :columns="[
          {
            key: 'id',
            scopedSlots: true,
          },
        ]"
        :search="true"
        :scroll="true"
        :replace-func="(text: string) => text.toUpperCase()"
        :find-by="['id']"
        value-key="id"
        label-key="id"
      />
    </div>
    <div class="flex-1">
      <div class="mb-1 text-sm page-my-wallet-assets-convert-text">
        {{ $t('page.my.wallet.assets.convert_modal.amount') }}
      </div>
      <ZInput v-model="amount" :type="InputType.Number" :placeholder="$t('page.my.wallet.assets.convert_modal.amount')" />
    </div>
    <div v-if="coinCurrency" class="text-[12px]">
      {{ $t('page.my.wallet.assets.convert_modal.available') }} {{ `${available} ${coinCurrency.toUpperCase()}` }}
    </div>
    <ZButton
      class="!w-full mt-4 mb-2 text-white rounded"
      type="primary"
      html-type="submit"
      :loading="loading"
      :disabled="disabledButton"
      @click="ConvertCoin"
    >
      {{ $t('page.global.action.confirm') }}
    </ZButton>
  </ZModal>
</template>

<style lang="less">
.page-my-wallet-assets-convert {
  &-currency {
    svg {
      margin: 0 8px;
      margin-top: 20px;
      width: 24px;
      height: 24px;

      .cls-1 {
        fill: @gray-color;
      }

      .cls-2 {
        fill: @text-color;
      }
    }
  }

  .z-modal-layout {
    @media @mobile {
      width: 300px;
    }
  }

  &-text {
    @media @tablet {
      font-size: 14px;
    }
  }

  .z-input {
    height: 40px;
  }

  .z-button {
    height: 36px;
    font-size: 16px;
    color: #fff;

    &:disabled {
      background-color: #e9eff3 !important;
      border: 1px solid #e9eff3 !important;
    }
  }

  .z-checkbox {
    &-inner {
      @media @tablet {
        width: 20px;
        height: 20px;
      }
    }
  }

  .z-dropdown {
    &-trigger {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 12px;
      width: 100%;
      height: 40px;
      border: 1px solid rgba(33, 47, 79, 0.1);
      border-radius: 4px;
      transition: all 0.3s;
      &:hover {
        border: 1px solid @primary-color;
      }
    }

    &-triggered {
      border: 1px solid @primary-color;
      box-shadow: 0 0 0 2px rgba(@primary-color, 0.2);
    }
  }
}
</style>
