<script setup lang="ts">
import type { Country } from '@zsmartex/types'
import type { ZTableColumn } from '@zsmartex/components/types'
import { InputType } from '@zsmartex/components/types'
import type Button from '#components/Button.vue'
import countries from '~/library/countries'

const visible = ref(false)

function openModal() {
  visible.value = true
}

const delayButtonEmail = ref<InstanceType<typeof Button>>()
const delayButtonPhone = ref<InstanceType<typeof Button>>()

const userStore = useUserStore()

const loading = ref(false)

const searchBoxVisible = ref(false)
const number = ref('')
const emailCode = ref('')
const phoneCode = ref('')
const OTPCode = ref('')
const region = ref('VN')
const codeSelected = computed(() => {
  return countries.find(c => c.code === unref(region))?.mobile_code as string
})

if (userStore.phone) {
  region.value = userStore.phone.region
  number.value = userStore.phone.number.replace(`+${codeSelected.value}`, '')
}

const phoneError = computed(() => {
  if (number.value.length === 0) return 'input.error.email'
  return ''
})

const phoneModalSelectColumns: ZTableColumn[] = [
  {
    key: 'mobile_code',
    scopedSlots: true,
  },
]

function onSearchBoxClicked(item: Country) {
  searchBoxVisible.value = false
  region.value = item.code
}

function GeneratePhoneCode(type: 'email' | 'phone') {
  loading.value = true
  if (type === 'phone' && ((`+${codeSelected.value}${number.value}` !== userStore.phone?.number) || !userStore.phone)) {
    userStore.CreatePhone({
      phone_number: codeSelected.value.replace('+', '') + number.value,
      region: unref(region),
    }, delayButtonPhone.value?.StartDelay)
  } else {
    const button = type === 'email' ? delayButtonEmail : delayButtonPhone
    userStore.GenerateCodePhone(type, button.value?.StartDelay)
  }
  loading.value = false
}

async function VerifyPhone() {
  loading.value = true
  await userStore.VerifyPhone({
    phone_code: unref(phoneCode),
    email_code: unref(emailCode),
    otp_code: unref(OTPCode) || '',
  }, () => visible.value = false)
  loading.value = false
}

defineExpose({
  openModal,
})
</script>

<template>
  <ZModal
    v-model="visible"
    class="page-my-security-modal-phone"
    :title="$t('page.my.security.modal_phone.title')"
  >
    <div class="relative flex">
      <ZSearchBox
        v-model="searchBoxVisible"
        :data-source="countries"
        :columns="phoneModalSelectColumns"
        :find-by="['mobile_code', 'name']"
        @click="onSearchBoxClicked"
      >
        +{{ codeSelected }}
        <ZIconAngleDownFilled class="ml-2" />
        <template #mobile_code="{ item }">
          +{{ item.mobile_code }} {{ item.name }}
        </template>
      </ZSearchBox>
      <ZCol class="bg-gray-100">
        <ZInput
          v-model="number"
          :type="InputType.Number"
          :placeholder="$t('page.global.placeholder.phone_number')"
        />
      </ZCol>
    </div>

    <ZForm autocomplete="off" @submit="VerifyPhone">
      <div class="rounded bg-gray-100 mt-4 px-4 py-4">
        <ZFormRow label="Phone Code" :required="true">
          <ZInput
            v-model="phoneCode"
            name="phone_code"
            :type="InputType.Number"
            :placeholder="$t('page.global.placeholder.phone_confirmation_code')"
            :max-length="6"
            class="px-2 h-32 bg-gray-100"
            :required="true"
          >
            <template #suffix>
              <ZButton
                ref="delayButtonPhone"
                :disabled="!!phoneError"
                :delay="{
                  time: 60,
                  content: 'Get [#{time}] again',
                }"
                @click="GeneratePhoneCode('phone')"
              >
                {{ $t('page.global.action.get_code') }}
              </ZButton>
            </template>
          </ZInput>
        </ZFormRow>
        <ZFormRow label="Email Code" :required="true">
          <ZInput
            v-model="emailCode"
            name="email_code"
            :type="InputType.Number"
            :placeholder="$t('page.global.placeholder.e-confirmation_code')"
            :max-length="6"
          >
            <template #suffix>
              <ZButton
                ref="delayButtonEmail"
                :disabled="!!phoneError"
                :delay="{
                  time: 60,
                  content: 'Get [#{time}] again',
                }"
                @click="GeneratePhoneCode('email')"
              >
                {{ $t('page.global.action.get_code') }}
              </ZButton>
            </template>
          </ZInput>
        </ZFormRow>
        <ZFormRow v-if="userStore.otp" label="OTP Code" :required="true">
          <ZCodeInput v-model="OTPCode" class="relative" :length="6" />
        </ZFormRow>
      </div>

      <ZButton
        class="!w-full mt-4 mb-2"
        type="primary"
        html-type="submit"
        :loading="loading"
        :disabled="number.length === 0 || phoneCode.length < 6"
      >
        {{ $t('page.global.action.save') }}
      </ZButton>
    </ZForm>
  </ZModal>
</template>

<style lang="less">
.page-my-security-modal-phone {
  svg {
    width: 20px;
    height: 20px;
    fill: @gray-color;
  }

  .z-overlay {
    width: 450px;
  }

  .phone-number {
    height: 40px;
    line-height: 40px;
    border: none;

    &.z-input-focused {
      box-shadow: none !important;
      outline: 0;
    }
  }

  .z-dropdown {
    &-trigger {
      display: flex;
      border-radius: 4px;
      background-color: rgba(@gray-color, 0.1);
      padding: 8px 12px;
      align-items: center;
      cursor: pointer;
    }

    &-overlay {
      width: 250px;
    }
  }

  .z-input {
    background-color: #fff;
    height: 40px;
    line-height: 40px;
  }

  .z-table-row-col {
    display: block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .z-button {
    height: 40px;
    line-height: 40px;
    font-size: 14px;
    color: #fff;
  }
}
</style>
