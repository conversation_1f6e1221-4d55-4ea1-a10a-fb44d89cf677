<script setup lang="ts">
import { InputType } from '@zsmartex/components/types'
import type Button from '#components/Button.vue'

const visible = ref(false)
const action = ref('')

function openModal(typeModal: string) {
  visible.value = true
  action.value = typeModal
}

const delayButtonEmail = ref<InstanceType<typeof Button>>()
const delayButtonPhone = ref<InstanceType<typeof Button>>()

const userStore = useUserStore()

const loading = ref(false)

const emailCode = ref('')
const phoneCode = ref('')
const OTPCode = ref('')

function GenerateCode(type: 'email' | 'phone') {
  loading.value = true
  const button = type === 'email' ? delayButtonEmail : delayButtonPhone

  if (action.value === '2fa') {
    userStore.GenerateCodeUnbindOTP(type, button.value?.StartDelay)
  } else if (action.value === 'phone') {
    userStore.GenerateCodeUnbindPhone(type, button.value?.StartDelay)
  } else if (action.value === 'email') {
    userStore.GenerateCodeUnbindEmail(type, button.value?.StartDelay)
  }
  loading.value = false
}

const disabledButton = computed(() => {
  if (emailCode.value === '' || emailCode.value.length !== 6) return true
  if (userStore.hasPhone && phoneCode.value.length < 6) return true
  if (userStore.otp && OTPCode.value.length < 6) return true
  return false
})

async function VerifyUnbind() {
  loading.value = true
  if (action.value === '2fa') {
    await userStore.Disable2FA({
      code: OTPCode.value,
      email_code: emailCode.value,
      phone_code: phoneCode.value,
    }, () => {
      visible.value = false
    })
  } else if (action.value === 'phone') {
    await userStore.DisablePhone({
      email_code: emailCode.value,
      phone_code: phoneCode.value,
      otp_code: OTPCode.value,
    }, () => {
      visible.value = false
    })
  } else if (action.value === 'email') {
    await userStore.DisableEmail({
      email_code: emailCode.value,
      phone_code: phoneCode.value,
      otp_code: OTPCode.value,
    }, () => {
      visible.value = false
    })
  }
  loading.value = false
}

defineExpose({
  openModal,
})
</script>

<template>
  <ZModal
    v-model="visible"
    class="page-my-security-modal-phone"
    :title="$t('layout.my.security.unbind.title')"
  >
    <ZForm autocomplete="off" @submit="VerifyUnbind">
      <div class="rounded bg-gray-100 mt-4 px-4 py-4">
        <ZFormRow v-if="userStore.hasEmail" label="Email Code" :required="true">
          <ZInput
            v-model="emailCode"
            name="email_code"
            :type="InputType.Number"
            :placeholder="$t('page.global.placeholder.e-confirmation_code')"
            :max-length="6"
          >
            <template #suffix>
              <ZButton
                ref="delayButtonEmail"
                :delay="{
                  time: 60,
                  content: 'Get [#{time}] again',
                }"
                @click="GenerateCode('email')"
              >
                {{ $t('page.global.action.get_code') }}
              </ZButton>
            </template>
          </ZInput>
        </ZFormRow>
        <ZFormRow v-if="userStore.hasPhone" label="Phone Code" :required="true">
          <ZInput
            v-model="phoneCode"
            name="phone_code"
            :type="InputType.Number"
            :placeholder="$t('page.global.placeholder.phone_confirmation_code')"
            :max-length="6"
            class="px-2 py-8 h-32 bg-gray-100"
            :required="true"
          >
            <template #suffix>
              <ZButton
                ref="delayButtonPhone"
                :delay="{
                  time: 60,
                  content: 'Get [#{time}] again',
                }"
                @click="GenerateCode('phone')"
              >
                {{ $t('page.global.action.get_code') }}
              </ZButton>
            </template>
          </ZInput>
        </ZFormRow>
        <ZFormRow v-if="userStore.otp" label="OTP Code" :required="true">
          <ZCodeInput v-model="OTPCode" class="relative" :length="6" />
        </ZFormRow>
      </div>
      <ZButton
        class="!w-full mt-4 mb-2"
        type="primary"
        html-type="submit"
        :loading="loading"
        :disabled="disabledButton"
      >
        {{ $t('page.global.action.save') }}
      </ZButton>
    </ZForm>
  </ZModal>
</template>

<style lang="less">
.page-my-security-modal-unbind {
  .z-overlay {
    width: 450px;
  }

  .phone-number {
    height: 40px;
    line-height: 40px;
    border: none;

    &.z-input-focused {
      box-shadow: none !important;
      outline: 0;
    }
  }

  .z-dropdown {
    &-trigger {
      border-radius: 4px;
      background-color: rgba(@gray-color, 0.1);
      padding: 8px 12px;
      align-items: center;
      cursor: pointer;
    }

    &-overlay {
      width: 250px;
    }
  }

  .z-input {
    background-color: #fff;
    height: 40px;
    line-height: 40px;
  }

  .z-table-row-col {
    display: block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .z-button {
    height: 40px;
    font-size: 18px;
    color: #fff;
  }
}
</style>
