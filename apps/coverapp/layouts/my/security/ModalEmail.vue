<script setup lang="ts">
import Validate from '~/validation/validate'
import type But<PERSON> from '#components/Button.vue'

const visible = ref(false)

const delayButtonEmail = ref<InstanceType<typeof Button>>()

function openModal() {
  visible.value = true
}

const userStore = useUserStore()

const loading = ref(false)

const step = ref(1)
const token = ref('')
const email = ref('')
const emailCode = ref('')
const OTPCode = ref('')

const emailError = computed(() => {
  if (!email.value.length) return ''
  return Validate.email(email.value)
})

const disabledButtonStep1 = computed(() => {
  if (emailCode.value.length < 6) return true
  if (userStore.otp && OTPCode.value.length < 6) return true
  return false
})

const disabledButtonStep2 = computed(() => {
  if (emailCode.value.length < 6) return true
  if (emailError.value || !email.value.length) return true
  return false
})

function GeneratePrepareChangeEmailCode() {
  userStore.GeneratePrepareChangeEmailCode('email', delayButtonEmail.value?.StartDelay)
}

function GenerateChangeEmailCode() {
  userStore.GenerateChangeEmailCode(email.value, delayButtonEmail.value?.StartDelay)
}

async function VerifyPrepareEmailCode() {
  loading.value = true
  const tokenResult = await userStore.VerifyPrepareEmailCode({
    email_code: emailCode.value,
    otp_code: OTPCode.value,
  })

  if (tokenResult.length) {
    token.value = tokenResult
    step.value = 2
    emailCode.value = ''
  }
  loading.value = false
}

async function ChangeEmail() {
  loading.value = true
  await userStore.ChangeEmail({
    email: email.value,
    email_code: emailCode.value,
    token: token.value,
  }, () => {
    step.value = 1
    visible.value = false
  })
  loading.value = false
}

defineExpose({
  openModal,
})
</script>

<template>
  <ZModal
    v-model="visible"
    class="page-my-security-modal-email"
    :title="$t('page.my.security.modal_email.title')"
    :button-close="userStore.hasEmail"
  >
    <div v-if="step === 1">
      <div class="rounded bg-gray-100 mt-4 px-4 py-4">
        <ZFormRow label="Email Code" :required="true">
          <ZInput
            v-model="emailCode"
            name="email_code"
            :type="InputType.Number"
            :placeholder="$t('page.global.placeholder.e-confirmation_code')"
            :max-length="6"
          >
            <template #suffix>
              <ZButton
                ref="delayButtonEmail"
                :delay="{
                  time: 60,
                  content: 'Get [#{time}] again',
                }"
                @click="GeneratePrepareChangeEmailCode"
              >
                {{ $t('page.global.action.get_code') }}
              </ZButton>
            </template>
          </ZInput>
        </ZFormRow>
        <ZFormRow label="OTP Code" :required="true">
          <ZCodeInput v-model="OTPCode" class="relative" :length="6" />
        </ZFormRow>
      </div>

      <ZButton
        class="!w-full mt-4 mb-2"
        type="primary"
        :loading="loading"
        :disabled="disabledButtonStep1"
        @click="VerifyPrepareEmailCode"
      >
        {{ $t('page.global.action.save') }}
      </ZButton>
    </div>
    <div v-else>
      <ZFormRow label="New Email" :required="true">
        <ZInput
          v-model="email"
          name="email"
          :type="InputType.Text"
          :placeholder="$t('page.global.placeholder.email')"
          class="px-2 h-32 bg-gray-100"
          :required="true"
          :error="emailError"
        />
      </ZFormRow>
      <ZFormRow label="Email Code" :required="true">
        <ZInput
          v-model="emailCode"
          name="email_code"
          :type="InputType.Number"
          :placeholder="$t('page.global.placeholder.e-confirmation_code')"
          :max-length="6"
        >
          <template #suffix>
            <ZButton
              ref="delayButtonEmail"
              :delay="{
                time: 60,
                content: 'Get [#{time}] again',
              }"
              :disabled="!!emailError || !email.length"
              @click="GenerateChangeEmailCode"
            >
              {{ $t('page.global.action.get_code') }}
            </ZButton>
          </template>
        </ZInput>
      </ZFormRow>
      <ZButton
        class="!w-full mt-4 mb-2"
        type="primary"
        html-type="submit"
        :loading="loading"
        :disabled="disabledButtonStep2"
        @click="ChangeEmail"
      >
        {{ $t('page.global.action.save') }}
      </ZButton>
    </div>
  </ZModal>
</template>

<style lang="less">
.page-my-security-modal-email {
  .z-overlay {
    width: 450px;
  }

  .z-dropdown {
    &-trigger {
      display: flex;
      border-radius: 4px;
      background-color: rgba(@gray-color, 0.1);
      padding: 8px 12px;
      align-items: center;
      cursor: pointer;
    }

    &-overlay {
      width: 250px;
    }
  }

  .z-input {
    background-color: #fff;
    height: 40px !important;
    line-height: 40px;
  }

  .z-table-row-col {
    display: block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .z-button {
    height: 40px;
    font-size: 14px;
    color: #fff;
  }
}
</style>
