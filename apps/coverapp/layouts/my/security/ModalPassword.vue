<script setup lang="ts">
import { validatePassword } from '@zsmartex/utils'
import { InputType } from '@zsmartex/components/types'
import type Button from '#components/Button.vue'

const delayButtonEmail = ref<InstanceType<typeof Button>>()
const delayButtonPhone = ref<InstanceType<typeof Button>>()
const userStore = useUserStore()

const loading = ref(false)
const oldPassword = ref('')
const newPassword = ref('')
const confirmNewPassword = ref('')
const otpCode = ref('')
const emailCode = ref('')
const phoneCode = ref('')

const visible = ref(false)

function openModal() {
  visible.value = true
  oldPassword.value = ''
  newPassword.value = ''
  confirmNewPassword.value = ''
  otpCode.value = ''
  emailCode.value = ''
  phoneCode.value = ''
}

const oldPasswordError = computed(() => {
  if (oldPassword.value.length !== 0 && oldPassword.length < 8) return $t('page.global.error.password')
})

const newPasswordError = computed(() => {
  if (!validatePassword(newPassword.value) && newPassword.value.length !== 0) return $t('page.global.error.password')
})

const confirmNewPasswordError = computed(() => {
  if ((newPassword.value.length !== confirmNewPassword.value.length || !validatePassword(confirmNewPassword.value)) && confirmNewPassword.value.length !== 0) return $t('page.global.error.password_confirmation')
})

const emailCodeError = computed(() => {
  if (emailCode.value.length && emailCode.value.length !== 6) {
    return $t('page.global.error.min_length', { min: 6 })
  }
})

const phoneCodeError = computed(() => {
  if (phoneCode.value.length && phoneCode.value.length !== 6) {
    return $t('page.global.error.min_length', { min: 6 })
  }
})

const disabledButton = computed(() => {
  if (oldPassword.value.length === 0 || oldPasswordError.value) return true
  if (newPassword.value.length === 0 || newPasswordError.value) return true
  if (confirmNewPassword.value.length === 0 || confirmNewPasswordError.value) return true
  if (emailCodeError.value || emailCode.value.length === 0) return true
  if (userStore.otp && otpCode.value.length < 6) return true
  if (userStore.hasPhone && (phoneCodeError.value || phoneCode.value.length === 0)) return true
  return false
})

function GenerateEmailCode() {
  userStore.GenerateChangePasswordCode('email', delayButtonEmail.value?.StartDelay)
}

function GeneratePhoneCode() {
  userStore.GenerateChangePasswordCode('phone', delayButtonPhone.value?.StartDelay)
}

async function SaveAction() {
  loading.value = true

  await userStore.SetNewPassword({
    old_password: oldPassword.value,
    new_password: newPassword.value,
    confirm_password: confirmNewPassword.value,
    otp_code: otpCode.value,
    email_code: emailCode.value,
    phone_code: phoneCode.value,
  }, () => {
    visible.value = false
  })

  loading.value = false
}

defineExpose({
  openModal,
})
</script>

<template>
  <ZModal
    v-model="visible"
    class="page-my-security-password-modal"
    :title="$t('page.my.security.modal_password.title')"
  >
    <div class="mb-8 page-my-security-password-modal-input">
      <span class="text-base mb-2 block">{{ $t('page.global.placeholder.old_password') }}</span>
      <ZInput v-model="oldPassword" :type="InputType.Password" class="my-2 h-16 !bg-gray-100 border-gray-100" :placeholder="$t('page.my.security.modal_password.placeholder.old_password')" :error="oldPasswordError" />
    </div>
    <div class="mb-8 page-my-security-password-modal-input">
      <span class="text-base mb-2 block">{{ $t('page.global.placeholder.new_password') }}</span>
      <ZInput v-model="newPassword" :type="InputType.Password" class="my-2 h-16 bg-gray-100 border-gray-100" :placeholder="$t('page.my.security.modal_password.placeholder.new_password')" :error="newPasswordError" />
    </div>
    <div class="mb-8 page-my-security-password-modal-input">
      <span class="text-base mb-2 block">{{ $t('page.global.placeholder.confirm_password') }}</span>
      <ZInput v-model="confirmNewPassword" :type="InputType.Password" class="my-2 h-16 bg-gray-100 border-gray-100" :placeholder="$t('page.my.security.modal_password.placeholder.confirm_password')" :error="confirmNewPasswordError" />
    </div>
    <div class="page-my-security-password-modal-gray bg-gray-100 py-6">
      <div v-if="userStore.hasEmail" class="page-my-security-password-modal-code page-my-security-password-modal-code">
        <div class="text-center text-lg mb-2">
          {{ $t('page.global.placeholder.email_code') }}
        </div>
        <ZInput
          v-model="emailCode"
          name="email_code"
          :type="InputType.Number"
          :placeholder="$t('page.global.placeholder.e-confirmation_code')"
          :max-length="6"
          class="px-2 h-32 bg-white mb-4"
          :required="true"
          :error="emailCodeError"
        >
          <template #suffix>
            <ZButton
              ref="delayButtonEmail"
              :delay="{
                time: 60,
                content: 'Get [#{time}] again',
              }"
              @click="GenerateEmailCode"
            >
              {{ $t('page.global.action.get_code') }}
            </ZButton>
          </template>
        </ZInput>
      </div>
      <div v-if="userStore.hasPhone" class="page-my-security-password-modal-code mb-4">
        <div class="text-center text-lg mb-2 mt-8">
          {{ $t('page.global.placeholder.phone_code') }}
        </div>
        <ZInput
          v-model="phoneCode"
          name="phone_code"
          :type="InputType.Number"
          :placeholder="$t('page.global.placeholder.phone_confirmation_code')"
          :max-length="6"
          class="px-2 py-8 h-32 bg-white mb-4"
          :required="true"
          :error="phoneCodeError"
        >
          <template #suffix>
            <ZButton
              ref="delayButtonPhone"
              :delay="{
                time: 60,
                content: 'Get [#{time}] again',
              }"
              @click="GeneratePhoneCode"
            >
              {{ $t('page.global.action.get_code') }}
            </ZButton>
          </template>
        </ZInput>
      </div>
      <div v-if="userStore.otp" class="mb-4 page-my-security-password-modal-code-otp">
        <div class="text-center text-lg mt-8 mb-2">
          {{ $t('page.global.placeholder.otp_code') }}
        </div>
        <ZCodeInput v-model="otpCode" class="relative" :length="6" />
      </div>
    </div>
    <div class="page-my-security-password-modal-submit">
      <ZButton
        class="!w-full mt-4 mb-2 text-white"
        type="primary"
        html-type="submit"
        :loading="loading"
        :disabled="disabledButton"
        @click="SaveAction"
      >
        {{ $t('page.global.action.save') }}
      </ZButton>
    </div>
  </ZModal>
</template>

<style lang="less">
.page-my-security-password-modal {
  .z-overlay {
    width: 500px;
  }

  &-gray {
    padding: 32px;
  }

  .z-input {
    margin: 0;
    height: 40px;
    border: none;
  }

  &-input {
    input {
      &:-webkit-autofill,
      &:-webkit-autofill:hover,
      &:-webkit-autofill:focus,
      &:-webkit-autofill:active {
        box-shadow: 0 0 0 1000px #F3F4F6 inset !important;
        -webkit-text-fill-color: @text-color !important;
      }
    }
  }

  &-code {
    input {
      &:-webkit-autofill,
      &:-webkit-autofill:hover,
      &:-webkit-autofill:focus,
      &:-webkit-autofill:active {
        box-shadow: 0 0 0 1000px #fff inset !important;
        -webkit-text-fill-color: @text-color !important;
      }
    }
  }

  .z-dropdown-overlay {
    width: 250px;
  }

  &-code {
    .z-button {
      height: 36px;
      font-size: 14px;
      color: #fff;
      border-radius: 4px;

      &:disabled {
        background-color: #fff !important;
      }

      span {
        align-items: center;
      }
    }

    &-otp {
      .z-input-error-container {
        top: 100%;
      }
    }
  }

  .z-input-error {
    &-container {
      height: 28px;
      font-size: 12px;
    }

    &-content {
      margin-top: 4px;
    }
  }

  &-submit {
    .z-button {
      height: 40px;
      font-size: 18px;
      color: #fff;
      border-radius: 4px;
    }
  }

  .z-input {
    border: 1px solid @base-border-color;
  }

  .z-code-input {
    .z-input-focused {
      input {
        border: 1px solid rgba(@primary-color, 0.25);
      }
    }

    input {
      background-color: white;
      border: 1px solid @base-border-color;
      border-radius: 4px;
    }
  }
}
</style>
