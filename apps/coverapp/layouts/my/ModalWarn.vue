<script setup lang="ts">
const emit = defineEmits<{
  (event: 'skip'): void
}>()

const visible = ref(false)
const needPhone = ref(false)

function openModal(phone?: boolean) {
  visible.value = true

  if (phone) needPhone.value = phone
}

const RedirectPhone = async () => {
  navigateTo('/my/security')
}

const RedirectOTP = async () => {
  navigateTo('/my/google-auth')
}

watch(visible, (value) => {
  if (!value) {
    emit('skip')
  }
})

defineExpose({
  openModal,
})
</script>

<template>
  <ZModal
    v-model="visible"
    class="page-my-security-modal-warn"
  >
    <div class="text-center mb-6 text-[#ffc107] page-my-security-modal-warn-icon">
      <ZIconWarnDuotone />
      <span class="block font-bold text-2xl mt-2">{{ $t('page.my.modal_warn.title') }}</span>
    </div>
    <div class="flex justify-around mb-8">
      <div v-if="needPhone" class="page-my-security-modal-warn-item">
        <div class="page-my-security-modal-warn-item-icon">
          <ZIconPhoneFilled />
        </div>
        <div class="page-my-security-modal-warn-item-title">
          {{ $t('page.my.modal_warn.link_to_phone') }}
        </div>
        <div class="page-my-security-modal-warn-item-button">
          <ZButton @click="RedirectPhone">
            {{ $t('page.global.action.come_to') }}
          </ZButton>
        </div>
      </div>
      <div class="page-my-security-modal-warn-item">
        <div class="page-my-security-modal-warn-item-icon">
          <img class="w-full h-full" src="../../assets/img/google-authenticator.svg" />
        </div>
        <div class="page-my-security-modal-warn-item-title">
          {{ $t('page.my.modal_warn.google_auth') }}
        </div>
        <div class="page-my-security-modal-warn-item-button">
          <ZButton @click="RedirectOTP">
            {{ $t('page.global.action.come_to') }}
          </ZButton>
        </div>
      </div>
    </div>
    <div v-if="needPhone" class="page-my-security-modal-submit">
      <ZButton
        class="!w-full mt-4 mb-2 h-[40px]! text-lg text-white"
        type="primary"
        html-type="submit"
        @click="visible = false"
      >
        {{ $t('page.global.action.skip') }}
      </ZButton>
    </div>
  </ZModal>
</template>

<style lang="less">
.page-my-security-modal-warn {
  &-icon {
    svg {
      width: 160px;
      height: 160px;

      .cls-1 {
        fill: @warn-color;
      }

      .cls-2 {
        fill: white;
      }
    }
  }

  &-item {
    padding: 24px;
    max-width: 150px;
    text-align: center;
    border: 1px solid #ddd;
    border-radius: 4px;

    &-icon {
      svg {
        width: 32px;
        height: 32px;

        .cls-1 {
          fill: @gray-color;
        }
      }

      img {
        width: 52px;
        height: 52px;
      }
    }

    &-title {
      margin-bottom: 8px;
      height: 48px;
      font-size: 16px;
    }

    &-button {
      margin-top: 16px;
      height: 28px;
      display: flex;
      justify-content: center;

      .z-button {
        height: 28px;
        font-size: 14px;
      }
    }
  }
}
</style>
