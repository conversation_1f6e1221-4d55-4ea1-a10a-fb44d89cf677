<script setup lang="ts">
import type { ZTableColumn } from '@zsmartex/components/types'
import type { Activity } from '@zsmartex/types'
import { Align, Format, ParseType } from '@zsmartex/types'

const props = defineProps<{
  dataSource: Activity[]
}>()

const { query } = useQuery()
const total = ref(0)
const loading = ref(false)

const columns: ZTableColumn[] = [
  {
    title: $t('page.global.table.time'),
    key: 'created_at',
    formatBy: Format.DateTime,
    parse: ParseType.DateTime,
  },
  {
    title: $t('page.global.table.ip'),
    key: 'user_ip',
    align: Align.Center,
  },
  {
    title: $t('page.global.table.country'),
    key: 'user_ip_country',
    align: Align.Right,
  },
]

const dataSource = computed(() => {
  total.value = props.dataSource.length
  return props.dataSource.slice((Number(query.value.page) - 1) * Number(query.value.limit), Number(query.value.page) * Number(query.value.limit))
})
</script>

<template>
  <ZTablePro class="page-my-dashboard-recent-logins" :title="$t('page.my.dashboard.recent_logins.title_table')" :columns="columns" :data-source="dataSource">
    <template #foot>
      <ZPagination v-model="query.page" v-model:page-size="query.limit" :is-loading="loading" :total="total" />
    </template>
  </ZTablePro>
</template>

<style lang="less">
.page-my-dashboard-recent-logins {
  .user_ip_country {
    @media @mobile {
      max-width: 60px;
    }
  }

  .z-table-pro {
    &-head {
      @media @mobile {
        padding: 16px;
      }
    }
  }

  .z-table {
    &-head {
      @media @mobile {
        padding: 0 16px;
      }
    }

    &-row {
      @media @mobile {
        padding: 0 16px;
      }
    }
  }
}
</style>
