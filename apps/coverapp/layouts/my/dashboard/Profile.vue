<script setup lang="ts">
import { format } from 'date-fns'
import { encryptEmail, encryptPhone } from '@zsmartex/utils'
import type { Activity } from '@zsmartex/types'

const props = defineProps<{
  lastLogin: Activity
}>()

const emit = defineEmits(['click'])
const userStore = useUserStore()

const lastLoginTime = computed(() => {
  return format(new Date(props.lastLogin.created_at), 'yyyy-MM-dd HH:mm:ss')
})
</script>

<template>
  <ZCard class="profile" content-class="flex justify-between bold-text">
    <ZRow class="profile-row">
      <div class="profile-avatar">
        {{ (userStore.username || userStore.email || 'U' as string)[0] }}
      </div>
      <div class="profile-row-content grid grid-cols-1 content-between">
        <div class="profile-username flex items-center">
          <span class="flex items-center">
            {{ userStore.username || "Set Username" }}
            <ZIconEditDuotone class="ml-2" @click="emit('click')" />
          </span>
          <span class="vip bold-text ml-2">
            <ZIcon type="vip" /> <span>{{ $t('page.my.dashboard.profile.vip_0') }}</span>
          </span>
        </div>
        <div class="profile-information">
          <div class="flex justify-between">
            <span>{{ `${$t('page.my.dashboard.profile.account')}: ` }}</span>
            <span v-if="userStore.email">{{ encryptEmail(userStore.email as string) }}</span>
            <span v-else-if="userStore.phone">{{ encryptPhone(userStore.phone.number as string) }}</span>
          </div>
          <div class="flex justify-between">
            <span>{{ `${$t('page.my.dashboard.profile.uid')}: ` }}</span>
            <span>{{ userStore.uid }}</span>
          </div>
        </div>
      </div>
    </ZRow>

    <div class="last-session">
      <div class="last-session-time">
        <span class="title">{{ $t('page.my.dashboard.profile.last_login_time') }}:</span> {{ lastLoginTime }}
      </div>
      <div class="last-session-ip">
        <span class="title">{{ $t('page.my.dashboard.profile.ip') }}:</span> {{ lastLogin.user_ip }}
      </div>
    </div>
  </ZCard>
</template>

<style lang="less">
.profile {
  @media @mobile {
    display: block;
    margin-bottom: 12px;
  }

  &-row {
    @media @mobile {
      display: block !important;
    }

    &-content {
      margin-left: 12px;

      @media @mobile {
        margin-left: 0;
      }
    }
  }

  .z-card-overall > div {
    @media @mobile {
      display: block;
    }
  }

  &-avatar {
    height: 75px;
    width: 75px;
    line-height: 75px;
    background-color: @primary-color;
    border-radius: 50%;
    color: #fff;
    text-align: center;
    font-size: 28px;

    @media @mobile {
      display: none;
    }
  }

  &-username {
    margin-left: 8px;
    font-size: 24px;
    font-weight: 500;

    svg {
      width: 20px;
      height: 20px;
      fill: @gray-color;
      cursor: pointer;
    }

    @media @mobile {
      margin-left: 0;
      justify-content: space-between;
    }

    i {
      font-size: 20px;
      color: @gray-color;
      cursor: pointer;
    }
  }

  &-information {
    display: flex;

    @media @mobile {
      display: block;
    }

    div {
      @media @large-desktop, @desktop, @tablet {
        border: 1px solid @base-border-color;
        margin: 0 4px;
        padding: 4px 8px;
        border-radius: 4px;
      }
    }
  }

  img {
    height: 85px;
  }

  .vip {
    line-height: 1.5;
    vertical-align: middle;
    align-items: center;
    padding: 0 12px;
    background-color: rgba(@primary-color, 0.3);
    width: max-content;
    border-radius: 19px;
    font-size: 14px;
    color: @primary-color;

    i {
      color: @primary-color;
      font-size: 16px;
      line-height: 1.5;
    }

    span {
      margin-left: 6px;
    }
  }

  .last-session {
    text-align: right;

    @media @mobile {
      text-align: left;
    }

    .title {
      color: @gray-color;
    }

    &-time {
      margin-bottom: 8px;

      @media @mobile {
        margin-bottom: 0;
        display: flex;
        justify-content: space-between;
      }
    }

    &-ip {
      @media @mobile {
        display: flex;
        justify-content: space-between;
      }
    }
  }
}
</style>
