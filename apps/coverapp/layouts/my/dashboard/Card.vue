<script setup lang="ts">
defineProps<{
  title: string
  to: string
  disabled?: boolean
}>()
</script>

<template>
  <div class="my-dashboard-card grid grid-cols-1 content-between shadow-md">
    <div class="my-dashboard-card-content">
      <h1 class="bold-text text-xl">
        {{ title }}
      </h1>
      <slot />
    </div>
    <div v-if="!disabled" class="my-dashboard-card-footer">
      <NuxtLink class="bold-text" :to="to">
        {{ $t('page.my.dashboard.card.manage') }}
      </NuxtLink>
    </div>
  </div>
</template>

<style lang="less">
.my-dashboard-card {
  background-color: #fff;
  padding: 16px 0;
  border-radius: 4px;

  @media @mobile {
    margin-bottom: 12px;
  }

  &-content {
    padding: 0 24px;
    margin-bottom: 12px;

    @media @mobile {
      padding: 0 16px;
    }
  }

  &-footer {
    margin-top: 16px;
    padding: 16px 24px 0;
    border-top: 1px solid @base-border-color;

    @media @mobile {
      padding: 16px;
      padding-bottom: 0;
    }
  }
}
</style>
