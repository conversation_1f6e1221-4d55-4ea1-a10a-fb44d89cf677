<script setup lang="ts">
import { InputType } from '@zsmartex/components/types'

const visible = ref(false)

const userStore = useUserStore()
const loading = ref(false)
const username = ref('')

function openModal() {
  visible.value = true
}

const usernameError = computed(() => {
  if (!username.value.length) return ''
  return /^[a-zA-Z0-9-_]+$/.test(username.value) ? '' : 'input.error.username'
})

async function SetUsername() {
  loading.value = true
  await userStore.SetUserName(username.value, () => {
    visible.value = false
  })
  loading.value = false
}

const disabledButton = computed(() => {
  if (username.value === '') return true
  if (usernameError.value) return true
  return false
})

defineExpose({
  openModal,
})
</script>

<template>
  <ZModal
    v-model="visible"
    class="page-my-dashboard-username"
    :title="$t('layout.my.dashboard.username.title')"
  >
    <ZForm autocomplete="off" @submit="SetUsername">
      <ZFormRow label="Username" :required="true">
        <ZInput
          v-model="username"
          name="username"
          :type="InputType.Text"
          :placeholder="$t('page.global.placeholder.username')"
          :error="usernameError"
        />
      </ZFormRow>
      <ZButton
        class="!w-full mt-10 mb-2"
        type="primary"
        html-type="submit"
        :loading="loading"
        :disabled="disabledButton"
      >
        {{ $t('page.global.action.save') }}
      </ZButton>
    </ZForm>
  </ZModal>
</template>

<style lang="less">
.page-my-dashboard-username {
  .z-overlay {
    width: 450px;
  }

  .z-modal-layout {
    @media @mobile {
      width: 300px;
    }
  }

  .z-input {
    background-color: #fff;
    height: 40px;
    line-height: 40px;
  }

  .z-table-row-col {
    display: block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .z-button {
    height: 40px;
    font-size: 18px;
    color: #fff;
  }
}
</style>
