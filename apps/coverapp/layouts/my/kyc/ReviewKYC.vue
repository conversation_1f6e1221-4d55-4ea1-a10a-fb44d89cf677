<template>
  <ZCard class="review-kyc">
    <div class="flex justify-center">
      <div class="review-kyc-icon flex justify-center items-center">
        <ZIconCheckFilled />
      </div>
    </div>
    <div class="review-kyc-title bold-text">
      {{ $t('page.my.kyc.review.title') }}
    </div>
    <div class="review-kyc-description">
      {{ $t('page.my.kyc.review.description') }}
    </div>
    <NuxtLink class="review-kyc-button" to="/my/dashboard">
      {{ $t('page.my.kyc.back') }}
    </NuxtLink>
  </ZCard>
</template>

<style lang="less">
.review-kyc {
  padding: 40px 0;
  text-align: center;

  &-icon {
    background-color: #1aa760;
    width: 40px;
    height: 40px;
    border-radius: 50%;

    svg {
      width: 28px;
      height: 28px;
      fill: white;
    }

    i {
      zoom: 1.1;
      color: @white-color;
    }
  }

  &-title {
    margin-top: 20px;
    font-size: 20px;
    color: #000;
  }

  &-description {
    margin: 20px 0;
    font-weight: 500;
    font-size: 16px;
  }

  &-button {
    font-size: 18px;
    color: @text-color;
  }
}
</style>
