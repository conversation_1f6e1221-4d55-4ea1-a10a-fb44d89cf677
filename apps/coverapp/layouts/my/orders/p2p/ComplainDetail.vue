<script setup lang="ts">
import type { Complain } from '@zsmartex/types'

const runtimeConfig = useRuntimeConfig()
const visible = ref(false)
const complain = ref<Complain>({} as Complain)

function openModal(data: Complain) {
  visible.value = true
  complain.value = data
}

defineExpose({
  openModal,
})
</script>

<template>
  <ZModal
    v-model="visible"
    class="layout-orders-p2p-complain-detail"
    :title="$t('layout.my.orders.p2p.complain_detail.title')"
  >
    <div class="layout-orders-p2p-complain-detail-row mb-5 pt-4">
      <div class="layout-orders-p2p-complain-detail-row-item flex-1">
        <div class="text-gray mr-3">
          {{ $t('layout.my.orders.p2p.complain_detail.static_id') }}
        </div>
        <div class="bold-text">
          {{ complain.static_id }}
        </div>
      </div>
      <div class="layout-orders-p2p-complain-detail-row-item">
        <div class="text-gray mr-3">
          {{ $t('layout.my.orders.p2p.complain_detail.state') }}
        </div>
        <div
          class="capitalize bold-text"
          :class="[
            { 'text-gray-400': complain.state === 'pending' },
            { 'text-green-500': complain.state === 'processed' },
            { 'text-red-500': complain.state === 'denied' },
          ]"
        >
          {{ complain.state }}
        </div>
      </div>
    </div>
    <div class="mb-5">
      <div class="text-gray mb-2">
        {{ $t('layout.my.orders.p2p.complain_detail.content') }}
      </div>
      <div class="bold-text">
        {{ complain.content }}
      </div>
    </div>
    <div v-if="complain.filename" class="mb-5">
      <div class="text-gray mb-2">
        Image
      </div>
      <div class="bold-text">
        <img class="w-full" :src="`${runtimeConfig.public.apiUrl}trade/account/p2p_complains/${complain.static_id}/${complain.filename}`">
      </div>
    </div>
    <div v-if="complain.response" class="mb-5">
      <div class="text-gray mb-2">
        {{ $t('layout.my.orders.p2p.complain_detail.response') }}
      </div>
      <div class="bold-text">
        {{ complain.response }}
      </div>
    </div>
  </ZModal>
</template>

<style lang="less">
.layout-orders-p2p-complain-detail {
  .z-button {
    border: 1px solid @primary-color !important;
    background-color: @primary-color;
    color: white !important;
  }

  .z-modal-layout {
    @media @mobile {
      width: 320px;
    }
  }

  &-row {
    display: flex;

    @media @mobile {
      display: block;
    }

    &-item {
      display: flex;

      @media @mobile {
        display: block;
        margin-bottom: 20px;
      }
    }
  }
}
</style>
