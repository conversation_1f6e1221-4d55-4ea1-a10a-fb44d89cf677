<script setup lang="ts">
const props = defineProps<{
  loading: boolean
}>()

const emit = defineEmits<{
  (event: 'click', content: string, file?: File): void
}>()
const visible = ref(false)

function openModal() {
  visible.value = true
}

const content = ref('')
const file = ref<File>()
const imagePreview = ref('')

const disabled = computed(() => {
  if (content.value.length <= 0) return true
  return false
})

const onImageMessageChange = async (e: Event) => {
  const target = e.target as HTMLInputElement
  const input = target.files![0]

  imagePreview.value = URL.createObjectURL(input)
  file.value = input
}

const onClick = () => {
  if (file.value) {
    emit('click', content.value, file.value)
  } else {
    emit('click', content.value)
  }

  visible.value = false
}

defineExpose({
  openModal,
})
</script>

<template>
  <ZModal
    v-model="visible"
    class="layout-orders-p2p-complain"
    :title="$t('layout.my.orders.p2p.create_complain.title')"
  >
    <ZFormRow class="mb-6" label="Content" required>
      <textarea v-model="content" class="layout-orders-p2p-complain-area" />
    </ZFormRow>
    <ZFormRow class="mb-6 layout-orders-p2p-complain-image" label="Image">
      <input id="image_message" type="file" class="hidden" accept="image/*" multiple @change="onImageMessageChange">
      <label for="image_message">
        <span v-if="!imagePreview" class="text-gray cursor-pointer">
          Choose your image
        </span>
        <img :src="imagePreview">
      </label>
    </ZFormRow>
    <div class="flex">
      <div class="flex-1 text-center text-gray cursor-pointer h-[40px] leading-[40px]" @click="visible = false">
        {{ $t('page.global.action.cancel') }}
      </div>
      <ZButton class="flex-1" :loading="loading" :disabled="disabled" @click="onClick">
        {{ $t('page.global.action.create') }}
      </ZButton>
    </div>
  </ZModal>
</template>

<style lang="less">
.layout-orders-p2p-complain {
  .z-modal-layout {
    @media @mobile {
      width: 320px;
    }
  }

  &-image {
    img {
      width: 100%;
    }
  }

  &-area {
    width: 100%;
    min-height: 100px;
    padding: 12px;
    font-size: 14px;
    font-family: 'URWDIN-Regular';
    outline: none;
    border-radius: 4px;
    border: 1px solid @base-border-color;
    transition: 0.3s all;

    &:hover {
      border: 1px solid @primary-color;
    }

    &:focus {
      border: 1px solid @primary-color;
      box-shadow: 0 0 0 2px rgba(@primary-color, 0.2);
    }
  }

  .z-button {
    border: 1px solid @primary-color;
    background-color: @primary-color;
    color: white;
  }
}
</style>
