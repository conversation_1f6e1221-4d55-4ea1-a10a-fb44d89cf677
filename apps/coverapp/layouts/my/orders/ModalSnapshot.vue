<script setup lang="ts">
import type { Snapshot } from '@zsmartex/types';
import { startOfDay, addDays, addMonths, addYears, compareAsc, getUnixTime, startOfYear, endOfYear } from 'date-fns'

const emit = defineEmits<{
  (event: 'submit', snapshot: Snapshot): void
}>()

const userStore = useUserStore()

const visible = ref(false)
const type = ref('transactions')
const typeModal = ref('')
const loading = ref(false)
const otpCode = ref('')

const timeType = ref('7')
const dateRange = ref<(Date | null)[]>([null, null])
const timeFrom = ref(0)
const timeTo = ref(0)
const calendarYear = ref('')

const types = [
  {
    value: 'transactions',
  },
  {
    value: 'trades',
  },
]

const timeTypes = [
  {
    key: '7',
    value: 'Past 7 days',
  },
  {
    key: '30',
    value: 'Past 30 days',
  },
  {
    key: '90',
    value: 'Past 90 days',
  },
  {
    key: '365',
    value: 'Past 12 months',
  },
  {
    key: 'calendar_year',
    value: 'A calendar year',
  },
  {
    key: 'customize',
    value: 'Customize',
  },
]

const calendarYears = computed(() => {
  const now = new Date()
  const amountYear = now.getFullYear() - 2018 + 1

  const years = []
  for (let i = 0; i < amountYear; i++) {
    years.push({
      key: `${i}`,
      value: `${now.getFullYear() - i}`,
    })
  }
  return years
})

const disableButton = computed(() => {
  if (type.value.length === 0) return true
  if (type.value !== 'accounts' && timeFrom.value === 0) return true
  if (type.value !== 'accounts' && timeTo.value === 0) return true
  return false
})

const disableSubmitButton = computed(() => {
  if (otpCode.value.length !== 6) return true
  return false
})

const SubmitAction = async (type: string, timeFrom: number, timeTo: number, otpCode: string) => {
  loading.value = true

  if (typeModal.value === 'create-confirm') {
    try {
      const snapshot = await userStore.CreateSnapshot({
        type,
        time_from: type !== 'accounts' ? timeFrom: 0,
        time_to: type !== 'accounts' ? timeTo: 0,
        otp_code: otpCode,
      })

      if (Object.keys(snapshot).length > 0) {
        emit('submit', snapshot)
      }
    } catch (error) {
      return
    } finally {
      loading.value = false
    }
  }

  loading.value = false
  visible.value = false
}

watch(dateRange.value, () => {
  if (dateRange.value[0] && dateRange.value[1] && dateRange.value[0] <= dateRange.value[1]) {
    timeFrom.value = getUnixTime(startOfDay(dateRange.value[0]))
    timeTo.value = getUnixTime(startOfDay(dateRange.value[1]))
  }
}, { immediate: true })

watch(timeType, () => {
  if (!timeType.value.length) return

  if (timeType.value === 'customize' || timeType.value === 'calendar_year') {
    timeFrom.value = 0
    timeTo.value = 0
    calendarYear.value = ''
  } else {
    const now = startOfDay(new Date())
    timeFrom.value = getUnixTime(startOfDay(addDays(now, -Number(timeType.value))))
    timeTo.value = getUnixTime(now)
  }
}, { immediate: true })

watch(calendarYear, () => {
  if (!calendarYear.value.length) return

  const now = new Date()
  timeFrom.value = getUnixTime(startOfYear(addYears(now, -Number(calendarYear.value))))
  timeTo.value = getUnixTime(endOfYear(addYears(now, -Number(calendarYear.value))))
}, { immediate: true })

function openModal(type: string) {
  visible.value = true
  typeModal.value = type
  otpCode.value = ''
}

function clearDate() {
  timeFrom.value = 0
  timeTo.value = 0
}

function disabledDate(date: Date) {
  const now = new Date()
  const maxDate = now
  if (compareAsc(maxDate, date) !== 1) return true
  return false
}

function capitalize(text: string) {
  return text[0].toUpperCase() + text.slice(1)
}

defineExpose({
  openModal,
})
</script>

<template>
  <ZModal
    v-model="visible"
    class="page-my-snapshots-book-modal"
    title="Create Snapshot"
  >
    <template v-if="typeModal === 'create'">
      <div class="flex-1 mb-4">
        <div class="mb-2">
          Type
        </div>
        <ZSelect
          v-model="type"
          :data-source="types"
          :columns="[
            {
              key: 'value',
              scopedSlots: true,
            },
          ]"
          :find-by="['value']"
          :search="false"
          :scroll="true"
          :replace-func="capitalize"
          placeholder="Type"
          value-key="value"
          label-key="value"
        />
      </div>
      <div v-if="type !== 'accounts'" class="flex-1 mb-4">
        <div class="mb-2">
          Time
        </div>
        <ZSelect
          v-model="timeType"
          class="mb-4"
          :data-source="timeTypes"
          :columns="[
            {
              key: 'value',
            },
          ]"
          :find-by="['value']"
          :search="false"
          placeholder="Time"
          value-key="key"
          label-key="value"
        />
        <ZSelect
          v-if="timeType === 'calendar_year'"
          v-model="calendarYear"
          :data-source="calendarYears"
          :columns="[
            {
              key: 'value',
            },
          ]"
          :find-by="['value']"
          :search="false"
          :scroll="true"
          placeholder="Year"
          value-key="key"
          label-key="value"
        />
        <ZRangePicker v-if="timeType === 'customize'" v-model="dateRange" class="mr-4" :disabled-date="disabledDate" :max-range-date="365" :placement="Placement.BottomLeft" @clear="clearDate" />
      </div>
      <ZButton
        class="!w-full mt-4 mb-2 text-white"
        type="primary"
        html-type="submit"
        :disabled="disableButton"
        @click="typeModal += '-confirm'"
      >
        {{ $t('page.global.action.next') }}
      </ZButton>
    </template>
    <template v-else>
      <div class="page-my-api-code bg-gray-100 px-12 py-6 rounded">
        <div class="text-center text-lg mb-4">
          {{ $t('page.my.api.google_auth') }}
        </div>
        <ZCodeInput v-model="otpCode" class="relative" :length="6" />
      </div>
      <ZButton
        class="!w-full mt-4 mb-2 text-white"
        type="primary"
        html-type="submit"
        :disabled="disableSubmitButton"
        :loading="loading"
        @click="SubmitAction(type, timeFrom, timeTo, otpCode)"
      >
        {{ $t('page.global.action.confirm') }}
      </ZButton>
    </template>
  </ZModal>
</template>

<style lang="less">
.page-my-snapshots-book-modal {
  svg {
    width: 20px;
    height: 20px;

    .cls-1 {
      fill: @gray-color;
    }
  }

  &-select {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 8px;
    height: 40px;
    border: 1px solid @base-border-color;
    border-radius: 4px;
    cursor: pointer;
  }

  &-block {
    margin-bottom: 24px;
  }

  .z-input {
    height: 40px;

    .z-button {
      height: 36px;
      font-size: 14px;
      color: #fff;
      border-radius: 4px;
      padding-right: 0 !important;

      &:disabled {
        background-color: #fff !important;
        border: none !important;
      }

      span {
        align-items: center;
      }
    }
  }

  .z-button {
    height: 40px;
    font-size: 18px;
    color: #fff;

    &:disabled {
      background-color: #e9eff3 !important;
      border: 1px solid #e9eff3 !important;
    }
  }

  .z-input-error-content {
    margin-top: 4px;
    height: 14px;
    line-height: 1;
  }

  .z-search-box {
    .z-dropdown {
      &-overlay {
        width: 100%;
      }

      &-trigger {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 8px;
        height: 38px;
        border: 1px solid @base-border-color;
        border-radius: 4px;
      }
    }
  }
}
</style>
