<script setup lang="ts">
import { copyToClipboard, selectElement } from '@zsmartex/utils'
import type { APIKey } from '@zsmartex/types'

const visible = ref(false)
const apiKey = ref<APIKey>({} as APIKey)
const APIKeyKidElement = templateRef<HTMLElement>('kid')
const APIKeySecretElement = templateRef<HTMLElement>('secret')

function openModal(data: APIKey) {
  visible.value = true
  apiKey.value = data
}

const copyKid = () => {
  Message.success({
    message: $t('success'),
  })

  copyToClipboard(apiKey.value.kid)
  selectElement(APIKeyKidElement.value)
}

const copySecret = () => {
  Message.success({
    message: $t('success'),
  })

  copyToClipboard(apiKey.value.secret)
  selectElement(APIKeySecretElement.value)
}

defineExpose({
  openModal,
})
</script>

<template>
  <ZModal
    v-model="visible"
    class="page-my-save-info-api-modal"
    class-modal="!w-[480px]"
    :title="$t('layout.api.modal.title_new')"
  >
    <div class="page-my-save-info-api-modal-warn">
      {{ $t('layout.api.modal.remember_save') }}
    </div>
    <ZFormRow label="API Key">
      <div class="flex justify-between items-center bg-gray-100 p-2 rounded-md text-sm">
        <div ref="kid">
          {{ apiKey.kid }}
        </div>
        <ZIconCopyAltFilled class="cursor-pointer ml-1" @click="copyKid" />
      </div>
    </ZFormRow>
    <ZFormRow label="API Secret">
      <div class="flex justify-between items-center bg-gray-100 p-2 rounded-md text-sm">
        <div ref="secret">
          {{ apiKey.secret }}
        </div>
        <ZIconCopyAltFilled class="cursor-pointer ml-1" @click="copySecret" />
      </div>
    </ZFormRow>
    <ZButton
      class="!w-full mt-8 mb-2 text-white"
      type="primary"
      html-type="submit"
      @click="visible = false"
    >
      {{ $t('page.global.action.haved_save') }}
    </ZButton>
  </ZModal>
</template>

<style lang="less">
.page-my-save-info-api-modal {
  svg {
    width: 24px;
    height: 24px;
    fill: @gray-color;
  }

  &-warn {
    margin-bottom: 32px;
    padding: 4px 24px;
    font-size: 14px;
    color: @warn-color;
    background-color: rgba(@warn-color, 0.15);
    border: 1px solid @warn-color;
    border-radius: 4px;
  }

  .z-overlay {
    width: 500px;
  }

  .z-input {
    height: 40px;
  }

  .z-dropdown-overlay {
    width: 250px;
  }

  .z-button {
    height: 40px;
    font-size: 18px;
    color: #fff;

    &:disabled {
      background-color: #e9eff3 !important;
      border: 1px solid #e9eff3 !important;
    }
  }
}
</style>
