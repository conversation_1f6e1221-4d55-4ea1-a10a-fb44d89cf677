<script setup lang="ts">
defineProps<{
  loading: boolean
}>()

const emit = defineEmits<{
  (event: 'submit', type: string, label: string, otp_code: string, trusted_ips: string, restrictions: string[]): void
}>()
const visible = ref(false)
const type = ref('')

const label = ref('')
const trustedIPs = ref('')
const allowWithdraw = ref(false)
const otpCode = useState(() => '')

const trustedIPsError = computed(() => {
  if (allowWithdraw.value && !trustedIPs.value.length) return "input.error.length_must_greater_0"
  if (/[^0-9. ]/.test(trustedIPs.value)) return "input.error.trusted_ips"
  return ""
})

function openModal(typeModal: "create" | "update" | "delete", labelValue: string, trusted_ips: string, restrictions: string[]) {
  visible.value = true
  type.value = typeModal
  label.value = labelValue
  trustedIPs.value = trusted_ips
  if (restrictions) {
    allowWithdraw.value = restrictions.includes('withdrawal')
  }
}

const title = computed(() => {
  const myType = type.value
  if (myType === 'create' || myType === 'create-confirm') return 'Create API Key'
  if (myType === 'update' || myType === 'update-confirm') return 'Update API Key'
  return 'Delete API Key'
})

const disableButton = computed(() => {
  if (label.value.length === 0) return true
  if (trustedIPsError.value) return true
  return false
})

const disableSubmitButton = computed(() => {
  if (otpCode.value.length !== 6) return true
  return false
})

const submit = () => {
  const restrictions: string[] = [];
  if (allowWithdraw.value) restrictions.push('withdrawal')

  emit('submit', type.value, label.value, otpCode.value, trustedIPs.value, restrictions)
  visible.value = false
}

defineExpose({
  openModal,
})
</script>

<template>
  <ZModal
    v-model="visible"
    class="page-my-api-modal"
    :title="title"
  >
    <template v-if="type === 'create'">
      <div class="page-my-api-modal-row">
        <div class="page-my-api-modal-row-label">{{ $t('page.global.placeholder.label') }}</div>
        <ZInput v-model="label" class="my-2 h-16" :placeholder="$t('page.my.api.placeholder.tag')" />
      </div>
      <div class="page-my-api-modal-row">
        <div class="page-my-api-modal-row-label">Restrictions</div>
        <div>
          <ZCheckbox v-model="allowWithdraw">
            Withdraw
          </ZCheckbox>
        </div>
      </div>
      <div class="page-my-api-modal-row">
        <div class="page-my-api-modal-row-label">Trusted IPs</div>
        <div>
          <ZInput v-model="trustedIPs" class="my-2 h-16" :error="trustedIPsError" placeholder="When entering more than one IP, please separate them with spaces" />
        </div>
      </div>
      <div class="text-sm text-gray-400 text-justify mt-8 mb-16">
        {{ $t('page.my.api.notify_security') }}
      </div>
      <ZButton
        class="!w-full mt-4 mb-2 text-white"
        type="primary"
        html-type="submit"
        :disabled="disableButton"
        @click="type = 'create-confirm'"
      >
        {{ $t('page.global.action.next') }}
      </ZButton>
    </template>
    <template v-else-if="type === 'update'">
      <div class="page-my-api-modal-row">
        <div class="page-my-api-modal-row-label">{{ $t('page.global.placeholder.label') }}</div>
        <ZInput v-model="label" class="my-2 h-16" />
      </div>
      <div class="page-my-api-modal-row">
        <div class="page-my-api-modal-row-label">Restrictions</div>
        <div>
          <ZCheckbox v-model="allowWithdraw">
            Withdraw
          </ZCheckbox>
        </div>
      </div>
      <div class="page-my-api-modal-row">
        <div class="page-my-api-modal-row-label">Trusted IPs</div>
        <div>
          <ZInput v-model="trustedIPs" class="my-2 h-16" :error="trustedIPsError" placeholder="When entering more than one IP, please separate them with spaces" />
        </div>
      </div>
      <div class="text-sm text-gray-400 text-justify mt-8 mb-16">
        {{ $t('page.my.api.notify_security') }}
      </div>
      <ZButton
        class="!w-full mt-4 mb-2 text-white"
        type="primary"
        html-type="submit"
        :disabled="disableButton"
        @click="type = 'update-confirm'"
      >
        {{ $t('page.global.action.next') }}
      </ZButton>
    </template>
    <template v-else>
      <div class="page-my-api-code bg-gray-100 px-12 py-6 rounded">
        <div class="text-center text-lg mb-4">
          {{ $t('page.my.api.google_auth') }}
        </div>
        <ZCodeInput v-model="otpCode" class="relative" :length="6" />
      </div>
      <ZButton
        class="!w-full mt-4 mb-2 text-white"
        type="primary"
        html-type="submit"
        :disabled="disableSubmitButton"
        :loading="loading"
        @click="submit"
      >
        {{ $t('page.global.action.confirm') }}
      </ZButton>
    </template>
  </ZModal>
</template>

<style lang="less">
.page-my-api-modal {
  .z-overlay {
    width: 500px;
  }

  &-row {
    display: flex;
    flex-direction: column;
    margin-bottom: 16px;
    gap: 8px;

    &-label {
      font-size: 14px;
      color: @gray-color;
    }

    .z-checkbox {
      cursor: pointer;
      user-select: none;
    }
  }

  .z-input {
    height: 40px;
  }

  .z-dropdown-overlay {
    width: 250px;
  }

  .z-button {
    height: 40px;
    font-size: 18px;
    color: #fff;

    &:disabled {
      background-color: #e9eff3 !important;
      border: 1px solid #e9eff3 !important;
    }
  }
}
</style>
