<script setup lang="ts">
import type { Advertisement } from '@zsmartex/types'
import getSymbolFromCurrency from 'currency-symbol-map'
import { roundNumber } from '@zsmartex/utils'

const props = defineProps<{
  advertisement: Advertisement
  side: string
}>()

const visible = ref(false)
const loading = ref(false)
const userStore = useUserStore()
const ads = ref<Advertisement>(props.advertisement)
const deleteConfirm = ref(false)

onMounted(() => {
  const instance = getCurrentInstance()
  onClickOutside(instance?.proxy?.$el, () => {
    visible.value = false
  })
})

const ChangeState = async () => {
  loading.value = true
  const state = ads.value.state === AdvertisementState.Enabled ? AdvertisementState.Disabled : AdvertisementState.Enabled

  await userStore.UpdateAdvertisement({
    id: ads.value.id,
    state,
  }, () => {
    ads.value.state = state
  })

  loading.value = false
}

const DeleteAdvertisement = async () => {
  loading.value = true

  await userStore.DeleteAdvertisement(ads.value.id)

  loading.value = false
}
</script>

<template>
  <div class="layout-advertisement">
    <div class="layout-advertisement-row">
      <div class="layout-advertisement-row-currency flex items-center">
        <span class="text-base bold-text mr-2">{{ ads.coin_currency.toUpperCase() }}</span>
      </div>
      <div class="layout-advertisement-row-currency flex items-center">
        <span class="text-base bold-text mr-2">{{ ads.fiat_currency.toUpperCase() }}</span>
      </div>
      <div class="layout-advertisement-row-price flex items-center">
        <span class="text-xl bold-text mr-2">{{ roundNumber(ads.price, 2) }}</span>
        <span class="text-[12px] bold-text mt-[6px]">{{ ads.fiat_currency.toUpperCase() }}</span>
      </div>
      <div class="layout-advertisement-row-limit flex flex-col justify-between">
        <div>
          <span class="text-gray mr-2">{{ $t('page.global.table.available') }}</span>
          <span class="bold-text">{{ `${roundNumber(ads.available_amount, 2)} ${ads.coin_currency.toUpperCase()}` }}</span>
        </div>
        <div>
          <span class="text-gray mr-2">{{ $t('page.global.placeholder.limit') }}</span>
          <span class="bold-text">{{ `${roundNumber(ads.min, 2)} ${getSymbolFromCurrency(ads.fiat_currency.toUpperCase())}` }} - {{ `${roundNumber(ads.max, 2)} ${getSymbolFromCurrency(ads.fiat_currency.toUpperCase())}` }}</span>
        </div>
      </div>
      <div class="layout-advertisement-row-payments">
        <div v-for="payment in ads.payments" :key="payment.id">
          <ZTooltip :title="payment.type">
            {{ payment.type }}
          </ZTooltip>
        </div>
      </div>
      <div class="layout-advertisement-row-state flex items-center">
        <span
          v-if="ads.state === AdvertisementState.Banned"
          class="capitalize mr-2"
          :class="[
            { 'text-red-500': ads.state === AdvertisementState.Banned },
          ]"
        >
          {{ ads.state }}
        </span>
        <ZSwitch v-else :model-value="ads.state === AdvertisementState.Enabled ? true : false" size="medium" :loading="loading" @click="ChangeState" />
      </div>
      <div class="layout-advertisement-row-button flex justify-end items-center">
        <!-- <ZButton
          @click="visible = true"
        >
          {{ $t('page.global.action.delete') }}
        </ZButton> -->

        <ZPopconfirm v-model="deleteConfirm" :placement="Placement.TopRight" trigger="click" @click="DeleteAdvertisement()">
          <ZIconTimesRegular />
        </ZPopconfirm>
      </div>
    </div>
  </div>
</template>

<style lang="less">
.layout-advertisement-row {
  display: flex;
  padding: 24px 0;
  height: 92px;
  border-top: 1px solid @base-border-color;

  &-button {
    svg {
      width: 20px;
      height: 20px;
      fill: @gray-color;
      cursor: pointer;
    }
  }

  &-currency {
    font-size: 14px;
  }

  & > div {
    flex: 1;
  }

  &-payments {
    display: flex;
    flex-wrap: wrap;
    max-width: 280px;

    & > div {
      margin-bottom: 6px;
      padding: 2px 6px;
      height: fit-content;
      line-height: normal;
      background-color: rgba(@gray-color, 0.1);
      border-radius: 4px;
      color: @primary-color;
      cursor: pointer;

      & ~ div {
        margin-left: 6px;
      }
    }
  }
}
</style>
