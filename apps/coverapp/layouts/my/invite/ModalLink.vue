<script setup lang="ts">
import type { InviteLink } from '@zsmartex/types'
const emit = defineEmits<{
  (event: 'submit', invite_link: InviteLink): void
}>()
const visible = ref(false)

function openModal() {
  visible.value = true
}

const userStore = useUserStore()
const loading = useState(() => false)
const commissionRebate = useState(() => '0.2')
const cashRebate = useState(() => '0')
const isDefault = useState(() => false)

const rebates = [
  {
    text: '0%',
    value: '0',
  },
  {
    text: '5%',
    value: '0.05',
  },
  {
    text: '10%',
    value: '0.1',
  },
  {
    text: '15%',
    value: '0.15',
  },
  {
    text: '20%',
    value: '0.2',
  },
]

const changeRebate = (value: string, type: string) => {
  if (type === 'commission') {
    cashRebate.value = String(0.2 - Number(value)).slice(0, 4)
  } else {
    commissionRebate.value = String(0.2 - Number(value)).slice(0, 4)
  }
}

const CreateInviteLink = async () => {
  loading.value = true

  const inviteLink = await userStore.CreateInviteLink({
    commission_rebate: Number(commissionRebate.value),
    cash_rebate: Number(cashRebate.value),
    default: isDefault.value,
  })

  if (Object.keys(inviteLink).length > 0) {
    visible.value = false
    emit('submit', inviteLink)
  }

  loading.value = false
}

defineExpose({
  openModal,
})
</script>

<template>
  <ZModal
    v-model="visible"
    class="page-my-invite-link"
    :title="$t('page.my.invite.generate_link')"
  >
    <div class="pb-4">
      <span class="text-base">{{ $t('page.my.invite.spot') }}</span>
      <div class="flex items-center mt-3">
        <div class="flex-1">
          <div class="mb-1 text-xs page-my-invite-link-text">
            {{ $t('page.my.invite.commission_rebate') }}
          </div>
          <ZSelect
            v-model="commissionRebate"
            :data-source="rebates"
            :columns="[
              {
                key: 'text',
              },
            ]"
            :search="false"
            :scroll="true"
            :find-by="['text']"
            value-key="value"
            label-key="text"
            @change="(item: string) => changeRebate(item, 'commission')"
          />
        </div>
        <ZIconArrowRepeatDuotone />
        <div class="flex-1">
          <div class="mb-1 text-xs page-my-invite-link-text">
            {{ $t('page.my.invite.cash_rebate') }}
          </div>
          <ZSelect
            v-model="cashRebate"
            :data-source="rebates"
            :columns="[
              {
                key: 'text',
              },
            ]"
            :search="false"
            :scroll="true"
            :find-by="['text']"
            value-key="value"
            label-key="text"
            @change="(item: string) => changeRebate(item, 'cash')"
          />
        </div>
      </div>
    </div>
    <div class="flex justify-between py-2 select-none">
      <span>{{ $t('page.my.invite.set_default') }}</span>
      <ZCheckbox v-model="isDefault" />
    </div>
    <ZButton
      class="!w-full mt-4 mb-2 text-white rounded"
      type="primary"
      html-type="submit"
      :loading="loading"
      @click="CreateInviteLink"
    >
      {{ $t('page.global.action.generate_link') }}
    </ZButton>
  </ZModal>
</template>

<style lang="less">
.page-my-invite-link {
  svg {
    margin: 0 8px;
    margin-top: 20px;
    width: 24px;
    height: 24px;

    .cls-1 {
      fill: @gray-color;
    }

    .cls-2 {
      fill: @text-color;
    }
  }

  &-text {
    @media @tablet {
      font-size: 14px;
    }
  }

  .z-overlay {
    width: 500px;
  }

  .z-input {
    height: 40px;
  }

  .z-dropdown-overlay {
    width: 250px;
  }

  .z-button {
    height: 36px;
    font-size: 16px;
    color: #fff;

    &:disabled {
      background-color: #e9eff3 !important;
      border: 1px solid #e9eff3 !important;
    }
  }

  .z-checkbox {
    &-inner {
      @media @tablet {
        width: 20px;
        height: 20px;
      }
    }
  }

  .z-dropdown {
    &-trigger {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 12px;
      width: 100%;
      height: 40px;
      border: 1px solid rgba(33, 47, 79, 0.1);
      border-radius: 4px;
      transition: all 0.3s;
      &:hover {
        border: 1px solid @primary-color;
      }
    }

    &-triggered {
      border: 1px solid @primary-color;
      box-shadow: 0 0 0 2px rgba(@primary-color, 0.2);
    }
  }
}
</style>
