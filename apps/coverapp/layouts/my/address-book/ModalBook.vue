<script setup lang="ts">
import type { ZTableColumn } from '@zsmartex/components/types'
import type { Currency } from '@zsmartex/types'
import ModalNetwork from './ModalNetwork.vue'
import Validate from '~/validation/validate'
import { memoList } from '~/constants/index'
import type But<PERSON> from '#components/Button.vue'

const emit = defineEmits<{
  (event: 'submit', label: string, otp_code: string, withdraw_address: string, blockchain_key: string, currency_id: string): void
}>()
const visible = ref(false)
const type = ref('')
const publicStore = usePublicStore()
const userStore = useUserStore()

const modalNetwork = ref<InstanceType<typeof ModalNetwork>>()
const delayButtonEmail = ref<InstanceType<typeof Button>>()

const currencies = computed(() => {
  return publicStore.currencies.filter(c => c.networks.some(n => n.status === CurrencyNetworkStatus.Active))
})

function openModal(typeModal: string) {
  visible.value = true
  type.value = typeModal
  label.value = ''
  withdrawAddress.value = ''
  emailCode.value = ''
  otpCode.value = ''
}

const loading = ref(false)
const label = ref('')
const currencyID = ref(currencies.value[0]?.id as string)
const withdrawAddress = ref('')
const memo = ref('')
const withdrawAddressError = ref('')
const emailCode = ref('')
const otpCode = ref('')
const searchBoxVisible = ref(false)

const title = computed(() => {
  const myType = type.value
  if (myType === 'create' || myType === 'create-confirm') return 'Add Withdrawal Address'
  return 'Delete Withdrawal Address'
})

const emailCodeError = computed(() => {
  if (emailCode.value.length && emailCode.value.length !== 6) {
    return $t('page.global.error.min_length', { min: 6 })
  }
})

const BookModalSelectColumns: ZTableColumn[] = [
  {
    key: 'id',
    scopedSlots: true,
  },
]

const disableButton = computed(() => {
  if (label.value.length === 0) return true
  if (withdrawAddress.value.length === 0 || withdrawAddressError.value.length > 0) return true
  if (blockchainKey.value.length === 0) return true
  return false
})

const disableSubmitButton = computed(() => {
  if (emailCode.value.length !== 6) return true
  if (otpCode.value.length !== 6) return true
  return false
})

function onSearchBoxClicked(item: Currency) {
  searchBoxVisible.value = false
  currencyID.value = item.id
  blockchainKey.value = ''
}

const currencyNetworks = computed(() => {
  const networks = publicStore.getWithdrawalEnabledNetworks(currencyID.value)

  return networks
})

function onClickNetwork(key: string) {
  blockchainKey.value = key
}

const currency = computed(() => {
  return publicStore.currencies.find(currency => currency.id === currencyID.value) as Currency
})

const blockchainKey = ref<string>(currencyNetworks.value[0]?.blockchain_key)

const network = computed(() => {
  if (!blockchainKey.value) return

  return currency.value.networks.find(network => network.blockchain_key === unref(blockchainKey))!
})

const networkName = computed(() => {
  if (!blockchainKey.value) return

  const currency = publicStore.currencies.find(c => c.id === currencyID.value)!
  const network = currency.networks.find(c => c.blockchain_key === blockchainKey.value)!

  return `${network.protocol_name} (${network.protocol})`
})

watch([withdrawAddress, network], async () => {
  if (!network.value) return

  const passedValidate = await Validate.address(withdrawAddress.value, currency.value.id, network.value.client)

  withdrawAddressError.value = passedValidate ? '' : 'input.error.address_format'
})

function GenerateEmailCode() {
  userStore.GenerateCreateBeneficiaryCode({
    type: 'email',
    blockchain_key: blockchainKey.value,
    currency_id: currencyID.value,
    data: {
      address: memo.value ? `${withdrawAddress.value}?memo=${memo.value}` : withdrawAddress.value,
    },
  }, delayButtonEmail.value?.StartDelay)
}

async function SubmitAction(label: string, otpCode: string, address: string, blockchainKey: string, currencyID: string) {
  loading.value = true

  if (type.value === 'create-confirm') {
    try {
      await userStore.CreateBeneficiary({
        label,
        email_code: emailCode.value,
        otp_code: otpCode,
        data: {
          address: memo.value ? `${address}?memo=${memo.value}` : address,
        },
        blockchain_key: blockchainKey,
        currency_id: currencyID,
      })
    } catch (error) {
      return
    } finally {
      loading.value = false
    }
  }

  loading.value = false
  visible.value = false
}

defineExpose({
  openModal,
})
</script>

<template>
  <ZModal
    v-model="visible"
    class="page-my-address-book-modal"
    :title="title"
  >
    <template v-if="type === 'create'">
      <div class="page-my-address-book-modal-block">
        <span class="text-base">{{ $t('page.global.placeholder.label') }}</span>
        <ZInput v-model="label" class="my-2 h-16" :placeholder="$t('page.my.api.placeholder.tag')" />
      </div>
      <div class="page-my-address-book-modal-block mb-2">
        <span class="text-base mb-2 block">{{ $t('page.global.placeholder.select_asset') }}</span>
        <ZSearchBox
          v-model="searchBoxVisible"
          :data-source="currencies"
          :columns="BookModalSelectColumns"
          :find-by="['id']"
          @click="onSearchBoxClicked"
        >
          <div class="flex items-center">
            <img
              class="mr-1 h-6 rounded-full"
              :src="publicStore.getCurrencyByID(currencyID)?.icon_url"
            >
            {{ currencyID.toUpperCase() }}
          </div>
          <ZIconAngleDownFilled />
          <template #id="{ item }">
            <img
              class="mr-2 h-6 rounded-full"
              :src="publicStore.getCurrencyByID(item.id)?.icon_url"
            >
            {{ item.id.toUpperCase() }} {{ item.name }}
          </template>
        </ZSearchBox>
      </div>
      <div class="page-my-address-book-modal-block">
        <span class="text-base mb-2 block">{{ $t('page.global.placeholder.network') }}</span>
        <div class="page-my-address-book-modal-select" @click="modalNetwork?.openModal(currencyNetworks)">
          <span :class="{ 'text-gray-400': !blockchainKey }">
            {{ networkName || 'Select Network' }}
          </span>
          <ZIconAngleDownFilled />
        </div>
      </div>
      <div class="page-my-address-book-modal-block">
        <span class="text-base mb-2 block">{{ $t('page.global.placeholder.withdraw_address') }}</span>
        <ZInput v-model="withdrawAddress" :error="withdrawAddressError" class="h-16" :placeholder="$t('page.global.placeholder.address')" :trim-space="true" />
      </div>
      <div v-if="network && memoList.includes(network.client)" class="page-my-address-book-modal-block">
        <span class="text-base mb-2 block">{{ $t('page.global.placeholder.memo') }}</span>
        <ZInput v-model="memo" class="h-16" :placeholder="$t('page.global.placeholder.memo')" :trim-space="true" />
      </div>
      <ZButton
        class="!w-full mt-4 mb-2 text-white"
        type="primary"
        html-type="submit"
        :disabled="disableButton"
        @click="type = `${type}-confirm`"
      >
        {{ $t('page.global.action.next') }}
      </ZButton>
    </template>
    <template v-else>
      <div v-if="userStore.hasEmail">
        <div class="text-center text-lg mb-2">
          {{ $t('page.global.placeholder.email_code') }}
        </div>
        <ZInput
          v-model="emailCode"
          name="email_code"
          :type="InputType.Number"
          :placeholder="$t('page.global.placeholder.e-confirmation_code')"
          :max-length="6"
          class="px-2 h-32 bg-white mb-4"
          :required="true"
          :error="emailCodeError"
        >
          <template #suffix>
            <ZButton
              ref="delayButtonEmail"
              :delay="{
                time: 60,
                content: 'Get [#{time}] again',
              }"
              @click="GenerateEmailCode"
            >
              {{ $t('page.global.action.get_code') }}
            </ZButton>
          </template>
        </ZInput>
      </div>
      <div class="page-my-api-code bg-gray-100 px-12 py-6 rounded">
        <div class="text-center text-lg mb-4">
          {{ $t('page.my.api.google_auth') }}
        </div>
        <ZCodeInput v-model="otpCode" class="relative" :length="6" />
      </div>
      <ZButton
        class="!w-full mt-4 mb-2 text-white"
        type="primary"
        html-type="submit"
        :disabled="disableSubmitButton"
        :loading="loading"
        @click="SubmitAction(label, otpCode, withdrawAddress, blockchainKey, currencyID)"
      >
        {{ $t('page.global.action.confirm') }}
      </ZButton>
    </template>
  </ZModal>
  <ModalNetwork ref="modalNetwork" @click="onClickNetwork" />
</template>

<style lang="less">
.page-my-address-book-modal {
  svg {
    width: 20px;
    height: 20px;

    .cls-1 {
      fill: @gray-color;
    }
  }

  &-select {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 8px;
    height: 40px;
    border: 1px solid @base-border-color;
    border-radius: 4px;
    cursor: pointer;
  }

  &-block {
    margin-bottom: 24px;
  }

  .z-overlay {
    width: 500px;
  }

  .z-input {
    height: 40px;

    .z-button {
      height: 36px;
      font-size: 14px;
      color: #fff;
      border-radius: 4px;
      padding-right: 0 !important;

      &:disabled {
        background-color: #fff !important;
        border: none !important;
      }

      span {
        align-items: center;
      }
    }
  }

  .z-dropdown-overlay {
    width: 250px;
  }

  .z-button {
    height: 40px;
    font-size: 18px;
    color: #fff;

    &:disabled {
      background-color: #e9eff3 !important;
      border: 1px solid #e9eff3 !important;
    }
  }

  .z-input-error-content {
    margin-top: 4px;
    height: 14px;
    line-height: 1;
  }

  .z-search-box {
    .z-dropdown {
      &-overlay {
        width: 100%;
      }

      &-trigger {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 8px;
        height: 38px;
        border: 1px solid @base-border-color;
        border-radius: 4px;
      }
    }
  }
}
</style>
