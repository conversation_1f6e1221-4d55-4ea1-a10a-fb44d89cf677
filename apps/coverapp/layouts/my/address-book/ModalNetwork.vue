<script setup lang="ts">
import type { ZTableColumn } from '@zsmartex/components/types'
import type { CurrencyNetwork } from '@zsmartex/types'

const emit = defineEmits<{
  (event: 'click', blockchain_key: string): void
}>()
const visible = ref(false)
const network = ref<CurrencyNetwork[]>([])

const publicStore = usePublicStore()

function openModal(data: CurrencyNetwork[]) {
  visible.value = true
  network.value = data
}

const columns: ZTableColumn[] = [
  {
    key: 'name',
    scopedSlots: true,
  },
  {
    key: 'fee',
    align: Align.Right,
    scopedSlots: true,
  },
]

function currencyNetwork(currency_id: string) {
  return publicStore.currencies.find(item => item.id === currency_id)
}

function currencyFee(network: CurrencyNetwork) {
  if (!network.use_parent_fee || !network.parent_id) return network.currency_id.toUpperCase()
  return network.parent_id.toUpperCase()
}

function onClick(item: CurrencyNetwork) {
  visible.value = false
  emit('click', item.blockchain_key)
}

defineExpose({
  openModal,
})
</script>

<template>
  <ZModal
    v-model="visible"
    class="page-my-address-book-network-modal"
    :title="$t('layout.my.address_book.network.title')"
  >
    {{ $t('layout.my.address_book.network.note') }}
    <ZTable
      class="mt-4"
      :columns="columns"
      :data-source="network"
      :head-enabled="false"
      :hover="true"
      @click="onClick"
    >
      <template #name="{ item }">
        {{ `${item.protocol_name} (${item.protocol})` }}
      </template>
      <template #fee="{ item }">
        {{ `${$t('layout.my.address_book.network.fee')} ${item.withdraw_fee_percentage ? `${Number(item.withdraw_fee) * 100}%` : item.withdraw_fee} ${currencyFee(item)}` }}
      </template>
    </ZTable>
  </ZModal>
</template>

<style lang="less">
.page-my-address-book-network-modal {
  &-select {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 8px;
    height: 40px;
    border: 1px solid @base-border-color;
    border-radius: 4px;
    cursor: pointer;
  }

  .z-overlay {
    width: 500px;
  }

  .z-input {
    height: 40px;
  }

  .z-dropdown-overlay {
    width: 250px;
  }

  .z-button {
    height: 40px;
    font-size: 18px;
    color: #fff;

    &:disabled {
      background-color: #e9eff3 !important;
      border: 1px solid #e9eff3 !important;
    }
  }

  .z-table {
    &-row {
      height: 64px;
    }
  }

  .z-search-box {
    .z-dropdown {
      &-overlay {
        width: 100%;
      }

      &-trigger {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 8px;
        height: 38px;
        border: 1px solid @base-border-color;
        border-radius: 4px;
      }
    }
  }
}
</style>
