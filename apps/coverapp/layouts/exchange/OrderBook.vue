<script setup lang="ts">
import OrderBookSideSwitch from './order-book/SideSwitch.vue'
import OrderBookSide from './order-book/Side.vue'
import OrderBookTicker from './order-book/Ticker.vue'
import BookSide from './order-book/BookSide.vue'
import { generateSpreadList } from '~/library/orderbook-table/decimal-spread';
import { roundNumber } from '@zsmartex/utils';

const side = ref('avg')
const rowCount = ref(200)

const depthStore = useDepthStore()
const tradeStore = useTradeStore()

const selectedDecimalFilter = ref<string>()

function toNearestPowerOfTen(num: number) {
  const exponent = Math.floor(Math.log10(num));
  return Number((Math.pow(10, exponent)).toFixed(10));
}

await useAsyncData(async () => {
  const data = await depthStore.FetchDepth(tradeStore.market.id, 500)

  depthStore.ProcessDepth(tradeStore.market.id, data)
  return {}
})

const filters = computed(() => {
  const highestAskPrice = depthStore.sell[0]?.price || 0
  return generateSpreadList(tradeStore.market.price_precision, toNearestPowerOfTen(highestAskPrice))
})

if (filters.value.length > 0) {
  selectedDecimalFilter.value = filters.value[0]
  depthStore.decimalRound = Math.abs(Math.log10(Number(selectedDecimalFilter.value)))
} else {
  depthStore.decimalRound = tradeStore.market.price_precision
}

watch(selectedDecimalFilter, () => {
  if (selectedDecimalFilter.value === undefined) {
    depthStore.decimalRound = tradeStore.market.price_precision
  } else {
    depthStore.decimalRound = Math.abs(Math.log10(Number(selectedDecimalFilter.value)))
  }
})
</script>

<template>
  <ZCard class="page-exchange-orderbook" :class="[side]">
    <template #head>
      <div class="flex justify-between items-center w-full">
        <OrderBookSideSwitch v-model="side" />
        <ZDropdown v-if='selectedDecimalFilter && filters.length' trigger="click">
          {{ roundNumber(selectedDecimalFilter, tradeStore.market.price_precision, true) }}
          <template #overlay>
            <ZMenu>
              <ZMenuItem
                v-for="(value, index) in filters"
                :key="index"
                class="text-center"
                @click="selectedDecimalFilter = value"
              >
                {{ roundNumber(value, tradeStore.market.price_precision, true) }}
              </ZMenuItem>
            </ZMenu>
          </template>
        </ZDropdown>
      </div>
    </template>
    <div class="z-table z-table-headable">
      <div class="z-table-head">
        <span class="price text-left">{{ $t("page.global.table.price") }} ({{ tradeStore.market.quote_unit.toUpperCase() }})</span>
        <span class="amount text-right">{{ $t("page.global.table.amount") }} ({{ tradeStore.market.base_unit.toUpperCase() }})</span>
        <span class="sum text-right">{{ $t("page.global.table.sum") }} ({{ tradeStore.market.quote_unit.toUpperCase() }})</span>
      </div>
      <div v-if="side === 'sell'" class="z-table-content" :class="[side]">
        <OrderBookSide :type="side" :side="OrderSide.Sell" :row-count="rowCount" :loading="depthStore.loading" />
        <OrderBookTicker />
      </div>
      <div v-else-if="side === 'avg'" class="z-table-content" :class="[side]">
        <OrderBookSide :type="side" :side="OrderSide.Sell" :row-count="rowCount" :loading="depthStore.loading" />
        <OrderBookTicker />
        <OrderBookSide :type="side" :side="OrderSide.Buy" :row-count="rowCount" :loading="depthStore.loading" />
      </div>
      <div v-else class="z-table-content" :class="[side]">
        <OrderBookTicker />
        <OrderBookSide :type="side" :side="OrderSide.Buy" :row-count="rowCount" :loading="depthStore.loading" />
      </div>
    </div>
  </ZCard>
  <div class="page-exchange-orderbook-mobile">
    <div class="flex">
      <div class="flex-initial w-[24px] text-[#9eaed6] py-[0.5rem] text-xs">
        {{ $t('layout.exchange.order_book.bid') }}
      </div>
      <div class="flex-1 text-[#9eaed6] py-[0.5rem] text-xs">
        {{ $t('layout.exchange.order_book.volume', { currency: depthStore.market.base_unit.toUpperCase() }) }}
      </div>
      <div class="flex-1 text-[#9eaed6] py-[0.5rem] text-xs text-center">
        {{ $t('layout.exchange.order_book.price', { currency: depthStore.market.quote_unit.toUpperCase() }) }}
      </div>
      <div class="flex-1 text-[#9eaed6] py-[0.5rem] text-xs text-right">
        {{ $t('layout.exchange.order_book.volume', { currency: depthStore.market.base_unit.toUpperCase() }) }}
      </div>
      <div class="flex-initial w-[24px] text-[#9eaed6] py-[0.5rem] text-xs text-right">
        {{ $t('layout.exchange.order_book.ask') }}
      </div>
    </div>
    <div class="flex">
      <div class="flex-1">
        <BookSide :side="OrderSide.Buy" :data-source="depthStore.buy" :index="true" :count="15" reverse-column />
      </div>
      <div class="flex-1">
        <BookSide :side="OrderSide.Sell" :data-source="depthStore.sell" :index="true" :count="15" />
      </div>
    </div>
  </div>
</template>

<style lang="less">
@orderbook-ticker-height: 40px;

.page-exchange-orderbook {
  position: relative;

  @media @mobile {
    display: none;
  }

  &-mobile {
    display: none;
    background-color: @exchange-card-background;
    padding: 0 16px;

    @media @mobile {
      display: block;
    }
  }

  .z-table-content {
    height: calc(100% - 32px);
  }

  &-ticker {
    display: flex;
    height: @orderbook-ticker-height;
    line-height: @orderbook-ticker-height;
    padding: 0 16px;
    border-top: 1px solid;
    border-bottom: 1px solid;
    border-color: @exchange-border-color;
    user-select: none;
    justify-content: space-between;

    &-last {
      font-size: 18px;
    }

    &-change {
      font-size: 14px;
    }
  }

  .z-table-content.avg {
    .page-exchange-orderbook-side {
      height: calc(50% - @orderbook-ticker-height / 2);
    }
  }

  .z-table-content.buy {
    .page-exchange-orderbook-side {
      height: calc(100% - @orderbook-ticker-height);
    }
  }

  .z-table-content.sell {
    .page-exchange-orderbook-side {
      height: calc(100% - @orderbook-ticker-height);
    }
  }

  // &.avg &-side {
  //   height: calc(50% - @orderbook-ticker-height / 2);
  // }

  // &.buy &-side {
  //   height: 0;

  //   &-buy {
  //     height: calc(100% - @orderbook-ticker-height);
  //   }
  // }

  // &.sell &-side {
  //   height: 0;

  //   &-sell {
  //     height: calc(100% - @orderbook-ticker-height);
  //   }
  // }

  &-side {
    position: relative;
    transition: height .5s;
    overflow: scroll;

    &-switch {
      display: flex;

      &-item {
        width: 24px;
        height: 24px;
        border: 1px solid @base-border-color;
        border-radius: 3px;
        margin-right: 4px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all .15s;

        &-selected, &:hover {
          border-color: @primary-color;
        }
      }
    }
  }

  &.avg {
    .page-exchange-orderbook-side {
      position: relative;
      transition: height .5s;
      overflow: hidden;
    }
  }

  &-overlay {
    &-mask {
      background-color: @dropdown-background;
    }

    &-item {
      display: flex;
      width: 100%;
      justify-content: space-between;
      font-size: 14px;
      line-height: 24px;
      font-weight: 500;
      padding: 4px 0;
      align-items: center;
    }

    &-content {
      position: fixed;
      transform: translateX(-101%) translateY(-50%);
      width: 320px;
      padding: 8px;
      background-color: @active-background-color;
      border-radius: 4px;
      z-index: 2;
    }
  }

  &-hover {
    position: relative;

    &-item {
      cursor: pointer;
      width: 100%;
      height: 20px;
    }

    &-buy &-item {
      &:hover {
        border-bottom: 1px solid @base-border-color;
      }
    }

    &-sell &-item {
      &:hover {
        border-top: 1px solid @base-border-color;
      }
    }
  }
}
</style>
