<script setup lang="ts">
import { classChangePercent, roundNumber } from '@zsmartex/utils'
import ExchangeTradePairs from '~/layouts/exchange/TradePairs.vue'

const emits = defineEmits<{
  (event: 'click'): void
}>()

const publicStore = usePublicStore()
const tradeStore = useTradeStore()
const market = computed(() => tradeStore.market)
const ticker = computed(() => tradeStore.ticker)
const visible = ref(false)

const tickers = computed(() => {
  return publicStore.enabledTickers.filter(t => t.market.base_unit === market.value.base_unit)
})

const onClick = (ticker: Ticker) => {
  navigateTo(`/exchange/${ticker.market.base_unit.toUpperCase()}-${ticker.market.quote_unit.toUpperCase()}?type=${tradeStore.exchange_layout}`)
}
</script>

<template>
  <ZCard class="page-exchange-ticker">
    <div class="page-exchange-ticker-name flex items-center" @click="visible = !visible">
      <ZDropdown v-if="tickers.length" :placement="Placement.BottomLeft" trigger="click">
        <div class="flex items-center">
          <h1 class="page-exchange-ticker-name-span">{{ market.name }}</h1>
          <ZIcon type="arrow-down" class="mt-[4px]" />
        </div>
        <template #overlay>
          <div class="page-exchange-ticker-relevant">
            <div class="page-exchange-ticker-relevant-head">
              Relevant markets
            </div>
            <div class="page-exchange-ticker-relevant-spot">
              Spot
            </div>
            <div class="page-exchange-ticker-relevant-content">
              <div v-for="ticker in tickers" class="page-exchange-ticker-relevant-item flex justify-between items-center" @click="onClick(ticker)">
                <div>
                  {{ ticker.market.name }}
                </div>
                <div class="flex items-center">
                  <div class="mr-1" :class="[classChangePercent(ticker.price_change_percent)]">
                    {{ ticker.last }}
                  </div>
                  <div class="py-[2px] px-2 page-exchange-ticker-relevant-item-percent" :class="[classChangePercent(ticker.price_change_percent)]">
                    {{ ticker.price_change_percent }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </template>
      </ZDropdown>
      <!-- <ExchangeTradePairs v-if="visible" /> -->
    </div>
    <div class="page-exchange-ticker-last" :class="[classChangePercent(ticker.price_change_percent)]">
      {{ roundNumber(ticker.last, market.price_precision) }}
    </div>
    <div class="page-exchange-ticker-change page-exchange-ticker-col">
      <div class="page-exchange-ticker-col-title">
        {{ $t('page.exchange.ticker.24h_change') }}
      </div>
      <span class="page-exchange-ticker-col-value" :class="[classChangePercent(ticker.price_change_percent)]">
        {{ ticker.price_change_percent }}
      </span>
    </div>
    <div class="page-exchange-ticker-change page-exchange-ticker-col">
      <div class="page-exchange-ticker-col-title">
        {{ $t('page.exchange.ticker.24h_high') }}
      </div>
      {{ roundNumber(ticker.high, market.price_precision) }}
    </div>
    <div class="page-exchange-ticker-change page-exchange-ticker-col">
      <div class="page-exchange-ticker-col-title">
        {{ $t('page.exchange.ticker.24h_low') }}
      </div>
      {{ roundNumber(ticker.low, market.price_precision) }}
    </div>
    <div class="page-exchange-ticker-change page-exchange-ticker-col">
      <div class="page-exchange-ticker-col-title">
        {{ $t('page.exchange.ticker.24h_amount') }}
      </div>
      {{ roundNumber(ticker.amount, 2) }} {{ market.base_unit.toUpperCase() }}
    </div>
    <div class="page-exchange-ticker-change page-exchange-ticker-col">
      <div class="page-exchange-ticker-col-title">
        {{ $t('page.exchange.ticker.24h_volume') }}
      </div>
      {{ roundNumber(ticker.volume, 2) }} {{ market.quote_unit.toUpperCase() }}
    </div>
  </ZCard>
  <ZCard class="page-exchange-ticker page-exchange-ticker-mobile">
    <div class="mr-[60px]" @click="emits('click')">
      <div class="page-exchange-ticker-name">
        {{ market.name }}
      </div>
      <div class="page-exchange-ticker-last" :class="[classChangePercent(ticker.price_change_percent)]">
        {{ roundNumber(ticker.last, market.price_precision) }}
      </div>
      <div class="page-exchange-ticker-change page-exchange-ticker-col">
        <span class="page-exchange-ticker-col-value" :class="[classChangePercent(ticker.price_change_percent)]">
          {{ ticker.price_change_percent }}
        </span>
      </div>
    </div>
    <div class="flex flex-wrap page-exchange-ticker-mobile-right">
      <div class="page-exchange-ticker-change page-exchange-ticker-col w-6/12 mb-2">
        <div class="page-exchange-ticker-col-title">
          {{ $t('page.exchange.ticker.24h_high') }}
        </div>
        {{ roundNumber(ticker.high, market.price_precision) }}
      </div>
      <div class="page-exchange-ticker-change page-exchange-ticker-col w-6/12 mb-2">
        <div class="page-exchange-ticker-col-title">
          {{ $t('page.exchange.ticker.24h_amount') }}
        </div>
        {{ roundNumber(ticker.amount, 2) }} {{ market.base_unit.toUpperCase() }}
      </div>
      <div class="page-exchange-ticker-change page-exchange-ticker-col w-6/12">
        <div class="page-exchange-ticker-col-title">
          {{ $t('page.exchange.ticker.24h_low') }}
        </div>
        {{ roundNumber(ticker.low, market.price_precision) }}
      </div>
      <div class="page-exchange-ticker-change page-exchange-ticker-col w-6/12">
        <div class="page-exchange-ticker-col-title">
          {{ $t('page.exchange.ticker.24h_volume') }}
        </div>
        {{ roundNumber(ticker.volume, 2) }} {{ market.quote_unit.toUpperCase() }}
      </div>
    </div>
  </ZCard>
</template>

<style lang="less">
.page-exchange-ticker {
  @media @mobile {
    display: none;
  }

  .page-exchange-trade-pairs {
    @media @large-desktop, @desktop {
      display: none;
    }
  }

  &-relevant {
    padding: 12px 0;
    width: 300px;
    background-color: @exchange-border-color;
    border-radius: 12px;

    &-spot {
      padding: 8px 12px;
      background-image: linear-gradient(to right, rgba(@primary-color, 0.15) , rgba(@exchange-border-color, 0.15));
    }

    &-head {
      margin-bottom: 8px;
      padding: 0 12px;
      font-weight: 500;
      font-size: 14px;
    }

    &-item {
      padding: 8px 12px;
      cursor: pointer;

      &-percent {
        &.text-up {
          background-color: rgba(@up-color, 0.15) !important;;
        }

        &.text-down {
          background-color: rgba(@down-color, 0.15) !important;;
        }
      }

      &:hover {
        background-color: #394d71;
      }

      &-percent {
        border-radius: 4px;
      }
    }
  }

  &-mobile {
    display: none;

    @media @mobile {
      display: flex;
    }

    &-right {
      @media @mobile {
        margin-right: 24px;
      }
    }
  }

  .z-card-content {
    display: flex;
    padding: 8px 16px;
    align-items: center;

    & > div {
      margin-right: 24px;
    }

    @media @mobile {
      padding: 8px 0;
    }
  }

  &-name {
    &-span {
      font-size: 22px;
      font-weight: bold;
      margin: 0 .5rem 0 0;
    }

    @media @tablet {
      position: relative;
    }

    .z-dropdown-bottomLeft-overlay {
      top: calc(100% + 12px);
    }
  }

  &-last {
    font-size: 20px;
    font-weight: bold;
  }

  &-col {
    display: block;

    &-title {
      color: var(--gray-color);
    }
  }
}
</style>
