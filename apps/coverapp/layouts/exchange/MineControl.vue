<script setup lang="ts">
import type { ZTabItem } from '@zsmartex/components/types'
import OrdersHistory from './mine-control/OrdersHistory.vue'
import OpenOrders from './mine-control/OpenOrders.vue'
import TradesHistory from './mine-control/TradesHistory.vue'

const tradeStore = useTradeStore()
const userStore = useUserStore()
const ordersManagerOpenOrders = useOrdersManager('open_orders')
const ordersManagerOrdersHistory = useOrdersManager('orders_history')
const tradesManager = useTradesManager('trades_history')

const activeTab = ref('open_orders')
const hideOtherPairs = computed({
  get() {
    return [ordersManagerOpenOrders.config.market, ordersManagerOrdersHistory.config.market, tradesManager.config.market].every(m => m !== 'all')
  },
  set(hideOtherPairs: boolean) {
    const market = hideOtherPairs ? tradeStore.market.id : 'all'

    changeMineControlMarket(market)

    ordersManagerOpenOrders.clear()
    ordersManagerOrdersHistory.clear()
    tradesManager.clear()

    if (userStore.isAuthenticated) fetchAll()
  },
})

const tabs: ZTabItem[] = [
  {
    key: 'open_orders',
    text: 'Open Orders',
  },
  {
    key: 'orders_history',
    text: 'Orders History',

  },
  {
    key: 'trades_history',
    text: 'Trades History',
  },
]

onMounted(() => {
  const market = hideOtherPairs.value ? tradeStore.market.id : 'all'

  changeMineControlMarket(market)
})

function changeMineControlMarket(market: string) {
  ordersManagerOpenOrders.config.market = market
  ordersManagerOrdersHistory.config.market = market
  tradesManager.config.market = market
}

changeMineControlMarket(hideOtherPairs.value ? tradeStore.market.id : 'all')

if (userStore.isAuthenticated) {
  if (hideOtherPairs.value) {
    ordersManagerOpenOrders.clear()
    ordersManagerOrdersHistory.clear()
    tradesManager.clear()
  }

  await useAsyncData('layouts_exchange_mind_control', fetchAll, {
    lazy: true,
  })
}

function fetchAll() {
  return Promise.all([
    ordersManagerOpenOrders.GetData(),
    ordersManagerOrdersHistory.GetData(),
    tradesManager.GetData(),
  ])
}
</script>

<template>
  <ZCard class="page-exchange-mine-control">
    <template #head>
      <ZTab v-model="activeTab" :tabs="tabs" />

      <ZCheckbox v-model="hideOtherPairs" class="page-exchange-mine-control-checkbox">
        {{ $t('page.exchange.mine_control.hide_others_pairs') }}
      </ZCheckbox>
    </template>
    <OpenOrders v-if="activeTab === 'open_orders'" />
    <OrdersHistory v-else-if="activeTab === 'orders_history'" />
    <TradesHistory v-else-if="activeTab === 'trades_history'" id="trades_history" />
  </ZCard>
</template>

<style lang="less">
.page-exchange-mine-control {
  &-checkbox {
    @media @mobile {
      display: none;
    }

    svg {
      width: 16px;
      height: 16px;
    }
  }

  &-market {
    &:hover {
      cursor: pointer;
      text-decoration: underline;
    }
  }

  .z-checkbox-inner {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .z-table {
    &-empty {
      color: white !important;

      svg {
        .cls-1 {
          fill: rgba(@gray-color, 0.1);
        }

        .cls-2 {
          fill: rgba(@gray-color, 0.5);
        }
      }
    }
  }

  .z-card {
    &-head {
      display: flex;
      justify-content: space-between;

      @media @mobile {
        height: 34px !important;
      }
    }

    &-content {
      > div {
        @media @mobile {
          height: 360px;
        }
      }
    }
  }

  .z-tab {
    @media @mobile {
      display: block;
      width: max-content;
    }
  }

  .z-table {
    &-head {
      border-bottom: 1px solid @exchange-border-color;
    }

    &-row {
      height: 40px;
      line-height: 40px;

      &:hover {
        background-color: rgba(@exchange-border-color, 0.25);
      }

      &-col {
        border-bottom: 1px solid @exchange-border-color;
      }

      .created_at {
        color: #9eaed6;
      }

      .action {
        color: @exchange-gray-color;

        .cancel {
          color: rgba(@exchange-gray-color, 0.6);
        }
      }
    }
  }
}
</style>
