<script setup lang="ts">
import type { ZTabItem } from '@zsmartex/components/types'
import { OrderSide, OrderType } from '@zsmartex/types'
import { screenDevice } from '@zsmartex/utils'
import TradeActionSide from './trade-action/Side.vue'
import ModalSlippage from '~~/layouts/exchange/ModalSlippage.vue'

defineProps<{
  side?: OrderSide
}>()

const userStore = useUserStore()
const modalSlippage = ref<InstanceType<typeof ModalSlippage>>()
const activeTab = ref('limit')

const tabs: ZTabItem[] = [
  {
    key: 'limit',
    text: $t('layout.exchange.tradeaction.litmit'),
  },
  {
    key: 'market',
    text: $t('layout.exchange.tradeaction.market'),
  },
  {
    key: 'stop_limit',
    text: $t('layout.exchange.tradeaction.stop_limit'),
  },
]

const type = computed(() => {
  return activeTab.value.includes('limit') ? OrderType.Limit : OrderType.Market
})

const isStop = computed(() => {
  return activeTab.value.includes('stop')
})

const device = screenDevice()
</script>

<template>
  <ZCard class="page-exchange-trade-action">
    <template #head>
      <div class="w-full flex items-center justify-between">
        <ZTab v-model="activeTab" :tabs="tabs" />
        <span v-if="activeTab === 'market'" v class="text-[14px] cursor-pointer slippage" @click="modalSlippage?.openModal()">
          {{ `Set slippage${userStore.enabledSlippage && userStore.slippage > 0 ? `(${userStore.slippage}%)` : ''}` }}
        </span>
      </div>
    </template>
    <div :class="{ 'grid grid-cols-2 gap-0': device === ScreenDevice.Desktop || device === ScreenDevice.LargeDesktop }">
      <TradeActionSide v-show="!(side === OrderSide.Sell && device !== ScreenDevice.Desktop && device !== ScreenDevice.LargeDesktop)" :side="OrderSide.Buy" :type="type" :stop="isStop" />
      <TradeActionSide v-show="!(side === OrderSide.Buy && device !== ScreenDevice.Desktop && device !== ScreenDevice.LargeDesktop)" :side="OrderSide.Sell" :type="type" :stop="isStop" />
    </div>
    <ModalSlippage ref="modalSlippage" />
  </ZCard>
</template>

<style lang="less">
.page-exchange-trade-action {
  @media @tablet {
    background-color: @input-background-color !important;
  }

  .z-card-content {
    @media @mobile, @tablet {
      display: block;
    }
  }

  .slippage {
    color: @primary-color;
  }

  &-side {
    padding: 0 12px 12px;

    > * {
      margin-bottom: 12px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    @media @mobile, @tablet {
      width: 100%;
    }

    &.buy {
      .page-exchange-trade-action-slider-handle {
        border-color: @up-color;

        &:hover {
          box-shadow: 0 0 0 7px @up-bg-color;
        }
      }

      .z-button:hover {
        box-shadow: 0 0 0 4px @up-bg-color;
      }
    }

    &.sell {
      .page-exchange-trade-action-slider-handle {
        border-color: @down-color;

        &:hover {
          box-shadow: 0 0 0 7px @down-bg-color;
        }
      }

      .z-button:hover {
        box-shadow: 0 0 0 4px @down-bg-color;
      }
    }

    .z-button {
      width: 100%;
      height: 35px;
      line-height: 35px;
      border-radius: 4px;
      border: none;
      font-size: 12px;
      color: @exchange-text-color;

      &:hover {
        opacity: .8;
      }

      &:disabled {
        opacity: .75;
      }
    }
  }

  &-balance {
    display: flex;
    margin-top: 12px;
    line-height: 18px;
    justify-content: space-between;
    align-items: center;

    &-action {
      font-size: 14px;
    }
  }

  &-slider {
    position: relative;
    height: 12px;
    font-size: 14px;
    margin: 20px 8px 12px;
    padding: 4px 0;
    cursor: pointer;
    touch-action: none;

    &-rail {
      position: absolute;
      background-color: @exchange-border-color;
      height: 4px;
      width: 100%;
      border-radius: 2px;
    }

    &-track {
      position: absolute;
      left: 0%;
      right: auto;
      border-radius: 4px;
      background-color: @exchange-gray-color;
      height: 4px;
      transition: background-color .3s;
    }

    &-step {
      position: absolute;
      width: 100%;

      &-dot {
        position: absolute;
        transform: translateX(-50%);
        top: -3px;
        border: 2px solid @gray-color;
        background-color: @base-border-color;
        width: 10px;
        height: 10px;
        border-radius: 50%;
        cursor: pointer;
      }
    }

    &-handle {
      position: absolute;
      left: 0;
      transform: translateX(-50%);
      height: 16px;
      width: 16px;
      margin-top: -6px;
      background-color: @exchange-border-color;
      border: 2px solid @primary-color;
      border-radius: 50%;
      cursor: pointer;
      transition: border-color .3s,box-shadow .6s,transform .3s cubic-bezier(.18,.89,.32,1.28);
    }

    .z-tooltip-overlay {
      left: 0%;
      top: -55px;
    }
  }

  &-input-container {
    .z-tooltip-overlay {
      left: calc(100% - 150px);
    }
  }

  &-input-disabled {
    background-color: rgba(@exchange-border-color, 0.4) !important;
  }

  .z-input {
    position: relative;
    width: 100%;
    height: 35px;
    background-color: transparent;
    border: 1px solid @exchange-border-color;
    padding: 0 70px;
    border-radius: 4px;

    &:not(&-disabled):hover {
      border-color: @primary-color;
    }

    input {
      text-align: right;
    }

    &-prefix, &-suffix {
      position: absolute;
      color: @exchange-gray-color;
      font-size: 12px;
      top: 50%;
      transform: translateY(-50%);
    }

    &-prefix {
      left: 16px;
    }

    &-suffix {
      right: 16px;
    }

    .page-exchange-trade-action-slider-handle {
        border-color: @up-color;

        &:hover {
          box-shadow: 0 0 0 7px @up-bg-color;
        }
      }
  }

  &-input-error {
    border: 1px solid #fe6262 !important;
    color: #fe6262 !important;
    background-color: rgba(#fe6262, 0.1) !important;
    outline: #fe6262;
  }

  &-total-calculator {
    color: @exchange-gray-color;

    &-value {
      color: @exchange-text-color;
    }
  }
}
</style>
