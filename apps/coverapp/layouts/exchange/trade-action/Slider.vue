<script setup lang="ts">
const props = withDefaults(defineProps<{
  modelValue: number
}>(), {
  modelValue: 0,
})

const emit = defineEmits<{
  (event: 'update:modelValue', value: number): void
  (event: 'input', percent: number): void
}>()

const parentElement = useCurrentElement()

const value = useVModel(props, 'modelValue', emit)

const steps = [0, 25, 50, 75, 100]
const moving = ref(false)

function setPercent(x: number) {
  const parentRect = parentElement.value.getBoundingClientRect()
  const parentWidth = parentElement.value.clientWidth

  let percent = Math.round((x - parentRect.x) * 100 / parentWidth)

  if (percent < 0) percent = 0
  if (percent > 100) percent = 100

  // Add change value when click or move
  value.value = percent
  emit('input', percent)
}

function onStepClick(step: number) {
  value.value = step
}

function onMouseStart() {
  moving.value = true
}

function onMouseLeave() {
  moving.value = false
}

function onMouseMove(event: MouseEvent) {
  if (!moving.value) return
  setPercent(event.clientX)
}

function onMouseClick(event: MouseEvent) {
  setPercent(event.clientX)
}

onMounted(() => {
  window.addEventListener('mousemove', onMouseMove)
  window.addEventListener('mouseup', onMouseLeave)
})

onBeforeUnmount(() => {
  window.removeEventListener('mousemove', onMouseMove)
  window.removeEventListener('mouseup', onMouseLeave)
})
</script>

<template>
  <div class="page-exchange-trade-action-slider" @mousedown="onMouseStart" @click="onMouseClick">
    <div class="page-exchange-trade-action-slider-rail" />
    <div class="page-exchange-trade-action-slider-track" :style="`width: ${value}%`" />
    <div class="page-exchange-trade-action-slider-step">
      <ClientOnly>
        <div v-for="(step, index) in steps" :key="index" class="page-exchange-trade-action-slider-step-dot" :style="`left: ${step}%`" @click="onStepClick(step)" />
      </ClientOnly>
    </div>
    <ZTooltip :active="moving" :title="`${value.toFixed(0)}%`" :style="`left: ${value}%`">
      <div class="page-exchange-trade-action-slider-handle" />
    </ZTooltip>
  </div>
</template>
