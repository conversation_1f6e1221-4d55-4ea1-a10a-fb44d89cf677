<script setup lang="ts">
import { roundNumber } from '@zsmartex/utils'
import { OrderSide, OrderType, Placement } from '@zsmartex/types'
import TradeActionInput from './Input.vue'
import TradeActionSlider from './Slider.vue'

const props = defineProps<{
  side: OrderSide
  stop: boolean
  type: OrderType
}>()

const enum SumBy {
  Amount = 'amount',
  Total = 'total',
}

const loading = ref(false)
const stopPrice = ref('')
const price = ref('')
const amount = ref('')
const sumBy = ref<SumBy>(SumBy.Amount)

const depthStore = useDepthStore()
const tradeStore = useTradeStore()
const userStore = useUserStore()
const assetsStore = useAssetsStore()

const currencyID = computed(() => {
  return props.side === OrderSide.Buy ? tradeStore.market.quote_unit : tradeStore.market.base_unit
})

const market = computed(() => {
  return tradeStore.market
})

const ticker = computed(() => {
  return tradeStore.ticker
})

const stopPriceError = computed(() => {
  if (stopPrice.value === '') {
    return ''
  }

  if (Number.parseFloat(stopPrice.value) < Number.parseFloat(market.value.min_price)) {
    return $t('page.global.error.min_stop_price', { min_price: market.value.min_price })
  }

  if (market.value.max_price !== '0' && Number.parseFloat(stopPrice.value) > Number.parseFloat(market.value.max_price)) {
    return $t('page.global.error.max_stop_price', { min_price: market.value.max_price })
  }

  return ''
})

const priceError = computed(() => {
  if (price.value === '') {
    return ''
  }

  if (Number.parseFloat(price.value) < Number.parseFloat(market.value.min_price)) {
    return $t('page.global.error.min_price', { min_price: market.value.min_price })
  }

  if (market.value.max_price !== '0' && Number.parseFloat(price.value) > Number.parseFloat(market.value.max_price)) {
    return $t('page.global.error.max_price', { max_price: market.value.max_price })
  }

  return ''
})

const amountError = computed(() => {
  if (amount.value === '') {
    return ''
  }

  if (Number.parseFloat(amount.value) < Number.parseFloat(market.value.min_amount)) {
    return $t('page.global.error.min_amount', { min_amount: market.value.min_amount })
  }

  return ''
})

const available = computed(() => {
  return userStore.isAuthenticated
    ? assetsStore.spot_assets.find(asset => asset.currency === currencyID.value)?.balance || '---'
    : '---'
})

const maxAmount = computed(() => {
  const balance = Number(available.value) || 0

  return props.side === OrderSide.Buy ? balance / Number(price.value) : balance
})

const maxBalance = computed(() => {
  if (props.type === OrderType.Market) {
    if ((sumBy.value === SumBy.Total && props.side === OrderSide.Buy) || (sumBy.value === SumBy.Amount && props.side === OrderSide.Sell)) {
      return Number(available.value) || 0
    } else {
      price.value = ticker.value.last

      if (props.side === OrderSide.Sell) {
        return Number(available.value) * Number(price.value)
      }
    }
  }

  return maxAmount.value
})

const total = computed(() => {
  if (props.type !== OrderType.Market || sumBy.value === SumBy.Amount) return roundNumber(Number(price.value) * Number(amount.value), tradeStore.market.total_precision)
  return amount.value || '---'
})

const sliderPercent = computed({
  get() {
    return Number(amount.value) * 100 / maxBalance.value || 0
  },
  set(percent: number) {
    if (props.side === OrderSide.Buy) {
      price.value ||= (depthStore.sell[0]?.price || Number(ticker.value.last)).toString()
    } else {
      price.value ||= (depthStore.buy[0]?.price || Number(ticker.value.last)).toString()
    }

    if (props.stop) {
      stopPrice.value ||= price.value
    }

    amount.value = (maxBalance.value * percent / 100).toString()
    if (amount.value === '0') {
      amount.value = ''
    }
  },
})

onMounted(() => {
  if (props.side === OrderSide.Buy) {
    sumBy.value = SumBy.Total
  } else {
    sumBy.value = SumBy.Amount
  }

  useEvent.on('orderbook:click', appendOrderInfo)
})

onBeforeUnmount(() => {
  useEvent.off('orderbook:click', appendOrderInfo)
})

async function placeOrder() {
  if (!amount.value) return
  loading.value = true

  const payload: {
    market: string
    side: OrderSide
    price?: string
    stop_price?: string
    amount?: string
    total?: string
    type: OrderType
    max_price?: string
  } = {
    market: market.value.id,
    side: props.side,
    type: props.type,
  }

  if (props.type === OrderType.Market && userStore.enabledSlippage && Number(tradeStore.ticker.last) > 0) {
    if (props.side === OrderSide.Buy) {
      payload.max_price = (Number(tradeStore.ticker.last) * (1 + userStore.slippage / 100)).toFixed(tradeStore.market.price_precision)
    } else {
      payload.max_price = (Number(tradeStore.ticker.last) * (1 - userStore.slippage / 100)).toFixed(tradeStore.market.price_precision)
    }
  }

  if (props.type === OrderType.Limit) {
    payload.price = price.value
    payload.amount = amount.value
  } else {
    if (sumBy.value === SumBy.Total) {
      payload.total = amount.value
    } else {
      payload.amount = amount.value
    }
  }

  if (props.stop) {
    payload.stop_price = stopPrice.value
  }

  await tradeStore.PlaceOrder(payload)
  stopPrice.value = ''
  price.value = ''
  amount.value = ''
  loading.value = false
}

function roundPrecision(value: string, kind: 'price' | 'amount') {
  const valueWithSplit = value.split('.')
  const n1 = valueWithSplit[0]
  const n2 = valueWithSplit[1]

  let precision = 0
  switch (kind) {
    case 'price':
      precision = tradeStore.market.price_precision
      break
    case 'amount':
      precision = tradeStore.market.amount_precision
      break
  }

  if (precision === 0) {
    return n1
  } else if (n2) {
    return [n1, n2.slice(0, precision)].join('.')
  } else {
    return value
  }
}

function FixAmount() {
  amount.value = roundPrecision(amount.value, props.type === OrderType.Market && sumBy.value === SumBy.Total ? 'price' : 'amount')

  if (maxBalance.value === 0) {
    amount.value = ''
    return
  }

  if (Number(amount.value) > Number(maxBalance.value)) {
    amount.value = maxBalance.value.toString()
  }
}

watch(stopPrice, (sp) => {
  stopPrice.value = roundPrecision(sp, 'price')
})

watch(price, (p) => {
  price.value = roundPrecision(p, 'price')
  if (props.side === OrderSide.Buy) FixAmount()
})

watch(amount, (a) => {
  if (!a.length) return
  if (Number(a) <= 0) return

  if (props.side === OrderSide.Buy && !price.value && depthStore.sell.length > 0) {
    price.value = depthStore.sell[0].price.toFixed(tradeStore.market.price_precision)
  }

  if (props.side === OrderSide.Sell && !price.value && depthStore.buy.length > 0) {
    price.value = depthStore.buy[0].price.toFixed(tradeStore.market.price_precision)
  }

  FixAmount()
})

watch(sumBy, () => {
  amount.value = ''
})

watch(() => props.type, () => {
  price.value = ''
  amount.value = ''
})

watch(maxBalance, () => {
  if (Number(amount.value) > Number(maxBalance.value)) FixAmount()
})

const sideColumns = [
  {
    key: 'text',
  },
]

const sides = [
  {
    text: $t('page.global.placeholder.amount').toUpperCase(),
    key: 'amount',
  },
  {
    text: $t('page.global.placeholder.total').toUpperCase(),
    key: 'total',
  },
]

function appendOrderInfo(params: { price: number; amount: number; total: number }) {
  if (props.type === OrderType.Market && sumBy.value === SumBy.Total) {
    amount.value = params.total.toFixed(tradeStore.market.total_precision)
    return
  }

  price.value = params.price.toFixed(tradeStore.market.price_precision)
  stopPrice.value = params.price.toFixed(tradeStore.market.price_precision)
  if (maxBalance.value >= params.amount) {
    amount.value = params.amount.toFixed(tradeStore.market.amount_precision)
  } else {
    amount.value = ''
  }
}

const disabledButton = computed(() => {
  if (loading.value) return true
  return false
})
</script>

<template>
  <div class="page-exchange-trade-action-side" :class="[side]">
    <div class="page-exchange-trade-action-balance">
      <span>
        {{ $t("page.global.table.available") }}: {{ roundNumber(available, 8) }} {{ side === "buy" ? tradeStore.market.quote_unit.toUpperCase() : tradeStore.market.base_unit.toUpperCase() }}
      </span>

      <NuxtLink class="page-exchange-trade-action-balance-action" to="/my/wallet/assets/deposit/usdt">
        {{ $t("page.global.action.deposit") }}
      </NuxtLink>
    </div>
    <TradeActionInput
      v-if="stop"
      v-model="stopPrice"
      :message-error="stopPriceError"
    >
      <template #prefix>
        {{ $t('page.global.placeholder.stop').toUpperCase() }}
      </template>
      <template #suffix>
        {{ tradeStore.market.quote_unit.toUpperCase() }}
      </template>
    </TradeActionInput>
    <TradeActionInput
      v-if="type === OrderType.Market"
      model-value="Market"
      :disabled="true"
    >
      <template #prefix>
        {{ $t('page.global.placeholder.price').toUpperCase() }}
      </template>
      <template #suffix>
        {{ tradeStore.market.quote_unit.toUpperCase() }}
      </template>
    </TradeActionInput>
    <TradeActionInput
      v-else
      v-model="price"
      :message-error="priceError"
    >
      <template #prefix>
        {{ $t('page.global.placeholder.price').toUpperCase() }}
      </template>
      <template #suffix>
        {{ tradeStore.market.quote_unit.toUpperCase() }}
      </template>
    </TradeActionInput>
    <TradeActionInput
      v-model="amount"
      :message-error="amountError"
    >
      <template #prefix>
        <template v-if="type === OrderType.Market">
          <ZSelect
            v-model="sumBy"
            :data-source="sides"
            :search="false"
            :columns="sideColumns"
            :find-by="['key']"
            value-key="key"
            label-key="text"
            placeholder="Amount"
            :placement="Placement.BottomLeft"
          />
        </template>
        <template v-else>
          {{ $t('page.global.placeholder.amount').toUpperCase() }}
        </template>
      </template>
      <template #suffix>
        <template v-if="type === OrderType.Market">
          {{ sumBy === 'amount' ? tradeStore.market.base_unit.toUpperCase() : tradeStore.market.quote_unit.toUpperCase() }}
        </template>
        <template v-else>
          {{ tradeStore.market.base_unit.toUpperCase() }}
        </template>
      </template>
    </TradeActionInput>
    <TradeActionSlider v-model.number="sliderPercent" />
    <div class="page-exchange-trade-action-total-calculator">
      {{ $t('page.exchange.trade_action.side.total') }}: <span class="page-exchange-trade-action-total-calculator-value">{{ total }} {{ tradeStore.market.quote_unit.toUpperCase() }}</span>
    </div>
    <ZButton v-if="userStore.isAuthenticated" class="bold-text" :class="[`bg-${side === 'buy' ? 'up' : 'down'}`]" :disabled="disabledButton" @click="placeOrder">
      {{ $t(`page.global.action.${side}`).toUpperCase() }} {{ tradeStore.market.base_unit.toUpperCase() }}
    </ZButton>
    <ZButton v-else class="bg-login" :disabled="true">
      <I18n path="page.global.login_or_register">
        <template #login>
          <NuxtLink to="/login" class="mr-1">
            {{ $t('page.global.action.login') }}
          </NuxtLink>
        </template>
        <template #register>
          <NuxtLink to="/register" class="ml-1">
            {{ $t('page.global.action.register') }}
          </NuxtLink>
        </template>
      </I18n>
    </ZButton>
  </div>
</template>

<style lang="less">
.page-exchange-trade-action-side {
  .bg-login {
    background-color: @exchange-card-background;
    border: 1px solid @exchange-border-color !important;
    box-shadow: none !important;
    opacity: 1 !important;
    font-size: 14px !important;
    cursor: auto;

    a {
      color: @primary-color !important;
    }
  }

  .z-select {
    .z-dropdown {
      &-trigger {
        padding: 0;
        height: max-content;

        &-icon {
          color: #9EAED6;
        }
      }

      &-overlay {
        width: 125px;
        background-color: @exchange-dropdown-background;
        border: 1px solid @exchange-border-color;
        border-radius: 4px;
        text-align: left;
        padding: 4px 0;

        .z-table {
          &-row {
            padding: 0 8px;
            margin-left: 0;

            &-selected, &:hover {
              background-color: rgba(0, 0, 0, 0.15);
            }
          }
        }
      }
    }
  }
}
</style>
