<script setup lang="ts">
import { InputType } from '@zsmartex/components/types'

const props = defineProps<{
  prefix?: string
  suffix?: string
  disabled?: boolean
  messageError?: string
  modelValue?: string
}>()
const emit = defineEmits<{
  (event: 'update:modelValue', value: string): void
}>()
const value = useVModel(props, 'modelValue', emit)

const blur = (event: any) => {
  if (props.messageError) {
    value.value = ''
  }
}
</script>

<template>
  <div class="page-exchange-trade-action-input-container">
    <ZTooltip :title="`${messageError}`">
      <ZInput
        v-model="value"
        class="page-exchange-trade-action-input z-1"
        :class="[
          { 'page-exchange-trade-action-input-disabled': disabled },
          { 'page-exchange-trade-action-input-error': !!messageError },
        ]"
        :type="InputType.Decimal"
        :disabled="disabled"
        @blur="(e: any) => blur(e)"
      >
        <template #prefix>
          <slot name="prefix" />
        </template>
        <template #suffix>
          <slot name="suffix" />
        </template>
      </ZInput>
    </ZTooltip>
  </div>
</template>
