<script setup lang="ts">
const props = defineProps<{
  modelValue: string
}>()
const emit = defineEmits<{
  (event: 'update:modelValue', value: string): void
}>()
const value = useVModel(props, 'modelValue', emit)

const tradeStore = useTradeStore()

const charts = [
  // { key: 'original', text: 'Original' }, TODO: add support this chart
  { key: 'tradingview', text: 'TradingView' },
  { key: 'depth', text: 'Depth' },
]

const chartIntervals = [
  { key: 'time', text: 'Time', value: 'time' },
  { key: '1', text: '1m', value: '1' },
  { key: '3', text: '3m', value: '3' },
  { key: '5', text: '5m', value: '5' },
  { key: '15', text: '15m', value: '15' },
  { key: '30', text: '30m', value: '30' },
  { key: '60', text: '1H', value: '60' },
  { key: '120', text: '2H', value: '120' },
  { key: '240', text: '4H', value: '240' },
  { key: '360', text: '6H', value: '360' },
  { key: '480', text: '8H', value: '480' },
  { key: '720', text: '12H', value: '720' },
  { key: '1d', text: '1D', value: '1440' },
  { key: '3d', text: '3D', value: '4320' },
  { key: '1w', text: '1W', value: '10080' },
  { key: '1m', text: '1M', value: '43200' },
]

const recommendedChartIntervals = computed(() => {
  return chartIntervals.filter(chartInterval => ['time', '15', '60', '240', '1d', '1w'].includes(chartInterval.key))
})

function onChartIntervalClick(interval: string, type = tradeStore.chart_type) {
  tradeStore.chart_interval = interval
  tradeStore.chart_type = type
}
</script>

<template>
  <div class="page-exchange-chart-head">
    <div class="page-exchange-chart-head-nav">
      <template v-if="value !== 'depth'">
        <div
          v-for="chart_interval in recommendedChartIntervals"
          :key="chart_interval.key"
          class="page-exchange-chart-head-nav-item bold-text" :class="[{ 'page-exchange-chart-head-nav-item-selected': tradeStore.chart_interval === chart_interval.value }]"
          @click="onChartIntervalClick(chart_interval.value)"
        >
          {{ chart_interval.text }}
        </div>
      </template>
    </div>
    <div class="page-exchange-chart-head-nav page-exchange-chart-head-nav-charts">
      <div
        v-for="chart in charts"
        :key="chart.key"
        class="page-exchange-chart-head-nav-item bold-text" :class="[{ 'page-exchange-chart-head-nav-item-selected': value === chart.key }]"
        @click="value = chart.key"
      >
        {{ chart.text }}
      </div>
    </div>
  </div>
</template>

<style lang="less">
.page-exchange-chart-head {
  width: 100%;
  display: flex;
  justify-content: space-between;

  &-nav {
    display: flex;

    &-charts {
      font-size: 14px;
    }

    &-item {
      color: @exchange-gray-color;
      padding: 0 4px;
      margin: 0 4px;
      cursor: pointer;
      transition: all 0.3s;

      &-selected {
        color: @primary-color;
      }
    }
  }
}
</style>
