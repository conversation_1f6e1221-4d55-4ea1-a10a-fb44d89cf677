<script setup lang="ts">
import { OrderSide } from '@zsmartex/types'
import { Chart } from '@/library/depth-chart'

const tradeStore = useTradeStore()
const depthStore = useDepthStore()

const el = useCurrentElement<HTMLElement>()
let chart!: Chart
const limitDepth = ref(100)

const depth = computed(() => {
  const SIDE = [OrderSide.Buy, OrderSide.Sell]
  const data: Chart['depth_data'] = { buy: [], sell: [] }

  for (const side of SIDE) {
    let total = 0
    const depthBySide = depthStore.toArray(
      side,
      limitDepth.value,
    )
    depthBySide.forEach((row) => {
      if (!row.price || !row.amount) return

      total = total + row.amount

      data[side].push({
        price: Number(row.price),
        volume: Number(row.amount),
        total: Number(total.toFixed(tradeStore.market.total_precision)),
      })
    })
  }

  return {
    buy: data.buy,
    sell: data.sell,
  }
})

const setChartConfig = () => {
  chart.config.chart.strokeSize = 2

  chart.config.xAxis.height = 30
  chart.config.xAxis.axisLine.size = 1
  chart.config.xAxis.tickText.size = 12
  chart.config.xAxis.tickText.margin = (30 + 12) / 2

  chart.config.yAxis.width = 60
  chart.config.yAxis.axisLine.size = 1
  chart.config.yAxis.tickLine.display = true
  chart.config.yAxis.tickLine.width = 8
  chart.config.yAxis.tickLine.size = 1
  chart.config.yAxis.tickText.size = 12
  chart.config.yAxis.tickText.margin = 12
  chart.config.yAxis.tickText.baseLine = 'middle'
  chart.config.yAxis.tickText.position = 'outside'

  chart.config.gird.horizontal.size = 0.5
  chart.config.gird.vertical.size = 1

  chart.config.tooltip.type = 'rect'
  chart.config.tooltip.crosshair.type = 'dash'
  chart.config.tooltip.crosshair.color = 'side'
  chart.config.tooltip.crosshair.dashValue = [4, 4]
}

onMounted(() => {
  chart = new Chart(el.value)

  setChartConfig()
  chart.init()

  useResizeObserver(el, () => {
    chart.resize()
  })
  if (chart.chart_ready) {
    chart.depth_data = depth.value
    chart.draw(tradeStore.market.price_precision)
  }
})

watch(depth, () => {
  if (chart.chart_ready) {
    chart.depth_data = depth.value
    chart.draw(tradeStore.market.price_precision)
  }
})
</script>

<template>
  <div />
</template>
