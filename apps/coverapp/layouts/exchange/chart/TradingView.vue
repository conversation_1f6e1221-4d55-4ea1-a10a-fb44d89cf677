<script setup lang="ts">
import { ChartType } from '@zsmartex/types'
import colors from '@zsmartex/colors'
import TradingViewDataFeed from '@/library/TradingViewDataFeed'

const tradeStore = useTradeStore()

const el = useCurrentElement<HTMLElement>()

let chart!: TradingView.IChartingLibraryWidget

function intervalConvert(interval: string) {
  return interval as TradingView.ResolutionString
}

function onChartTypeChanged(type: ChartType) {
  const enum SeriesStyle {
    Bars = 0,
    Candles = 1,
    Line = 2,
    Area = 3,
    HeikenAshi = 8,
    HollowCandles = 9,
    Baseline = 10,
    Renko = 4,
    <PERSON>gi = 5,
    PointAndFigure = 6,
    LineBreak = 7,
  }

  let tradingviewChartType: SeriesStyle

  switch (type) {
    case ChartType.Bars:
      tradingviewChartType = SeriesStyle.Bars
      break
    case ChartType.Candles:
      tradingviewChartType = SeriesStyle.Candles
      break
    case ChartType.Line:
      tradingviewChartType = SeriesStyle.Line
      break
    case ChartType.Area:
      tradingviewChartType = SeriesStyle.Area
      break
    case ChartType.HeikenAshi:
      tradingviewChartType = SeriesStyle.HeikenAshi
      break
    case ChartType.HollowCandles:
      tradingviewChartType = SeriesStyle.HollowCandles
      break
    case ChartType.Baseline:
      tradingviewChartType = SeriesStyle.Baseline
      break
    case ChartType.Renko:
      tradingviewChartType = SeriesStyle.Renko
      break
    case ChartType.Kagi:
      tradingviewChartType = SeriesStyle.Kagi
      break
    case ChartType.PointAndFigure:
      tradingviewChartType = SeriesStyle.PointAndFigure
      break
    case ChartType.LineBreak:
      tradingviewChartType = SeriesStyle.LineBreak
      break
  }

  chart.chart().setChartType(tradingviewChartType as unknown as TradingView.SeriesStyle)
}

onMounted(() => {
  if (process.server) return

  chart = new TradingView.widget({
    debug: false,
    container: el.value,
    symbol: tradeStore.market.name,
    datafeed: new TradingViewDataFeed(tradeStore.market),
    library_path: '/charting_library/',
    locale: 'en',
    interval: intervalConvert(tradeStore.chart_interval),
    timezone: 'exchange',
    autosize: true,
    theme: 'Light',
    custom_css_url: 'custom.css',
    charts_storage_url: 'https://saveload.tradingview.com',
    charts_storage_api_version: '1.1',
    client_id: 'tradingview.com',
    user_id: 'public_user_id',
    toolbar_bg: 'transparent',
    fullscreen: false,
    disabled_features: [
      'use_localstorage_for_settings',
      'header_widget',
      'source_selection_markers',
      'adaptive_logo',
      'constraint_dialogs_movement',
      'display_market_status',
      'volume_force_overlay',
      'symbol_search_hot_key',
      'property_pages',
      'timeframes_toolbar',
      'symbol_info',
      'border_around_the_chart',
      'star_some_intervals_by_default',
      'datasource_copypaste',
      'right_bar_stays_on_scroll',
      'go_to_date',
      'save_chart_properties_to_local_storage',
      'study_templates',
    ],
    enabled_features: [
      'dont_show_boolean_study_arguments',
      'hide_last_na_study_output',
      'move_logo_to_main_pane',
      'side_toolbar_in_fullscreen_mode',
      'keep_left_toolbar_visible_on_small_screens',
    ],
    overrides: {
      'volumePaneSize': 'medium',
      'scalesProperties.lineColor': colors['exchange-gray-color'],
      'scalesProperties.textColor': colors['exchange-gray-color'],
      'paneProperties.background': colors['exchange-card-background'],
      'paneProperties.vertGridProperties.color': 'rgb(255 255 255 / 5%)',
      'paneProperties.horzGridProperties.color': 'rgb(255 255 255 / 5%)',
      'paneProperties.crossHairProperties.color': colors['gray-color'],
      'paneProperties.legendProperties.showLegend': true,
      'paneProperties.legendProperties.showStudyArguments': true,
      'paneProperties.legendProperties.showStudyTitles': true,
      'paneProperties.legendProperties.showStudyValues': true,
      'paneProperties.legendProperties.showSeriesTitle': true,
      'paneProperties.legendProperties.showSeriesOHLC': true,
      'mainSeriesProperties.candleStyle.upColor': colors['up-color'],
      'mainSeriesProperties.candleStyle.downColor': colors['down-color'],
      'mainSeriesProperties.candleStyle.drawWick': true,
      'mainSeriesProperties.candleStyle.drawBorder': true,
      'mainSeriesProperties.candleStyle.borderColor': '#4e5b85',
      'mainSeriesProperties.candleStyle.borderUpColor': colors['up-color'],
      'mainSeriesProperties.candleStyle.borderDownColor': colors['down-color'],
      'mainSeriesProperties.candleStyle.wickUpColor': colors['up-color'],
      'mainSeriesProperties.candleStyle.wickDownColor': colors['down-color'],
      'mainSeriesProperties.candleStyle.barColorsOnPrevClose': false,
      'mainSeriesProperties.hollowCandleStyle.upColor': colors['up-color'],
      'mainSeriesProperties.hollowCandleStyle.downColor': colors['down-color'],
      'mainSeriesProperties.hollowCandleStyle.drawWick': true,
      'mainSeriesProperties.hollowCandleStyle.drawBorder': true,
      'mainSeriesProperties.hollowCandleStyle.borderColor': '#4e5b85',
      'mainSeriesProperties.hollowCandleStyle.borderUpColor': colors['up-color'],
      'mainSeriesProperties.hollowCandleStyle.borderDownColor': colors['down-color'],
      'mainSeriesProperties.hollowCandleStyle.wickColor': '#737375',
      'mainSeriesProperties.haStyle.upColor': colors['up-color'],
      'mainSeriesProperties.haStyle.downColor': colors['down-color'],
      'mainSeriesProperties.haStyle.drawWick': false,
      'mainSeriesProperties.haStyle.drawBorder': false,
      'mainSeriesProperties.haStyle.borderColor': '#4e5b85',
      'mainSeriesProperties.haStyle.borderUpColor': colors['up-color'],
      'mainSeriesProperties.haStyle.borderDownColor': colors['down-color'],
      'mainSeriesProperties.haStyle.wickColor': '#4e5b85',
      'mainSeriesProperties.haStyle.barColorsOnPrevClose': false,
      'mainSeriesProperties.barStyle.upColor': colors['up-color'],
      'mainSeriesProperties.barStyle.downColor': colors['down-color'],
      'mainSeriesProperties.barStyle.barColorsOnPrevClose': false,
      'mainSeriesProperties.barStyle.dontDrawOpen': false,
      'mainSeriesProperties.lineStyle.color': '#4e5b85',
      'mainSeriesProperties.lineStyle.linewidth': 1,
      'mainSeriesProperties.lineStyle.priceSource': 'close',
      'mainSeriesProperties.areaStyle.color1': 'rgba(122, 152, 247, .1)',
      'mainSeriesProperties.areaStyle.color2': 'rgba(122, 152, 247, .02)',
      'mainSeriesProperties.areaStyle.linecolor': '#4e5b85',
      'mainSeriesProperties.areaStyle.linewidth': 1,
      'mainSeriesProperties.areaStyle.priceSource': 'close',
    },
    studies_overrides: {
      'volume.volume.color.0': colors['down-color'] as TradingView.StudyOverrideValueType,
      'volume.volume.color.1': colors['up-color'] as TradingView.StudyOverrideValueType,
      'volume.volume.transparency': 70 as TradingView.StudyOverrideValueType,
    },
  })
})

watch(() => tradeStore.chart_interval, (interval) => {
  if (process.server) return

  if (interval === 'time') {
    chart.chart().setResolution('1' as TradingView.ResolutionString, () => {})
    chart.chart().setChartType(2 as TradingView.SeriesStyle)
  } else {
    chart.chart().setResolution(intervalConvert(interval), () => {})
    onChartTypeChanged(tradeStore.chart_type)
  }
})

watch(() => tradeStore.chart_type, onChartTypeChanged)
</script>

<template>
  <div />
</template>
