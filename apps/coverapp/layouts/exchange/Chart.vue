<script setup lang="ts">
import ExchangeChartHead from './chart/Head.vue'
import ExchangeChartTradingView from './chart/TradingView.vue'
import ExchangeChartDepth from './chart/Depth.vue'

const chartSelected = ref('tradingview')
</script>

<template>
  <ZCard class="page-exchange-chart">
    <template #head>
      <ExchangeChartHead v-model="chartSelected" />
    </template>
    <ClientOnly>
      <ExchangeChartTradingView v-show="chartSelected === 'tradingview'" />
      <ExchangeChartDepth v-show="chartSelected === 'depth'" />
    </ClientOnly>
  </ZCard>
</template>

<style lang="less">
.page-exchange-chart {
  @media @mobile {
    padding-top: 0px !important;
  }

  .z-card-content > * {
    position: absolute;
    width: 100%;
    height: 100%;
  }
}
</style>
