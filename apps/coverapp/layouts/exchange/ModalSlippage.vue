<script setup lang="ts">
import { InputType } from '@zsmartex/components/types'

const visible = ref(false)

const userStore = useUserStore()
const slippage = ref(String(userStore.slippage))
const checkbox = ref(userStore.enabledSlippage)

function openModal() {
  visible.value = true
}

const slippageError = computed(() => {
  if (!slippage.value.length) return 'Slippage is required'

  if (Number(slippage.value) < 0 || Number(slippage.value) > 100) return 'Slippage must be between 0 and 100'
})

const disabledButton = computed(() => {
  if (checkbox.value && slippageError.value) return true
  return false
})

const Save = () => {
  userStore.slippage = Number(slippage.value)
  userStore.enabledSlippage = checkbox.value
  visible.value = false
}

defineExpose({
  openModal,
})
</script>

<template>
  <ZModal
    v-model="visible"
    class="page-exchange-slippage"
    title="Set Slippage"
  >
    <ZForm autocomplete="off" @submit="Save">
      <div class="flex justify-between items-center mb-4">
        <span class="text-white">Enable slippage</span>
        <ZCheckbox v-model="checkbox" />
      </div>
      <ZFormRow v-if="checkbox" label="Slippage">
        <ZInput
          v-model="slippage"
          name="slippage"
          :type="InputType.Number"
          placeholder="Slippage"
          :error="slippageError"
          input-number
          :min="0"
          :max="100"
        />
      </ZFormRow>
      <ZButton
        class="!w-full mt-10 mb-2"
        type="primary"
        html-type="submit"
        :disabled="disabledButton"
      >
        Save
      </ZButton>
    </ZForm>
  </ZModal>
</template>

<style lang="less">
.page-exchange-slippage {
  .z-overlay {
    width: 450px;
  }

  .z-modal-overlay-background {
    background-color: rgb(107 114 128 / 0.3);;
  }

  .z-modal-layout {
    @media @mobile {
      width: 300px;
    }

    &-container {
      background-color: @exchange-card-background !important;

      &-title {
        color: white;
      }

      .z-form-row-label {
        margin-bottom: 8px;
        color: white;
      }

      .z-input {
        position: relative;
        width: 100%;
        height: 35px;
        background-color: transparent;
        border: 1px solid @exchange-border-color;
        border-radius: 4px;
        color: white;
      }
    }
  }

  .z-input {
    background-color: #fff;
    height: 40px;
    line-height: 40px;
  }

  .z-table-row-col {
    display: block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .z-button {
    height: 40px;
    font-size: 18px;
    color: #fff;
  }
}
</style>
