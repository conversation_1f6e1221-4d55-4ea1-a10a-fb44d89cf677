<script setup lang="ts">
import type { ZTabItem, ZTableColumn } from '@zsmartex/components/types'
import { Align, Format, ParseType } from '@zsmartex/types'
import TradesHistory from './mine-control/TradesHistory.vue'
import { format as formatDate, formatDistanceToNow, parseISO } from 'date-fns'

const tradeStore = useTradeStore()
const userStore = useUserStore()
const tradeManager = useTradesManager('my_trades')
const activeTab = ref('market_trades')

const visibleTab = computed(() => tradeStore.exchange_layout === 'basic')

const tabs = computed(() => {
  const tabs: ZTabItem[] = [
    {
      key: 'market_trades',
      text: 'Market Trades',
    },
  ]
  if (tradeStore.exchange_layout === 'basic') tabs.push({
    key: 'my_trades',
    text: 'My Trades',
  })
  return tabs
})

const columns = computed<ZTableColumn[]>(() => {
  return [
    {
      key: 'price',
      title: `${$t('page.global.table.price')} (${tradeStore.market.quote_unit.toUpperCase()})`,
      align: Align.Left,
      formatBy: Format.Price,
      sideKey: 'side',
      parse: ParseType.Decimal,
      precision: tradeStore.market.price_precision,
    },
    {
      key: 'amount',
      title: `${$t('page.global.table.amount')} (${tradeStore.market.base_unit.toUpperCase()})`,
      align: Align.Right,
      parse: ParseType.Decimal,
      precision: tradeStore.market.amount_precision,
    },
    {
      key: 'created_at',
      title: $t('page.global.table.time'),
      align: Align.Right,
      formatBy: Format.Time,
      parse: ParseType.Time,
      scopedSlots: true,
    },
  ]
})

const tradesColumns = computed<ZTableColumn[]>(() => {
  return [
    {
      key: 'price',
      title: `${$t('page.global.table.price')} (${tradeStore.market.quote_unit.toUpperCase()})`,
      align: Align.Left,
      formatBy: Format.Price,
      sideKey: activeTab.value === 'market_trades' ? 'side' : 'order_side',
      parse: ParseType.Decimal,
      precision: tradeStore.market.price_precision,
    },
    {
      key: 'amount',
      title: `${$t('page.global.table.amount')} (${tradeStore.market.base_unit.toUpperCase()})`,
      align: Align.Right,
      parse: ParseType.Decimal,
      precision: tradeStore.market.amount_precision,
    },
    {
      key: 'created_at',
      title: $t('page.global.table.time'),
      align: Align.Right,
      formatBy: Format.Time,
      parse: ParseType.Time,
      scopedSlots: true,
    },
  ]
})

onMounted(async () => {
  tradeManager.config.market = tradeStore.market_id
  if (userStore.isAuthenticated) {
    await tradeManager.GetData()
  }
})

await useAsyncData(() => tradeStore.FetchPublicTrades(tradeStore.market_id as string, 100), {
  lazy: true,
  default() {
    return []
  },
})
</script>

<template>
  <ZCard class="page-exchange-trades" :head="visibleTab">
    <template #head>
      <ZTab v-model="activeTab" :tabs="tabs" />
    </template>
    <ZTable v-if="activeTab === 'market_trades' || tradeStore.exchange_layout === 'pro'" :columns="columns" :data-source="tradeStore.public_trades.trades" :scroll="true" :loading="tradeStore.public_trades.loading">
      <template #created_at="{ item, index }">
        <ZTooltip v-if="index <= 1" :title="formatDistanceToNow(new Date(item.created_at))" :placement="TooltipPlacement.BottomCenter">
          {{ formatDate(parseISO(item.created_at), 'HH:mm:ss') }}
        </ZTooltip>
        <ZTooltip v-else :title="formatDistanceToNow(new Date(item.created_at))" :placement="TooltipPlacement.TopCenter">
          {{ formatDate(parseISO(item.created_at), 'HH:mm:ss') }}
        </ZTooltip>
      </template>
    </ZTable>
    <TradesHistory v-else id="my_trades" :option-columns="tradesColumns" />
  </ZCard>
</template>

<style lang="less">
.page-exchange-trades {
  @media @mobile {
    padding-top: 0px !important;
  }

  .trades_history {
    .created_at {
      flex: 1;
    }
  }
  .z-card-content {
    > div {
      @media @mobile {
        height: 360px;
      }
    }
  }

  .z-table {
    .z-tooltip {
      &-overlay {
        left: 0;
        z-index: 9999;
      }

      &-top {
        top: -38px;
      }

      &-bottom {
        top: 20px;
      }
    }

    &-empty {
      color: white !important;

      svg {
        .cls-1 {
          fill: rgba(@gray-color, 0.1);
        }

        .cls-2 {
          fill: rgba(@gray-color, 0.5);
        }
      }
    }
  }
}
</style>
