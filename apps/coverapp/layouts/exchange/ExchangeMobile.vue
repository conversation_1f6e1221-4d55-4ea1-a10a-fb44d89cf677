<script setup lang="ts">
import type { ZTabItem } from '@zsmartex/components/types'
import { OrderSide } from '@zsmartex/types'
import ExchangeTicker from '~/layouts/exchange/Ticker.vue'
import ExchangeTradePairs from '~/layouts/exchange/TradePairs.vue'
import ExchangeChart from '~/layouts/exchange/Chart.vue'
import ExchangeOrderBook from '~/layouts/exchange/OrderBook.vue'
import ExchangeTrades from '~/layouts/exchange/Trades.vue'
import ExchangeTradeAction from '~/layouts/exchange/TradeAction.vue'
import ExchangeMineControl from '~/layouts/exchange/MineControl.vue'

const activeTab = ref('chart')
const popupTrade = ref(false)
const popupPairs = ref(false)
const side = ref(OrderSide.Buy)

const tabs: ZTabItem[] = [
  {
    key: 'chart',
    text: 'Chart',
  },
  {
    key: 'order_book',
    text: 'Order Book',
  },
  {
    key: 'trade',
    text: 'Trade',
  },
]

function handleTradeActionSide(s: OrderSide) {
  side.value = s
  popupTrade.value = true
}
</script>

<template>
  <div class="page-exchange-mobile">
    <ExchangeTicker @click="popupPairs = true" />
    <ZTab v-model="activeTab" class="page-exchange-tab" :tabs="tabs" />
    <ExchangeChart v-show="activeTab === 'chart'" class="h-[360px]" />
    <div v-show="activeTab === 'order_book'" class="h-[330px]">
      <ExchangeOrderBook />
    </div>
    <div v-show="activeTab === 'trade'" class="h-[360px]">
      <ExchangeTrades />
    </div>
    <div class="h-[424px] pb-[64px]">
      <ExchangeMineControl />
    </div>
    <div class="page-exchange-mobile-buttons">
      <ZButton class="mr-2 page-exchange-mobile-buttons-green" @click="handleTradeActionSide(OrderSide.Buy)">
        {{ $t('page.global.action.buy').toUpperCase() }}
      </ZButton>
      <ZButton class="ml-2 page-exchange-mobile-buttons-red" @click="handleTradeActionSide(OrderSide.Sell)">
        {{ $t('page.global.action.sell').toUpperCase() }}
      </ZButton>
    </div>
    <ZDrawer v-model="popupTrade" :header="false" :footer="false" height="340" :position="Position.Bottom" @close="popupTrade = false">
      <ExchangeTradeAction :side="side" />
    </ZDrawer>
    <ZDrawer v-model="popupPairs" :header="false" :footer="false" height="600" :position="Position.Bottom" @close="popupPairs = false">
      <ExchangeTradePairs />
    </ZDrawer>
  </div>
</template>
