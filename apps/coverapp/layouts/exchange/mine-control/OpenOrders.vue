<script setup lang="ts">
import type { ZTableColumn } from '@zsmartex/components/types'
import type { Market, Order } from '@zsmartex/types'
import { Align, Format, OrdersManagerType, ParseType } from '@zsmartex/types'
import { roundNumber } from '@zsmartex/utils'
import { onScrollToBottom } from './mixins'

const publicStore = usePublicStore()
const tradeStore = useTradeStore()
const ordersManagerOpenOrders = useOrdersManager('open_orders')

const columns = reactive<ZTableColumn[]>([
  {
    title: $t('page.global.table.date'),
    key: 'created_at',
    align: Align.Left,
    formatBy: Format.DateTimeNoYear,
    parse: ParseType.DateTime,
  },
  {
    title: $t('page.global.table.market'),
    key: 'market',
    align: Align.Left,
    scopedSlots: true,
  },
  {
    title: $t('page.global.table.type'),
    key: 'type',
    align: Align.Left,
  },
  {
    title: $t('page.global.table.side'),
    key: 'side',
    align: Align.Left,
    sideKey: 'side',
    formatBy: Format.Price,
    scopedSlots: true,
  },
  {
    title: $t('page.global.table.triggered_at'),
    key: 'triggered_at',
    align: Align.Left,
    formatBy: Format.DateTimeNoYear,
    parse: ParseType.DateTime,
  },
  {
    title: $t('page.global.table.price'),
    key: 'price',
    align: Align.Center,
    formatBy: Format.Price,
    parse: ParseType.Decimal,
    precision(order: Order) {
      const market = getMarket(order.market) as Market

      return market.price_precision
    },
  },
  {
    title: $t('page.global.table.avg_price'),
    key: 'avg_price',
    align: Align.Center,
    formatBy: Format.Price,
    parse: ParseType.Decimal,
    precision(order: Order) {
      const market = getMarket(order.market)

      return market?.price_precision
    },
    headScopedSlots: true,
  },
  {
    title: $t('page.global.table.filled'),
    key: 'filled',
    align: Align.Center,
    scopedSlots: true,
  },
  {
    title: `${$t('page.global.table.amount')}`,
    key: 'origin_amount',
    align: Align.Right,
    formatBy: Format.Amount,
    parse: ParseType.Decimal,
    precision(order: Order) {
      const market = getMarket(order.market) as Market

      return market.amount_precision
    },
  },
  {
    title: `${$t('page.global.table.total')}`,
    key: 'total',
    align: Align.Right,
    scopedSlots: true,
    headScopedSlots: true,
  },
  {
    key: 'action',
    align: Align.Right,
    scopedSlots: true,
    headScopedSlots: true,
  },
])

const orders = computed(() => {
  return ordersManagerOpenOrders.orders.map((order) => {
    (order as any).total = (Number(order.price) * Number(order.origin_volume))

    return order
  })
})

const loading = computed(() => {
  return ordersManagerOpenOrders.config.loading
})

function getMarket(marketID: string) {
  return publicStore.markets.find(market => market.id === marketID)
}

function onClick(order: Order) {
  const market = getMarket(order.market)
  if (market) {
    navigateTo(`/exchange/${market.base_unit.toUpperCase()}-${market.quote_unit.toUpperCase()}?type=${tradeStore.exchange_layout}`)
  }
}

function getTotal(order: Order) {
  let total = 0
  if (order.state === OrderState.Cancel) total = Number(order.filled_amount) * Number(order.avg_price)
  else total = Number(order.origin_amount) * (Number(order.avg_price) || Number(order.price))

  return roundNumber(total, getMarket(order.market)!.total_precision, true)
}
</script>

<template>
  <ZTable
    class="open_orders"
    :columns="columns"
    :data-source="orders"
    :scroll="true"
    :loading="loading"
    :need-authenticated="true"
    responsive
    @scroll-bottom="onScrollToBottom(OrdersManagerType.OrdersHistory)"
  >
    <template #head.total>
      <ZTooltip title="total is calculated as average price * amount" :placement="TooltipPlacement.TopRight">
        <span class="underline decoration-dashed">{{ `${$t('page.global.table.total')}` }}</span>
      </ZTooltip>
    </template>
    <template #head.avg_price>
      <ZTooltip title="average execution price" :placement="TooltipPlacement.TopCenter">
        <span class="underline decoration-dashed">{{ `${$t('page.global.table.avg_price')}` }}</span>
      </ZTooltip>
    </template>
    <template #ord_type="{ item }">
      {{ $t(`page.global.orders.type.${item.ord_type}`) }}
    </template>
    <template #side="{ item }">
      {{ $t(`page.global.orders.side.${item.side}`) }}
    </template>
    <template #market="{ item }">
      <div class="page-exchange-mine-control-market" @click="onClick(item)">
        {{ getMarket(item.market)?.name }}
      </div>
    </template>
    <template #filled="{ item }">
      {{ (item.filled_amount * 100 / item.origin_amount).toFixed(2) }}%
    </template>
    <template #total="{ item }">
      {{ getTotal(item) }} {{ (item.filled_amount > 0 || item.state === OrderState.Wait) ? item.bid.toUpperCase() : '' }}
    </template>
    <template #action="{ item }">
      <a @click="tradeStore.CancelOrder(item.id)">
        {{ $t('page.global.action.cancel') }}
      </a>
    </template>
  </ZTable>
</template>
