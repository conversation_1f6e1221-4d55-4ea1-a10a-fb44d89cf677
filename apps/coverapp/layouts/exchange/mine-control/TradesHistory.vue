<script setup lang="ts">
import type { ZTableColumn } from '@zsmartex/components/types'
import type { Market, Trade } from '@zsmartex/types'
import { Align, Format, ParseType } from '@zsmartex/types'
import { format as formatDate, formatDistanceToNow, parseISO } from 'date-fns'

const props = defineProps<{
  id: "my_trades" | "trades_history"
  optionColumns?: ZTableColumn[]
}>()

const publicStore = usePublicStore()
const tradesManager = useTradesManager(props.id)
const tradeStore = useTradeStore()

const columns = reactive<ZTableColumn[]>([
  {
    title: $t('page.global.table.date'),
    key: 'created_at',
    align: Align.Left,
    formatBy: Format.DateTimeNoYear,
    parse: ParseType.DateTime,
  },
  {
    title: $t('page.global.table.market'),
    key: 'market',
    align: Align.Left,
    scopedSlots: true,
  },
  {
    title: $t('page.global.table.side'),
    key: 'side',
    sideKey: 'order_side',
    align: Align.Left,
    formatBy: Format.Price,
    scopedSlots: true,
  },
  {
    title: $t('page.global.table.price'),
    key: 'price',
    align: Align.Center,
    formatBy: Format.Price,
    parse: ParseType.Decimal,
    precision(trade: Trade) {
      const market = getMarket(trade.market) as Market

      return market.price_precision
    },
  },
  {
    title: `${$t('page.global.table.amount')}`,
    key: 'amount',
    align: Align.Left,
    formatBy: Format.Amount,
    parse: ParseType.Decimal,
    precision(trade: Trade) {
      const market = getMarket(trade.market) as Market

      return market.amount_precision
    },
  },
  {
    title: `${$t('page.global.table.fee')}`,
    key: 'fee',
    align: Align.Left,
    scopedSlots: true,
  },
  {
    title: `${$t('page.global.table.total')}`,
    key: 'total',
    align: Align.Left,
    formatBy: Format.Price,
    parse: ParseType.Decimal,
    precision(trade: Trade) {
      const market = getMarket(trade.market) as Market

      return market.price_precision
    },
    suffix(trade: Trade) {
      const market = getMarket(trade.market) as Market

      return market.quote_unit.toUpperCase()
    },
  },
])

const trades = computed(() => {
  if (props.optionColumns) return tradesManager.trades.filter(t => t.market === tradeStore.market_id)
  return tradesManager.trades
})

const loading = computed(() => {
  return tradesManager.config.loading
})

function getMarket(marketID: string) {
  return publicStore.markets.find(market => market.id === marketID)
}

function onScrollToBottom() {
  const headers = tradesManager.headers

  tradesManager.GetData(headers.page + 1, headers.limit)
}

function getFeeTrade(trade: Trade) {
  return Number(trade.fee).toFixed(8).replace(/(\.\d*?[1-9])0+$/g, '$1').replace(/^(\d+)(\.0+)?$/, '$1')
}
</script>

<template>
  <ZTable
    class="trades_history"
    :columns="optionColumns || columns"
    :data-source="trades"
    :scroll="true"
    :hover="true"
    :loading="loading"
    :need-authenticated="true"
    responsive
    @scroll-bottom="onScrollToBottom"
  >
    <template #ord_type="{ item }">
      {{ $t(`page.global.orders.type.${item.ord_type}`) }}
    </template>
    <template #side="{ item }">
      {{ $t(`page.global.orders.side.${item.order_side}`) }}
    </template>
    <template #market="{ item }">
      {{ getMarket(item.market)?.name }}
    </template>
    <template #fee="{ item }">
      {{ `${getFeeTrade(item)} ${item.fee_currency.toUpperCase()}` }}
    </template>
    <template #created_at="{ item, index }">
      <ZTooltip v-if="index <= 1" :title="formatDistanceToNow(new Date(item.created_at))" :placement="TooltipPlacement.BottomCenter">
        {{ formatDate(parseISO(item.created_at), 'HH:mm:ss') }}
      </ZTooltip>
      <ZTooltip v-else :title="formatDistanceToNow(new Date(item.created_at))" :placement="TooltipPlacement.TopCenter">
        {{ formatDate(parseISO(item.created_at), 'HH:mm:ss') }}
      </ZTooltip>
    </template>
  </ZTable>
</template>

<style lang="less">
.trades_history {
  .created_at {
    flex: 0 0 150px;
  }

  .market {
    flex: 0 0 150px;
  }

  .side {
    flex: 0 0 100px;
  }
}
</style>
