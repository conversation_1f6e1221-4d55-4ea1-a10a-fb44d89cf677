<script setup lang="ts">
import type { ZTableColumn } from '@zsmartex/components/types'
import { type Market, type Order, OrderState } from '@zsmartex/types'
import { Align, Format, OrdersManagerType, ParseType } from '@zsmartex/types'
import { roundNumber } from '@zsmartex/utils'
import { onScrollToBottom } from './mixins'

const userStore = useUserStore()
const publicStore = usePublicStore()
const tradeStore = useTradeStore()
const ordersManagerOrdersHistory = useOrdersManager('orders_history')

const columns = reactive<ZTableColumn[]>([
  {
    title: $t('page.global.table.date'),
    key: 'created_at',
    align: Align.Left,
    formatBy: Format.DateTimeNoYear,
    parse: ParseType.DateTime,
  },
  {
    title: $t('page.global.table.market'),
    key: 'market',
    align: Align.Left,
    scopedSlots: true,
  },
  {
    title: $t('page.global.table.type'),
    key: 'type',
    align: Align.Left,
  },
  {
    title: $t('page.global.table.side'),
    key: 'side',
    align: Align.Left,
    sideKey: 'side',
    formatBy: Format.Price,
    scopedSlots: true,
  },
  {
    title: $t('page.global.table.triggered_at'),
    key: 'triggered_at',
    align: Align.Left,
    formatBy: Format.DateTimeNoYear,
    parse: ParseType.DateTime,
  },
  {
    title: $t('page.global.table.price'),
    key: 'price',
    align: Align.Center,
    formatBy: Format.Price,
    parse: ParseType.Decimal,
    precision(order: Order) {
      const market = getMarket(order.market) as Market

      return market.price_precision
    },
  },
  {
    title: $t('page.global.table.avg_price'),
    key: 'avg_price',
    align: Align.Center,
    formatBy: Format.Price,
    parse: ParseType.Decimal,
    precision(order: Order) {
      const market = getMarket(order.market)

      return market?.price_precision
    },
    headScopedSlots: true,
  },
  {
    title: $t('page.global.table.executed'),
    key: 'filled_amount',
    align: Align.Center,
    formatBy: Format.Amount,
    parse: ParseType.Decimal,
    precision(order: Order) {
      const market = getMarket(order.market) as Market

      return market.amount_precision
    },
  },
  {
    title: `${$t('page.global.table.amount')}`,
    key: 'origin_amount',
    align: Align.Right,
    formatBy: Format.Amount,
    parse: ParseType.Decimal,
    precision(order: Order) {
      const market = getMarket(order.market) as Market

      return market.amount_precision
    },
  },
  {
    title: `${$t('page.global.table.total')}`,
    key: 'total',
    align: Align.Right,
    scopedSlots: true,
    headScopedSlots: true,
  },
  {
    key: 'action',
    align: Align.Right,
    scopedSlots: true,
    headScopedSlots: true,
  },
])

const orders = computed(() => {
  return ordersManagerOrdersHistory.orders.map((order) => {
    (order as any).total = (Number(order.price) * Number(order.origin_volume))

    return order
  })
})

const loading = computed(() => {
  return ordersManagerOrdersHistory.config.loading
})

function getMarket(marketID: string) {
  return publicStore.markets.find(market => market.id === marketID)
}

function onStateChanged() {
  if (!userStore.isAuthenticated) return

  ordersManagerOrdersHistory.clear()
  ordersManagerOrdersHistory.GetData()
}

const stateColumns = [
  {
    key: 'text',
  },
]

const states = [
  {
    text: $t('page.global.orders.state.all'),
    key: 'all',
  },
  {
    text: $t('page.global.orders.state.done'),
    key: 'done',
  },
  {
    text: $t('page.global.orders.state.cancel'),
    key: 'cancel',
  },
  {
    text: $t('page.global.orders.state.wait'),
    key: 'wait',
  },
]

function getTotal(order: Order) {
  let total = 0
  if (order.state === OrderState.Cancel) total = Number(order.filled_amount) * Number(order.avg_price)
  else total = Number(order.origin_amount) * (Number(order.avg_price) || Number(order.price))

  return roundNumber(total, getMarket(order.market)!.total_precision, true)
}
</script>

<template>
  <ZTable
    class="orders_history"
    :columns="columns"
    :data-source="orders"
    :scroll="true"
    :hover="true"
    :loading="loading"
    :need-authenticated="true"
    responsive
    @scroll-bottom="onScrollToBottom(OrdersManagerType.OrdersHistory)"
  >
    <template #head.total>
      <ZTooltip title="total is calculated as average price * amount" :placement="TooltipPlacement.TopRight">
        <span class="underline decoration-dashed">{{ `${$t('page.global.table.total')}` }}</span>
      </ZTooltip>
    </template>
    <template #head.avg_price>
      <ZTooltip title="average execution price" :placement="TooltipPlacement.TopCenter">
        <span class="underline decoration-dashed">{{ `${$t('page.global.table.avg_price')}` }}</span>
      </ZTooltip>
    </template>
    <template #ord_type="{ item }">
      {{ $t(`page.global.orders.type.${item.ord_type}`) }}
    </template>
    <template #side="{ item }">
      {{ $t(`page.global.orders.side.${item.side}`) }}
    </template>
    <template #market="{ item }">
      {{ getMarket(item.market)?.name }}
    </template>
    <template #[`head.action`]>
      <ZSelect
        v-model="ordersManagerOrdersHistory.state as 'all' | OrderState"
        :data-source="states"
        :search="false"
        :columns="stateColumns"
        :find-by="['key']"
        value-key="key"
        label-key="text"
        :placeholder="$t(`page.global.orders.state.${ordersManagerOrdersHistory.state}`)"
        :placement="Placement.BottomRight"
        @change="onStateChanged"
      />
    </template>
    <template #total="{ item }">
      {{ getTotal(item) }} {{ (item.filled_amount > 0 || item.state === OrderState.Wait) ? item.bid.toUpperCase() : '' }}
    </template>
    <template #action="{ item }">
      <a v-if="item.state === OrderState.Wait" @click="tradeStore.CancelOrder(item.id)">
        {{ $t('page.global.action.cancel') }}
      </a>
      <span v-else :class="[item.state, { 'text-down': item.state === 'reject' }]">
        {{ $t(`page.global.orders.state.${item.state}`) }}
      </span>
    </template>
  </ZTable>
</template>

<style lang="less">
.orders_history {
  .market {
    flex: 0 0 200px;
  }

  .z-select {
    .z-dropdown {
      &-trigger {
        height: 100%;
        justify-content: flex-end;

        &:hover {
          border-color: transparent !important;
        }
      }

      &-triggered {
        border-color: transparent !important;
        box-shadow: none;
      }
    }
  }

  .ord_type {
    flex: 0 0 150px;
  }

  .side {
    flex: 0 0 85px;
  }

  .z-table-head {
    .action {
      position: relative;

      .head-action .z-select-trigger {
        position: absolute;
        right: 8px;
        padding-right: 16px;
        cursor: pointer;

        i {
          font-size: 10px;
          position: absolute;
          top: 50%;
          transform: translateY(-50%) scale(0.5);
        }
      }
    }
  }

  .z-select {
    .z-dropdown {
      &-trigger {
        padding: 0;

        &-icon {
          color: #9EAED6;
        }
      }

      &-overlay {
        width: 125px;
        height: 162px;
        background-color: @exchange-dropdown-background;
        border: 1px solid @exchange-border-color;
        border-radius: 4px;
        text-align: left;
        padding: 4px 0;

        .z-table {
          &-content {
            height: 162px;
          }

          &-row {
            padding: 0 8px;
            margin-left: 0;

            &-selected, &:hover {
              background-color: rgba(0, 0, 0, 0.15);
            }
          }
        }
      }
    }
  }
}
</style>
