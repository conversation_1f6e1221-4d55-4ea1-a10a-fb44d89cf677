<script setup lang="ts">
import { OrderSide } from '@zsmartex/types'

const props = defineProps<{
  modelValue: number
  rowCount: number
  side: OrderSide
}>()
const emit = defineEmits<{
  (event: 'rect', rect: DOMRect): void
  (event: 'update:modelValue', value: number): void
}>()

const value = useVModel(props, 'modelValue', emit)
const depthStore = useDepthStore()
const refs = useTemplateRefsList()

const hoverIndex = ref(-1)

const setHoverIndex = (index: number) => {
  hoverIndex.value = index

  value.value = index
  if (index >= 0) {
    emit('rect', refs.value[index].getBoundingClientRect())
  }
}

const onHoverItemClicked = (index: number) => {
  let amount = 0
  let total = 0
  const depth = depthStore.toArray(props.side, 200)
  if (props.side === OrderSide.Sell) {
    for (let i = 0; i <= props.rowCount - 1 - index; i++) {
      if (!depth[i]) {
        break
      }

      amount += depth[i].amount
      total += depth[i].amount * depth[i].price
    }

    if (amount === 0) {
      return
    }

    useEvent.emit('orderbook:click', {
      price: depth[props.rowCount - 1 - index].price,
      amount,
      total,
    })
  } else {
    for (let i = 0; i <= index; i++) {
      if (!depth[i]) {
        break
      }

      amount += depth[i].amount
      total += depth[i].amount * depth[i].price
    }

    if (amount === 0) {
      return
    }

    useEvent.emit('orderbook:click', {
      price: depth[index].price,
      amount,
      total,
    })
  }
}
</script>

<template>
  <div class="page-exchange-orderbook-hover" :class="[`page-exchange-orderbook-hover-${side}`]">
    <div
      v-for="(_, index) in rowCount"
      :key="index"
      :ref="refs.set"
      class="page-exchange-orderbook-hover-item"
      @mouseover="setHoverIndex(index)"
      @mouseleave="setHoverIndex(-1)"
      @click="onHoverItemClicked(index)"
    />
  </div>
</template>
