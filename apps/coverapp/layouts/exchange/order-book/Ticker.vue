<script setup lang="ts">
import { classChangePercent, roundNumber } from '@zsmartex/utils'

const tradeStore = useTradeStore()
</script>

<template>
  <div class="page-exchange-orderbook-ticker">
    <div class="page-exchange-orderbook-ticker-last" :class="[classChangePercent(tradeStore.ticker.price_change_percent)]">
      {{ roundNumber(tradeStore.ticker.last, tradeStore.market.price_precision) }}
    </div>
    <div class="page-exchange-orderbook-ticker-change" :class="[classChangePercent(tradeStore.ticker.price_change_percent)]">
      {{ tradeStore.ticker.price_change_percent }}
    </div>
  </div>
</template>
