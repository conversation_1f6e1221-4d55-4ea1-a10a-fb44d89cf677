<script setup lang="ts">
import BookAsksIcon from '~/assets/img/book_asks.svg'
import BookBidsIcon from '~/assets/img/book_bids.svg'
import BookAvgIcon from '~/assets/img/book_avg.svg'

const props = defineProps<{
  modelValue: string
}>()
const emit = defineEmits<{
  (event: 'update:modelValue', value: string): void
}>()
const value = useVModel(props, 'modelValue', emit)

const switchItems = [
  {
    key: 'sell',
    icon: BookAsksIcon,
  },
  {
    key: 'avg',
    icon: BookAvgIcon,
  },
  {
    key: 'buy',
    icon: BookBidsIcon,
  },
]
</script>

<template>
  <div class="page-exchange-orderbook-side-switch">
    <div
      v-for="item in switchItems"
      :key="item.key"
      class="page-exchange-orderbook-side-switch-item" :class="[{ 'page-exchange-orderbook-side-switch-item-selected': item.key === value }]"
      @click="value = item.key"
    >
      <img :src="item.icon">
    </div>
  </div>
</template>
