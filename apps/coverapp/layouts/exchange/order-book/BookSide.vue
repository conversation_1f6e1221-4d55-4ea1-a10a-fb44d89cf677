<script setup lang="ts">
import type { ZTableColumn } from '@zsmartex/components/types'
import type { DepthRow } from '@zsmartex/types'
import { OrderSide } from '@zsmartex/types'
import colors from '@zsmartex/colors'

const props = withDefaults(
  defineProps<{
    side: OrderSide
    reverseColumn?: boolean
    reverseData?: boolean
    headEnabled?: boolean
    count: number
    dataSource: DepthRow[]
    index?: boolean
  }>(),
  {
    headEnabled: false,
    reverseColumn: false,
    reverseData: false,
    index: false,
  },
)

const depthStore = useDepthStore()

const columns = computed(() => {
  const result: ZTableColumn[] = [
    {
      key: 'price',
      title: 'Price',
      parse: ParseType.Decimal,
      align: props.reverseColumn ? Align.Right : Align.Left,
      class: `text-${props.side === OrderSide.Buy ? 'up' : 'down'} p${props.reverseColumn ? 'r' : 'l'}-[6px]`,
      precision: depthStore.market.price_precision,
    },
    {
      key: 'amount',
      title: 'Amount',
      align: props.reverseColumn ? Align.Left : Align.Right,
      parse: ParseType.Decimal,
      precision: depthStore.market.amount_precision,
    },
    {
      key: 'style',
      scopedSlots: true,
      class: 'absolute h-full w-full opacity-15',
    },
  ]

  if (props.index) {
    result.push({
      key: 'index',
      title: 'Index',
      scopedSlots: true,
      class: 'flex-initial! w-[24px]',
      align: props.reverseColumn ? Align.Left : Align.Right,
    })
  }

  if (props.reverseColumn) {
    result.reverse()
  }

  return result
})

const data = computed(() => {
  const result: Record<string, any>[] = []
  let data = [...props.dataSource].splice(0, props.count)
  const totalAmount = data.reduce((a, b) => a + b.amount, 0)
  let count = 1

  if (props.reverseData) {
    let currentAmount = totalAmount
    data = data.reverse()
    for (const item of data) {
      currentAmount -= item.amount
      result.push({
        ...item,
        index: count,
        style: `${props.reverseColumn ? 'right: 0' : 'left: 0'}; background-color: ${
          props.side === 'buy' ? colors['up-color'] : colors['down-color']
        }; width: calc(${Math.round((currentAmount / totalAmount) * 100)}%); height: 20px`,
      })
      count++
    }
  } else {
    let currentAmount = 0
    for (const item of data) {
      currentAmount += item.amount
      result.push({
        ...item,
        index: count,
        style: `${props.reverseColumn ? 'right: 0' : 'left: 0'}; background-color: ${
          props.side === 'buy' ? colors['up-color'] : colors['down-color']
        }; width: calc(${Math.round((currentAmount / totalAmount) * 100)}%); height: 20px`,
      })
      count++
    }
  }

  const length = result.length
  for (let i = 0; i < props.count - length; i++) {
    if (props.reverseData) {
      result.unshift({
        index: count,
        price: '---',
        amount: '---',
      })
    } else {
      result.push({
        index: count,
        price: '---',
        amount: '---',
      })
    }
    count++
  }

  return result
})

// const onClick = (order: DepthRow, index: number) => {
//   let amount = 0
//   const price = order.price
//   if (props.side === OrderSide.Sell) {
//     for (let i = 0; i <= props.count - 1 - index; i++) {
//       amount += props.dataSource[i].amount
//     }
//   } else {
//     for (let i = 0; i <= index; i++) {
//       amount += props.dataSource[i].amount
//     }
//   }
//   ZEventBus.emit('orderbook-click', {
//     price,
//     amount,
//   })
// }
</script>

<template>
  <div class="z-side">
    <ZTable
      :data-source="data"
      :columns="columns"
      :head-enabled="headEnabled"
    >
      <template #style="{ item }">
        <div class="absolute" :style="item.style" />
      </template>
      <template #index="{ item }">
        <span class="text-gray">{{ item.index }}</span>
      </template>
    </ZTable>
  </div>
</template>

<style lang="less">
.z-side {
  .z-table {
    &-head {
      height: 1.75rem;
    }

    &-row {
      position: relative;
      height: 20px;
      padding: 0;

      &-style {
        position: absolute;
        height: 100%;
        top: 0;
        opacity: 0.15;
      }
    }
  }
}
</style>
