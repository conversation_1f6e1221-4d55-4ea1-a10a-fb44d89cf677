<script setup lang="ts">
import { OrderSide } from '@zsmartex/types'
import type { Row } from '~/library/orderbook-table/config'
import { roundNumber } from '../../../../../packages/utils';

const props = withDefaults(defineProps<{
  hoverIndex?: number
  rect?: DOMRect
  side: OrderSide
  depth: Row[]
  rowCount: number
  lineHeight: number
}>(), {
  hoverIndex: -1,
})

const tradeStore = useTradeStore()

const depthHover = computed(() => {
  return (props.side === OrderSide.Buy ? props.depth.slice(0, props.hoverIndex + 1) : props.depth.slice(props.hoverIndex, props.rowCount)).filter(row => !row.fake)
})

const avgPrice = computed(() => {
  return roundNumber(depthHover.value.map(order => order.depth_row.price).reduce((a, b) => Number(a) + Number(b), 0) / depthHover.value.length || 0, tradeStore.market.price_precision, true)
})

const maskHeight = computed(() => {
  if (props.hoverIndex === -1) return 0

  if (props.side === OrderSide.Buy) {
    return props.lineHeight * (props.hoverIndex + 1)
  } else {
    return props.lineHeight * (props.rowCount - props.hoverIndex)
  }
})

const isShowing = computed(() => {
  return maskHeight.value > 0
})

const sumAmount = computed(() => {
  return Number(depthHover.value.map(order => order.depth_row.amount.replace(/,/g, '')).reduce((a, b) => Number(a) + Number(b), 0)).toFixed(tradeStore.market.amount_precision).replace(/(\.\d*?[1-9])0+$/g, '$1');
})

const sumTotal = computed(() => {
  return Number(depthHover.value.map(order => order.depth_row.total.replace(/,/g, '')).reduce((a, b) => Number(a) + Number(b), 0)).toFixed(tradeStore.market.total_precision).replace(/(\.\d*?[1-9])0+$/g, '$1');
})
</script>

<template>
  <div
    class="page-exchange-orderbook-overlay" :class="[
      `page-exchange-orderbook-overlay-${side}`,
    ]"
  >
    <div
      class="page-exchange-orderbook-overlay-mask"
      :style="`height: ${maskHeight}px`"
    />
    <div
      v-if="isShowing"
      class="page-exchange-orderbook-overlay-content"
      :style="`top: ${rect?.top}px`"
    >
      <div class="page-exchange-orderbook-overlay-item">
        <span style="font-size: 16px;">{{ $t('page.global.table.avg_price') }}</span>
        <span>≈ {{ avgPrice }}</span>
      </div>
      <div class="page-exchange-orderbook-overlay-item">
        <span>{{ $t('page.exchange.order_book.overlay.sum') }} ({{ tradeStore.market.base_unit.toUpperCase() }})</span>
        <span>{{ sumAmount }}</span>
      </div>
      <div class="page-exchange-orderbook-overlay-item">
        <span>{{ $t('page.exchange.order_book.overlay.sum') }} ({{ tradeStore.market.quote_unit.toUpperCase() }})</span>
        <span>{{ sumTotal }}</span>
      </div>
    </div>
  </div>
</template>
