<script setup lang="ts">
import { screenDevice, roundNumber } from '@zsmartex/utils'
import { OrderSide } from '@zsmartex/types'
import colors from '@zsmartex/colors'
import OrderBookOverlay from './Overlay.vue'
import OrderBookHover from './Hover.vue'
import OrderBookTable from '@/library/orderbook-table'
import type { Row } from '~/library/orderbook-table/config'

const props = defineProps<{
  rowCount: number
  side: OrderSide
  loading: boolean
  type: string
}>()

const depthStore = useDepthStore()
const tradeStore = useTradeStore()

let orderbookTable: OrderBookTable
const refSide = templateRef<HTMLElement>('side')
const canvas = templateRef<HTMLElement>('canvas')
const lineHeight = 20
const hoverIndex = ref(-1)
const clientHeight = ref(0)
const rect = ref<DOMRect>()

const style = computed(() => {
  return props.side === OrderSide.Buy
    ? 'position: absolute;width: 100%;top: 0;'
    : 'position: absolute;width: 100%;bottom: 0px;'
})

const maxSum = computed(() => {
  const depth = depthStore.toArray(props.side, 200)
  let i = 0
  let total = 0

  for (const row of depth) {
    total += Number(row.price) * Number(row.amount)
  }

  return total
})

const depth = computed(() => {
  const depth = depthStore.toArray(props.side, 200)
  const market = tradeStore.market

  const _depth = depth.map<Row>((depthRow) => {
    const row = {
      depth_row: {
        price: roundNumber(depthRow.price, depthStore.decimalRound),
        amount: roundNumber(depthRow.amount, market.amount_precision),
        total: roundNumber(depthRow.price * depthRow.amount, market.total_precision),
      },
      change: depthRow.change,
      removing: depthRow.removing,
      fake: false,
    } as Row

    row.backgroundWidth = Math.min((Number(depthRow.price * depthRow.amount) / maxSum.value) * 100 * 3, 100)
    row.backgroundColor = props.side === OrderSide.Buy ? colors['up-bg-color'] : colors['down-bg-color']

    return row
  }) as Row[]

  const depthCount = _depth.length

  for (let index = 0; index < props.rowCount - depthCount; index++) {
    _depth.push({
      fake: true,
    } as Row)
  }

  return props.side === OrderSide.Sell ? _depth.reverse() : _depth
})

const drawDepth = () => {
  if (process.server) return
  if (!orderbookTable) return

  orderbookTable.setData(depth.value)
  orderbookTable.draw_table()
}

const device = screenDevice()

onMounted(async () => {
  if (process.server) return

  while (refSide.value) {
    clientHeight.value = refSide.value.clientHeight
    if (props.type === 'sell') {
      refSide.value.scrollTop = refSide.value.scrollHeight
    }
    break
  }

  const width = device.value === ScreenDevice.Tablet ? ref(globalThis.document.documentElement.clientWidth).value / 3 : 320

  // eslint-disable-next-line no-unreachable-loop
  while (canvas.value) {
    orderbookTable = new OrderBookTable(canvas.value, {
      rowCount: props.rowCount,
      height: props.rowCount * lineHeight,
      width,
      line_height: lineHeight,
      columns: [
        {
          key: 'price',
          color:
            props.side === OrderSide.Buy
              ? colors['up-color']
              : colors['down-color'],
          fontSize: 12,
          align: 'left',
          x: 16,
        },
        {
          key: 'amount',
          color: colors['exchange-text-color'],
          fontSize: 12,
          align: 'right',
          x: (width / 3) * 2 - 8,
        },
        {
          key: 'total',
          color: colors['exchange-text-color'],
          fontSize: 12,
          align: 'right',
          x: width - 16,
        },
      ],
    })
    orderbookTable.init()
    drawDepth()
    break
  }
})

watch(depth, () => {
  drawDepth()
})
</script>

<template>
  <div
    ref="side"
    class="page-exchange-orderbook-side" :class="[
      `page-exchange-orderbook-side-${side}`,
    ]"
  >
    <OrderBookOverlay
      :line-height="lineHeight"
      :hover-index="hoverIndex"
      :row-count="rowCount"
      :side="side"
      :depth="depth"
      :rect="rect"
      :style="`position: absolute;width: 100%;${side === 'buy' ? 'top' : 'bottom'}: ${type === 'sell' ? -(lineHeight * rowCount - clientHeight) : 0}px;`"
    />
    <div ref="canvas" :style="`${type !== 'avg' ? 'position: absolute;width: 100%;top: 0;' : style}display:flex;`" />
    <OrderBookHover
      v-model="hoverIndex"
      :row-count="rowCount"
      :side="side"
      :style="`position: absolute;width: 100%;${side === 'buy' ? 'top' : 'bottom'}: ${type === 'sell' ? -(lineHeight * rowCount - clientHeight) : 0}px;`"
      @rect="(_rect) => (rect = _rect)"
    />
    <ZLoading v-if="loading" />
  </div>
</template>
