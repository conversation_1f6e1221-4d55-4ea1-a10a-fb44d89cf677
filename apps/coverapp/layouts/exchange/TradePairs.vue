<script setup lang="ts">
import { findBy, screenDevice } from '@zsmartex/utils'
import type { ZTableColumn } from '@zsmartex/components/types'
import type { Ticker } from '@zsmartex/types'

import { Align, Format, ParseType, SortBy } from '@zsmartex/types'

const publicStore = usePublicStore()
const tradeStore = useTradeStore()
const tabSelected = useState(() => tradeStore.market.quote_unit)
const search = ref('')
const query = useState<Record<string, string>>(() => ({}))

const extraQuoteList = computed(() => {
  const extraQuoteList: string[] = []

  for (const market of publicStore.enabledMarkets) {
    if (!publicStore.quote_list.includes(market.quote_unit) && !extraQuoteList.includes(market.quote_unit)) {
      extraQuoteList.push(market.quote_unit)
    }
  }

  return extraQuoteList
})

const device = screenDevice()

const columns = computed<ZTableColumn[]>(() => ([
  {
    key: 'favorite',
    scopedSlots: true,
    hideColumn: true,
  },
  {
    key: 'market.base_unit',
    title: $t('page.global.table.currency'),
    sort: search.value.length < 1,
    sortBy: SortBy.String,
    scopedSlots: true,
  },
  {
    key: 'last',
    title: $t('page.global.table.price'),
    sort: search.value.length < 1,
    sortBy: SortBy.Number,
    align: Align.Right,
    parse: ParseType.Decimal,
    precision: (ticker: Ticker) => ticker.market.price_precision,
  },
  {
    key: 'price_change_percent',
    title: $t('page.global.table.change'),
    align: Align.Right,
    sort: search.value.length < 1,
    sortBy: SortBy.Number,
    sideKey: 'price_change_percent',
    formatBy: Format.Change,
  },
]))

const quoteList = computed(() => {
  if (device.value === ScreenDevice.Desktop || device.value === ScreenDevice.LargeDesktop) return publicStore.quote_list.slice(0, 4)
  else return publicStore.quote_list
})

const tickersFilter = computed(() => {
  return findBy(publicStore.enabledTickers, ['id', 'market.name', 'market.base_currency.name'], search.value).filter((ticker) => {
    if (tabSelected.value === 'favorites') {
      return tradeStore.favorites.includes(ticker.market.id as string)
    } else if (tabSelected.value === 'all') {
      return true
    } else {
      return tabSelected.value === ticker.market.quote_unit
    }
  })
})

const searchTickers = computed(() => {
  return findBy(publicStore.enabledTickers, ['id', 'market.name', 'market.base_currency.name'], search.value)
})

const selectedIndex = computed(() => {
  return tickersFilter.value.findIndex(ticker => ticker.id === tradeStore.market_id)
})

watch(() => tradeStore.market.quote_unit, () => {
  if (tabSelected.value === 'favorites') return

  tabSelected.value = tradeStore.market.quote_unit
})
</script>

<template>
  <ZCard class="page-exchange-trade-pairs">
    <div class="page-exchange-trade-pairs-search">
      <ZInputSearch v-model="search" placeholder="Search" />
    </div>
    <div v-if="search.length < 1" class="page-exchange-trade-pairs-tabs">
      <div
        class="page-exchange-trade-pairs-tabs-item page-exchange-trade-pairs-tabs-favorite" :class="[{ 'page-exchange-trade-pairs-tabs-item-selected': 'favorites' === tabSelected }]"
        @click="tabSelected = 'favorites'"
      >
        <ZIconStarFilled />
      </div>
      <div
        v-for="(currency_id, index) in quoteList"
        :key="index"
        class="page-exchange-trade-pairs-tabs-item bold-text" :class="[{ 'page-exchange-trade-pairs-tabs-item-selected': currency_id === tabSelected }]"
        @click="tabSelected = currency_id"
      >
        {{ currency_id.toUpperCase() }}
      </div>
      <!-- TODO: add support dropdown for more quote list -->
      <div class="page-exchange-trade-pairs-tabs-more flex justify-center px-[4px]">
        <ZDropdown trigger="click" :placement="Placement.BottomRight" class="w-full">
          <ZIcon type="arrow-down" />
          <template #overlay>
            <div
              v-for="item in extraQuoteList"
              :key="item"
              class="page-exchange-trade-pairs-tabs-item page-exchange-trade-pairs-tabs-others"
              :class="[{ 'page-exchange-trade-pairs-tabs-item-selected': item === tabSelected }]"
              @click="tabSelected = item"
            >
              <span>{{ item.toUpperCase() }}</span>
            </div>
          </template>
        </ZDropdown>
      </div>
    </div>
    <ZTable
      v-if="search.length < 1"
      v-model:query="query"
      class="page-exchange-trade-pairs-table"
      :columns="columns"
      :data-source="tickersFilter"
      :hover="true"
      :scroll="true"
      :is-router-link="true"
      :router-builder="`/exchange/#{market.base_unit.toUpper}-#{market.quote_unit.toUpper}?type=${tradeStore.exchange_layout}`"
      :selected-index="selectedIndex"
    >
      <template #favorite="{ item }">
        <span :class="[{ 'favorite-selected': tradeStore.favorites.includes(item.id) }]">
          <ZIconStarFilled @click.prevent="tradeStore.ChangeFavorite(item.id)" />
        </span>
      </template>
      <template #market.base_unit="{ item }">
        <div class="flex items-end">
          {{ item.market.base_unit.toUpperCase() }}/<span class="text-gray text-[11px]">{{ item.market.quote_unit.toUpperCase() }}</span>
        </div>
      </template>
    </ZTable>
    <ZTable
      v-else
      v-model:query="query"
      class="page-exchange-trade-pairs-table"
      :columns="columns"
      :data-source="searchTickers"
      :hover="true"
      :scroll="true"
      :is-router-link="true"
      :router-builder="`/exchange/#{market.base_unit.toUpper}-#{market.quote_unit.toUpper}?type=${tradeStore.exchange_layout}`"
    >
      <template #favorite="{ item }">
        <span :class="[{ 'favorite-selected': tradeStore.favorites.includes(item.id) }]">
          <ZIconStarFilled @click.prevent="tradeStore.ChangeFavorite(item.id)" />
        </span>
      </template>
      <template #market.base_unit="{ item }">
        <div class="flex items-end">
          {{ item.market.base_unit.toUpperCase() }}/<span class="text-gray text-[11px]">{{ item.market.quote_unit.toUpperCase() }}</span>
        </div>
      </template>
    </ZTable>
  </ZCard>
</template>

<style lang="less">
.page-exchange-trade-pairs {
  .z-card-content {
    display: flex;
    flex-direction: column;
  }

  @media @tablet {
    position: absolute;
    left: 0;
    top: 100%;
    width: 420px !important;
    height: 300px !important;
    z-index: 9;
    box-shadow: rgba(0, 0, 0, 0.35) 0px 5px 15px !important;
  }

  .z-card-content {
    @media @tablet {
      flex-direction: column;
    }

    & > div {
      @media @tablet {
        margin-right: 0 !important;
      }
    }
  }

  &-search {
    padding: 4px 8px;

    @media @mobile {
      margin-bottom: 8px;
      padding: 0;
    }

    @media @tablet {
      margin-bottom: 8px;
      padding: 0;
      width: 100%;
    }

    .z-input-search {
      background-color: @exchange-layout-background-color;
    }
  }

  &-tabs {
    display: flex;
    height: 35px;
    line-height: 35px;
    background-color: @exchange-border-color;
    user-select: none;

    &-others {
      width: 62px;
      height: 24px;
      line-height: 24px;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 11px;
      background-color: @exchange-border-color;
      border-bottom: 1px solid @exchange-layout-background-color;

      &:hover {
        background-color: @exchange-card-background;
      }
    }

    @media @mobile, @tablet {
      margin-bottom: 4px;
      height: auto;
      line-height: normal;
      background-color: transparent;
      overflow-x: auto;
    }

    @media @tablet {
      height: 20px;
      width: 100%;
    }

    &-item {
      flex: 1;
      text-align: center;
      border-right: 1px solid @exchange-card-background;
      cursor: pointer;

      @media @mobile, @tablet {
        flex: none;
        display: flex;
        align-items: center;
      }

      & + .page-exchange-trade-pairs-tabs-item {
        padding: 0 4px;
      }

      &-selected {
        background-color: @primary-color !important;

        @media @mobile, @tablet {
          color: @primary-color;
          background-color: transparent !important;
        }
      }

      &:last-child {
        border-right: none;
      }
    }

    &-more {
      flex: 0;

      .z-overlay {
        right: -5px;
      }

      i {
        font-size: 10px;
      }

      &-table {
        &-item {
          display: block;
        }
      }
    }

    &-favorite {
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 18px;

      svg {
        width: 24px;
        height: 24px;
        fill: #9eaed6;
      }

      @media @mobile, @tablet {
        font-size: 14px;
      }
    }
  }

  .z-table {
    @media @tablet {
      height: 100%;
    }

    &-head, &-row {
      @media @tablet {
        padding: 0;
      }
    }

    &-empty {
      color: white !important;

      svg {
        .cls-1 {
          fill: rgba(@gray-color, 0.1);
        }

        .cls-2 {
          fill: rgba(@gray-color, 0.5);
        }
      }
    }

    &-head-sort-caret {
      .up {
        top: -5px;
      }

      .down {
        bottom: 11px;
      }
    }

    &-row {
      &-selected {
        &::before {
          @media @mobile, @tablet {
            background-color: transparent !important;
          }
        }
      }
    }

    .favorite {
      display: flex;
      justify-content: center;
      align-items: center;
      position: absolute;
      height: 100%;

      span {
        display: flex;
        justify-content: center;
        align-items: center;

        svg {
          width: 16px;
          height: 16px;

          .cls-1 {
            fill: @exchange-gray-color;
          }
        }
      }

      &-selected {
        color: @primary-color;

        .cls-1 {
          fill: @primary-color !important;
        }
      }
    }

    .base_unit {
      padding-left: 24px;
    }
  }
}
</style>
