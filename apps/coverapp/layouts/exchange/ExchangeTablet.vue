<script setup lang="ts">
import { OrderSide } from '@zsmartex/types'
import ExchangeTicker from '~/layouts/exchange/Ticker.vue'
import ExchangeChart from '~/layouts/exchange/Chart.vue'
import ExchangeOrderBook from '~/layouts/exchange/OrderBook.vue'
import ExchangeTrades from '~/layouts/exchange/Trades.vue'
import ExchangeTradeAction from '~/layouts/exchange/TradeAction.vue'
import ExchangeMineControl from '~/layouts/exchange/MineControl.vue'

const side = ref(OrderSide.Buy)
</script>

<template>
  <div class="grid gap-1 p-1 h-screen w-screen page-exchange-tablet">
    <ExchangeTicker />
    <ExchangeChart />
    <ExchangeTrades />
    <ExchangeOrderBook />
    <ExchangeMineControl />
    <div class="page-exchange-trade-action">
      <div class="page-exchange-tablet-side bold-text">
        <ZButton
          :class="{ buy: side === OrderSide.Buy }"
          @click="side = OrderSide.Buy"
        >
          {{ $t('page.global.action.buy').toUpperCase() }}
        </ZButton>
        <ZButton
          :class="{ sell: side === OrderSide.Sell }"
          @click="side = OrderSide.Sell"
        >
          {{ $t('page.global.action.sell').toUpperCase() }}
        </ZButton>
      </div>
      <ExchangeTradeAction :side="side" />
    </div>
  </div>
</template>
