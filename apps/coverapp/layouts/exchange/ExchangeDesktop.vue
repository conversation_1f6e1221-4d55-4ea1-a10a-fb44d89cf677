<script setup lang="ts">
import ExchangeTicker from '~/layouts/exchange/Ticker.vue'
import ExchangeTradePairs from '~/layouts/exchange/TradePairs.vue'
import ExchangeChart from '~/layouts/exchange/Chart.vue'
import ExchangeOrderBook from '~/layouts/exchange/OrderBook.vue'
import ExchangeTrades from '~/layouts/exchange/Trades.vue'
import ExchangeTradeAction from '~/layouts/exchange/TradeAction.vue'
import ExchangeMineControl from '~/layouts/exchange/MineControl.vue'

const tradeStore = useTradeStore()
</script>

<template>
  <ZLayoutContent class="page-exchange-desktop" :class="[tradeStore.exchange_layout]">
    <ExchangeTicker />
    <ExchangeTradePairs />
    <ExchangeChart />
    <ExchangeOrderBook />
    <ExchangeTrades />
    <ExchangeMineControl />
    <ExchangeTradeAction />
  </ZLayoutContent>
</template>
