<script setup lang="ts">
import FingerprintJS from '@fingerprintjs/fingerprintjs'
import { type ZAuthFormField, ZAuthFormFieldType, type ZTableColumn } from '@zsmartex/components/types'
import type { Country } from '@zsmartex/types'
import Validate from '~/validation/validate'
import type Button from '#components/Button.vue'
import countries from '~/library/countries'

definePageMeta({
  middleware: ['guest-only'],
})

defineOgImageComponent('BlogPost')

const runtimeConfig = useRuntimeConfig()

useSeoMeta({
  title: 'Safetrade - Login',
  ogTitle: 'Safetrade - Login',
  description: 'Safetrade is a secure online platform for trading, transferring, and storing cryptocurrency.',
  ogDescription: 'Safetrade is a secure online platform for trading, transferring, and storing cryptocurrency.',
  ogUrl: runtimeConfig.public.mainSiteUrl,
  twitterTitle: 'Safetrade - Login',
  twitterSite: 'SafeTrade',
})

const delayButtonEmail = ref<InstanceType<typeof Button>>()
const delayButtonPhone = ref<InstanceType<typeof Button>>()

const userStore = useUserStore()
const extendUserStore = useExtendUserStore()

const hiddenForm = ref(false)

const qrSession = ref<{
  type: string
  state: string
  value: string
}>()

const activeTab = ref('email')
const searchBoxVisible = ref(false)
const number = ref('')
const region = ref('VN')

const codeSelected = computed(() => {
  return countries.find(c => c.code === unref(region))?.mobile_code as string
})

const phoneModalSelectColumns: ZTableColumn[] = [
  {
    key: 'mobile_code',
    scopedSlots: true,
  },
]

const hiddenQRCode = computed(() => userStore.need_email)

let checkQRSessionTimer: NodeJS.Timeout

async function startCheckQRSession() {
  checkQRSessionTimer = setInterval(async () => {
    if (!qrSession.value) return

    try {
      const { data } = await userStore.CheckQRCodeSession(qrSession.value?.value)
      qrSession.value.state = data.state

      if (qrSession.value.state === 'confirmed') {
        clearInterval(checkQRSessionTimer)
        await userStore.GetLogged()
        Message.success({
          message: $t('success.login'),
        })
      }
    } catch (error) {
      clearInterval(checkQRSessionTimer)
      qrSession.value.state = 'expired'
    } finally {
      if (['reject'].includes(qrSession.value.state)) {
        clearInterval(checkQRSessionTimer)
      }
    }
  }, 1000)
}

async function generateQrSession() {
  const agent = await FingerprintJS.load()
  const result = await agent.get()

  const { data } = await userStore.CreateQRCodeSession(result.visitorId)
  qrSession.value = {
    type: 'z-session',
    state: 'pending',
    value: data.qr_code,
  }

  startCheckQRSession()
}

if (process.client) {
  await generateQrSession()
}

const needCode = computed(() => {
  return userStore.need_otp || userStore.need_email || userStore.need_phone
})

const fields = computed(() => {
  const fields: ZAuthFormField[] = [
    {
      key: 'password',
      hidden: needCode.value,
      label: $t('page.global.label.password'),
      name: 'password',
      type: ZAuthFormFieldType.Input,
      inputType: InputType.Password,
      placeholder: $t('page.global.placeholder.password'),
      required: true,
      validate: [Validate.pattern(/^(?=.{8,})/)],
      transformErrors: {
        'input.error.pattern': $t('page.global.error.password'),
      },
    },
    {
      key: 'email_code',
      name: 'email_code',
      type: ZAuthFormFieldType.Input,
      inputType: InputType.Number,
      hidden: !userStore.need_email,
      label: $t('page.global.label.email_code'),
      placeholder: $t('page.global.placeholder.e-confirmation_code'),
      required: userStore.need_email,
      maxLength: 6,
      validate: [Validate.minLength(6)],
      transformErrors: {
        'input.error.min_length': $t('page.global.error.min_length', { min: 6 }),
      },
      autoFocus: userStore.need_email,
    },
    {
      key: 'phone_code',
      name: 'phone_code',
      type: ZAuthFormFieldType.Input,
      inputType: InputType.Number,
      hidden: !userStore.need_phone,
      label: $t('page.global.label.phone_code'),
      placeholder: $t('page.global.placeholder.phone_confirmation_code'),
      required: userStore.need_phone,
      maxLength: 6,
      validate: [Validate.minLength(6)],
      transformErrors: {
        'input.error.min_length': $t('page.global.error.min_length', { min: 6 }),
      },
    },
    {
      key: 'otp_code',
      name: 'otp_code',
      type: ZAuthFormFieldType.Input,
      inputType: InputType.Number,
      hidden: !userStore.need_otp,
      label: $t('page.global.label.otp_code'),
      placeholder: $t('page.global.placeholder.otp_code'),
      required: userStore.need_otp,
      maxLength: 6,
      validate: [Validate.minLength(6)],
      transformErrors: {
        'input.error.min_length': $t('page.global.error.min_length', { min: 6 }),
      },
      autoFocus: !userStore.need_email,
    },
  ]

  if (activeTab.value === 'email') {
    fields.unshift(
      {
        key: 'email',
        name: 'email',
        hidden: needCode.value,
        label: $t('page.global.label.email'),
        type: ZAuthFormFieldType.Input,
        inputType: InputType.Text,
        placeholder: $t('page.global.placeholder.email'),
        required: true,
        validate: [Validate.email],
        transformErrors: {
          'input.error.email': $t('page.global.error.email'),
        },
      },
    )
  }

  return fields
})

async function GenerateLoginCode(type: 'email' | 'phone') {
  const button = type === 'email' ? delayButtonEmail : delayButtonPhone

  await userStore.GenerateCodeLogin(
    userStore._cache.email,
    userStore._cache.phone_number,
    userStore._cache.region,
    type,
  )

  button.value?.StartDelay()
}

onBeforeUnmount(() => {
  userStore.need_otp = false
  userStore.need_email = false
  userStore.need_phone = false
  clearInterval(checkQRSessionTimer)
})

async function login({ email, password, email_code: emailCode, otp_code: OTPCode, phone_code: phoneCode }: { email: string; password: string; email_code: string; phone_code: string; otp_code: string }) {
  const params: Record<string, string> = {
    password,
  }

  if (email) {
    params.email = email
  } else {
    params.phone_number = number.value
    params.region = region.value
  }

  await userStore.Login(params, {
    email_code: emailCode,
    phone_code: phoneCode,
    otp_code: OTPCode,
  }, extendUserStore.CallbackLogin)
}

watch(() => userStore.loginLoading, (loading) => {
  if (loading) {
    clearInterval(checkQRSessionTimer)
  }
})

let hiddenFormQueue: NodeJS.Timeout | undefined

function onSearchBoxClicked(item: Country) {
  searchBoxVisible.value = false
  region.value = item.code
}

async function showForm() {
  if (!hiddenForm.value) {
    return
  }

  hiddenForm.value = false
}

async function hideForm() {
  if (hiddenFormQueue) {
    return
  }

  if (hiddenForm.value) {
    return
  }

  hiddenFormQueue = setTimeout(() => {
    hiddenForm.value = true
    hiddenFormQueue = undefined
  }, 100)
}

async function onFormMouseLeave() {
  showForm()

  if (hiddenFormQueue) {
    clearTimeout(checkQRSessionTimer)
  }
}

watch(() => userStore.need_email, async (needEmail) => {
  if (needEmail) {
    await userStore.GenerateCodeLogin(userStore._cache.email, userStore._cache.phone_number, userStore._cache.region, 'email')
    delayButtonEmail.value?.StartDelay()
  }
})
</script>

<template>
  <ZLayoutContent class="flex justify-center">
    <div class="page-login-group-form" :class="{ 'hidden-qr': hiddenQRCode || !runtimeConfig.public.qrLogin, 'block-qr': !runtimeConfig.public.qrLogin }" @mouseleave="onFormMouseLeave">
      <ZAuthForm
        class="pt-0! inline-block"
        :class="{ 'hidden-form': hiddenForm }"
        :title="$t('page.login.form.login.title', { name: runtimeConfig.public.exchangeName })"
        :submit-text="$t('page.global.action.submit')"
        :loading="userStore.loginLoading"
        :fields="fields"
        :values-other="number.length === 0 && activeTab === 'phone'"
        @submit="login"
      >
        <!-- <template v-if="!needCode" #head>
          <div class="page-login-side">
            <div class="page-login-side-item" :class="{ active: activeTab === 'email' }" @click="activeTab = 'email'">
              {{ $t('page.global.email') }}
            </div>
            <div class="page-login-side-item" :class="{ active: activeTab === 'phone' }" @click="activeTab = 'phone'">
              {{ $t('page.global.phone') }}
            </div>
          </div>
        </template> -->
        <template v-if="activeTab === 'phone' && !needCode" #phone>
          <div class="page-login-phone relative flex">
            <ZSearchBox
              v-model="searchBoxVisible"
              :data-source="countries"
              :columns="phoneModalSelectColumns"
              :find-by="['mobile_code', 'name']"
              @click="onSearchBoxClicked"
            >
              +{{ codeSelected }}
              <ZIconAngleDownFilled class="ml-2" />
              <template #mobile_code="{ item }">
                +{{ item.mobile_code }} {{ item.name }}
              </template>
            </ZSearchBox>
            <ZCol>
              <ZAuthInput
                v-model="number"
                :type="InputType.Number"
                :placeholder="$t('page.global.placeholder.phone_number')"
                name="phone_number"
              />
            </ZCol>
          </div>
        </template>
        <template #email_code-suffix>
          <ZButton
            ref="delayButtonEmail"
            :delay="{
              time: 60,
              content: 'Get [#{time}] again',
            }"
            @click="GenerateLoginCode('email')"
          >
            {{ $t('page.global.action.get_code') }}
          </ZButton>
        </template>
        <template #phone_code-suffix>
          <ZButton
            ref="delayButtonPhone"
            :delay="{
              time: 60,
              content: 'Get [#{time}] again',
            }"
            @click="GenerateLoginCode('phone')"
          >
            {{ $t('page.global.action.get_code') }}
          </ZButton>
        </template>
        <div class="page-login-links">
          <nuxt-link to="/forgot-password" class="bold-text">
            {{ $t("page.global.action.forgot_password") }}
          </nuxt-link>
          <nuxt-link to="/register" class="bold-text">
            {{ $t("page.global.action.register") }}
          </nuxt-link>
        </div>
      </ZAuthForm>
      <div v-if="runtimeConfig.public.qrLogin" class="page-login-qr-session" :class="{ 'page-login-qr-session-full': hiddenForm }">
        <div class="page-login-qr-session-row" :class="{ 'justify-center!': !hiddenForm }">
          <div class="page-login-qr-session-row-img">
            <img src="https://b.peatio.com/anduin/sign-in/qrcode_left_en.png">
          </div>
          <div class="qr-code flex justify-center items-center" @mouseenter="hideForm">
            <ClientOnly>
              <ZQRCode v-if="qrSession" :key="qrSession.value" :model-value="JSON.stringify({ value: qrSession.value, type: qrSession.type })" :width="120" />
            </ClientOnly>
            <div v-if="qrSession && qrSession.state !== 'pending'" class="qr-code-mark">
              <div class="text-center">
                <template v-if="qrSession.state === 'expired'">
                  {{ $t('page.login.qrcode.expired') }}
                  <ZButton type="primary" @click="generateQrSession">
                    {{ $t('page.global.action.refresh') }}
                  </ZButton>
                </template>
                <template v-if="qrSession.state === 'scan'">
                  <ZIcon type="check" />
                  {{ $t('page.login.qrcode.confirm_app') }}
                </template>
                <template v-if="qrSession.state === 'confirmed'">
                  <ZIcon type="check" />
                  {{ $t('page.login.qrcode.login_successful') }}
                </template>
                <template v-if="qrSession.state === 'reject'">
                  <span class="text-down">
                    {{ $t('page.login.qrcode.client_denied') }}
                  </span>
                  <ZButton type="primary" @click="generateQrSession">
                    {{ $t('page.global.action.refresh') }}
                  </ZButton>
                </template>
              </div>
            </div>
          </div>
        </div>
        <div class="title text-center">
          {{ $t('page.login.qrcode.login_qrcode') }}
        </div>
        <I18n tag="p" class="desc text-center" path="page.login.qrcode.scan_qrcode">
          <template #name>
            <a>{{ $t('page.login.qrcode.app_name', { exchange_name: runtimeConfig.public.exchangeName }) }}</a>
          </template>
        </I18n>
        <ZButton class="download-app mx-auto">
          {{ $t('page.global.action.download_app') }}
        </ZButton>
      </div>
    </div>
  </ZLayoutContent>
</template>

<style lang="less">
@import '~/assets/styles/layouts/auth.less';

.page-login {
  &-links {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  &-side {
    display: flex;
    margin-bottom: 16px;
    padding: 4px;
    border: 1px solid @base-border-color;
    border-radius: 4px;

    &-item {
      padding: 6px 0;
      flex: 1;
      text-align: center;
      border-radius: 4px;
      color: @gray-color;
      font-size: 14px;
      cursor: pointer;

      &.active {
        background-color: @base-border-color;
        color: @text-color;
      }
    }
  }

  &-phone {
    margin-top: 12px;
    padding-bottom: 18px;
    height: 68px;

    svg {
      width: 20px;
      height: 20px;
      fill: @gray-color;
    }

    .z-input {
      height: 50px;
      // background-color: white;
    }

    .z-dropdown {
      margin-right: 12px;

      &-trigger {
        display: flex;
        align-items: center;
        cursor: pointer;
      }

      &-overlay {
        width: 250px;
      }
    }
  }

  .block-qr {
    @width: 520px;

    .z-auth-form {
      width: @width;

      .z-container {
        width: @width;
      }
    }
  }

  .z-auth-form-title {
    text-align: left !important;
    color: @text-color;
    margin-left: 24px;
    margin-bottom: 24px;
  }

  .z-search-box {
    .z-table {
      height: 130px;
    }

    .z-input {
      height: 32px;
    }
  }

  &-group-form {
    display: flex;
    margin: 24vh 24px auto;
    background-color: white;
    border-radius: 4px;
    padding: 24px 12px;
    overflow: hidden;
    transition: 0.3s all;
    animation: AuthFormScale 0.3s;

    @media @mobile {
      margin: 0;
      border: none;
      padding: 0;
      padding-top: 80px;
      width: 100%;
    }

    &.hidden-qr {
      .z-auth-form {
        &::after {
          content: none;
        }
      }

      .page-login-qr-session {
        width: 0;
        padding: 0;
        overflow: hidden;
        transform: translateX(200px);

        @media @mobile {
          display: none;
        }

        &-row .qr-code {
          height: 0;
          opacity: 0;
          transform: translateX(200px);
        }

        > .title, > .desc {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: clip;
        }
      }
    }
  }

  &-qr-session {
    position: relative;
    padding: 12px 36px;
    width: 252px;
    text-align: center;
    transition: 0.5s all;

    @media @mobile {
      display: none;
    }

    &-row {
      display: flex;
      justify-content: space-around;

      &-img {
        display: none;
        animation: qrcode-img 0.75s;

        img {
          width: 260px;
          height: 260px;
        }
      }
    }

    .qr-code {
      position: relative;
      height: 136px;
      width: 136px;
      border: 1px solid rgba(@text-color, 0.15);
      margin-bottom: 12px;
      opacity: 1;
      transition: 1s all;
      overflow: hidden;

      &-mark {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-self: center;
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(255, 255, 255, 0.9);

        i {
          background-color: @primary-color;
          color: @white-color;
          font-size: 20px;
          padding: 10px;
          border-radius: 30px;
          display: block;
          width: fit-content;
          margin: 0 auto 4px;
        }

        .z-button {
          height: 36px;
          margin: 4px auto 0;

          &:hover {
            background-color: rgba(@primary-color, 0.75);
          }
        }
      }

      canvas {
        width: 120px !important;
        height: 120px !important;
        transition: 1s all;
      }
    }

    .title {
      color: @text-color;
    }

    .desc {
      width: 100%;
      font-size: 12px;
      line-height: 16px;
      margin-bottom: 18px;
    }

    .z-button {
      border-radius: 4px;
    }

    .download-app {
      border-color: @base-border-color;
      color: @text-color;
      height: 40px;
      line-height: 40px;

      &:hover {
        color: @white-color;
      }
    }

    &-full {
      width: 702px;

      .page-login-qr-session-row {
        width: 100%;

        &-img {
          display: block;
        }
      }

      .qr-code {
        width: 244px !important;
        height: 244px !important;

        canvas {
          width: 212px !important;
          height: 212px !important;
        }
      }
    }
  }

  .z-form {
    margin: 0 36px 0 24px;
    background-color: transparent;
    border-radius: 0;
    padding: 0;
    transition: 0.3s all;

    @media @mobile {
      margin: 0 24px;
    }
  }

  .z-auth-form {
    position: relative;
    width: 450px;
    transition: 0.5s all;
    overflow: hidden;

    @media @mobile {
      width: 100% !important;
    }

    .z-container {
      width: 450px;

      @media @mobile {
        width: 100% !important;
      }
    }

    &::after {
      content: '';
      position: absolute;
      top: 0;
      right: 0;
      width: 1px;
      height: 100%;
      background-color: @base-border-color;
    }

    &.hidden-form {
      transform: translateX(-450px);
      width: 0 !important;
    }
  }
}

@keyframes qrcode-img {
  from {
    transform: translateX(50px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes hidden-form {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(-1000px);
    opacity: 0;
  }
}
</style>
