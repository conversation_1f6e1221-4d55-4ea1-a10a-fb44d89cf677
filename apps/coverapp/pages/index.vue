<script setup lang="ts">
import Preview from '~/layouts/home/<USER>'
import Advantage from '~/layouts/home/<USER>'
import MarketList from '~/layouts/home/<USER>'
import Partnership from '~/layouts/home/<USER>'
import Banner from '~/layouts/home/<USER>'
import Announcements from '~/layouts/home/<USER>'

defineOgImageComponent('BlogPost')

useSeoMeta({
  title: 'Safetrade - Safely Buy & Sell Cryptocurrency',
  ogTitle: 'Safetrade - Safely Buy & Sell Cryptocurrency',
  description: 'Safetrade is a secure online platform for trading, transferring, and storing cryptocurrency.',
  ogDescription: 'Safetrade is a secure online platform for trading, transferring, and storing cryptocurrency.',
})
</script>

<template>
  <ZLayoutContent class="mt-50px">
    <Preview />
    <ZContainer class="flex">
      <div class="w-5/12 pr-[40px]">
        <Announcements />
      </div>
      <div class="w-7/12 flex justify-between mt-[150px]">
        <Banner :mul="7 / 12" />
      </div>
    </ZContainer>
    <MarketList />
    <Partnership />
    <Advantage />
  </ZLayoutContent>
</template>

<style lang="less">
@import '~/assets/styles/layouts/home.less';

.page-home {
  .z-container {
    @media @mobile {
      width: 100%;
    }

    @media @tablet {
      width: 100%;
    }
  }

  .z-layout-content {
    background-color: #fff;
  }
}
</style>
