<script setup lang="ts">
import { type ZAuthFormField, ZAuthFormFieldType, type ZTableColumn } from '@zsmartex/components/types'
import type { Country } from '@zsmartex/types'
import Validate from '~/validation/validate'
import countries from '~/library/countries'

definePageMeta({
  middleware: ['guest-only'],
})

defineOgImageComponent('BlogPost')

const runtimeConfig = useRuntimeConfig()

useSeoMeta({
  title: 'Safetrade - Register',
  ogTitle: 'Safetrade - Register',
  description: 'Safetrade is a secure online platform for trading, transferring, and storing cryptocurrency.',
  ogDescription: 'Safetrade is a secure online platform for trading, transferring, and storing cryptocurrency.',
  ogUrl: runtimeConfig.public.mainSiteUrl,
  twitterTitle: 'Safetrade - Register',
  twitterSite: 'SafeTrade',
})

const userStore = useUserStore()
const extendUserStore = useExtendUserStore()

const activeTab = ref('email')
const searchBoxVisible = ref(false)
const number = ref('')
const password = ref('')
const region = ref('VN')
// const inviteCode = ref(useRoute().query.invite_code as string)

const codeSelected = computed(() => {
  return countries.find(c => c.code === unref(region))?.mobile_code as string
})

const phoneModalSelectColumns: ZTableColumn[] = [
  {
    key: 'mobile_code',
    scopedSlots: true,
  },
]

const fields = computed(() => {
  const result: ZAuthFormField[] = [
    {
      key: 'password',
      label: $t('page.global.label.password'),
      name: 'password',
      type: ZAuthFormFieldType.Input,
      inputType: InputType.Password,
      placeholder: $t('page.global.placeholder.password'),
      required: true,
      value: password,
      validate: [Validate.pattern(/^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])(?=.*?[#?!@$%^&*-]).{8,}$/)],
      transformErrors: {
        'input.error.pattern': $t('page.global.error.password'),
      },
    },
    {
      key: 'confirm_password',
      label: $t('page.global.label.confirm_password'),
      name: 'confirm_password',
      type: ZAuthFormFieldType.Input,
      inputType: InputType.Password,
      placeholder: $t('page.global.placeholder.confirm_password'),
      required: true,
      validate: [Validate.equal(password.value)],
      transformErrors: {
        'input.error.pattern': $t('page.global.error.confirm_password'),
      },
    },
    {
      key: 'terms',
      name: 'terms',
      type: ZAuthFormFieldType.Terms,
      required: true,
    },
  ]

  if (activeTab.value === 'email') {
    result.unshift(
      {
        key: 'email',
        label: $t('page.global.label.email'),
        name: 'email',
        type: ZAuthFormFieldType.Input,
        inputType: InputType.Text,
        placeholder: $t('page.global.placeholder.email'),
        required: true,
        validate: [Validate.email],
        transformErrors: {
          'input.error.email': $t('page.global.error.email'),
        },
      },
    )
  }

  return result
})

function onSearchBoxClicked(item: Country) {
  searchBoxVisible.value = false
  region.value = item.code
}

function register({ email, password }: { email: string; password: string }) {
  const params: Record<string, string> = {
    password,
    // invite_code: inviteCode.value,
  }

  if (email) {
    params.email = email
  } else {
    params.phone_number = number.value
    params.region = region.value
  }

  return userStore.Register(params, extendUserStore.CallbackLogin)
}
</script>

<template>
  <ZLayoutContent class="page-register-layout">
    <ZAuthForm
      :title="$t('page.register.form.title', { name: runtimeConfig.public.exchangeName })"
      :submit-text="$t('page.global.action.submit')"
      :loading="userStore.loginLoading"
      :fields="fields"
      :values-other="activeTab === 'phone' && number.length === 0"
      @submit="register"
    >
      <template #head>
        <div v-if="runtimeConfig.public.phone" class="page-register-side">
          <div class="page-register-side-item" :class="{ active: activeTab === 'email' }" @click="activeTab = 'email'">
            {{ $t('page.global.email') }}
          </div>
          <div class="page-register-side-item" :class="{ active: activeTab === 'phone' }" @click="activeTab = 'phone'">
            {{ $t('page.global.phone') }}
          </div>
        </div>
      </template>
      <template v-if="activeTab === 'phone'" #phone>
        <div v-if="runtimeConfig.public.phone" class="page-register-phone relative flex">
          <ZSearchBox
            v-model="searchBoxVisible"
            :data-source="countries"
            :columns="phoneModalSelectColumns"
            :find-by="['mobile_code', 'name']"
            @click="onSearchBoxClicked"
          >
            +{{ codeSelected }}
            <ZIconAngleDownFilled class="ml-2" />
            <template #mobile_code="{ item }">
              +{{ item.mobile_code }} {{ item.name }}
            </template>
          </ZSearchBox>
          <ZCol>
            <ZAuthInput
              v-model="number"
              :type="InputType.Number"
              :placeholder="$t('page.global.placeholder.phone_number')"
              name="phone_number"
            />
          </ZCol>
        </div>
      </template>
      <template #default>
        <div class="page-register-links">
          <span>
            {{ $t('page.register.links.have_account') }}
            <NuxtLink to="/login" class="bold-text">
              {{ $t("page.login.form.login") }}
            </NuxtLink>
          </span>
        </div>
      </template>
    </ZAuthForm>
  </ZLayoutContent>
</template>

<style lang="less">
@import '~/assets/styles/layouts/auth.less';

.page-register {
  svg {
    width: 20px;
    height: 20px;
    fill: @gray-color;
  }

  &-side {
    display: flex;
    margin-bottom: 16px;
    padding: 4px;
    border: 1px solid @base-border-color;
    border-radius: 4px;

    &-item {
      padding: 6px 0;
      flex: 1;
      text-align: center;
      border-radius: 4px;
      color: @gray-color;
      font-size: 14px;
      cursor: pointer;

      &.active {
        background-color: @base-border-color;
        color: @text-color;
      }
    }
  }

  &-links {
    display: flex;
    justify-content: center;
  }

  &-phone {
    margin-top: 12px;
    padding-bottom: 18px;
    height: 68px;

    .z-input {
      height: 50px;
      // background-color: white;
    }

    .z-dropdown {
      margin-right: 12px;

      &-trigger {
        display: flex;
        align-items: center;
        cursor: pointer;
      }

      &-overlay {
        width: 250px;
      }
    }
  }

  &-layout {
    @media @mobile {
      background-color: white;
    }
  }

  .z-auth-form {
    @media @mobile {
      padding: 0;
    }
  }

  .z-auth-form-title {
    @media @mobile {
      text-align: left !important;
      color: @text-color;
      margin-left: 24px;
      margin-bottom: 24px;
      width: calc(100% - 48px);
    }
  }

  .z-container {
    @media @mobile {
      padding-top: 80px;
      width: 100% !important;
    }
  }
}
</style>
