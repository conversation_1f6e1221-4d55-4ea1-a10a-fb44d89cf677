<script setup lang="ts">
const publicStore = usePublicStore()
const { query } = useQuery()
const total = ref(0)
const loading = ref(false)
const isSearch = ref(false)

const currencies = computed(() => {
  const result = publicStore.enabledCurrencies.filter(currency => currency.networks.length)
  total.value = result.length

  if (isSearch.value) return result

  return result.slice((Number(query.value.page) - 1) * Number(query.value.limit), Number(query.value.page) * Number(query.value.limit))
})

const columns: ZTableColumn[] = [
  {
    key: 'code',
    title: $t('page.global.table.coin_token'),
    scopedSlots: true,
  },
  {
    key: 'name',
    title: $t('page.global.table.name'),
  },
  {
    key: 'networks',
    title: $t('page.global.table.networks'),
    scopedSlots: true,
  },
  {
    key: 'deposits',
    title: 'Deposits',
    scopedSlots: true,
  },
  {
    key: 'withdraws',
    title: 'Withdraws',
    scopedSlots: true,
  },
  {
    key: 'min_confirmations',
    title: 'Min Confirmations',
    align: Align.Center,
    scopedSlots: true,
  },
]

function getParentCurrency(parentID: number) {
  return publicStore.currencies.find((c) => {
    return c.networks.find(n => n.id === parentID)
  })
}
</script>

<template>
  <ZLayoutContent>
    <ZContainer class="py-6">
      <ZTablePro
        title="Currency Status"
        :columns="columns"
        :data-source="currencies"
        :search-enabled="true"
        :find-by="['id']"
        :query="query"
        responsive
        @search="(s: string) => isSearch = s.length ? true : false"
      >
        <template #code="{ item }">
          <span class="currency_icon">
            <img :src="item.icon_url">
          </span>
          {{ item.id.toUpperCase() }}
        </template>
        <template #networks="{ item }">
          <div v-for="network in item.networks" :key="network.id" class="my-[12px]">
            <template v-if="network.parent_id">
              {{ getParentCurrency(network.parent_id)?.name }} <span class="bold-text">({{ network.protocol }})</span>
            </template>
            <template v-else>
              {{ item.name }} <span class="bold-text">({{ network.protocol }})</span>
            </template>
          </div>
        </template>
        <template #deposits="{ item }">
          <div v-for="network in item.networks" :key="network.id" class="my-[12px]">
            <span :class="`page-status-value capitalize ${network.deposit_enabled && item.status === CurrencyStatus.Enabled && network.status === CurrencyNetworkStatus.Active ? 'page-status-value-green' : 'page-status-value-red'}`">
              {{ network.deposit_enabled && item.status === CurrencyStatus.Enabled && network.status === CurrencyNetworkStatus.Active ? "Enabled" : "Disabled" }}
            </span>
          </div>
        </template>
        <template #withdraws="{ item }">
          <div v-for="network in item.networks" :key="network.id" class="my-[12px]">
            <span :class="`page-status-value capitalize ${network.withdraw_enabled && item.status === CurrencyStatus.Enabled && network.status === CurrencyNetworkStatus.Active ? 'page-status-value-green' : 'page-status-value-red'}`">
              {{ network.withdraw_enabled && item.status === CurrencyStatus.Enabled && network.status === CurrencyNetworkStatus.Active ? "Enabled" : "Disabled" }}
            </span>
          </div>
        </template>
        <template #min_confirmations="{ item }">
          <div v-for="network in item.networks" :key="network.id" class="text-center my-[12px]">
            {{ network.min_confirmations }}
          </div>
        </template>
        <template #foot>
          <ZPagination v-model="query.page" v-model:page-size="query.limit" :is-loading="loading" :total="total" />
        </template>
      </ZTablePro>
    </ZContainer>
  </ZLayoutContent>
</template>

<style lang="less">
.page-status {
  .z-container {
    @media @mobile, @tablet {
      width: 100%;
    }
  }

  .z-table-row {
    height: auto !important;
    padding-top: 12px !important;
    padding-bottom: 12px !important;
    line-height: 20px !important;
  }

  &-value {
    padding: 4px 12px;
    line-height: 1;
    border-radius: 4px;

    &.page-status-value-red {
      color: @error-color;
      background-color: rgba(@error-color, 0.15);
    }

    &.page-status-value-green {
      color: @up-color;
      background-color: rgba(@up-color, 0.15);
    }
  }

  .currency_icon {
    display: flex;
    align-items: center;
    margin-right: 6px;

    img {
      border-radius: 50%;
      width: 24px;
      height: 24px;
    }
  }

  .networks, .deposits, .withdraws, .min_confirmations {
    display: block;
  }
}
</style>
