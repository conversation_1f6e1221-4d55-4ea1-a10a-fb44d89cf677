<script setup lang="ts">
import type { Ref } from 'vue'
import type { ZAuthFormField, ZTableColumn } from '@zsmartex/components/types'
import { InputType, ZAuthFormFieldType } from '@zsmartex/components/types'
import type { Country } from '@zsmartex/types'
import type Button from '#components/Button.vue'
import Validate from '~/validation/validate'
import countries from '~/library/countries'

definePageMeta({
  middleware: ['guest-only'],
})

const userStore = useUserStore()
const config = useRuntimeConfig()

const loading = ref(false)
const step = ref(1)
const email = ref('')
const needEmail = ref(false)
const needPhone = ref(false)
const activeTab = ref('email')
const searchBoxVisible = ref(false)
const number = ref('')
const region = ref('VN')

const codeSelected = computed(() => {
  return countries.find(c => c.code === unref(region))?.mobile_code as string
})

const phoneModalSelectColumns: ZTableColumn[] = [
  {
    key: 'mobile_code',
    scopedSlots: true,
  },
]

const fields = computed(() => {
  const fields: ZAuthFormField[] = [
    {
      key: 'email_code',
      name: 'email_code',
      type: ZAuthFormFieldType.Input,
      inputType: InputType.Number,
      hidden: step.value !== 2 || !needEmail.value,
      label: $t('page.global.label.email_code'),
      placeholder: $t('page.global.placeholder.e-confirmation_code'),
      required: step.value === 2 && needEmail.value,
      maxLength: 6,
      validate: [Validate.minLength(6)],
      transformErrors: {
        'input.error.min_length': $t('page.global.error.min_length', { min: 6 }),
      },
    },
    {
      key: 'phone_code',
      name: 'phone_code',
      type: ZAuthFormFieldType.Input,
      inputType: InputType.Number,
      hidden: step.value !== 2 || !needPhone.value,
      label: $t('page.global.label.phone_code'),
      placeholder: $t('page.global.placeholder.phone_confirmation_code'),
      required: step.value === 2 && needPhone.value,
      maxLength: 6,
      validate: [Validate.minLength(6)],
      transformErrors: {
        'input.error.min_length': $t('page.global.error.min_length', { min: 6 }),
      },
    },
  ]

  if (activeTab.value === 'email') {
    fields.unshift(
      {
        key: 'email',
        name: 'email',
        type: ZAuthFormFieldType.Input,
        inputType: InputType.Text,
        hidden: step.value !== 1,
        label: $t('page.global.label.email'),
        placeholder: $t('page.global.placeholder.email'),
        required: step.value === 1,
        validate: [Validate.email],
        value: email,
        transformErrors: {
          'input.error.email': $t('page.global.error.email'),
        },
      },
    )
  }

  return fields
})

const delayButtonEmail = ref<InstanceType<typeof Button>>()
const delayButtonPhone = ref<InstanceType<typeof Button>>()

function GenerateForgotCode(type: 'email' | 'phone') {
  const button = type === 'email' ? delayButtonEmail : delayButtonPhone

  userStore.GenerateCodeResetPassword(userStore._cache.email, type, button.value?.StartDelay)
}

async function forgotPassword() {
  loading.value = true
  const type = email.value ? 'email' : 'phone'
  const button = type === 'email' ? delayButtonEmail : delayButtonPhone

  await userStore.ForgotPassword(
    email.value,
    number.value,
    region.value,
    type,
    async () => {
      step.value = 2

      if (type === 'email') needEmail.value = true
      else needPhone.value = true
    // await nextTick()
    // DelayButtonEmail.value.StartDelay()
    },
  )

  button.value?.StartDelay()
  loading.value = false
}

function onSearchBoxClicked(item: Country) {
  searchBoxVisible.value = false
  region.value = item.code
}

async function checkCode(params: { email_code: string; phone_code?: string; otp_code?: string }) {
  loading.value = true
  await userStore.CheckCodeResetPassword({
    email: email.value,
    phone_number: number.value,
    region: region.value,
    email_code: params.email_code,
    phone_code: params.phone_code,
  })
  loading.value = false
}

function onSubmit(params: { email_code: string; phone_code?: string; otp_code?: string }) {
  if (step.value === 1) {
    forgotPassword()
  } else if (step.value === 2) {
    checkCode(params)
  }
}
</script>

<template>
  <ZLayoutContent class="page-forgot-password-layout">
    <ZAuthForm
      :fields="fields"
      :title="$t('page.forgot-password.form.title')"
      :submit-text="$t('page.global.action.confirm')"
      :loading="loading"
      :values-other="activeTab === 'phone' && number.length === 0"
      @submit="onSubmit"
    >
      <template v-if="config.public.phone && step !== 2" #head>
        <div class="page-forgot-password-side">
          <div class="page-forgot-password-side-item" :class="{ active: activeTab === 'email' }" @click="activeTab = 'email'">
            {{ $t('page.global.email') }}
          </div>
          <div class="page-forgot-password-side-item" :class="{ active: activeTab === 'phone' }" @click="activeTab = 'phone'">
            {{ $t('page.global.phone') }}
          </div>
        </div>
      </template>
      <template v-if="activeTab === 'phone' && step === 1" #phone>
        <div class="page-register-phone relative flex">
          <ZSearchBox
            v-model="searchBoxVisible"
            :data-source="countries"
            :columns="phoneModalSelectColumns"
            :find-by="['mobile_code', 'name']"
            @click="onSearchBoxClicked"
          >
            +{{ codeSelected }}
            <ZIconAngleDownFilled class="ml-2" />
            <template #mobile_code="{ item }">
              +{{ item.mobile_code }} {{ item.name }}
            </template>
          </ZSearchBox>
          <ZCol>
            <ZAuthInput
              v-model="number"
              :type="InputType.Number"
              :placeholder="$t('page.global.placeholder.phone_number')"
              name="phone_number"
            />
          </ZCol>
        </div>
      </template>
      <template #email_code-suffix>
        <ZButton
          ref="delayButtonEmail"
          :delay="{
            time: 60,
            content: 'Get [#{time}] again',
          }"
          @click="GenerateForgotCode('email')"
        >
          {{ $t('page.global.action.get_code') }}
        </ZButton>
      </template>
      <template #phone_code-suffix>
        <ZButton
          ref="delayButtonPhone"
          :delay="{
            time: 60,
            content: 'Get [#{time}] again',
          }"
          @click="GenerateForgotCode('phone')"
        >
          {{ $t('page.global.action.get_code') }}
        </ZButton>
      </template>
    </ZAuthForm>
  </ZLayoutContent>
</template>

<style lang="less">
@import '~/assets/styles/layouts/auth.less';
.page-forgot-password {
  svg {
    width: 20px;
    height: 20px;
    fill: @gray-color;
  }

  &-side {
    display: flex;
    margin-bottom: 16px;
    padding: 4px;
    border: 1px solid @base-border-color;
    border-radius: 4px;

    &-item {
      padding: 6px 0;
      flex: 1;
      text-align: center;
      border-radius: 4px;
      color: @gray-color;
      font-size: 14px;
      cursor: pointer;

      &.active {
        background-color: @base-border-color;
        color: @text-color;
      }
    }
  }

  &-layout {
    @media @mobile {
      background-color: white;
    }
  }

  .z-auth-form {
    @media @mobile {
      padding: 0;
    }
  }

  .z-auth-form-title {
    @media @mobile {
      text-align: left !important;
      color: @text-color;
      margin-left: 24px;
      margin-bottom: 24px;
      width: calc(100% - 48px);
    }
  }

  .z-container {
    @media @mobile {
      padding-top: 80px;
      width: 100% !important;
    }
  }
}
</style>
