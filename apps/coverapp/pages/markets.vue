<script setup lang="ts">
import FeatureMarkets from '~/layouts/markets/FeatureMarkets.vue'
import MarketList from '~/layouts/markets/MarketList.vue'

defineOgImageComponent('BlogPost')

const runtimeConfig = useRuntimeConfig()

useSeoMeta({
  title: 'Safetrade - Markets',
  ogTitle: 'Safetrade - Markets',
  description: 'Safetrade is a secure online platform for trading, transferring, and storing cryptocurrency.',
  ogDescription: 'Safetrade is a secure online platform for trading, transferring, and storing cryptocurrency.',
  ogUrl: runtimeConfig.public.mainSiteUrl,
  twitterTitle: 'Safetrade - Markets',
  twitterSite: 'SafeTrade',
})
</script>

<template>
  <ZLayoutContent class="page-markets layout">
    <FeatureMarkets />
    <MarketList />
  </ZLayoutContent>
</template>

<style lang="less">
.page-markets {
  &.layout {
    padding: 40px 0;
  }

  .page-markets-feature-markets {
    @media @mobile {
      display: none;
    }

    @media @tablet {
      overflow-x: auto;
      height: 142px;
    }
  }
}
</style>
