<script setup lang="ts">
import SwaggerUI from 'swagger-ui-dist'
import 'swagger-ui/dist/swagger-ui.css'

const bundle = SwaggerUI.SwaggerUIBundle

defineOgImageComponent('BlogPost')

useSeoMeta({
  title: 'API | Safetrade - Safely Buy & Sell Cryptocurrency',
  description: 'List of public API',
  ogTitle: 'API | SafeTrade',
  ogDescription: 'List of public API',
})

onMounted(() => {
  if (process.client) {
    const ui = bundle({
      url: `${location.origin}/api/v2/trade/public/swagger.json`,
      dom_id: '#swagger',
      presets: [
        bundle.presets.apis,
        bundle.SwaggerUIStandalonePreset,
      ],
    })

    ui.initOAuth({
      appName: 'Swagger',
      clientId: 'implicit',
    })
  }
})
</script>

<template>
  <ZLayoutContent class="flex justify-center">
    <div id="swagger" />
  </ZLayoutContent>
</template>
