<script setup lang="ts">
import type { ZTabItem } from '@zsmartex/components/types'
import type { Payment } from '@zsmartex/types'
import ModalPayment from '~/layouts/my/payments/ModalPayment.vue'
import Bank from '~/layouts/my/payments/Bank.vue'

definePageMeta({
  middleware: ['p2p'],
})

const activeTab = useState(() => 'bank')

const modalPayment = ref<InstanceType<typeof ModalPayment>>()

const tabs: ZTabItem[] = [
  {
    key: 'bank',
    text: 'Bank Account',
  },
  {
    key: 'momo',
    text: 'MoMo',
  },
]
</script>

<template>
  <ZContainer class="my-5 page-my-payments">
    <ZHeadBack to="/my/dashboard" class="mb-5">
      <ZIconArrowLeftDuotone />
      {{ $t('page.payments.title') }}
    </ZHeadBack>

    <ZCard>
      <template #head>
        <ZTab v-model="activeTab" :tabs="tabs">
          <template v-for="tab in tabs" #[tab.key] :key="tab.key">
            {{ tab.text }}
          </template>
        </ZTab>
        <div class="pb-[16px]">
          <ZButton class="h-[30px]" @click="modalPayment?.openModal('create')">
            {{ $t('page.payments.add') }}
          </ZButton>
        </div>
      </template>

      <Bank :tab="activeTab" @update="(payment: Payment) => modalPayment?.openModal('update', payment)" />
    </ZCard>
    <ModalPayment ref="modalPayment" />
  </ZContainer>
</template>

<style lang="less">
.page-my-payments {
  min-height: 615px;

  .z-back {
    display: flex;
    align-items: center;

    svg {
      width: 24px;
      height: 24px;
    }

    &-title {
      @media @mobile {
        margin-left: 16px;
      }

      @media @tablet {
        margin-left: 24px;
      }
    }
  }

  @media @mobile, @tablet {
    width: 100%;
  }

  .z-back-title {
    @media @mobile, @tablet {
      margin-top: 0;
    }
  }

  &-table {
    &-item {
      &-side {
        padding: 4px 12px;
        line-height: 1;
        border-radius: 4px;

        &.page-my-orders-table-item-side-red {
          background-color: rgba(#ea4d4d, 0.15);
        }

        &.page-my-orders-table-item-side-green {
          background-color: rgba(#16c39342, 0.15);
        }
      }
    }
  }

  .z-table {
    &-head {
      padding: 0;
    }

    &-row {
      padding: 0;
    }
  }

  .z-card {
    overflow: visible;

    &-head {
      display: flex;
      justify-content: space-between;
      margin: 0 -24px;
      padding: 0 24px;
      border-bottom: 1px solid @base-border-color;

      @media @mobile {
        display: flex !important;
        justify-content: space-between;
        margin: 0;
        padding: 0;
      }
    }

    .z-tab {
      display: flex;
      align-items: center;
      height: 46px;

      @media @mobile {
        display: block;
        width: max-content;
      }

      &-item {
        font-size: 16px;
      }
    }

    .z-range-picker {
      .z-dropdown-trigger {
        background-color: rgba(@gray-color, 0.1);
      }
    }

    .z-page {
      .z-dropdown-trigger {
        background-color: white;
      }
    }
  }

  .z-pagination {
    .z-dropdown {
      &-trigger {
        display: flex;
        justify-content: center;
        background-color: #fff;
      }
    }
  }
}
</style>
