<script setup lang="ts">
import type { P2PProfile } from '@zsmartex/types'
import { roundNumber } from '@zsmartex/utils'
import AdvertisementRow from '~/layouts/my/advertisements/AdvertisementRow.vue'

const userStore = useUserStore()
const query1 = useState<Record<string, string>>(() => ({}))
const query2 = useState<Record<string, string>>(() => ({}))

definePageMeta({
  middleware: ['p2p'],
})

const limit1 = computed({
  get() {
    return Number(query1.value.limit) || 15
  },
  set(val: number) {
    query1.value.limit = val.toString()
  },
})

const page1 = computed({
  get() {
    return Number(query1.value.page) || 1
  },
  set(val: number) {
    query1.value.page = val.toString()
  },
})

const limit2 = computed({
  get() {
    return Number(query2.value.limit) || 15
  },
  set(val: number) {
    query2.value.limit = val.toString()
  },
})

const page2 = computed({
  get() {
    return Number(query2.value.page) || 1
  },
  set(val: number) {
    query2.value.page = val.toString()
  },
})

const { data: profile } = await useAsyncData( async () => {
  const { data } = await userStore.FetchP2PProfile()

  return data
}, { default: () => ({} as P2PProfile) })

const { data: sellAdvertisements, pending: pendingSell } = await useAsyncData(async () => {
  const { data } = await userStore.FetchAdvertisements({
    side: 'sell',
    ordering: 'asc',
  })

  return data
}, { default: () => ([]) })

const { data: buyAdvertisements, pending: pendingBuy } = await useAsyncData(async () => {
  const { data } = await userStore.FetchAdvertisements({
    side: 'buy',
    ordering: 'desc',
  })

  return data
}, { default: () => ([]) })

function convertSecondToTimeString(time: number) {
  return (Math.floor(time / 60)).toFixed(2)
}
</script>

<template>
  <div class="page-my-advertisements">
    <div class="page-my-advertisements-head bg-white">
      <ZContainer>
        <div class="page-my-advertisements-head-title flex justify-between items-center pt-5 pb-8">
          <div class="bold-text text-2xl">
            {{ $t('page.p2p.profile.title') }}
          </div>
        </div>
        <div class="page-my-advertisements-head-username flex justify-between mb-4">
          <div class="flex items-center">
            <div class="mr-3 rounded-full bg-blue-600 h-[24px] w-[24px] flex justify-center items-center text-white">
              {{ userStore.username ? userStore.username[0].toUpperCase() : userStore.username }}
            </div>
            <div class="text-blue-500 bold-text cursor-pointer text-base">
              {{ userStore.username }}
            </div>
          </div>
          <div class="flex items-center">
            <div class="flex items-center page-my-advertisements-head-level">
              <span class="mr-1">{{ $t('page.p2p.profile.email') }}</span>
              <ZIconCheckCircleDuotone />
            </div>
            <div class="flex items-center ml-5 page-my-advertisements-head-level">
              <span class="mr-1">{{ $t('page.p2p.profile.sms') }}</span>
              <ZIconCheckCircleDuotone />
            </div>
            <div class="flex items-center ml-5 page-my-advertisements-head-level">
              <span class="mr-1">{{ $t('page.p2p.profile.kyc') }}</span>
              <ZIconCheckCircleDuotone />
            </div>
          </div>
        </div>
        <div class="page-my-advertisements-head-information flex justify-between">
          <div class="page-my-advertisements-head-information-item flex flex-col items-center">
            <div class="mb-3 text-gray">
              {{ $t('page.p2p.profile.total_order') }}
            </div>
            <div class="text-[18px] bold-text">
              {{ profile?.total_trade }}
            </div>
          </div>
          <div class="page-my-advertisements-head-information-item flex flex-col items-center">
            <div class="mb-3 text-gray">
              {{ $t('page.p2p.profile.completion_rate') }}
            </div>
            <div class="text-[18px] bold-text">
              {{ profile ? `${roundNumber(Number(profile.success_rate) * 100, 2)}%` : '0%' }}
            </div>
          </div>
          <div class="page-my-advertisements-head-information-item flex flex-col items-center">
            <div class="mb-3 text-gray">
              {{ $t('page.p2p.profile.orders_30day') }}
            </div>
            <div class="text-[18px] bold-text">
              {{ profile?.total_trade }}
            </div>
          </div>
          <div class="page-my-advertisements-head-information-item flex flex-col items-center">
            <div class="mb-3 text-gray">
              {{ $t('page.p2p.profile.average_unlock_time') }}
            </div>
            <div class="text-[18px] bold-text">
              {{ `${convertSecondToTimeString(Number(profile?.time_average))} minute` }}
            </div>
          </div>
        </div>
      </ZContainer>
    </div>

    <div class="py-[32px] page-my-advertisements-advertisement">
      <ZContainer>
        <div class="mb-4 text-xl bold-text page-my-advertisements-advertisement-head">
          {{ $t('page.p2p.profile.ads_are_online') }}
        </div>
        <ZCard>
          <div class="page-my-advertisements-advertisement-side bold-text text-[22px] pb-[24px]">
            {{ $t('page.p2p.profile.sell_to_users') }}
          </div>
          <div class="page-my-advertisements-table">
            <div class="page-my-advertisements-table-head bg-white">
              <span class="max-w-[178px]">{{ $t('page.my.advertisement.coin_currency') }}</span>
              <span class="max-w-[177px]">{{ $t('page.my.advertisement.fiat_currency') }}</span>
              <span class="max-w-[178px]">{{ $t('page.p2p.price') }}</span>
              <span class="max-w-[178px]">{{ $t('page.p2p.limit_available') }}</span>
              <span class="max-w-[177px]">{{ $t('page.p2p.payments') }}</span>
              <span class="max-w-[178px]">{{ $t('page.my.advertisement.state') }}</span>
              <span class="max-w-[178px] flex justify-end">{{ $t('page.my.advertisement.action') }}</span>
            </div>
            <div v-if="sellAdvertisements!.length === 0" class="h-[300px] w-full flex flex-col items-center justify-center empty">
              <ZIconClipboardTimesDuotone />
              <span class="text-gray">{{ $t('page.global.table.empty') }}</span>
            </div>
            <AdvertisementRow v-for="advertisement in sellAdvertisements" :key="advertisement.id" class="bg-white" :advertisement="advertisement" side="buy" />
            <ZPagination v-model="page1" v-model:page-size="limit1" :is-loading="pendingSell" :total="sellAdvertisements?.length || 0" />
          </div>
        </ZCard>
        <ZCard class="mt-6">
          <div class="page-my-advertisements-advertisement-side bold-text text-[22px] pb-[24px]">
            {{ $t('page.p2p.profile.buy_to_users') }}
          </div>
          <div class="page-my-advertisements-table">
            <div class="page-my-advertisements-table-head bg-white">
              <span class="max-w-[178px]">{{ $t('page.my.advertisement.coin_currency') }}</span>
              <span class="max-w-[177px]">{{ $t('page.my.advertisement.fiat_currency') }}</span>
              <span class="max-w-[178px]">{{ $t('page.p2p.price') }}</span>
              <span class="max-w-[178px]">{{ $t('page.p2p.limit_available') }}</span>
              <span class="max-w-[177px]">{{ $t('page.p2p.payments') }}</span>
              <span class="max-w-[178px]">{{ $t('page.my.advertisement.state') }}</span>
              <span class="max-w-[178px] flex justify-end">{{ $t('page.my.advertisement.action') }}</span>
            </div>
            <div v-if="buyAdvertisements!.length === 0" class="h-[300px] w-full flex flex-col items-center justify-center empty">
              <ZIconClipboardTimesDuotone />
              <span class="text-gray">{{ $t('page.global.table.empty') }}</span>
            </div>
            <AdvertisementRow v-for="advertisement in buyAdvertisements" :key="advertisement.id" class="bg-white" :advertisement="advertisement" side="sell" />
            <ZPagination v-model="page2" v-model:page-size="limit2" :is-loading="pendingBuy" :total="buyAdvertisements?.length || 0" />
          </div>
        </ZCard>
      </ZContainer>
    </div>
  </div>
</template>

<style lang="less">
.page-my-advertisements {
  .empty {
    svg {
      width: 120px;
      height: 120px;

      .cls-1 {
        fill: @base-border-color;
      }

      .cls-2 {
        fill: @gray-color;
      }
    }
  }

  .z-back {
    display: flex;
    align-items: center;

    svg {
      width: 24px;
      height: 24px;
    }
  }

  .z-container {
    @media @mobile, @tablet {
      width: 100%;
    }
  }

  .z-pagination-left {
    padding-left: 24px;
  }

  &-table {
    @media @mobile, @tablet {
      overflow-x: auto;
    }

    & > div {
      @media @mobile, @tablet {
        width: 1288px;
      }
    }

    &-head {
      display: flex;
      padding: 12px 0;
      color: @gray-color;
      font-size: 14px;

      & > span {
        flex: 1;
      }
    }
  }

  &-head {
    padding-bottom: 32px;

    &-title {
      @media @mobile, @tablet {
        padding: 20px 16px 32px 16px;
      }
    }

    &-username {
      @media @mobile, @tablet {
        padding: 0 16px;
      }
    }

    &-information {
      @media @mobile, @tablet {
        flex-wrap: wrap;
        padding: 0 16px;
      }

      &-item {
        @media @mobile {
          width: 50%;
          margin-top: 12px;
        }
      }
    }

    &-level {
      svg {
        width: 20px;
        height: 20px;

        .cls-1 {
          fill: @up-color;
        }

        .cls-2 {
          fill: white;
        }
      }
    }
  }

  &-advertisement {
    @media @tablet {
      min-height: 642px;
    }

    &-head {
      padding-bottom: 24px;

      @media @mobile, @tablet {
        padding-left: 16px;
      }
    }
  }
}
</style>
