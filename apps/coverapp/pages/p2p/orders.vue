<script setup lang="ts">
import { fromUnixTime, getUnixTime } from 'date-fns'
import type { ZTableColumn } from '@zsmartex/components/types'
import type { Complain, P2PTrade } from '@zsmartex/types'
import { Align, Format, ParseType } from '@zsmartex/types'
import { v4 as uuidv4 } from 'uuid'
import ModalComplain from '~/layouts/my/orders/p2p/ModalComplain.vue'
import ComplainDetail from '~/layouts/my/orders/p2p/ComplainDetail.vue'
import ModalFeedback from '~/layouts/p2p/trade/ModalFeedback.vue'

definePageMeta({
  middleware: ['p2p'],
})

const total = ref(0)
const publicStore = usePublicStore()
const userStore = useUserStore()
const tradeStore = useTradeStore()
const staticID = ref('')
const p2pTrade = ref({} as P2PTrade)
const complain = ref({} as Complain)
const dateRange = ref<(Date | null)[]>([null, null])
const { query, callbacks } = useQuery()
const complainLoading = ref(false)
const feedbackLoading = ref(false)

const complainDetail = ref<InstanceType<typeof ComplainDetail>>()
const modalComplain = ref<InstanceType<typeof ModalComplain>>()
const modalFeedback = ref<InstanceType<typeof ModalFeedback>>()

const columns: ZTableColumn[] = [
  {
    title: $t('page.global.table.date'),
    key: 'created_at',
    align: Align.Left,
    formatBy: Format.DateTimeNoYear,
    parse: ParseType.DateTime,
  },
  {
    title: 'Static ID',
    key: 'static_id',
    align: Align.Left,
  },
  {
    title: 'State',
    key: 'state',
    align: Align.Left,
    scopedSlots: true,
  },
  {
    title: $t('page.global.table.amount'),
    key: 'amount',
    align: Align.Left,
    scopedSlots: true,
  },
  {
    title: $t('page.global.table.price'),
    key: 'price',
    align: Align.Left,
    formatBy: Format.Price,
    parse: ParseType.Decimal,
    scopedSlots: true,
  },
  {
    title: $t('page.global.table.action'),
    key: 'action',
    align: Align.Right,
    scopedSlots: true,
  },
]

const { data: p2pTrades, pending, refresh } = await useAsyncData(async () => {
  const { headers, data } = await tradeStore.FetchP2PTrades(query.value)
  total.value = Number(headers.total)

  return data
}, { default: () => ([]) })
callbacks.push(refresh)

if (query.value.time_from) dateRange.value[0] = fromUnixTime(Number(query.value.time_from as string))
if (query.value.time_to) dateRange.value[1] = fromUnixTime(Number(query.value.time_to as string))

watch(dateRange.value, () => {
  if (dateRange.value[0] && dateRange.value[1] && dateRange.value[0] <= dateRange.value[1]) {
    query.value = {
      ...query.value,
      time_from: String(getUnixTime(dateRange.value[0])),
      time_to: String(getUnixTime(dateRange.value[1])),
    }
  }
})

function clearDate() {
  query.value.time_from = ''
  query.value.time_to = ''

  query.value = {
    ...query.value,
    page: 1,
    limit: 15,
  }
}

function clearParams() {
  query.value = {
    page: 1,
    limit: query.value.limit,
  }

  dateRange.value[0] = null
  dateRange.value[1] = null
}

const showClear = computed(() => {
  if (query.value.currency || dateRange.value[0] || dateRange.value[1]) {
    return true
  }
  return false
})

const currenciesColumn: ZTableColumn[] = [
  {
    key: 'id',
    scopedSlots: true,
  },
]

function uppercase(text: string) {
  return text.toUpperCase()
}

function showModalComplain(id: string) {
  staticID.value = id
  modalComplain.value?.openModal()
}

function showModalFeedback(trade: P2PTrade) {
  p2pTrade.value = trade
  modalFeedback.value?.openModal(p2pTrade.value)
}

function showModalDetail(trade: P2PTrade) {
  complain.value = trade.complain
  complainDetail.value?.openModal(complain.value)
}

async function CreateComplain(content: string, file?: File) {
  complainLoading.value = true

  const data = new FormData()
  data.append('static_id', staticID.value)
  data.append('content', content)

  if (file) {
    const uuid = uuidv4()
    data.append('image', file)
    data.append('uuid', uuid)
  }

  const complain = await userStore.CreateComplain(data)
  if (Object.keys(complain).length > 0) {
    const trade = p2pTrades.value?.find(t => t.static_id === staticID.value)
    if (trade) {
      trade.complain = complain
    }
  }

  complainLoading.value = false
}

async function CreateP2PFeedback(type: string, tags: string[], content: string) {
  feedbackLoading.value = true

  const params: Record<string, any> = {
    static_id: p2pTrade.value.static_id,
    maker_id: p2pTrade.value.maker_id,
    type,
    tags,
    content,
  }

  await userStore.CreateP2PFeedback(params, () => {
    const trade = p2pTrades.value?.find(t => t.static_id === p2pTrade.value.static_id)
    if (trade) {
      trade.responded = true
    }
  })
  feedbackLoading.value = false
}
</script>

<template>
  <ZContainer class="page-my-orders-p2p">
    <div class="page-my-orders-p2p-title text-2xl pt-4 pb-8">
      {{ $t('page.my.orders.p2p_order') }}
    </div>
    <ZCard class="mb-6">
      <div class="page-my-orders-filter">
        <ZSelect
          v-model="query.currency"
          :data-source="publicStore.currencies"
          :search="true"
          :scroll="true"
          :columns="currenciesColumn"
          :find-by="['id']"
          value-key="id"
          label-key="id"
          placeholder="Currency"
          :replace-func="uppercase"
          allow-all
          class="mr-4 w-[160px]"
        />
        <ZRangePicker v-model="dateRange" class="mr-4" :placement="Placement.BottomLeft" @clear="clearDate" />
        <ZButton v-if="showClear" class="h-[32px]" @click="clearParams">
          {{ $t('page.global.action.clear') }}
        </ZButton>
      </div>
      <ZTable
        class="p2p"
        :columns="columns"
        :data-source="p2pTrades"
        :loading="pending"
        responsive
      >
        <template #state="{ item }">
          <span
            class="capitalize"
            :class="[
              { 'text-green-500': item.state === P2PTradeState.Success },
              { 'text-blue-500': item.state === P2PTradeState.Pending || item.state === P2PTradeState.Processing },
              { 'text-red-500': item.state === P2PTradeState.Failed || item.state === P2PTradeState.Expired },
            ]"
          >
            {{ item.state }}
          </span>
        </template>
        <template #amount="{ item }">
          {{ `${item.amount} ${item.coin_currency_id.toUpperCase()}` }}
        </template>
        <template #price="{ item }">
          {{ `${item.price} ${item.fiat_currency_id.toUpperCase()}` }}
        </template>
        <template #action="{ item }">
          <div
            v-if="item.complain.id"
            class="text-blue cursor-pointer"
            @click="showModalDetail(item)"
          >
            {{ $t('page.my.orders.p2p_order.view_complain') }}
          </div>
          <div
            v-if="item.state === 'expired'"
            class="ml-3 text-blue cursor-pointer"
            @click="showModalComplain(item.static_id)"
          >
            {{ $t('page.my.orders.p2p_order.create_complain') }}
          </div>
          <div
            v-if="!item.responded"
            class="ml-3 text-blue cursor-pointer"
            @click="showModalFeedback(item)"
          >
            {{ $t('page.my.orders.p2p_order.create_feedback') }}
          </div>
          <div
            v-if="item.state === 'pending' || item.state === 'processing'"
            class="text-blue cursor-pointer"
            @click="navigateTo(`/p2p/trade/${item.static_id}`)"
          >
            {{ $t('page.global.action.link') }}
          </div>
        </template>
        <template #foot>
          <ZPagination v-model="query.page" v-model:page-size="query.limit" :is-loading="pending" :total="total" />
        </template>
      </ZTable>
      <ModalComplain ref="modalComplain" :loading="complainLoading" @click="CreateComplain" />
      <ComplainDetail ref="complainDetail" />
      <ModalFeedback ref="modalFeedback" :loading="feedbackLoading" @click="CreateP2PFeedback" />
    </ZCard>
  </ZContainer>
</template>

<style lang="less">
.page-my-orders-p2p {
  .created_at {
    max-width: 120px;
  }

  &-title {
    @media @tablet {
      padding-left: 24px;
    }
  }

  .z-table {
    &-head {
      padding: 0;
    }

    &-row {
      padding: 0;
      // border-top: 1px solid @base-border-color;
    }
  }

  .amount {
    max-width: 150px;
  }

  .static_id {
    max-width: 200px;
  }

  .price {
    max-width: 200px;
  }

  .state {
    max-width: 120px;
  }
}
</style>
