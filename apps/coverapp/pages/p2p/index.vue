<script setup lang="ts">
import type { ZTabItem, ZTableColumn } from '@zsmartex/components/types'
import type { Advertisement } from '@zsmartex/types'
import getSymbolFromCurrency from 'currency-symbol-map'
import Preview from '~/layouts/p2p/Preview.vue'
import Step from '~/layouts/p2p/Step.vue'
import AdvertisementRow from '~/layouts/p2p/AdvertisementRow.vue'
import AdvertisementModal from '~/layouts/p2p/AdvertisementModal.vue'

definePageMeta({
  middleware: ['p2p'],
})

const publicStore = usePublicStore()
const userStore = useUserStore()
const tradeStore = useTradeStore()
const total = ref(0)
const isRefresh = ref(false)
const side = ref('')
let autoRefresh: NodeJS.Timer

const { query, callbacks } = useQuery({
  side: 'buy',
  coinCurrency: 'usdt',
  amount: '',
  fiatCurrency: '',
  payment: 'all',
  page: 1,
  limit: 15,
})

const advertisementModal = ref<InstanceType<typeof AdvertisementModal>>()

const fiatColumn: ZTableColumn[] = [
  {
    key: 'symbol',
    scopedSlots: true,
    notReplace: true,
  },
  {
    key: 'id',
    scopedSlots: true,
  },
]

const paymentColumn: ZTableColumn[] = [
  {
    key: 'color',
    scopedSlots: true,
  },
  {
    key: 'value',
    scopedSlots: true,
  },
]

const { data: advertisements, pending, refresh } = await useAsyncData<Advertisement[]>(async () => {
  const { headers, data } = await tradeStore.FetchAdvertisements(query.value)
  total.value = Number(headers.total)

  return data
}, { default: () => ([]) })

onMounted(() => {
  autoRefresh = setInterval(async () => {
    isRefresh.value = true
    await refresh()
    isRefresh.value = false
  }, 10000)
})

onUnmounted(() => {
  clearTimeout(autoRefresh)
})

callbacks.push(refresh)

const tabs: ZTabItem[] = [
  {
    key: 'usdt',
    text: 'USDT',
  },
  {
    key: 'btc',
    text: 'BTC',
  },
  {
    key: 'bnb',
    text: 'BNB',
  },
  {
    key: 'eth',
    text: 'ETH',
  },
]
</script>

<template>
  <ZLayoutContent>
    <Preview />
    <div class="h-[50px] page-p2p-header">
      <ZContainer>
        <div v-if="userStore.isAuthenticated" class="flex items-center cursor-pointer mr-6" @click="navigateTo('/p2p/orders')">
          <ZIconClipboardTextFilled />
          Orders
        </div>
        <ZDropdown v-if="userStore.isAuthenticated" class="h-full" :placement="Placement.BottomLeft">
          <span class="bold-text">More</span> <ZIconCaretDownFilled />
          <template #overlay>
            <ZMenu>
              <ZMenuItem key="payment" :is-router-link="true" to="/p2p/payments" :selected="$route.path.startsWith('/p2p/payments')">
                <ZIcon type="shujufenxi" />
                <div class="z-menu-item-content items-center flex">
                  <ZIconClipboardTextFilled />
                  Payment Methods
                </div>
              </ZMenuItem>
              <ZMenuItem key="new_advertisement">
                <ZIcon type="chuangkou" />
                <div class="z-menu-item-content items-center flex" @click="advertisementModal?.openModal()">
                  <ZIconClipboardTextFilled />
                  Post new Ad
                </div>
              </ZMenuItem>
              <ZMenuItem key="my_ad" :is-router-link="true" to="/p2p/advertisements" :selected="$route.path.startsWith('/p2p/advertisements')">
                <ZIcon type="chuangkou" />
                <div class="z-menu-item-content items-center flex">
                  <ZIconClipboardTextFilled />
                  My Ads
                </div>
              </ZMenuItem>
            </ZMenu>
          </template>
        </ZDropdown>
      </ZContainer>
    </div>
    <div class="page-p2p-menu py-4">
      <ZContainer class="justify-between">
        <div class="flex items-center justify-center h-full">
          <div class="flex mr-8">
            <div class="page-p2p-menu-button page-p2p-menu-button-buy bold-text" :class="{ selected: query.side === 'buy' }" @click="query.side = 'buy'">
              <span>{{ $t('page.global.action.buy') }}</span>
            </div>
            <div class="page-p2p-menu-button page-p2p-menu-button-sell bold-text" :class="{ selected: query.side === 'sell' }" @click="query.side = 'sell'">
              <span>{{ $t('page.global.action.sell') }}</span>
            </div>
          </div>
          <ZTab v-model="query.coinCurrency" class="page-p2p-menu-tab h-full" :tabs="tabs" :ink="false" />
        </div>
      </ZContainer>
    </div>
    <div class="page-p2p-menu-mobile">
      <div class="page-p2p-menu-mobile-container">
        <div class="flex items-center h-full">
          <div class="flex mr-8">
            <div class="page-p2p-menu-button page-p2p-menu-button-buy bold-text" :class="{ selected: query.side === 'buy' }" @click="query.side = 'buy'">
              <span>{{ $t('page.global.action.buy') }}</span>
            </div>
            <div class="page-p2p-menu-button page-p2p-menu-button-sell bold-text" :class="{ selected: query.side === 'sell' }" @click="query.side = 'sell'">
              <span>{{ $t('page.global.action.sell') }}</span>
            </div>
          </div>
          <ZTab v-model="query.coinCurrency" class="page-p2p-menu-tab h-full" :tabs="tabs" />
        </div>
        <div class="flex items-center">
          <div v-if="userStore.isAuthenticated" class="page-p2p-menu-mobile-new-advertisement" @click="advertisementModal?.openModal()">
            {{ $t('page.p2p.new_ads') }}
          </div>
        </div>
      </div>
    </div>
    <ZContainer>
      <div class="page-p2p-filter-container flex justify-between items-center">
        <div class="page-p2p-filter">
          <div class="page-p2p-filter-item">
            <ZInput v-model="query.amount" :placeholder="$t('page.p2p.enter_amount')">
              <template #suffix>
                <div class="mr-2 text-gray">
                  {{ (query.coinCurrency as string).toUpperCase() }}
                </div>
                <div class="z-input-search-line" />
                <div class="z-input-search bold-text" @click="() => refresh()">
                  {{ $t('page.global.action.search') }}
                </div>
              </template>
            </ZInput>
          </div>
          <div class="page-p2p-filter-item">
            <ZSelect
              v-model="query.fiatCurrency"
              :data-source="publicStore.fiatCurrencies"
              :search="true"
              :scroll="true"
              :show-clear="false"
              :columns="fiatColumn"
              :find-by="['id']"
              value-key="id"
              label-key="id"
              placeholder="Fiat"
              class="w-[180px]"
              :replace-func="(text: string) => text.toUpperCase()"
            >
              <template #symbol="{ item }">
                <div class="page-p2p-filter-item-symbol bold-text">
                  {{ getSymbolFromCurrency(item.id.toUpperCase()) }}
                </div>
              </template>
              <template #id="{ item }">
                {{ item.id.toUpperCase() }}
              </template>
            </ZSelect>
          </div>
          <div class="page-p2p-filter-item">
            <ZSelect
              v-model="query.payment"
              :data-source="[
                {
                  value: 'all',
                  text: 'All payment',
                  color: 'red',
                },
                {
                  value: 'bank',
                  text: 'Bank Account',
                  color: 'orange',
                },
                {
                  value: 'momo',
                  text: 'MoMo',
                  color: 'green',
                },
                {
                  value: 'zalo',
                  text: 'Zalo Pay',
                  color: 'blue',
                },
              ]"
              :search="true"
              :scroll="true"
              :show-clear="false"
              :columns="paymentColumn"
              :find-by="['value']"
              value-key="value"
              label-key="text"
              placeholder="Payment"
              class="w-[180px]"
            >
              <template #value="{ item }">
                {{ item.text }}
              </template>
              <template #color="{ item }">
                <div class="w-[5px] h-[16px]" :class="`color-${item.color}`" />
              </template>
            </ZSelect>
          </div>
        </div>
        <div class="page-p2p-filter-refresh">
          <ZButton @click="() => refresh()">
            <ZIconRotateRightFillted />
            {{ $t('page.global.action.refresh') }}
          </ZButton>
        </div>
      </div>
    </ZContainer>
    <ZContainer class="min-h-[300px]">
      <div class="page-p2p-table">
        <div class="page-p2p-table-head">
          <span>{{ $t('page.p2p.advertisers') }}</span>
          <span>{{ $t('page.p2p.price') }}</span>
          <span class="max-w-[293px]">{{ $t('page.p2p.limit_available') }}</span>
          <span class="max-w-[280px]">{{ $t('page.p2p.payments') }}</span>
          <span class="max-w-[130px]">{{ $t('page.p2p.trade') }}</span>
        </div>
        <ZLoading v-if="pending && !isRefresh" />
        <div v-else-if="advertisements!.length === 0" class="h-[300px] w-full flex flex-col items-center justify-center empty">
          <ZIconClipboardTimesDuotone />
          <span class="text-gray">{{ $t('page.global.table.empty') }}</span>
        </div>
        <AdvertisementRow v-for="advertisement in advertisements" :key="advertisement.id" :advertisement="advertisement" :side="side" />
        <ZPagination v-model="query.page" v-model:page-size="query.limit" :is-loading="pending && !isRefresh" :total="total" />
      </div>
    </ZContainer>
    <Step />
    <AdvertisementModal ref="advertisementModal" @click="() => refresh" />
  </ZLayoutContent>
</template>

<style lang="less">
@import '~/assets/styles/layouts/home.less';
@import "~/assets/styles/layouts/filter.less";

.page-p2p {
  &-header {
    background-color: @layout-header-background-color;
    color: white;

    svg {
      margin-right: 4px;
      width: 24px;
      height: 24px;
      fill: white;
    }

    .z-container {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      height: 100%;
    }

    .z-dropdown {
      &-trigger {
        display: flex;
        align-items: center;
      }

      &-overlay {
        width: 200px;
      }

      .z-menu {
        background-color: @layout-header-background-color;

        &-item {
          color: white;
          padding: 4px 12px;

          svg {
            margin-right: 4px;
            width: 16px;
            height: 16px;
            fill: white;
          }
        }
      }
    }
  }

  .empty {
    svg {
      width: 120px;
      height: 120px;

      .cls-1 {
        fill: @base-border-color;
      }

      .cls-2 {
        fill: @gray-color;
      }
    }
  }

  .z-container {
    @media @mobile {
      width: 100%;
    }

    @media @tablet {
      width: 100%;
    }
  }

  .layout-advertisement-row {
    @media @mobile {
      padding: 24px 16px;
    }
  }

  &-table {
    @media @mobile {
      padding-top: 16px;
    }

    .z-pagination-left {
      @media @mobile, @tablet {
        padding-left: 16px;
      }
    }

    &-head {
      display: flex;
      padding: 12px 0;
      color: @gray-color;
      font-size: 14px;

      @media screen {
        padding: 12px 0;
      }

      @media @mobile {
        display: none;
      }

      & > span {
        flex: 1;
      }
    }
  }

  &-filter {
    .z-filter();

    &-refresh {
      span {
        display: flex;
        align-items: center;
      }

      svg {
        margin-right: 8px;
        width: 22px;
        height: 22px;
        fill: white;
      }

      .z-button {
        background-color: @primary-color;
        color: white;
        width: 150px;
        height: 40px;
        line-height: 40px;
        border-radius: 4px;
      }
    }

    &-item {
      margin-right: 24px;

      &-symbol {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 20px;
        height: 20px;
        color: white;
        font-size: 12px;
        border-radius: 50%;
        background-color: @primary-color;
      }

      .symbol {
        max-width: 30px;
      }
    }

    @media @mobile {
      flex-wrap: wrap;
    }

    & > div {
      @media @mobile {
        margin-bottom: 8px;
      }
    }

    .z-input {
      &-suffix {
        display: flex;
        align-items: center;
      }

      &-search {
        color: @primary-color;
        cursor: pointer;
        user-select: none;
      }

      &-search-line {
        width: 1px;
        height: 18px;
        background-color: @base-border-color;
      }
    }
  }

  .z-table {
    &-head {
      padding: 12px 0;
      height: 56px;
    }

    .color {
      max-width: 16px;

      div {
        border-radius: 10px;
      }

      &-red {
        background-color: red;
      }

      &-green {
        background-color: green;
      }

      &-blue {
        background-color: blue;
      }

      &-orange {
        background-color: orange;
      }
    }
  }

  &-menu {
    @media @mobile {
      display: none;
    }

    @media @tablet {
      padding: 0 16px;
    }

    &-mobile {
      display: none;
      overflow-x: auto;
      padding: 12px 16px;

      .z-tab {
        height: 38px !important;
      }

      &-new-advertisement {
        width: 140px;
        color: @primary-color;
        cursor: pointer;
      }

      @media @mobile {
        display: block;
      }

      &-container {
        display: flex;
      }
    }

    &-tab {
      line-height: 32px;
      height: 32px;

      .z-tab-item {
        height: 100%;
        font-size: 18px;
        font-weight: 500;
        margin-right: 36px;
      }
    }

    &-new-advertisement {
      color: @primary-color;
      cursor: pointer;
    }

    .z-container {
      display: flex;
      align-items: center;
      height: 56px;
    }

    &-button {
      position: relative;
      flex: 1;
      display: flex;
      height: 47px;
      cursor: pointer;

      &::after {
        content: "";
        display: block;
        left: 0;
        top: 0;
        width: 96px;
        height: 0;
        line-height: 50px;
        border: 0 solid transparent;
        z-index: 1;
      }

      > span {
        position: absolute;
        z-index: 5;
        left: 50%;
        top: 50%;
        transform: translate(-50%,-50%);
        font-size: 16px;
        line-height: 24px;
      }

      &-buy {
        &::after {
          border-right-width: 10px;
          border-top-width: 47px;
          border-top-color: rgba(@action-color, 0.5);
          clip-path: inset(4px 0 4px 4px round 4px);
        }

        &.selected {
          color: white;

          &::after {
            border-top-color: @up-color;
          }
        }
      }

      &-sell {
        &::after {
          border-left-width: 10px;
          border-bottom-width: 47px;
          border-bottom-color: rgba(@action-color, 0.5);
          clip-path: inset(4px 4px 4px 0 round 4px);
        }

        &.selected {
          color: white;

          &::after {
            border-bottom-color: @down-color;
          }
        }
      }
    }
  }

  .z-layout-content {
    background-color: #fff;
  }
}
</style>
