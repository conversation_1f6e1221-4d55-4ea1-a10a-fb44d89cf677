<script setup lang="ts">
import { format as formatDate } from 'date-fns'
import { copyToClipboard, roundNumber } from '@zsmartex/utils'
import getSymbolFromCurrency from 'currency-symbol-map'
import type { ConversationMessage } from '@zsmartex/types'

import { P2PTradeState } from '@zsmartex/types'
import { v4 as uuidv4 } from 'uuid'
import PaymentConfirmModal from '~/layouts/p2p/PaymentConfirmModal.vue'
import UnlockModal from '~/layouts/p2p/UnlockModal.vue'
import CompleteModal from '~/layouts/p2p/CompleteModal.vue'
import PreviewModal from '~/layouts/p2p/PreviewModal.vue'
import ModalFeedback from '~/layouts/p2p/trade/ModalFeedback.vue'

const runtimeConfig = useRuntimeConfig()
const userStore = useUserStore()
const tradeStore = useTradeStore()
const webSocketStore = useWebSocketStore()

const modalFeedback = ref<InstanceType<typeof ModalFeedback>>()
const completeModal = ref<InstanceType<typeof CompleteModal>>()
const paymentConfirmModal = ref<InstanceType<typeof PaymentConfirmModal>>()
const previewModal = ref<InstanceType<typeof PreviewModal>>()
const unlockModal = ref<InstanceType<typeof UnlockModal>>()

definePageMeta({
  middleware: ['p2p'],
})

const id = useRoute().params.id as string
const content = ref('')
const isNextStep = ref(false)
const imagePreview = ref('')
const loading = ref(false)
const loadingStep = ref(false)
const feedbackLoading = ref(false)

const imageMessages = ref<string[]>([])
const files = ref<File[]>([])

const remainingTime = ref(0)
let timeInterval: NodeJS.Timeout

await useAsyncData(async () => await tradeStore.FetchP2PTrade(id))

const { data: conversation } = await useAsyncData<ConversationMessage[]>(async () => {
  const { data } = await tradeStore.FetchP2PTradeConversation(id)

  data.map((m: ConversationMessage) => {
    if (m.uid === userStore.uid) {
      m.status = 'sent'
    }
    return m
  })

  return data
}, { default: () => ([]) })

const isVerify = computed(() => {
  if (tradeStore.p2p_trade.state === P2PTradeState.Pending) return true
  if (tradeStore.p2p_trade.side === P2PTradeSide.Sell && ((tradeStore.p2p_trade.maker_uid === userStore.uid && tradeStore.p2p_trade.taker_accept_at === null) || (tradeStore.p2p_trade.taker_uid === userStore.uid && tradeStore.p2p_trade.maker_accept_at === null))) return true
  if (isNextStep.value) return true
  if (tradeStore.p2p_trade.maker_uid === userStore.uid && tradeStore.p2p_trade.maker_accept_at) return true
  if (tradeStore.p2p_trade.taker_uid === userStore.uid && tradeStore.p2p_trade.taker_accept_at) return true
  return false
})

const disabled = computed(() => {
  if (loading.value) return true
  if (!content.value.length && !files.value.length) return true
  return false
})

function handlePreview(url: string) {
  imagePreview.value = url
  previewModal.value?.openModal(imagePreview.value)
}

async function onImageMessageChange(e: Event) {
  const target = e.target as HTMLInputElement
  const inputFiles = Array.from(target.files!)

  if (inputFiles) {
    for (let i = 0; i < inputFiles.length; i++) {
      // const uuid = uuidv4()
      imageMessages.value.push(URL.createObjectURL(inputFiles[i]))
      files.value.push(inputFiles[i])

      // const data = new FormData()
      // data.append('image', inputFiles[i])
      // data.append('uuid', uuid)
      // data.append('conversation_id', String(tradeStore.p2p_trade.conversation_id))
      // await tradeStore.CreateAttachment(data)
    }
  }
}

function clearImage(index: number) {
  imageMessages.value.splice(index, 1)
  if (files.value) {
    files.value.splice(index, 1)
  }
}

function clearAllImage() {
  imageMessages.value = []
  files.value = []
}

async function SendMessageP2PTrade() {
  if (tradeStore.p2p_trade.state === 'pending') return
  if (disabled.value) return
  loading.value = true

  if (conversation.value) {
    if (files.value.length > 0) {
      for (let i = 0; i < files.value.length; i++) {
        const uuid = uuidv4()

        conversation.value!.push({
          content: '',
          conversation_id: tradeStore.p2p_trade.conversation_id,
          uid: userStore.uid,
          filename: uuid,
          image: imageMessages.value[i],
          status: 'sending',
        })

        const data = new FormData()
        data.append('image', files.value[i])
        data.append('uuid', uuid)

        const result = await tradeStore.SendMessageP2PTrade(id, data)
        conversation.value[conversation.value.length - 1].status = result
      }
      clearAllImage()
    }

    if (content.value) {
      const data = new FormData()
      data.append('content', content.value)
      const result = await tradeStore.SendMessageP2PTrade(id, data)
      conversation.value[conversation.value.length - 1].status = result
    }
  }

  loading.value = false
  content.value = ''
}

async function VerifyP2PTrade() {
  loadingStep.value = true
  await tradeStore.VerifyP2PTrade(id, () => {
    paymentConfirmModal.value?.openModal()
    isNextStep.value = true
    if (tradeStore.p2p_trade.side === P2PTradeSide.Sell) {
      completeModal.value?.openModal()
    }
  })
  loadingStep.value = false
}

function nextStep() {
  if (tradeStore.p2p_trade.side === P2PTradeSide.Buy) {
    paymentConfirmModal.value?.openModal()
  } else {
    unlockModal.value?.openModal()
  }
}

function copyStaticID(value: string) {
  Message.success({
    message: $t('success'),
  })

  copyToClipboard(value)
}

function clearDelay() {
  clearInterval(timeInterval)
  remainingTime.value = 0
  if (tradeStore.p2p_trade.state === P2PTradeState.Pending || tradeStore.p2p_trade.state === P2PTradeState.Processing) {
    tradeStore.p2p_trade.state = P2PTradeState.Expired
  }
}

function convertMilisecondToTimeString(mili: number) {
  let minute = (Math.floor(mili / 1000 / 60)).toFixed(0)
  let second = (mili / 1000 - Number(minute) * 60).toFixed(0)

  if (Number(minute) < 10) minute = `0${minute}`
  if (Number(second) < 10) second = `0${second}`

  return `00:${minute}:${second}`
}

onMounted(() => {
  if (process.server) return
  webSocketStore.subscribe(WebSocketType.Private, 'p2p_trade', 'conversation_p2p')
  useEvent.on('p2p_trade_message', newMessage)

  remainingTime.value = new Date(tradeStore.p2p_trade.expired_at).getTime() - new Date().getTime()
  timeInterval = setInterval(() => {
    remainingTime.value = remainingTime.value - 1000

    if (remainingTime.value < 1000) {
      clearDelay()
    }
  }, 1000)
})

onBeforeUnmount(() => {
  webSocketStore.unsubscribe(WebSocketType.Private, 'p2p_trade', 'conversation_p2p')
  useEvent.off('p2p_trade_message', newMessage)
})

function newMessage(mess: ConversationMessage) {
  if (conversation.value && mess.filename) {
    const index = conversation.value.findIndex(c => c.filename === mess.filename)
    if (index !== -1) {
      conversation.value[index].status = 'sent'
    } else {
      conversation.value?.push(mess)
    }
  } else {
    if (mess.uid === userStore.uid) mess.status = 'sent'
    conversation.value?.push(mess)
  }
}

async function CreateP2PFeedback(type: string, tags: string[], content: string) {
  feedbackLoading.value = true

  const params: Record<string, any> = {
    static_id: tradeStore.p2p_trade.static_id,
    maker_id: tradeStore.p2p_trade.maker_id,
    type,
    tags,
    content,
  }

  await userStore.CreateP2PFeedback(params, () => {
    tradeStore.p2p_trade.responded = true
  })
  feedbackLoading.value = false
}
</script>

<template>
  <ZContainer v-if="tradeStore.p2p_trade && tradeStore.p2p_trade.state === P2PTradeState.Pending || tradeStore.p2p_trade.state === P2PTradeState.Processing" class="my-5 page-p2p-trade">
    <div class="flex">
      <div class="w-8/12 pr-[48px]">
        <div class="page-p2p-trade-head flex justify-between items-center">
          <div class="text-3xl bold-text capitalize">
            {{ `${tradeStore.p2p_trade.side} ${tradeStore.p2p_trade.coin_currency_id.toUpperCase()}` }}
          </div>
          <div>
            <div class="mb-1">
              <span class="text-gray mr-2">{{ $t('page.p2p.created_at') }}</span>
              <span>{{ formatDate(new Date(tradeStore.p2p_trade.created_at), "yyy-dd-MM HH:mm:ss") }}</span>
            </div>
            <div class="copy flex items-center mb-1">
              <span class="text-gray mr-2">{{ $t('page.p2p.static_id') }}</span>
              <span>{{ tradeStore.p2p_trade.static_id }}</span>
              <ZIconCopyAltFilled class="cursor-pointer ml-1" @click="copyStaticID(tradeStore.p2p_trade.static_id)" />
            </div>
            <div class="capitalize">
              <span class="text-gray mr-2">State</span>
              <span
                class="capitalize" :class="[
                  { 'text-blue-500': tradeStore.p2p_trade.state === P2PTradeState.Processing },
                  { 'text-gray-400': tradeStore.p2p_trade.state === P2PTradeState.Pending },
                ]"
              >{{ tradeStore.p2p_trade.state[0].toUpperCase() + tradeStore.p2p_trade.state.slice(1) }}</span>
            </div>
          </div>
        </div>
        <div class="page-p2p-trade-info pt-[24px]">
          <div class="inline-flex flex-col jutify-between mr-16">
            <div class="bold-text text-gray text-[16px]">
              {{ $t('page.p2p.amount') }}
            </div>
            <div
              class="bold-text page-p2p-trade-info-amount"
              :class="[
                { 'text-green-500': tradeStore.p2p_trade.side === 'buy' },
                { 'text-red-500': tradeStore.p2p_trade.side === 'sell' },
              ]"
            >
              {{ `${roundNumber(tradeStore.p2p_trade.total, 2)} ${getSymbolFromCurrency(tradeStore.p2p_trade.fiat_currency_id.toUpperCase())}` }}
            </div>
          </div>
          <div class="h-[52px] inline-flex flex-col justify-between mr-16">
            <div class="bold-text text-gray text-[16px]">
              {{ $t('page.p2p.price') }}
            </div>
            <div class="bold-text text-[16px]">
              {{ `${roundNumber(tradeStore.p2p_trade.price, 2)} ${getSymbolFromCurrency(tradeStore.p2p_trade.fiat_currency_id.toUpperCase())}` }}
            </div>
          </div>
          <div class="h-[52px] inline-flex flex-col justify-between">
            <div class="bold-text text-gray text-[16px]">
              {{ $t('page.p2p.amount') }}
            </div>
            <div class="bold-text text-[16px]">
              {{ `${roundNumber(tradeStore.p2p_trade.amount, 2)} ${tradeStore.p2p_trade.coin_currency_id.toUpperCase()}` }}
            </div>
          </div>
        </div>
        <div class="page-p2p-trade-bank my-[24px] p-[24px]">
          <div class="bold-text text-[18px] mb-4">
            {{ $t('page.p2p.note') }}
          </div>
          <div class="page-p2p-trade-bank-note mb-6">
            {{ $t('page.p2p.desc') }}
          </div>
          <div>
            <div class="mb-2 text-xl bold-text capitalize">
              {{ `${tradeStore.p2p_trade.payment_type} Transfer` }}
            </div>
            <div class="mb-6">
              <div class="text-gray mb-2">
                {{ $t('page.p2p.name_account') }}
              </div>
              <div>
                {{ tradeStore.p2p_trade.name_account }}
              </div>
            </div>
            <div v-if="tradeStore.p2p_trade.payment_type === 'bank'">
              <div class="mb-6">
                <div class="text-gray mb-2">
                  {{ $t('page.p2p.number_account') }}
                </div>
                <div>
                  {{ tradeStore.p2p_trade.data.number_account }}
                </div>
              </div>
              <div class="mb-6">
                <div class="text-gray mb-2">
                  {{ $t('page.p2p.bank_name') }}
                </div>
                <div>
                  {{ tradeStore.p2p_trade.data.bank_name }}
                </div>
              </div>
              <div class="mb-6">
                <div class="text-gray mb-2">
                  {{ $t('page.p2p.bank_address') }}
                </div>
                <div>
                  {{ tradeStore.p2p_trade.data.bank_address }}
                </div>
              </div>
            </div>
            <div v-else-if="tradeStore.p2p_trade.payment_type === 'momo'">
              <div class="mb-6">
                <div class="text-gray mb-2">
                  {{ `${'Account Phone'}` }}
                </div>
                <div>
                  {{ tradeStore.p2p_trade.data.phone }}
                </div>
              </div>
            </div>
          </div>
        </div>
        <I18n class="page-p2p-trade-time bold-text" tag="div" path="page.p2p.time_count">
          <template #span>
            <span class="page-p2p-trade-time-text">{{ convertMilisecondToTimeString(remainingTime) }}</span>
          </template>
        </I18n>
        <div>
          {{ $t('page.p2p.time_desc') }}
        </div>
        <div class="page-p2p-trade-button">
          <ZButton :disabled="isVerify" @click="nextStep">
            {{ isVerify ? $t('page.p2p.wait_partner') : tradeStore.p2p_trade.side === P2PTradeSide.Buy ? $t('page.p2p.transferred') : $t('page.p2p.unlock') }}
          </ZButton>
        </div>
      </div>
      <div class="w-4/12 page-p2p-trade-chat">
        <div class="page-p2p-trade-chat-head">
          <div class="flex">
            <div class="mr-2 rounded-full bg-blue-600 h-[20px] w-[20px] flex justify-center items-center text-white">
              {{ tradeStore.p2p_trade.username ? tradeStore.p2p_trade.username[0].toUpperCase() : tradeStore.p2p_trade.username }}
            </div>
            {{ tradeStore.p2p_trade.username }}
          </div>
          <div class="bold-text text-[18px]">
            {{ tradeStore.p2p_trade.phone_number }}
          </div>
        </div>
        <div class="page-p2p-trade-chat-content">
          <div
            v-for="mess in conversation"
            :key="mess.id"
            class="flex"
            :class="[
              { 'justify-start': mess.uid !== userStore.uid },
              { 'justify-end': mess.uid === userStore.uid },
            ]"
          >
            <div v-if="mess.image" class="page-p2p-trade-chat-content-item-image">
              <img class="max-w-[200px]" :src="mess.image">
            </div>
            <div v-else-if="mess.filename" class="page-p2p-trade-chat-content-item-image">
              <img class="max-w-[200px]" :src="`${runtimeConfig.public.apiUrl}trade/account/p2p_trades/${tradeStore.p2p_trade.static_id}/conversations/${mess.filename}`">
            </div>
            <div
              v-else
              class="page-p2p-trade-chat-content-item"
              :class="{ 'page-p2p-trade-chat-content-item-my': mess.uid === userStore.uid }"
            >
              {{ mess.content }}
            </div>
            <div
              class="page-p2p-trade-chat-content-item-status"
              :class="[
                { 'page-p2p-trade-chat-content-item-status-failed': mess.status === 'failed' },
                { 'page-p2p-trade-chat-content-item-status-sent': mess.status === 'sent' },
                { 'page-p2p-trade-chat-content-item-status-sending': mess.status === 'sending' },
              ]"
            >
              <div class="page-p2p-trade-chat-content-item-status-icon">
                <ZIconCheckFilled v-if="mess.status === 'sent'" />
                <ZIconTimesRegular v-else-if="mess.status === 'failed'" />
              </div>
            </div>
          </div>
        </div>
        <div class="page-p2p-trade-chat-bottom">
          <div v-if="imageMessages.length > 0 && !loading" class="mb-3 page-p2p-trade-chat-bottom-image">
            <div v-for="image, index in imageMessages" :key="index" class="mb-3 page-p2p-trade-chat-bottom-image-container">
              <img :src="image" @click="handlePreview(image)">
              <ZIconTimesRegular class="page-p2p-trade-chat-bottom-image-container-close" @click="clearImage(index)" />
            </div>
          </div>
          <div class="page-p2p-trade-chat-bottom-message">
            <input v-if="tradeStore.p2p_trade.state !== 'pending'" id="image_message" type="file" class="hidden" accept="image/*" multiple @change="onImageMessageChange">
            <ZInput v-model="content" class="!flex-1" :placeholder="$t('page.global.placeholder.write_message')" />
            <label v-if="tradeStore.p2p_trade.state !== 'pending'" class="page-p2p-trade-chat-bottom-send flex items-center ml-3" for="image_message">
              {{ $t('page.global.action.file') }}
            </label>
            <div
              v-if="tradeStore.p2p_trade.state !== 'pending'"
              class="page-p2p-trade-chat-bottom-send flex items-center ml-3"
              :class="{ 'page-p2p-trade-chat-bottom-send-loading': disabled }"
              @click="SendMessageP2PTrade"
            >
              {{ $t('page.global.action.send') }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <PaymentConfirmModal ref="paymentConfirmModal" :loading="loadingStep" @click="VerifyP2PTrade" />
    <UnlockModal ref="unlockModal" :loading="loadingStep" @click="VerifyP2PTrade" />
    <CompleteModal ref="completeModal" />
    <PreviewModal ref="previewModal" />
  </ZContainer>
  <div v-else class="h-[700px] flex justify-center items-center">
    <ZContainer>
      <ZCard class="page-p2p-trade-review">
        <div class="flex justify-center">
          <div
            class="page-p2p-trade-review-icon flex justify-center items-center"
            :class="{ 'page-p2p-trade-review-icon-success': tradeStore.p2p_trade && tradeStore.p2p_trade.state === 'success' }"
          >
            <ZIconCheckFilled v-if="tradeStore.p2p_trade && tradeStore.p2p_trade.state === 'success'" />
            <ZIconTimesRegular v-else />
          </div>
        </div>
        <div class="page-p2p-trade-review-title bold-text">
          {{ $t('page.p2p.p2p_status', { status: Object.keys(tradeStore.p2p_trade).length > 0 ? tradeStore.p2p_trade.state : 'Does not exist' }) }}
        </div>
        <div class="page-p2p-trade-review-description">
          {{ $t('page.p2p.back_desc') }}
        </div>
        <div class="flex justify-center">
          <NuxtLink class="page-p2p-trade-review-button" to="/p2p">
            {{ $t('page.global.action.back') }}
          </NuxtLink>
          <div v-if="!tradeStore.p2p_trade.responded" class="page-p2p-trade-review-feedback ml-8" @click="modalFeedback?.openModal({} as P2PTrade)">
            {{ $t('page.global.action.feedback') }}
          </div>
        </div>
      </ZCard>
      <ModalFeedback ref="modalFeedback" :loading="feedbackLoading" @click="CreateP2PFeedback" />
    </ZContainer>
  </div>
</template>

<style lang="less">
.page-p2p-trade {
  .copy {
    svg {
      width: 24px;
      height: 24px;
      fill: @gray-color;
    }
  }

  &-head {
    padding-bottom: 24px;
    border-bottom: 1px solid @base-border-color;
  }

  &-chat {
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    &-head {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      padding: 12px;
      height: 80px;
      border: 1px solid @base-border-color;
      border-top-right-radius: 4px;
      border-top-left-radius: 4px;
    }

    &-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      max-height: 640px;
      padding: 12px;
      border: 1px solid @base-border-color;
      border-top: none;
      border-bottom: none;
      background-color: rgba(@gray-color, 0.03);
      overflow-y: auto;

      &-item {
        padding: 8px 12px;
        margin-bottom: 12px;
        background-color: white;
        border-radius: 8px;

        &-image {
          margin-bottom: 12px;
        }

        &-status {
          display: flex;
          align-items: flex-end;

          &-icon {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-left: 8px;
            margin-bottom: 16px;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            font-size: 8px;

            svg {
              width: 20px;
              height: 20px;
              fill: @gray-color;
            }
          }

          &-failed {
            .page-p2p-trade-chat-content-item-status-icon {
              border: 1px solid @down-color;
              color: @down-color;

              svg {
                fill: @down-color;
              }
            }
          }

          &-sending {
            .page-p2p-trade-chat-content-item-status-icon {
              border: 1px solid @gray-color;
              color: @gray-color;
            }
          }

          &-sent {
            .page-p2p-trade-chat-content-item-status-icon {
              border: 1px solid @primary-color;
              color: @primary-color;

              svg {
                fill: @primary-color;
              }
            }
          }
        }

        &-my {
          background-color: rgba(@primary-color, 0.6);
          color: white;
        }
      }
    }

    &-bottom {
      display: flex;
      flex-direction: column;
      padding: 12px;
      border: 1px solid @base-border-color;
      border-bottom-right-radius: 4px;
      border-bottom-left-radius: 4px;

      &-image {
        display: flex;

        &-container {
          position: relative;
          margin-right: 20px;

          svg {
            position: absolute;
            cursor: pointer;
            width: 20px;
            height: 20px;
            fill: @gray-color;
          }
        }

        img {
          height: 50px;
          width: 50px;
          object-fit: cover;
          border-radius: 12px;
        }
      }

      &-message {
        display: flex;
        flex: 1;

        .z-input {
          flex: 1;
        }
      }

      &-send {
        color: @primary-color;
        cursor: pointer;
        user-select: none;

        &-loading {
          color: @gray-color;
          cursor: not-allowed;
        }
      }
    }
  }

  &-info {
    &-amount {
      font-size: 24px;
    }
  }

  &-bank {
    border: 1px solid @base-border-color;
    border-radius: 4px;

    &-note {
      padding: 4px 8px;
      background-color: rgba(@warn-color, 0.2);
      color: @warn-color;
      border-radius: 4px;
    }
  }

  &-time {
    font-size: 20px;

    &-text {
      color: @warn-color;
    }
  }

  &-button {
    display: flex;
    align-items: flex-end;
    margin-top: 24px;

    .z-button {
      margin-right: 24px;
      width: 240px;
      height: 40px;
      background-color: @primary-color;
      color: white !important;
    }

    & > span {
      color: @primary-color;
      cursor: pointer;
    }
  }

  &-review {
    padding: 80px !important;
    text-align: center;

    &-feedback {
      font-size: 18px;
      color: @primary-color;
      cursor: pointer;
    }

    &-icon {
      background-color: @gray-color;
      width: 40px;
      height: 40px;
      border-radius: 50%;

      svg {
        width: 20px;
        height: 20px;
        fill: white;
      }

      i {
        zoom: 1.1;
        color: @white-color;
      }

      &-success {
        background-color: #1aa760 !important;
      }
    }

    &-title {
      margin-top: 20px;
      font-size: 20px;
      color: #000;
    }

    &-description {
      margin: 20px 0;
      font-weight: 500;
      font-size: 16px;
    }

    &-button {
      font-size: 18px;
      color: @text-color;
    }
  }
}
</style>
