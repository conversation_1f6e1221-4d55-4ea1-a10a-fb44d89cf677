<script setup lang="ts">
import type { ZTableColumn } from '@zsmartex/components/types'
import type { TradingFee } from '@zsmartex/types'
import { findBy } from '@zsmartex/utils'

defineOgImageComponent('BlogPost')

const runtimeConfig = useRuntimeConfig()

useSeoMeta({
  title: 'Safetrade - API Document',
  ogTitle: 'Safetrade - API Document',
  description: 'Safetrade is a secure online platform for trading, transferring, and storing cryptocurrency.',
  ogDescription: 'Safetrade is a secure online platform for trading, transferring, and storing cryptocurrency.',
  ogUrl: runtimeConfig.public.mainSiteUrl,
  twitterTitle: 'Safetrade - API Document',
  twitterSite: 'SafeTrade',
})

const publicStore = usePublicStore()
const router = useRouter()
const { query } = useQuery()
const totalFees = ref(0)
const totalDepositWithdraw = ref(0)
const loading = ref(false)

const currencies = computed(() => {
  const result = publicStore.currencies.filter(currency => currency.networks.length)

  totalDepositWithdraw.value = result.length

  if (!search.value) {
    return result.slice((Number(query.value.page) - 1) * Number(query.value.limit), Number(query.value.page) * Number(query.value.limit))
  }

  return findBy(result, ['id', 'name'], search.value)
})

const trading_fees = computed(() => {
  const tradingFees = [...publicStore.trading_fees]

  let globalTradingFee: TradingFee | undefined

  for (const tf of tradingFees) {
    if (tf.market_id === 'any' && tf.group === 'any') {
      globalTradingFee = tf
    }
  }

  if (globalTradingFee) {
    for (const market of publicStore.markets) {
      const tf = tradingFees.find(tf => tf.market_id === market.id && tf.group === 'any')

      if (!tf) {
        tradingFees.push({
          market_id: market.id,
          group: 'any',
          maker: globalTradingFee.maker,
          taker: globalTradingFee.taker,
        })
      }
    }
  }

  const result = tradingFees.map((fee) => {
    const market = publicStore.markets.find(market => market.id === fee.market_id)
    return {
      market_id: (`${market?.base_unit}/${market?.quote_unit}`).toUpperCase(),
      maker: fee.maker,
      taker: fee.taker,
    }
  })

  totalFees.value = result.length

  if (!search.value) {
    return result.slice((Number(query.value.page) - 1) * Number(query.value.limit), Number(query.value.page) * Number(query.value.limit))
  }

  return findBy(result, ['market_id'], search.value)
})

const activeTab = ref('trading_fee')
const search = ref('')

const tabs: ZTabItem[] = [
  {
    key: 'trading_fee',
    text: 'Trading Fee',
  },
  {
    key: 'deposit_withdraw_fee',
    text: 'Deposit/Withdrawal Fee',
  },
]

const currenciesColumns: ZTableColumn[] = [
  {
    key: 'code',
    title: $t('page.global.table.coin_token'),
    scopedSlots: true,
  },
  {
    key: 'name',
    title: $t('page.global.table.name'),
  },
  {
    key: 'networks',
    title: $t('page.global.table.networks'),
    scopedSlots: true,
  },
  {
    key: 'min_deposit',
    title: $t('page.global.table.min_deposit'),
    scopedSlots: true,
  },
  {
    key: 'deposit_fee',
    title: $t('page.global.table.deposit_fee'),
    scopedSlots: true,
  },
  {
    key: 'min_withdraw',
    title: $t('page.global.table.min_withdraw'),
    scopedSlots: true,
  },
  {
    key: 'withdraw_fee',
    title: $t('page.global.table.withdraw_fee'),
    scopedSlots: true,
  },
]

const tradingFeesColumns: ZTableColumn[] = [
  {
    key: 'market_id',
    title: 'MarketID',
    scopedSlots: true,
  },
  {
    key: 'maker',
    title: 'Maker',
    scopedSlots: true,
  },
  {
    key: 'taker',
    title: 'Taker',
    scopedSlots: true,
  },
]

function getParentCurrency(parentID: number) {
  return publicStore.currencies.find((c) => {
    return c.networks.find(n => n.id === parentID)
  })
}

const hasHistory = computed(() => { return window.history.length > 2 })

function back() {
  if (hasHistory.value) {
    router.back()
  }
  else {
    router.push('/')
  }
}

function changeTab() {
  query.value.page = 1
  query.value.limit = 25
}
</script>

<template>
  <ZLayoutContent>
    <ZContainer class="pt-6 pb-[40px]">
      <div class="page-fees-head" @click="back">
        <ZIcon type="back" />
        <h3 class="page-fees-head-title">
          <ZIconArrowLeftDuotone />
          {{ $t('page.my.fees') }}
        </h3>
      </div>
      <ZCard>
        <div class="page-fees-tab flex justify-between">
          <ZTab v-model="activeTab" :tabs="tabs" @click="changeTab" />
          <div class="w-[250px] page-fees-tab-search">
            <ZInputSearch
              v-model="search"
              placeholder="Search"
            />
          </div>
        </div>
        <div v-if="activeTab === 'trading_fee'">
          <ZTable
            :title="$t('page.fees.title_table')"
            :columns="tradingFeesColumns"
            :data-source="trading_fees"
          >
            <template #market_id="{ item }">
              {{ item.market_id }}
            </template>
            <template #maker="{ item }">
              {{ `${item.maker * 100}%` }}
            </template>
            <template #taker="{ item }">
              {{ `${item.taker * 100}%` }}
            </template>
            <template v-if="search.length < 1" #foot>
              <ZPagination v-model="query.page" v-model:page-size="query.limit" :is-loading="loading" :total="totalFees" />
            </template>
          </ZTable>
        </div>
        <div v-if="activeTab === 'deposit_withdraw_fee'">
          <ZTable
            :title="$t('page.fees.title_table')"
            :columns="currenciesColumns"
            :data-source="currencies"
            responsive
          >
            <template #code="{ item }">
              <span class="currency_icon">
                <img :src="item.icon_url">
              </span>
              {{ item.id.toUpperCase() }}
            </template>
            <template #networks="{ item }">
              <div v-for="network in item.networks" :key="network.id">
                <template v-if="network.parent_id">
                  {{ getParentCurrency(network.parent_id)?.name }} <span class="bold-text">({{ network.protocol }})</span>
                </template>
                <template v-else>
                  {{ item.name }} <span class="bold-text">({{ network.protocol }})</span>
                </template>
              </div>
            </template>
            <template #min_deposit="{ item }">
              <div v-for="network in item.networks" :key="network.id">
                {{ network.min_deposit_amount }}
              </div>
            </template>
            <template #deposit_fee="{ item }">
              <div v-for="network in item.networks" :key="network.id">
                {{ Number(network.deposit_fee) === 0 || !network.deposit_fee ? $t('page.fees.free') : network.deposit_fee }}
              </div>
            </template>
            <template #min_withdraw="{ item }">
              <div v-for="network in item.networks" :key="network.id">
                {{ network.min_withdraw_amount }}
              </div>
            </template>
            <template #withdraw_fee="{ item }">
              <div v-for="network in item.networks" :key="network.id">
                {{ Number(network.withdraw_fee) === 0 ? $t('page.fees.free') : network.withdraw_fee }}
              </div>
            </template>
            <template v-if="search.length < 1" #foot>
              <ZPagination v-model="query.page" v-model:page-size="query.limit" :is-loading="loading" :total="totalDepositWithdraw" />
            </template>
          </ZTable>
        </div>
      </ZCard>
    </ZContainer>
  </ZLayoutContent>
</template>

<style lang="less">
.page-fees {
  &-tab {
    @media @mobile {
      display: block;
    }

    &-search {
      @media @mobile {
        margin-top: 12px;
        width: 100%;
      }
    }
  }

  .z-container {
    @media @desktop {
      width: 100%;
    }
  }

  &-head {
    cursor: pointer;
    display: flex;
    align-items: center;

    svg {
      width: 24px;
      height: 24px;
    }

    &-title {
      font-size: 24px;

      @media @mobile {
        margin-left: 16px;
      }

      @media @tablet {
        margin-left: 24px;
      }
    }
  }

  @media @mobile, @tablet {
    width: 100%;
  }

  .z-container {
    @media @mobile, @tablet {
      width: 100%;
    }
  }

  .z-table {
    &-head {
      padding: 0;
    }

    &-row {
      border-top: 1px solid @base-border-color;
      height: auto !important;
      padding: 12px 0 !important;
      line-height: 20px !important;
    }
  }

  .currency_icon {
    display: flex;
    align-items: center;
    margin-right: 6px;

    img {
      border-radius: 50%;
      width: 24px;
      height: 24px;
    }
  }

  .networks, .min_withdraw, .withdraw_fee, .min_deposit, .deposit_fee {
    display: block;
  }
}
</style>
