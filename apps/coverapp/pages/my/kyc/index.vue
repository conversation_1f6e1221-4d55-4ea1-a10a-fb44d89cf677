<script setup lang="ts">
import type { Country } from '@zsmartex/types'
import type { ZTableColumn } from '@zsmartex/components/types'
import { InputType } from '@zsmartex/components/types'
import countries from '~/library/countries'

definePageMeta({
  middleware: ['review-kyc'],
})

const runtimeConfig = useRuntimeConfig()

const step = useState(() => 1)
const fullName = useState(() => '')
const IDType = useState(() => 'National ID Card')
const IDNumber = useState(() => '')
const country = useState<Country>(() => countries[0])
const searchBoxVisible = useState(() => false)
const frontImage = useState(() => '')
const backImage = useState(() => '')
const portraitImage = useState(() => '')
const loading = useState(() => false)
const frontLoading = useState(() => false)
const backLoading = useState(() => false)

const userStore = useUserStore()

const contrySelectColumns: ZTableColumn[] = [
  {
    key: 'country_name',
    scopedSlots: true,
  },
]

const idTypes = [
  {
    id: 1,
    value: 'National ID Card',
  },
  {
    id: 2,
    value: 'International Passport',
  },
]

const idTypeColumns: ZTableColumn[] = [
  {
    key: 'value',
  },
]

const fullNameError = computed(() => {
  if (/[^a-zA-z ]/.test(fullName.value)) return 'page.my.kyc.full_name_invalid'
  if ((fullName.value.split(' ').length < 2 || fullName.value.split(' ')[1].length === 0) && fullName.value.length !== 0) return 'page.my.kyc.full_name_invalid'
})

const buttonDisabledStep1 = computed(() => {
  if (fullNameError.value) return true
  if (fullName.value.length === 0) return true
  if (IDType.value.length === 0) return true
  if (IDNumber.value.length === 0) return true

  return false
})

const buttonDisabledStep2 = computed(() => {
  if (frontLoading.value) return true
  if (backLoading.value) return true
  if (frontImage.value.length === 0) return true
  if (backImage.value.length === 0) return true

  return false
})

const buttonDisabledStep3 = computed(() => {
  if (loading.value) return true
  if (portraitImage.value.length === 0) return true

  return false
})

async function onFrontImageChange(e: Event) {
  const target = e.target as HTMLInputElement
  const files = target.files

  if (files) {
    frontImage.value = URL.createObjectURL(files[0])

    const data = new FormData()
    data.append('tag', 'front')
    data.append('image', files[0])
    frontLoading.value = true
    await userStore.AddAttachment(data)
    frontLoading.value = false
  }
}

async function onBackImageChange(e: Event) {
  const target = e.target as HTMLInputElement
  const files = target.files

  if (files) {
    backImage.value = URL.createObjectURL(files[0])

    const data = new FormData()
    data.append('tag', 'back')
    data.append('image', files[0])
    backLoading.value = true
    await userStore.AddAttachment(data)
    backLoading.value = false
  }
}

async function onPortraitImageChange(e: Event) {
  const target = e.target as HTMLInputElement
  const files = target.files

  if (files) {
    portraitImage.value = URL.createObjectURL(files[0])

    const data = new FormData()
    data.append('tag', 'portrait')
    data.append('image', files[0])
    loading.value = true
    await userStore.AddAttachment(data)
    loading.value = false
  }
}

async function buttonClickStep1() {
  loading.value = true

  await userStore.AddProfile({
    full_name: fullName.value,
    country: country.value.code,
    document_type: IDType.value,
    document_number: IDNumber.value,
  }, () => {
    step.value = 2
  })

  loading.value = false
}

async function buttonClickStep2() {
  step.value = 3
}

async function buttonClickStep3() {
  loading.value = true
  await userStore.SubmitProfile(() => {
    navigateTo('/my/dashboard')
  })
  loading.value = false
}

function onSearchBoxClicked(item: Country) {
  searchBoxVisible.value = false
  country.value = item
}
</script>

<template>
  <ZContainer class="my-5">
    <ZHeadBack to="/my/security" class="mb-5">
      <ZIconArrowLeftDuotone />
      {{ $t('page.my.kyc.title') }}
    </ZHeadBack>

    <ZCard>
      <div class="-mx-6 px-6 pt-2 pb-6 border-b-1 text-base">
        {{ $t('page.my.kyc.header_text') }}
      </div>
      <div class="mt-8 -ml-6 -mr-6">
        <div class="page-my-kyc-step">
          <div class="page-my-kyc-step-title" @click="step = 1">
            <div class="page-my-kyc-step-title-number" :class="{ 'page-my-kyc-step-title-number-active': step >= 1 }">
              <ZIconCheckFilled v-if="step > 1" />
              <span v-else>1</span>
            </div>
            <div class="page-my-kyc-step-title-text">
              <div class="page-my-kyc-step-title-text-title">
                {{ $t('page.my.kyc.basic_information') }}
              </div>
              <div class="page-my-kyc-step-title-text-subtitle">
                {{ $t('page.my.kyc.basic_information_desc') }}
              </div>
            </div>
          </div>
          <Transition name="show">
            <div v-if="step === 1" class="page-my-kyc-step-content">
              <div class="page-my-kyc-step-content-text">
                <div class="page-my-kyc-step-content-text-input mb-6">
                  <span class="block text-base mb-2">{{ $t('page.global.placeholder.country') }}</span>
                  <ZSearchBox
                    v-model="searchBoxVisible"
                    :data-source="countries"
                    :columns="contrySelectColumns"
                    :find-by="['name']"
                    @click="onSearchBoxClicked"
                  >
                    <span class="z-input bg-gray-100 cursor-pointer">{{ country.name }}</span>
                    <template #country_name="{ item }">
                      {{ item.name }}
                    </template>
                  </ZSearchBox>
                </div>
                <div class="mb-6 page-my-kyc-step-content-text-input">
                  <span class="block text-base mb-2">{{ $t('page.global.placeholder.full_name') }}</span>
                  <ZInput v-model="fullName" :placeholder="$t('page.global.placeholder.full_name')" :error="fullNameError" class="bg-gray-100" />
                </div>
                <div class="mb-6 page-my-kyc-step-content-text-input">
                  <span class="block text-base mb-2">{{ $t('page.global.placeholder.id_type') }}</span>
                  <ZSelect
                    v-model="IDType"
                    :data-source="idTypes"
                    :search="true"
                    :columns="idTypeColumns"
                    :find-by="['value']"
                    value-key="value"
                    label-key="value"
                    placeholder="ID Type"
                  />
                </div>
                <div class="mb-8 page-my-kyc-step-content-text-input">
                  <span class="block text-base mb-2">{{ $t('page.global.placeholder.id_number') }}</span>
                  <ZInput v-model="IDNumber" :placeholder="$t('page.global.placeholder.id_number')" :type="InputType.Number" class="bg-gray-100" />
                </div>
                <ZButton :disabled="buttonDisabledStep1" :loading="loading" @click="buttonClickStep1">
                  {{ $t('page.global.action.next') }}
                </ZButton>
              </div>
            </div>
          </transition>
        </div>
      </div>
      <div class="-mx-6">
        <div class="page-my-kyc-step">
          <div class="page-my-kyc-step-title" @click="() => { if (step >= 2) step = 2 }">
            <div class="page-my-kyc-step-title-number" :class="{ 'page-my-kyc-step-title-number-active': step >= 2 }">
              <ZIconCheckFilled v-if="step > 2" />
              <span v-else>2</span>
            </div>
            <div class="page-my-kyc-step-title-text">
              <div class="page-my-kyc-step-title-text-title">
                {{ $t('page.my.kyc.upload_photo') }}
              </div>
              <div class="page-my-kyc-step-title-text-subtitle">
                {{ $t('page.my.kyc.upload_photo_desc') }}
              </div>
            </div>
          </div>
          <Transition name="show">
            <div v-if="step === 2" class="page-my-kyc-step-content">
              <div class="page-my-kyc-step-content-text">
                <div class="mb-8 page-my-kyc-step-content-text-flex">
                  <div class="text-center font-bold">
                    <p>{{ $t('page.my.kyc.upload_front') }}</p>
                    <label for="front_image" class="page-my-kyc-step-content-text-image">
                      <img v-if="frontImage" :src="frontImage">
                      <div v-else class="page-my-kyc-step-content-text-image-example" />
                    </label>
                    <input id="front_image" type="file" class="hidden" @change="onFrontImageChange">
                  </div>
                  <div class="text-center font-bold">
                    <p>{{ $t('page.my.kyc.upload_back') }}</p>
                    <input id="back_image" type="file" class="hidden" @change="onBackImageChange">

                    <label for="back_image" class="page-my-kyc-step-content-text-image">
                      <img v-if="backImage" :src="backImage">
                      <div v-else class="page-my-kyc-step-content-text-image-example" />
                    </label>
                  </div>
                </div>
                <ZButton :disabled="buttonDisabledStep2" :loading="frontLoading || backLoading" @click="buttonClickStep2">
                  {{ $t('page.global.action.next') }}
                </ZButton>
              </div>
            </div>
          </Transition>
        </div>
      </div>
      <div class="-mx-6">
        <div class="page-my-kyc-step">
          <div class="page-my-kyc-step-title" @click="() => { if (step >= 3) step = 3 }">
            <div class="page-my-kyc-step-title-number" :class="{ 'page-my-kyc-step-title-number-active': step >= 3 }">
              <ZIconCheckFilled v-if="step > 3" />
              <span v-else>3</span>
            </div>
            <div class="page-my-kyc-step-title-text">
              <div class="page-my-kyc-step-title-text-title">
                {{ $t('page.my.kyc.upload_portrait') }}
              </div>
              <div class="page-my-kyc-step-title-text-subtitle">
                {{ $t('page.my.kyc.upload_portrait_desc') }}
              </div>
            </div>
          </div>
          <Transition name="show">
            <div v-if="step === 3" class="page-my-kyc-step-content">
              <div class="page-my-kyc-step-content-text">
                <div class="font-bold mb-8">
                  <I18n tag="p" path="page.my.kyc.upload_portrait_request" class="page-my-google-auth-step-head-title">
                    <template #br>
                      <br>
                    </template>
                    <template #exchange_name>
                      {{ runtimeConfig.public.exchangeName }}
                    </template>
                  </I18n>

                  <input id="portrait_image" type="file" class="hidden" @change="onPortraitImageChange">

                  <label for="portrait_image" class="page-my-kyc-step-content-text-image">
                    <img v-if="portraitImage" :src="portraitImage">
                    <div v-else class="page-my-kyc-step-content-text-image-example" />
                  </label>
                </div>
                <ZButton :disabled="buttonDisabledStep3" :loading="loading" @click="buttonClickStep3">
                  {{ $t('page.global.action.submit') }}
                </ZButton>
              </div>
            </div>
          </Transition>
        </div>
      </div>
    </ZCard>
  </ZContainer>
</template>

<style lang="less">
.page-my-kyc {
  .z-back {
    display: flex;
    align-items: center;

    svg {
      width: 24px;
      height: 24px;
    }
  }

  .z-container {
    @media @mobile, @tablet {
      width: 100%;
    }
  }

  .z-back-title {
    @media @mobile, @tablet {
      margin-top: 0;
    }
  }

  &-step {
    &-title {
      display: flex;
      padding: 12px 24px;
      align-items: center;
      cursor: pointer;

      &:hover {
        background-color: #eee;
      }

      &-number {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 28px;
        height: 28px;
        font-size: 18px;
        color: #fff;
        border-radius: 50%;
        background-color: @gray-color;

        svg {
          width: 20px;
          height: 20px;
          fill: white;
        }

        &-active {
          background-color: @primary-color;
        }
      }

      &-text {
        margin-left: 16px;
        flex: 1;

        &-title {
          font-size: 16px;
          font-weight: 500;
        }

        &-subtitle {
          color: @gray-color;
        }
      }
    }

    &-content {
      position: relative;
      margin: 0 38px;
      max-height: 1000px;
      opacity: 1;
      transition: all 0.5s ease;
      overflow: hidden;

      &::before {
        content: "";
        position: absolute;
        left: 0;
        width: 1px;
        height: 100%;
        background-color: rgba(@gray-color, 0.4);
      }

      select {
        &:focus {
          border: 1px solid @primary-color;
        }
      }

      .head-action .z-select-trigger {
        display: flex;
        align-items: center;
        padding-left: 8px;
        height: 42px;
        font-size: 18px;
        background-color: rgba(#ccc, 0.2);
        border-radius: 4px;
        border: 1px solid rgba(#ccc, 0.6);
      }

      .z-select {
        .z-overlay {
          width: 100%;
          margin: 4px 0;
          z-index: 1;
        }

        .z-menu {
          padding: 4px 0;
          width: 100%;
          height: 200px;
          background-color: #fff;
          border: 1px solid rgba(#ccc, 0.6);
          border-radius: 4px;
          text-align: left;
          overflow: scroll;

          &-item {
            display: flex;
            align-items: center;
            margin-left: 0;
            padding: 0 8px;
            height: 38px;
            font-size: 18px;

            &:hover {
              background-color: rgba(0, 0, 0, 0.15);
            }
          }
        }
      }

      &-text {
        padding: 16px 30px;

        @media @mobile {
          padding-right: 0;
        }

        &-input {
          width: 392px;
          @media @mobile {
            width: 100%;
          }
        }

        &-flex {
          display: flex;

          @media @mobile {
            display: block;
          }
        }

        .z-input {
          &.hidden {
            display: none;
          }
        }

        &-image {
          cursor: pointer;
          display: flex;
          justify-content: center;
          align-items: center;
          width: 350px;
          height: 232px;
          padding: 12px;
          overflow: hidden;
          border: 2px solid transparent;

          @media @mobile {
            width: 100%;
          }

          &:hover {
            border: 2px dashed @primary-color;
          }

          &-example {
            width: 322px;
            height: 214px;
            background: url(https://b.peatio.com/kyc/id-front.png) no-repeat #fff;
            background-size: cover;
            padding: 24px;

            @media @mobile {
              width: 100%;
              height: 181px;
            }
          }

          img {
            object-fit: cover;
          }
        }
      }

      .z-input {
        font-size: 16px;
        height: 38px;

        &-error {
          &-content {
            margin-top: 4px;
          }
        }
      }

      .z-button {
        width: 180px;
        height: 36px;
        border-radius: 4px;
        color: #fff;
        font-size: 16px;
        background-color: @primary-color;
        box-shadow: 0 3px 1px -2px #0003, 0 2px 2px 0 #00000024, 0 1px 5px 0 #0000001f;

        &:disabled {
          box-shadow: none;
        }
      }
    }
  }
}

.show {
  &-enter-from, &-leave-to {
    max-height: 0;
    opacity: 0.5;
  }
}
</style>
