<script lang="ts" setup>
import ReviewKYC from '~/layouts/my/kyc/ReviewKYC.vue'
</script>

<template>
  <ZContainer class="my-5 page-my-kyc-review">
    <ZHeadBack to="/my/security" class="mb-5">
      <ZIconArrowLeftDuotone />
      {{ $t('page.my.kyc.title') }}
    </ZHeadBack>

    <ReviewKYC />
  </ZContainer>
</template>

<style lang="less">
.page-my-kyc-review {
  .z-back {
    display: flex;
    align-items: center;

    svg {
      width: 24px;
      height: 24px;
    }
  }
}
</style>
