<script setup lang="ts">
import type { ZTableColumn } from '@zsmartex/components/types'
import type { Beneficiary } from '@zsmartex/types'
import ModalBook from '~/layouts/my/address-book/ModalBook.vue'
import ModalWarn from '~/layouts/my/ModalWarn.vue'

const userStore = useUserStore()
const publicStore = usePublicStore()
const tempShow = useState<Record<string, boolean>>(() => ({}))
const tempDelete = useState<Record<string, boolean>>(() => ({}))
const tempDeleteLoading = useState<Record<string, boolean>>(() => ({}))

const modalBook = ref<InstanceType<typeof ModalBook>>()
const modalWarn = ref<InstanceType<typeof ModalWarn>>()

const columns: ZTableColumn[] = [
  {
    key: 'name',
    scopedSlots: true,
  },
]

const currencies = computed(() => {
  return publicStore.currencies.filter(c => c.networks.some(n => n.status === CurrencyNetworkStatus.Active && n.withdraw_enabled))
})

function ActionCreate() {
  if (userStore.otp || userStore.hasPhone) {
    modalBook.value?.openModal('create')
  } else {
    modalWarn.value?.openModal()
  }
}

async function DeleteAddressBook(item: Beneficiary) {
  const index = userStore.beneficiaries.findIndex(a => a.id === item.id)
  if (index !== -1 && item.id) {
    tempDeleteLoading.value[item.id] = true

    await userStore.DeleteBeneficiary(item.id)

    tempDeleteLoading.value[item.id] = false
  }
}

function ChangeShowAddress(item: any) {
  tempShow.value[item.currency_id] = !tempShow.value[item.currency_id]
}

const groupCurrencies = computed(() => {
  const result: {
    currency_id: string
    currency_name: string
    addresses: Beneficiary[]
  }[] = []

  if (userStore.beneficiaries) {
    userStore.beneficiaries.forEach((addressBook) => {
      const index = result.findIndex(r => r.currency_id === addressBook.currency_id)
      if (index !== -1) {
        result[index].addresses.push(addressBook)
      } else {
        const currency = publicStore.currencies.find(c => c.id === addressBook.currency_id)
        result.push({ currency_id: addressBook.currency_id, currency_name: currency!.name, addresses: [addressBook] })
      }
    })
  }

  return result
})

function GetParrentCurrencyName(currencyID: string, blockchainKey: string) {
  const currency = publicStore.currencies.find(c => c.id === currencyID)
  const network = currency!.networks.find(c => c.blockchain_key === blockchainKey)
  const parentID = network!.parent_id ? network!.parent_id : network!.currency_id
  return `${publicStore.currencies.find(c => c.id === parentID)!.name} (${network!.protocol})`
}

function getProtocol(beneficiary: Beneficiary) {
  const currency = publicStore.currencies.find(c => c.id === beneficiary.currency_id)
  const network = currency?.networks.find(network => network.blockchain_key === beneficiary.blockchain_key)
  return network?.protocol
}

function navigateWithdraw(beneficiary: Beneficiary) {
  const router = useRouter()

  router.push({
    path: `/my/wallet/assets/withdraw/${beneficiary.currency_id}`,
    query: {
      beneficiary_id: beneficiary.id,
    },
  })
}
</script>

<template>
  <ZContainer class="my-5 page-my-address-book-container">
    <ZHeadBack to="/my/dashboard" class="mb-5">
      <ZIconArrowLeftDuotone />
      {{ $t('page.my.address_book.title') }}
    </ZHeadBack>

    <ZCard class="page-my-address-book">
      <template #head>
        <ZButton v-if="currencies.length !== 0" class="custom-button" color="primary" @click="ActionCreate">
          <div class="flex items-center justify-center">
            <ZIconPlusFilled class="mr-2" />
            <span class="">{{ $t("page.global.action.add") }}</span>
          </div>
        </ZButton>
      </template>
      <div class="page-my-address-book-table">
        <ZTable
          class="text-base"
          :columns="columns"
          :data-source="groupCurrencies"
          :head-enabled="false"
        >
          <template #name="{ item }">
            <div class="name-container py-3 w-full" :class="{ show: tempShow[item.currency_id] }">
              <div class="flex items-center justify-between mb-2">
                <div class="flex items-center">
                  <img
                    class="mr-2 h-6 rounded-full"
                    :src="publicStore.getCurrencyByID(item.currency_id)?.icon_url"
                  >
                  <div class="flex flex-col justify-center">
                    <span class="leading-4 bold-text">
                      {{ item.currency_id.toUpperCase() }}
                    </span>
                    <span class="leading-4 text-xs text-gray-400">{{ item.currency_name }}</span>
                  </div>
                </div>
                <div class="flex items-center">
                  <span class="mr-2 text-sm">{{ $t('page.my.address_book.address_length', { length: item.addresses.length }) }}</span>
                  <div class="action" :class="[{ 'action-click': tempShow[item.currency_id] }]" @click="ChangeShowAddress(item)">
                    <ZIconAngleDownFilled class="cursor-pointer" />
                  </div>
                </div>
              </div>
              <div v-for="beneficiary in item.addresses" :key="beneficiary.id" :class="`flex justify-between items-center px-4 py-3 text-sm ${!tempShow[item.currency_id] ? '!hidden' : ''}`">
                <div class="flex">
                  <div class="mr-1">
                    {{ beneficiary.address && beneficiary.address.length > 80 ? `${beneficiary.address.slice(0, 37)}...${beneficiary.address.slice(-40)}` : beneficiary.address }}
                  </div>
                  <div class="name-container-box-text">
                    {{ GetParrentCurrencyName(beneficiary.currency_id, beneficiary.blockchain_key) }}
                  </div>
                </div>
                <div class="flex items-center">
                  <div class="mr-4">
                    {{ $t('page.my.address_book.address_label') }} <span class="name-container-box-text">{{ beneficiary.label }}</span>
                  </div>
                  <div v-if="publicStore.getWithdrawalEnabledNetworks(item.currency_id).find(n => n.blockchain_key === beneficiary.blockchain_key)" class="mr-4 page-my-address-book-link" @click="navigateWithdraw(beneficiary)">
                    {{ $t('page.my.address_book.withdraw') }}
                  </div>
                  <ZPopconfirm v-model="tempDelete[beneficiary.id]" :placement="Placement.TopRight" trigger="click" @click="DeleteAddressBook(beneficiary)">
                    <ZButton class="delete" :loading="tempDeleteLoading[beneficiary.id]">
                      {{ $t('page.global.action.delete') }}
                    </ZButton>
                  </ZPopconfirm>
                </div>
              </div>
            </div>
          </template>
        </ZTable>
      </div>
      <div class="mt-4 page-my-address-book-mobile">
        <div v-if="userStore.beneficiaries.length === 0" class="z-table-empty w-full h-full flex flex-col items-center justify-center">
          <ZIconClipboardTimesDuotone />
          {{ $t('page.global.table.empty') }}
        </div>
        <div v-for="beneficiary in userStore.beneficiaries" :key="beneficiary.id" class="page-my-address-book-table page-my-address-book-table-mobile">
          <div class="w-[24px] h-[24px]">
            <img class="w-full" :src="publicStore.getCurrencyByID(beneficiary.currency_id)?.icon_url">
          </div>
          <div class="flex-1 px-[8px]">
            <div class="mb-3">
              <span class="text-[15px]">{{ beneficiary.currency_id.toUpperCase() }}</span>
              <span class="ml-2 page-my-address-book-table-protocol">{{ getProtocol(beneficiary) }}</span>
            </div>
            <div class="text-gray text-[12px]">
              {{ beneficiary.address && beneficiary.address.length > 50 ? `${beneficiary.address.slice(0, 22)}...${beneficiary.address.slice(-25)}` : beneficiary.address }}
            </div>
          </div>
          <div>
            <ZIcon type="close" @click="DeleteAddressBook(beneficiary)" />
          </div>
        </div>
      </div>
      <ModalBook v-if="currencies.length !== 0" ref="modalBook" />
      <ModalWarn ref="modalWarn" />
    </ZCard>
  </ZContainer>
</template>

<style lang="less">
.page-my-address-book {
  .z-back {
    display: flex;
    align-items: center;

    svg {
      width: 24px;
      height: 24px;
    }
  }

  &-mobile {
    svg {
      width: 80px;
      height: 80px;

      .cls-1 {
        fill: @base-border-color;
      }

      .cls-2 {
        fill: @gray-color;
      }
    }

    @media @large-desktop, @desktop, @tablet {
      display: none;
    }
  }

  &-container {
    @media @mobile, @tablet {
      width: 100%;
    }
  }

  .z-back-title {
    @media @mobile, @tablet {
      margin-top: 0;
    }
  }

  .z-card-head {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin: 0 -24px;
    padding: 0 24px 12px 24px;
    border-bottom: 1px solid @base-border-color;
  }

  &-link {
    color: @primary-color;
    cursor: pointer;
    transition: all 0.3s;

    &:hover {
      color: rgba(@primary-color, 0.8);
    }
  }

  .custom-button {
    padding: 8px 16px;
    border-radius: 4px;

    svg {
      width: 18px;
      height: 18px;
      fill: @text-color;
    }

    &:hover {
      svg {
        fill: white;
      }
    }
  }

  &-code {
    .z-input-error-container {
      top: 100%;
    }
  }

  .z-popconfirm {
    .z-button.delete {
      height: 32px;
    }

    &-button {
      &-yes {
        border-color: @error-color;
        color: @error-color;

        &:hover {
          background-color: @error-color;
          color: white;
        }
      }
    }
  }

  &-table {
    @media @mobile, @tablet {
      display: none;
    }

    &-mobile {
      @media @mobile, @tablet {
        margin-bottom: 20px;
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
      }
    }

    &-protocol {
      padding: 2px 6px;
      background-color: @primary-bg-color;
      color: @primary-color;
      border-radius: 4px;
    }

    .z-table {
      &-row {
        height: auto;
        padding: 0;

        &-col {
          align-items: flex-start;

          &-content {
            align-items: flex-start;
          }
        }
      }

      &-row + .z-table-row {
        border-top: 1px solid @base-border-color;
      }
    }

    .name {
      &-container {
        height: 56px;

        i {
          user-select: none;
        }

        &.show {
          height: auto;
        }

        .action {
          width: 32px;
          max-height: 32px;
          text-align: center;
          color: rgba(@gray-color, 0.5);
          transition: all 0.3s;

          svg {
            width: 20px;
            height: 20px;
            fill: @gray-color;
          }

          &.action-click {
            transform: rotate(180deg);
          }
        }

        &-box-text {
          padding: 1px 6px;
          color: @text-color;
          background-color: rgba(@primary-color, 0.3);
          border-radius: 4px;
        }
      }
    }
  }
}

.z-code-input {
  input {
    background-color: #fff;
    border-radius: 4px;
  }
}
</style>
