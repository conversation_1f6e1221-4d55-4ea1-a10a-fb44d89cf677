<script setup lang="ts">
import type { ZTabItem } from '@zsmartex/components/types'

const route = useRoute()
const activeTab = useState(() => 'open')

onMounted(async () => {
  const path = route.path.split('/my/orders/')[1]
  if (!path) {
    await navigateTo(`/my/orders/${activeTab.value}`)
    return
  }

  activeTab.value = path
})

const tabs: ZTabItem[] = [
  {
    key: 'open',
    text: 'Open Order',
    slotName: true,
  },
  {
    key: 'history',
    text: 'History Order',
    slotName: true,
  },
  {
    key: 'trades',
    text: 'Trade History',
    slotName: true,
  },
]

function onClick(tabKey: string) {
  navigateTo(`/my/orders/${tabKey}`)
}
</script>

<template>
  <ZContainer class="page-my-orders">
    <div class="page-my-orders-title text-2xl pt-4 pb-8">
      {{ $t('page.my.orders.spot_order') }}
    </div>

    <ZCard>
      <template #head>
        <ZTab v-model="activeTab" :tabs="tabs" @click="onClick">
          <template v-for="tab in tabs" #[tab.key] :key="tab.key">
            {{ tab.text }}
          </template>
        </ZTab>
      </template>
      <NuxtPage />
    </ZCard>
  </ZContainer>
</template>

<style lang="less">
@import "~/assets/styles/layouts/filter.less";

.page-my-orders {
  padding-bottom: 30px;

  .z-back {
    display: flex;
    align-items: center;

    svg {
      width: 24px;
      height: 24px;
    }
  }

  &-title {
    @media @mobile {
      padding-left: 16px;
    }

    @media @tablet {
      padding-left: 24px;
    }
  }

  &-p2p {
    &-title {
      @media @mobile, @tablet {
        padding-left: 16px;
      }
    }
  }

  @media @mobile, @tablet {
    width: 100%;
  }

  .z-back-title {
    @media @mobile, @tablet {
      margin-top: 0;
    }
  }

  .z-container {
    @media @mobile, @tablet {
      width: 100%;
    }
  }

  &-tab {
    &-container {
      @media @mobile {
        padding-left: 16px;
      }

      @media @tablet {
        padding-left: 24px;
      }
    }
  }

  &-table {
    &-item {
      &-side {
        padding: 4px 12px;
        line-height: 1;
        border-radius: 4px;

        &.page-my-orders-table-item-side-red {
          background-color: rgba(#ea4d4d, 0.15);
        }

        &.page-my-orders-table-item-side-green {
          background-color: rgba(#16c39342, 0.15);
        }
      }
    }
  }

  .z-table {
    &-head {
      padding: 0;
    }

    &-row {
      padding: 0;
    }
  }

  &-filter {
    .z-filter();
  }

  .z-pagination {
    .z-dropdown {
      &-trigger {
        display: flex;
        justify-content: center;
        background-color: #fff;
      }
    }
  }
}
</style>
