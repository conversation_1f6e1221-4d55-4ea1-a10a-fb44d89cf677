<script setup lang="ts">
import { format as formatDate, fromUnixTime, getUnixTime } from 'date-fns'
import type { ZTableColumn } from '@zsmartex/components/types'
import { Align, Format, ParseType, type Snapshot } from '@zsmartex/types'
import ModalSnapshot from '~/layouts/my/orders/ModalSnapshot.vue'

const config = useRuntimeConfig()
const userStore = useUserStore()

const modalSnapshot = ref<InstanceType<typeof ModalSnapshot>>()
const total = useState(() => 0)

const { query, callbacks } = useQuery()

const columns: ZTableColumn[] = [
  {
    title: $t('page.global.table.date'),
    key: 'created_at',
    align: Align.Left,
    formatBy: Format.DateTimeNoYear,
    parse: ParseType.DateTime,
  },
  {
    title: 'Type',
    key: 'type',
    align: Align.Left,
    class: 'capitalize',
  },
  {
    title: 'Time from',
    key: 'time_from',
    align: Align.Left,
    scopedSlots: true,
  },
  {
    title: 'Time to',
    key: 'time_to',
    align: Align.Left,
    scopedSlots: true,
  },
  {
    title: 'Status',
    key: 'status',
    align: Align.Left,
    scopedSlots: true,
  },
  {
    title: $t('page.global.table.action'),
    key: 'action',
    align: Align.Right,
    scopedSlots: true,
  }
]

const { data: snapshots, pending, refresh } = await useAsyncData(async () => {
  try {
    const { headers, data } = await userStore.FetchSnapshots({
      type: query.value.type,
      page: query.value.page,
      limit: query.value.limit,
    })

    total.value = Number(headers.total)

    return data
  } catch (error) {
    console.log(error)
  }
}, { lazy: true, default: () => <Snapshot[]>([]) })
callbacks.push(refresh)

function addSnapshot(snapshot: Snapshot) {
  snapshots.value.unshift(snapshot)

  total.value = total.value + 1
}
</script>

<template>
  <ZContainer class="my-5 page-my-snapshots-container">
    <ZHeadBack to="/my/dashboard" class="mb-5">
      <ZIconArrowLeftDuotone />
      Snapshots
    </ZHeadBack>

    <ZCard class="page-my-snapshots">
      <template #head>
        <ZButton class="custom-button" color="primary" @click="modalSnapshot?.openModal('create')">
          <div class="flex items-center justify-center">
            <ZIconPlusFilled class="mr-2" />
            <span class="">{{ $t("page.global.action.add") }}</span>
          </div>
        </ZButton>
      </template>

      <div class="page-my-orders-snapshots">
        <ZTable
          class="snapshots"
          :columns="columns"
          :data-source="snapshots"
          :loading="pending"
          responsive
        >
          <template #time_from="{ item }">
            <div>
              {{ formatDate(fromUnixTime(item.time_from), "yyyy-MM-dd") }}
            </div>
          </template>
          <template #time_to="{ item }">
            <div>
              {{ formatDate(fromUnixTime(item.time_to), "yyyy-MM-dd") }}
            </div>
          </template>
          <template #status="{ item }">
            <span
              class="capitalize"
              :class="[
                {'text-warn': item.status == 'pending'},
                {'text-up': item.status === 'succeed'}
              ]"
            >
              {{ item.status }}
            </span>
          </template>
          <template #action="{ item }">
            <a v-if="item.status === 'succeed'" class="cursor-pointer" target="_blank" :href="`/api/v2/trade/account/snapshots/${Number(item.id)}`">
              Download
            </a>
          </template>
          <template #foot>
            <ZPagination v-model="query.page" v-model:page-size="query.limit" :is-loading="pending" :total="total" />
          </template>
        </ZTable>
        <ModalSnapshot ref="modalSnapshot" @submit="addSnapshot" />
      </div>
    </ZCard>
  </ZContainer>
</template>

<style lang="less">
.page-my-snapshots {
  .z-back {
    display: flex;
    align-items: center;

    svg {
      width: 24px;
      height: 24px;
    }
  }

  &-container {
    @media @mobile, @tablet {
      width: 100%;
    }
  }

  .z-back-title {
    @media @mobile, @tablet {
      margin-top: 0;
    }
  }

  .z-card-head {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin: 0 -24px;
    padding: 0 24px 12px 24px;
    border-bottom: 1px solid @base-border-color;
  }

  .custom-button {
    padding: 8px 16px;
    border-radius: 4px;

    svg {
      width: 18px;
      height: 18px;
      fill: @text-color;
    }

    &:hover {
      svg {
        fill: white;
      }
    }
  }

  &-code {
    .z-input-error-container {
      top: 100%;
    }
  }

  &-table {
    @media @mobile, @tablet {
      display: none;
    }

    &-mobile {
      @media @mobile, @tablet {
        margin-bottom: 20px;
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
      }
    }

    &-protocol {
      padding: 2px 6px;
      background-color: @primary-bg-color;
      color: @primary-color;
      border-radius: 4px;
    }

    .z-table {
      &-row {
        height: auto;
        padding: 0;

        &-col {
          align-items: flex-start;

          &-content {
            align-items: flex-start;
          }
        }
      }

      &-row + .z-table-row {
        border-top: 1px solid @base-border-color;
      }
    }
  }
}
</style>
