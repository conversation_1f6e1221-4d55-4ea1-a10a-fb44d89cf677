<script setup lang="ts">
import { fromUnixTime, getUnixTime } from 'date-fns'
import type { ZTableColumn } from '@zsmartex/components/types'
import type { Market, Order } from '@zsmartex/types'
import { Align, Format, OrderSide, OrderState, OrderType, ParseType } from '@zsmartex/types'
import { roundNumber } from '@zsmartex/utils'

const publicStore = usePublicStore()
const tradeStore = useTradeStore()

const total = useState(() => 0)
const dateRange = ref<(Date | null)[]>([null, null])

const { query, callbacks } = useQuery()

const sides = [
  {
    value: OrderSide.Buy,
  },
  {
    value: OrderSide.Sell,
  },
]

const types = [
  {
    value: OrderType.Limit,
  },
  {
    value: OrderType.Market,
  },
]

const columns: ZTableColumn[] = [
  {
    title: $t('page.global.table.date'),
    key: 'created_at',
    align: Align.Left,
    formatBy: Format.DateTimeNoYear,
    parse: ParseType.DateTime,
  },
  {
    title: $t('page.global.table.market'),
    key: 'market',
    align: Align.Left,
    scopedSlots: true,
  },
  {
    title: $t('page.global.table.type'),
    key: 'type',
    align: Align.Left,
    scopedSlots: true,
  },
  {
    title: $t('page.global.table.side'),
    key: 'side',
    align: Align.Left,
    sideKey: 'side',
    formatBy: Format.Price,
    scopedSlots: true,
  },
  {
    title: $t('page.global.table.state'),
    key: 'state',
    align: Align.Left,
    sideKey: 'state',
    scopedSlots: true,
  },
  {
    title: $t('page.global.table.price'),
    key: 'price',
    align: Align.Center,
    formatBy: Format.Price,
    parse: ParseType.Decimal,
    precision(order: Order) {
      const market = getMarket(order.market)

      return market?.price_precision
    },
  },
  {
    title: $t('page.global.table.avg_price'),
    key: 'avg_price',
    align: Align.Center,
    formatBy: Format.Price,
    parse: ParseType.Decimal,
    precision(order: Order) {
      const market = getMarket(order.market)

      return market?.price_precision
    },
    headScopedSlots: true,
  },
  {
    title: $t('page.global.table.filled'),
    key: 'filled',
    align: Align.Center,
    scopedSlots: true,
  },
  {
    title: `${$t('page.global.table.amount')}`,
    key: 'origin_amount',
    align: Align.Right,
    formatBy: Format.Amount,
    parse: ParseType.Decimal,
    precision(order: Order) {
      const market = getMarket(order.market)

      return market?.amount_precision
    },
  },
  {
    title: `${$t('page.global.table.total')}`,
    key: 'total',
    align: Align.Right,
    headScopedSlots: true,
    scopedSlots: true,
  },
]

const { data: orders, pending, refresh } = await useAsyncData(async () => {
  const { headers, data } = await tradeStore.FetchOrders(query.value)

  total.value = Number(headers.total)

  return data
}, { default: () => ([]) })

callbacks.push(refresh)

if (query.value.time_from) dateRange.value[0] = fromUnixTime(Number(query.value.time_from as string))
if (query.value.time_to) dateRange.value[1] = fromUnixTime(Number(query.value.time_to as string))

watch(dateRange.value, () => {
  if (dateRange.value[0] && dateRange.value[1] && dateRange.value[0] <= dateRange.value[1]) {
    query.value = {
      ...query.value,
      time_from: String(getUnixTime(dateRange.value[0])),
      time_to: String(getUnixTime(dateRange.value[1])),
    }
  }
})

function clearDate() {
  delete query.value.time_from
  delete query.value.time_to

  query.value = {
    ...query.value,
    page: 1,
    limit: query.value.limit,
  }
}

function getMarket(marketID: string) {
  return publicStore.markets.find(market => market.id === marketID)
}

function clearParams() {
  query.value = {
    page: 1,
    limit: query.value.limit,
  }

  dateRange.value[0] = null
  dateRange.value[1] = null
}

const showClear = computed(() => {
  if (query.value.market || dateRange.value[0] || dateRange.value[1] || query.value.side || query.value.type) {
    return true
  }
  return false
})

const marketsColumn: ZTableColumn[] = [
  {
    key: 'name',
    scopedSlots: true,
  },
]

const sidesColumn: ZTableColumn[] = [
  {
    key: 'value',
    scopedSlots: true,
  },
]

const typesColumn: ZTableColumn[] = [
  {
    key: 'value',
    scopedSlots: true,
  },
]

function capitalize(text: string) {
  return text[0].toUpperCase() + text.slice(1)
}

function uppercase(text: string) {
  return text.toUpperCase()
}

function getTotal(order: Order) {
  let total = 0
  if (order.state === OrderState.Cancel) total = Number(order.filled_amount) * Number(order.avg_price)
  else total = Number(order.origin_amount) * (Number(order.avg_price) || Number(order.price))

  return roundNumber(total, getMarket(order.market)!.total_precision, true)
}
</script>

<template>
  <div class="page-my-orders-history">
    <div class="page-my-orders-filter">
      <ZSelect
        v-model="query.market"
        :data-source="publicStore.markets"
        :search="true"
        :scroll="true"
        :columns="marketsColumn"
        :find-by="['name']"
        value-key="id"
        label-key="name"
        placeholder="Market"
        :replace-func="uppercase"
        allow-all
        class="mr-4 w-[160px]"
      />
      <ZRangePicker v-model="dateRange" class="mr-4" :placement="Placement.BottomLeft" @clear="clearDate" />
      <ZSelect
        v-model="query.side"
        :data-source="sides"
        :search="true"
        :columns="sidesColumn"
        :find-by="['value']"
        value-key="value"
        label-key="value"
        placeholder="Side"
        :replace-func="capitalize"
        allow-all
        class="mr-4 w-[100px]"
      >
        <template #value="{ item }">
          <span class="capitalize">{{ item.value }}</span>
        </template>
      </ZSelect>
      <ZSelect
        v-model="query.type"
        :data-source="types"
        :search="true"
        :columns="typesColumn"
        :find-by="['value']"
        value-key="value"
        label-key="value"
        placeholder="Type"
        :replace-func="capitalize"
        allow-all
        class="mr-4 w-[120px]"
      >
        <template #value="{ item }">
          <span class="capitalize">{{ item.value }}</span>
        </template>
      </ZSelect>
      <ZButton v-if="showClear" @click="clearParams">
        {{ $t('page.global.action.clear') }}
      </ZButton>
    </div>
    <ZTable
      v-model:query="query"
      class="page-my-orders-history-orders_history"
      :columns="columns"
      :data-source="orders"
      :loading="pending"
      responsive
    >
      <template #head.total>
        <ZTooltip title="total is calculated as average price * amount" :placement="TooltipPlacement.TopRight">
          <span class="underline decoration-dashed">{{ `${$t('page.global.table.total')}` }}</span>
        </ZTooltip>
      </template>
      <template #head.avg_price>
        <ZTooltip title="average execution price" :placement="TooltipPlacement.TopCenter">
          <span class="underline decoration-dashed">{{ `${$t('page.global.table.avg_price')}` }}</span>
        </ZTooltip>
      </template>
      <template #ord_type="{ item }">
        {{ $t(`page.global.orders.type.${item.ord_type}`) }}
      </template>
      <template #side="{ item }">
        <span
          class="page-my-orders-table-item-side"
          :class="[{ 'page-my-orders-table-item-side-red': item.side === OrderSide.Sell }, { 'page-my-orders-table-item-side-green': item.side === OrderSide.Buy }]"
        >
          {{ $t(`page.global.orders.side.${item.side}`) }}
        </span>
      </template>
      <template #state="{ item }">
        <span
          :class="[
            { 'text-red-500': item.state === OrderState.Cancel || item.state === OrderState.Rejected },
            { 'text-green-500': item.state === OrderState.Wait },
            { 'text-blue-500': item.state === OrderState.Done },
            { 'text-gray-500': item.state === OrderState.Pending },
          ]"
        >
          {{ $t(`page.global.orders.state.${item.state}`) }}
        </span>
      </template>
      <template #type="{ item }">
        {{ $t(`page.global.orders.type.${item.type}`) }}
      </template>
      <template #market="{ item }">
        {{ getMarket(item.market)?.name }}
      </template>
      <template #filled="{ item }">
        {{ item.filled_amount > 0 ? `${roundNumber(item.filled_amount * 100 / item.origin_amount, 2)}%` : '---' }}
      </template>
      <template #total="{ item }">
        {{ getTotal(item) }} {{ (item.filled_amount > 0 || item.state === OrderState.Wait) ? item.bid.toUpperCase() : '' }}
      </template>
      <template #foot>
        <ZPagination v-model="query.page" v-model:page-size="query.limit" :is-loading="pending" :total="total" />
      </template>
    </ZTable>
  </div>
</template>
