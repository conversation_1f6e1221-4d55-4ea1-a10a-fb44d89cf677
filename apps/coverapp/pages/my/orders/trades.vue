<script setup lang="ts">
import { fromUnixTime, getUnixTime } from 'date-fns'
import type { ZTableColumn } from '@zsmartex/components/types'
import type { Market, Trade } from '@zsmartex/types'
import { Align, Format, OrderSide, ParseType } from '@zsmartex/types'

const total = useState(() => 0)
const publicStore = usePublicStore()
const tradeStore = useTradeStore()
const dateRange = ref<(Date | null)[]>([null, null])
const { query, callbacks } = useQuery()

const sides = [
  {
    value: OrderSide.Buy,
  },
  {
    value: OrderSide.Sell,
  },
]
const columns: ZTableColumn[] = [
  {
    title: $t('page.global.table.date'),
    key: 'created_at',
    align: Align.Left,
    formatBy: Format.DateTimeNoYear,
    parse: ParseType.DateTime,
  },
  {
    title: $t('page.global.table.market'),
    key: 'market',
    align: Align.Left,
    scopedSlots: true,
  },
  {
    title: $t('page.global.table.side'),
    key: 'side',
    align: Align.Left,
    sideKey: 'order_side',
    formatBy: Format.Price,
    scopedSlots: true,
  },
  {
    title: $t('page.global.table.price'),
    key: 'price',
    align: Align.Center,
    formatBy: Format.Price,
    parse: ParseType.Decimal,
    precision(trade: Trade) {
      const market = getMarket(trade.market) as Market

      return market.price_precision
    },
  },
  {
    title: `${$t('page.global.table.amount')}`,
    key: 'amount',
    align: Align.Right,
    formatBy: Format.Amount,
    parse: ParseType.Decimal,
    precision(trade: Trade) {
      const market = getMarket(trade.market) as Market

      return market.amount_precision
    },
  },
  {
    title: `${$t('page.global.table.total')}`,
    key: 'total',
    align: Align.Right,
    formatBy: Format.Price,
    parse: ParseType.Decimal,
    precision(trade: Trade) {
      const market = getMarket(trade.market) as Market

      return market.price_precision
    },
    suffix(trade: Trade) {
      const market = getMarket(trade.market) as Market

      return market.quote_unit.toUpperCase()
    },
  },
]

const { data: trades, pending, refresh } = await useAsyncData(async () => {
  const { headers, data } = await tradeStore.FetchTrades(query.value)
  total.value = Number(headers.total)

  return data
}, { default: () => ([]) })
callbacks.push(refresh)

if (query.value.time_from) dateRange.value[0] = fromUnixTime(Number(query.value.time_from as string))
if (query.value.time_to) dateRange.value[1] = fromUnixTime(Number(query.value.time_to as string))

watch(dateRange.value, () => {
  if (dateRange.value[0] && dateRange.value[1] && dateRange.value[0] <= dateRange.value[1]) {
    query.value = {
      ...query.value,
      time_from: String(getUnixTime(dateRange.value[0])),
      time_to: String(getUnixTime(dateRange.value[1])),
    }
  }
})

function clearDate() {
  query.value.time_from = ''
  query.value.time_to = ''

  query.value = {
    ...query.value,
    page: 1,
    limit: 15,
  }
}

function getMarket(marketID: string) {
  return publicStore.markets.find(market => market.id === marketID)
}

function clearParams() {
  query.value = {
    page: 1,
    limit: query.value.limit,
  }

  dateRange.value[0] = null
  dateRange.value[1] = null
}

const showClear = computed(() => {
  if (query.value.market || dateRange.value[0] || dateRange.value[1] || query.value.side) {
    return true
  }
  return false
})

const marketsColumn: ZTableColumn[] = [
  {
    key: 'name',
    scopedSlots: true,
  },
]

const sidesColumn: ZTableColumn[] = [
  {
    key: 'value',
    scopedSlots: true,
  },
]

function capitalize(text: string) {
  return text[0].toUpperCase() + text.slice(1)
}

function uppercase(text: string) {
  return text.toUpperCase()
}
</script>

<template>
  <div class="page-my-orders-trades">
    <div class="page-my-orders-filter">
      <ZSelect
        v-model="query.market"
        :data-source="publicStore.markets"
        :search="true"
        :scroll="true"
        :columns="marketsColumn"
        :find-by="['name']"
        value-key="id"
        label-key="name"
        placeholder="Market"
        :replace-func="uppercase"
        allow-all
        class="mr-4 w-[160px]"
      />
      <ZRangePicker v-model="dateRange" class="mr-4" :placement="Placement.BottomLeft" @clear="clearDate" />
      <ZSelect
        v-model="query.side"
        :data-source="sides"
        :search="true"
        :columns="sidesColumn"
        :find-by="['value']"
        value-key="value"
        label-key="value"
        placeholder="Side"
        :replace-func="capitalize"
        allow-all
        class="mr-4 w-[100px]"
      >
        <template #value="{ item }">
          <span class="capitalize">{{ item.value }}</span>
        </template>
      </ZSelect>
      <ZButton v-if="showClear" @click="clearParams">
        {{ $t('page.global.action.clear') }}
      </ZButton>
    </div>
    <ZTable
      class="trades_history"
      :columns="columns"
      :data-source="trades"
      :loading="pending"
      responsive
    >
      <template #ord_type="{ item }">
        {{ $t(`page.global.orders.type.${item.ord_type}`) }}
      </template>
      <template #side="{ item }">
        {{ $t(`page.global.orders.side.${item.order_side}`) }}
      </template>
      <template #market="{ item }">
        {{ getMarket(item.market)?.name }}
      </template>
      <template #foot>
        <ZPagination v-model="query.page" v-model:page-size="query.limit" :is-loading="pending" :total="total" />
      </template>
    </ZTable>
  </div>
</template>
