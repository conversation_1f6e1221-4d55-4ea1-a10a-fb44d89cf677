<script setup lang="ts">
import { userAgent } from '@zsmartex/utils'
import type { ZTableColumn } from '@zsmartex/components/types'
import type { Device } from '@zsmartex/types'
import { Align, Format, ParseType } from '@zsmartex/types'
import ModalEmail from '~/layouts/my/security/ModalEmail.vue'
import ModalPhone from '~/layouts/my/security/ModalPhone.vue'
import ModalPassword from '~/layouts/my/security/ModalPassword.vue'
import ModalUnbind from '~/layouts/my/security/ModalUnbind.vue'
import ModalWarn from '~/layouts/my/ModalWarn.vue'
import countries from '~/library/countries'

const userStore = useUserStore()
const config = useRuntimeConfig()
const { data: devices } = await useAsyncData(() => userStore.FetchSessions().then(res => res.data))

const modalPassword = ref<InstanceType<typeof ModalPassword>>()
const modalEmail = ref<InstanceType<typeof ModalEmail>>()
const modalPhone = ref<InstanceType<typeof ModalPhone>>()
const modalUnbind = ref<InstanceType<typeof ModalUnbind>>()
const modalWarn = ref<InstanceType<typeof ModalWarn>>()

const securityColumns: ZTableColumn[] = [
  {
    key: 'name',
  },
  {
    key: 'desc',
  },
  {
    key: 'action',
    align: Align.Right,
    scopedSlots: true,
  },
]

const deviceColumns: ZTableColumn[] = [
  {
    key: 'device',
    title: $t('page.global.table.device'),
    class: 'bold-text',
    scopedSlots: true,
  },
  {
    key: 'location',
    title: $t('page.global.table.location'),
    scopedSlots: true,
  },
  {
    key: 'authenticated_at',
    title: $t('page.global.table.date'),
    parse: ParseType.DateTime,
    formatBy: Format.DateTime,
  },
  {
    key: 'state',
    title: $t('page.global.table.state'),
    scopedSlots: true,
  },
  {
    key: 'action',
    align: Align.Right,
    scopedSlots: true,
  },
]

const securities = computed(() => {
  const list = [
    {
      key: 'email',
      name: $t('page.my.security.email'),
      desc: $t('page.my.security.email_desc'),
    },
  ]

  if (config.public.phone) {
    list.push({
      key: 'phone',
      name: $t('page.my.security.mobile_phone'),
      desc: $t('page.my.security.mobile_phone_desc'),
    })
  }

  list.push(
    {
      key: 'password',
      name: $t('page.my.security.login_password'),
      desc: $t('page.my.security.login_password_desc'),
    },
    {
      key: '2fa',
      name: $t('page.my.security.google_authenticator'),
      desc: $t('page.my.security.google_authenticator_desc'),
    },
  )

  return list
})

onMounted(() => {
  if (!config.public.phone && !userStore.email) {
    modalEmail.value?.openModal()
  }
})

function destroySession(device: Device) {
  userStore.DestroySession(device.session_id, () => {
    const index = devices.value!.findIndex(d => d.session_id === device.session_id)

    if (index >= 0) {
      devices.value!.splice(index, 1)
    }

    if (device.current_session) {
      window.location.reload()
    } else {
      Message.success({
        message: $t('success.session_destroyed'),
      })
    }
  })
}

function onSecurityTableActionClicked(key: string) {
  switch (key) {
    case 'password':
      if (!userStore.otp && !userStore.hasPhone) {
        modalWarn.value?.openModal()
      } else {
        modalPassword.value?.openModal()
      }
      break
    case 'email':
      if (!userStore.otp) break
      modalEmail.value?.openModal()
      break
    case 'phone':
      modalPhone.value?.openModal()
      break
    case '2fa':
      navigateTo('/my/google-auth')
      break
    default:
      break
  }
}

function getCountryName(code: string) {
  const country = countries.find(c => c.code === code)

  return country ? country.name : code
}
</script>

<template>
  <ZContainer class="my-5">
    <ZHeadBack to="/my/dashboard" class="mb-5">
      <ZIconArrowLeftDuotone />
      {{ $t('page.my.security.title') }}
    </ZHeadBack>

    <ZTablePro class="security-table" :columns="securityColumns" :data-source="securities" :head-enabled="false">
      <template #head>
        <div class="flex flex-col">
          <div class="items-end security-table-flex">
            <span class="bold-text text-xl">{{ $t('page.my.security.security_verification') }}</span>
            <span class="flex items-center security-table-margin">
              {{ $t('page.my.security.account_risk_level') }}
              <template v-for="(_, index) in 3" :key="index">
                <span
                  v-if="index + 1 <= userStore.level"
                  :key="index"
                  class="level-bar ml-1" :class="[
                    {
                      'bg-up': userStore.level === 3,
                      'bg-warn': userStore.level === 2,
                      'bg-down': userStore.level === 1,
                    },
                  ]"
                />
                <span
                  v-else
                  class="level-bar ml-1"
                />
              </template>
            </span>
          </div>
          <div class="text-gray-500 mt-1">
            {{ $t('page.my.security.security_verification_desc') }}
          </div>
        </div>
      </template>
      <template #name="{ item }">
        <div>
          {{ item.name }}
          <div class="text-gray text-[13px] pr-4">
            {{ item.desc }}
          </div>
        </div>
      </template>
      <template #action="{ item }">
        <ZIconNutFilled v-if="(!userStore.otp && item.key === '2fa') || (item.key === 'email' && userStore.otp) || (!userStore.hasPhone && item.key === 'phone') || item.key === 'password'" @click="onSecurityTableActionClicked(item.key)" />
        <a v-else-if="!(item.key === 'phone' && !userStore.hasEmail) && !(item.key === 'email' && !userStore.hasPhone)" @click="modalUnbind?.openModal(item.key)">{{ $t('page.my.security.unbind') }}</a>
      </template>
    </ZTablePro>

    <ZTablePro class="mt-5" :title="$t('page.my.security.manage_devices')" :columns="deviceColumns" :data-source="devices">
      <template #device="{ item }">
        {{ $t('page.my.security.device', { browser: userAgent(item.user_agent).browser, os: userAgent(item.user_agent).os }) }}
      </template>
      <template #location="{ item }">
        {{ `${getCountryName(item.user_ip_country)} (${item.user_ip})` }}
      </template>
      <template #state>
        <span class="status-dot" />
        {{ $t('page.my.security.online') }}
      </template>
      <template #action="{ item }">
        <ZIconTimesRegular @click="destroySession(item)" />
      </template>
    </ZTablePro>

    <ModalPassword ref="modalPassword" />
    <ModalEmail ref="modalEmail" />
    <ModalPhone ref="modalPhone" />
    <ModalWarn ref="modalWarn" />
    <ModalUnbind ref="modalUnbind" />
  </ZContainer>
</template>

<style lang="less">
.page-my-security {
  .z-back {
    display: flex;
    align-items: center;

    svg {
      width: 24px;
      height: 24px;
    }

    &-title {
      @media @mobile {
        margin-left: 16px;
      }

      @media @tablet {
        margin-left: 24px;
      }
    }
  }

  .z-container {
    @media @mobile, @tablet {
      width: 100%;
    }
  }

  .z-back-title {
    @media @mobile, @tablet {
      margin-top: 0;
    }
  }

  .security-table {
    svg {
      width: 24px;
      height: 24px;
      fill: @gray-color;
    }

    .name {
      font-size: 16px;
      flex: 0 0 300px;
    }

    .desc {
      color: @gray-color;
    }

    &-flex {
      display: flex;

      @media @mobile {
        display: block;
      }
    }

    &-margin {
      margin-left: 12px;

      @media @mobile {
        margin-left: 0;
      }
    }

    .z-table-row {
      height: inherit;
      line-height: 1.5;
      padding: 18px 24px;

      @media @mobile {
        padding: 18px 16px;
      }

      &:first-child {
        border-top: none;

        @media @mobile, @tablet {
          border-top: 1px solid @base-border-color;
        }
      }
    }
  }

  .z-table-pro {
    &-head {
      @media @mobile {
        padding: 16px;
      }
    }
  }

  .z-table {
    .device {
      flex: 0 0 300px;

      @media @mobile {
        display: none;
      }

      @media @tablet {
        display: none;
      }
    }

    .authenticated_at {
      @media @mobile {
        display: none;
      }
    }

    .state {
      flex: 0 0 100px;

      @media @mobile {
        display: none;
      }
    }

    .action {
      flex: 0 0 100px;

      svg {
        width: 24px;
        height: 24px;
        fill: @text-color;
        cursor: pointer;
      }

      @media @mobile {
        flex: 1;
      }
    }

    .desc {
      @media @mobile {
        display: none;
      }
    }

    &-head {
      @media @mobile {
        padding: 0 16px;
      }
    }

    &-row {
      .device {
        font-size: 16px;
      }

      @media @mobile {
        padding: 0 16px;
      }

      .state {
        color: @up-color;

        .status-dot {
          width: 8px;
          height: 8px;
          margin-right: 4px;
          border-radius: 50%;
          background-color: @up-color;
        }
      }

      .action {
        i {
          cursor: pointer;
          font-size: 20px;
          color: @gray-color;
        }
      }
    }
  }

  .level-bar {
    display: inline-block;
    height: 3px;
    width: 24px;
    background-color: @action-color;
  }
}
</style>
