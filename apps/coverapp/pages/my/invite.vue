<script setup lang="ts">
import { copyToClipboard } from '@zsmartex/utils'
import type { ZTabItem } from '@zsmartex/components/types'

const route = useRoute()
const activeTab = useState(() => 'overview')

const userStore = useUserStore()
const origin = useState(() => '')

onMounted(() => {
  origin.value = location.origin
})

const tabs: ZTabItem[] = [
  {
    key: 'overview',
    slotName: true,
  },
  {
    key: 'links',
    slotName: true,
  },
  {
    key: 'lists',
    slotName: true,
  },
  {
    key: 'commissions',
    slotName: true,
  },
  {
    key: 'cashes',
    slotName: true,
  },
]

const getDefaultLink = computed(() => {
  return `${origin.value}/...${userStore.inviteLink.invite_code}`
})

function copyLink() {
  Message.success({
    message: $t('success'),
  })

  copyToClipboard(`${origin.value}/register?invite_code=${userStore.inviteLink.invite_code}`)
}

function copyCode(code: string) {
  Message.success({
    message: $t('success'),
  })

  copyToClipboard(code)
}

onBeforeMount(async () => {
  const path = route.path.split('/my/invite/')[1]
  if (!path) {
    await navigateTo(`/my/invite/${activeTab.value}`)
    return
  }

  activeTab.value = path
})

function onClick(tabKey: string) {
  navigateTo(`/my/invite/${tabKey}`)
}
</script>

<template>
  <div class="page-my-invite">
    <div class="page-my-invite-bg">
      <ZContainer class="page-my-invite-introduce">
        <div class="page-my-invite-bg-text">
          <div class="text-4xl font-extrabold page-my-invite-bg-text-1">
            {{ $t('page.my.invite.bg.invite_friend_for') }}
          </div>
          <div class="text-4xl font-extrabold mb-6 page-my-invite-bg-text-2">
            {{ $t('page.my.invite.bg.the_choice') }}
          </div>
          <div class="text-sm mb-8 text-[#a2b8da]">
            {{ $t('page.my.invite.bg.enjoy') }}
          </div>
          <div class="flex text-[#a2b8da] page-my-invite-bg-icon">
            <div class="w-[100px] text-center">
              <img class="w-[84px] h-[84px] mb-1 page-my-invite-bg-icon-img" src="../../assets/img/step1.png">
              <div class="break-words text-[13px]">
                {{ $t('page.my.invite.bg.step_1') }}
              </div>
            </div>
            <ZIconAngleRightFilled class="mt-10" />
            <div class="w-[100px] text-center">
              <img class="w-[84px] h-[84px] mb-1 page-my-invite-bg-icon-img" src="../../assets/img/step2.png">
              <div class="break-words text-[13px]">
                {{ $t('page.my.invite.bg.step_2') }}
              </div>
            </div>
            <ZIconAngleRightFilled class="mt-10" />
            <div class="w-[100px] text-center">
              <img class="w-[84px] h-[84px] mb-1 page-my-invite-bg-icon-img" src="../../assets/img/step3.png">
              <div class="break-words text-[13px]">
                {{ $t('page.my.invite.bg.step_3') }}
              </div>
            </div>
          </div>
        </div>
        <div class="page-my-invite-bg-box">
          <div class="mb-5 text-2xl bold-text">
            {{ $t('page.my.invite.bg.invite_friends') }}
          </div>
          <div class="mb-4">
            <span class="!text-[#a2b8da] mb-2 block">{{ $t('page.my.invite.default_link') }}</span>
            <div class="text-base">
              {{ getDefaultLink }}
              <ZIconCopyAltFilled class="cursor-pointer ml-1" @click="copyLink" />
            </div>
          </div>
          <div class="mb-4">
            <span class="!text-[#a2b8da] mb-2 block">{{ $t('page.my.invite.invitation_code') }}</span>
            <div class="text-base">
              {{ userStore.inviteLink.invite_code }}
              <ZIconCopyAltFilled class="cursor-pointer ml-1" @click="copyCode(userStore.inviteLink.invite_code)" />
            </div>
          </div>
          <div class="mt-8 flex">
            <div class="p-4 bg-[#141C2A] flex-1">
              <span class="block mb-3 text-[#a2b8da] text-xs">{{ $t('page.my.invite.commission_rate') }}</span>
              <div class="text-base mb-2">
                {{ userStore.inviteLink.commission_rebate * 100 }}%
              </div>
              <span class="!text-[#ffffff80]">{{ $t('page.my.invite.spot') }}</span>
            </div>
            <div class="p-4 bg-[#141C2A] flex-1 ml-4">
              <span class="block mb-3 text-[#a2b8da] text-xs">{{ $t('page.my.invite.cash_rate') }}</span>
              <div class="text-base mb-2">
                {{ userStore.inviteLink.cash_rebate * 100 }}%
              </div>
              <span class="!text-[#ffffff80]">{{ $t('page.my.invite.spot') }}</span>
            </div>
          </div>
        </div>
      </ZContainer>
    </div>
    <ZCard class="page-my-invite-tab !rounded-none">
      <ZContainer class="page-my-invite-tab-container">
        <ZTab v-model="activeTab" :tabs="tabs" class="page-my-invite-tab" @click="onClick">
          <template v-for="tab in tabs" #[tab.key] :key="tab.key">
            <div class="page-my-invite-tab-link">
              {{ $t(`page.my.invite.${tab.key}`) }}
            </div>
          </template>
        </ZTab>
      </ZContainer>
    </ZCard>
    <ZContainer class="py-4">
      <NuxtPage />
    </ZContainer>
  </div>
</template>

<style lang="less">
.page-my-invite {
  svg {
    width: 24px;
    height: 24px;
    fill: @gray-color;
  }

  .z-container {
    @media @mobile, @tablet {
      width: 100%;
    }
  }

  &-tab {
    @media @mobile {
      // padding: 12px;
    }

    &-link {
      @media @mobile {
        font-size: 14px;
      }
    }
  }

  &-introduce {
    display: flex;

    @media @mobile {
      display: block;
    }

    @media @tablet {
      padding: 0 12px;
    }
  }

  &-tab {
    color: @gray-color;

    &-container {
      @media @mobile {
        height: 34px !important;
        overflow-x: auto;
      }
    }
  }

  &-bg {
    margin: 0;
    padding: 40px 0;
    width: 100%;
    color: white;
    background-color: @exchange-layout-background-color;

    &-text {
      flex: 1;

      @media @mobile {
        padding: 0 16px;
      }

      &-1 {
        @media @mobile {
          width: 300px;
          font-size: 24px !important;
        }
      }

      &-2 {
        display: block;

        @media @mobile {
          display: none;
        }
      }
    }

    &-icon {
      @media @mobile {
        justify-content: space-between;
      }

      &-img {
        @media @mobile {
          width: 60px !important;
          height: 60px !important;
        }
      }

      svg {
        width: 24px;
        height: 24px;
        fill: white;
      }

      .z-icon-arrow {
        padding: 0 24px;

        @media @mobile {
          padding: 0;
        }

        @media @tablet {
          padding: 0 12px;
        }
      }
    }

    &-box {
      flex: 1;
      margin-left: 100px;
      padding: 32px;
      border-radius: 4px;
      background-color: @exchange-card-background;

      @media @mobile {
        margin-top: 24px;
        margin-left: 0;
      }
    }
  }

  &-table {
    &-item {
      &-side {
        padding: 4px 12px;
        line-height: 1;
        border-radius: 4px;

        &.page-my-orders-table-item-side-red {
          background-color: rgba(#ea4d4d, 0.15);
        }

        &.page-my-orders-table-item-side-green {
          background-color: rgba(#16c39342, 0.15);
        }
      }
    }
  }

  .z-table {
    &-row {
      line-height: 1.5;
    }

    &-head {
      padding: 0;
    }

    &-empty {
      min-height: 200px;
    }
  }

  .z-card {
    overflow: visible;
    padding-bottom: 0;

    @media @mobile {
      padding-bottom: 16px;
    }

    &-head {
      display: flex;
      justify-content: space-between;
      margin: 0 -24px;
      padding: 0 24px;
      border-bottom: 1px solid @base-border-color;

      @media @mobile {
        margin: 0;
        padding: 0;
      }
    }

    .z-tab {
      display: flex;
      align-items: center;

      @media @mobile {
        display: block;
        width: max-content;
      }

      &-item {
        font-size: 16px;
      }
    }

    .z-range-picker {
      .z-dropdown-trigger {
        background-color: rgba(@gray-color, 0.15);
      }
    }

    .z-page {
      .z-dropdown-trigger {
        background-color: white;
      }
    }
  }

  .z-pagination {
    .z-dropdown {
      &-trigger {
        display: flex;
        justify-content: center;
        background-color: #fff;
      }
    }
  }

  .z-tab {
    &-ink {
      height: 3px;
    }
  }
}
</style>
