<script setup lang="ts">
import type { ZTableColumn } from '@zsmartex/components/types'
import { APIKeyState, Align } from '@zsmartex/types'
import ModalAPI from '~/layouts/my/api/ModalAPI.vue'
import ModalSaveInfoAPI from '~/layouts/my/api/ModalSaveInfoAPI.vue'
import ModalWarn from '~/layouts/my/ModalWarn.vue'

const userStore = useUserStore()
const type = useState(() => '')
const state = useState<APIKeyState>(() => APIKeyState.Active)
const kid = useState(() => '')
const loading = useState(() => false)
const apiKey = useState<APIKey>(() => ({} as APIKey))

const modalAPI = ref<InstanceType<typeof ModalAPI>>()
const modalWarn = ref<InstanceType<typeof ModalWarn>>()
const modalSaveInfoAPI = ref<InstanceType<typeof ModalSaveInfoAPI>>()

const columns: ZTableColumn[] = [
  {
    title: $t('page.global.table.label'),
    key: 'label',
  },
  {
    title: $t('page.global.table.kid'),
    key: 'kid',
    align: Align.Center,
  },
  {
    title: $t('page.global.table.state'),
    key: 'state',
    scopedSlots: true,
  },
  {
    key: 'action',
    align: Align.Right,
    scopedSlots: true,
  },
]

const { data: apiKeys } = await useAsyncData(() => userStore.GetAPIKeys().then(res => res.data))
for (const apiKey of apiKeys.value!) {
  apiKey.loading = false
}

function ActionCreate() {
  if (userStore.otp) {
    modalAPI.value?.openModal('create', '', '', [])
  } else {
    modalWarn.value?.openModal()
  }
}

function ActionUpdate(item: APIKey) {
  if (userStore.otp) {
    modalAPI.value?.openModal('update', item.label, item.trusted_ips, item.restrictions)
  } else {
    modalWarn.value?.openModal()
  }

  kid.value = item.kid
  state.value = item.state
}
function ActionDelete(item: APIKey) {
  if (userStore.otp) {
    modalAPI.value?.openModal('delete', '', '', [])
  } else {
    modalWarn.value?.openModal()
  }
  kid.value = item.kid
}

async function SubmitAction(typeModal: string, label: string, otpCode: string, trustedIPs: string, restrictions: string[]) {
  loading.value = true
  type.value = typeModal

  if (type.value === 'create-confirm') {
    try {
      apiKey.value = await userStore.CreateAPIKey({
        label,
        otp_code: otpCode,
        trusted_ips: trustedIPs,
        restrictions,
      })
      modalSaveInfoAPI.value?.openModal(apiKey.value)
      apiKeys.value!.push(apiKey.value)
    } catch (error) {
      return
    } finally {
      loading.value = false
    }
  }

  if (type.value === 'update-confirm') {
    let apiKey: APIKey
    try {
      apiKey = await userStore.UpdateAPIKey({
        state: state.value,
        label,
        otp_code: otpCode,
        kid: kid.value,
        trusted_ips: trustedIPs,
        restrictions,
      })
      apiKeys.value![apiKeys.value!.findIndex(api => api.kid === apiKey.kid)] = apiKey
    } catch (error) {
      return
    } finally {
      loading.value = false
    }
  }

  if (type.value === 'delete') {
    const check = await userStore.DeleteAPIKey({
      otp_code: otpCode,
      kid: kid.value,
    })
    if (!check) {
      loading.value = false
      return
    }
    apiKeys.value!.splice(apiKeys.value!.findIndex(api => api.kid === kid.value), 1)
  }
}

async function changeStateAPIKey(item: APIKey) {
  item.loading = true
  await userStore.UpdateStateAPIKey({
    state: item.state === 'active' ? 'disabled' : 'active',
    kid: item.kid,
  }, () => {
    apiKeys.value![apiKeys.value!.findIndex(api => api.kid === item.kid)].state = item.state === APIKeyState.Active ? APIKeyState.Disabled : APIKeyState.Active
  })
  item.loading = false
}
</script>

<template>
  <ZContainer class="my-5 page-my-api-container">
    <ZHeadBack to="/my/dashboard" class="mb-5">
      <ZIconArrowLeftDuotone />
      {{ $t('page.my.api.title') }}
    </ZHeadBack>

    <ZCard class="page-my-api">
      <template #head>
        <ZButton
          class="custom-button"
          color="primary"
          @click="ActionCreate"
        >
          <div class="flex items-center justify-center">
            <ZIconPlusFilled class="mr-2" />
            <span class="">{{ $t('page.global.action.add') }}</span>
          </div>
        </ZButton>
      </template>
      <div class="page-my-api-table p-2">
        <ZTable
          class="text-base"
          :columns="columns"
          :data-source="apiKeys"
          :head-enabled="!!apiKeys!.length"
          responsive
        >
          <template
            #state="{ item }"
          >
            <ZSwitch :model-value="item.state === 'active' ? true : false" size="medium" :loading="item.loading" @click="changeStateAPIKey(item)" />
          </template>
          <template
            #action="{ item }"
          >
            <ZIconNutFilled class="cursor-pointer" @click="ActionUpdate(item)" />
            <ZIconTimesRegular class="cursor-pointer" @click="ActionDelete(item)" />
          </template>
        </ZTable>
      </div>
      <ModalAPI
        ref="modalAPI"
        :loading="loading"
        @submit="SubmitAction"
      />
      <ModalSaveInfoAPI ref="modalSaveInfoAPI" />
      <ModalWarn ref="modalWarn" />
    </ZCard>
  </ZContainer>
</template>

<style lang="less">
.page-my-api {
  .z-back {
    display: flex;
    align-items: center;

    svg {
      width: 24px;
      height: 24px;
    }

    &-title {
      @media @mobile {
        margin-left: 16px;
      }

      @media @tablet {
        margin-left: 24px;
      }
    }
  }

  &-container {
    @media @mobile, @tablet {
      width: 100%;
    }
  }

  .z-back-title {
    @media @mobile, @tablet {
      margin-top: 0;
    }
  }

  .z-card-head {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin: 0 -24px;
    padding: 0 24px 12px 24px;
    border-bottom: 1px solid @base-border-color;
  }

  .custom-button {
    padding: 8px 16px;
    border-radius: 4px;

    svg {
      width: 18px;
      height: 18px;
      fill: @text-color;
    }

    &:hover {
      svg {
        fill: white;
      }
    }
  }

  &-code {
    .z-input-error-container {
      top: 100%;
    }
  }

  &-table {
    min-height: 300px;

    .z-table-row {
      border-top: 1px solid @base-border-color;
      height: 48px;

      .action {
        display: flex;
        align-items: center;
        color: @gray-color;

        i {
          cursor: pointer;
        }

        .z-icon-close {
          margin-left: 8px;
          margin-top: 2px;

          @media @mobile {
            margin-top: 0;
          }
        }
      }

      &:last-child {
        .action {
          .z-icon-close {
            margin-top: 3px;

            @media @mobile {
              margin-top: 0;
            }
          }
        }
      }
    }

    .label {
      max-width: 150px;
    }

    .key {
      max-width: 400px;
    }

    .state {
      max-width: 200px;
    }

    .action {
      height: 100%;
      max-width: 70px;

      svg {
        height: 100%;
      };
    }
  }
}

.z-code-input {
  input {
    background-color: #fff;
    border-radius: 4px;
  }
}
</style>
