<script setup lang="ts">
import { format as formatDate } from 'date-fns'
import type { ZTabItem } from '@zsmartex/components/types'
import type { Notify, P2PTrade } from '@zsmartex/types'
import AcceptModal from '~/layouts/p2p/AcceptModal.vue'

const runtimeConfig = useRuntimeConfig()
const userStore = useUserStore()
const tradeStore = useTradeStore()

const page = ref(1)
const limit = ref(8)
const loading = ref(false)
const hidden = ref(false)
const activeTab = ref('all')
const staticID = ref('')

const acceptModal = ref<InstanceType<typeof AcceptModal>>()

const { data: p2pRequest, refresh } = await useAsyncData(async () => {
  const { data } = await tradeStore.FetchP2PRequest(staticID.value)

  return data
}, { default: () => ({} as P2PTrade) })

const notifies = computed(() => {
  let notifies = userStore.notifies

  if (activeTab.value !== 'all') {
    notifies = userStore.notifies.filter(n => n.type === activeTab.value)
  }

  if (hidden.value) {
    notifies = userStore.notifies.filter(n => !n.seen)
  }

  const from = (page.value - 1) * limit.value
  const to = from + limit.value
  return notifies.slice(from, to)
})

async function notifyClick(notify: Notify) {
  // if (notify.seen) return

  notify.seen = true
  userStore.SeenNotify(notify.id)

  if (notify.type === 'trade') {
    if (notify.title === 'page.global.notify.p2p.trade_accepted.title') {
      navigateTo(`/p2p/trade/${notify.data.static_id}`)
    }
    if (notify.title === 'page.global.notify.p2p.request_p2p.title') {
      staticID.value = notify.data.static_id
      await refresh()
      if (new Date(p2pRequest.value!.expired_at) < new Date()) return
      acceptModal.value?.openModal()
    }
  }
}

const notifiesLength = computed(() => {
  if (hidden.value) {
    return userStore.notifies.filter(n => !n.seen).length
  }
  return userStore.notifies.length
})

const tabs: ZTabItem[] = [
  {
    key: 'all',
    text: 'All',
    slotName: true,
  },
  {
    key: 'system',
    text: 'System Message',
    slotName: true,
  },
  {
    key: 'trade',
    text: 'Trade Notification',
    slotName: true,
  },
  {
    key: 'activity',
    text: 'Activities',
    slotName: true,
  },
  {
    key: 'news',
    text: `${runtimeConfig.public.exchangeName} News`,
    slotName: true,
  },
]

async function SeenAll() {
  await userStore.SeenAllNotifies()
}

async function DeleteAll() {
  await userStore.DeleteNotifies()
}
</script>

<template>
  <ZContainer class="my-5 page-my-notifications">
    <ZHeadBack to="/my/dashboard" class="mb-5">
      <ZIconArrowLeftDuotone />
      {{ $t('page.notifies.title') }}
    </ZHeadBack>

    <ZCard>
      <div class="page-my-notifications-head justify-between">
        <div class="page-my-notifications-head-tab">
          <ZTab v-model="activeTab" :tabs="tabs">
            <template v-for="tab in tabs" #[tab.key] :key="tab.key">
              {{ tab.text }}
            </template>
          </ZTab>
        </div>
        <div class="flex items-center">
          <div class="flex mr-4">
            <ZCheckbox v-model="hidden" />
            <span @click="hidden = !hidden">{{ $t('page.notifies.hidden') }}</span>
          </div>
          <div class="mr-4 page-my-notifications-head-action" @click="SeenAll">
            {{ $t('page.notifies.seen_all') }}
          </div>
          <div class="page-my-notifications-head-action" @click="DeleteAll">
            {{ $t('page.notifies.clear_all') }}
          </div>
        </div>
      </div>
      <div v-if="notifies.length === 0" class="h-[300px] w-full flex flex-col items-center justify-center">
        <ZIconClipboardTimesDuotone />
        <span class="text-gray">{{ $t('page.global.table.empty') }}</span>
      </div>
      <div v-else class="page-my-notifications-table mb-2">
        <div
          v-for="notify in notifies"
          :key="notify.id"
          class="page-my-notifications-table-row"
          @click="notifyClick(notify)"
        >
          <div
            class="page-my-notifications-table-row-status"
            :class="{ seen: notify.seen }"
          />
          <div class="page-my-notifications-table-row-icon">
            <ZIconDesktopDuotone />
          </div>
          <div class="page-my-notifications-table-row-content flex-1">
            <div>
              {{ $t(notify.title) }}
            </div>
            <div class="text-gray page-my-notifications-table-row-desc">
              {{ $t(notify.desc, {
                ...notify.data,
              }) }}
            </div>
          </div>
          <div class="page-my-notifications-table-row-date">
            {{ formatDate(new Date(notify.created_at), "dd-MM HH:mm") }}
          </div>
        </div>
      </div>
      <ZPagination v-model="page" v-model:page-size="limit" :is-loading="loading" :allow-change-size="false" :total="notifiesLength" />
      <AcceptModal ref="acceptModal" />
    </ZCard>
  </ZContainer>
</template>

<style lang="less">
.page-my-notifications {
  svg {
    width: 120px;
    height: 120px;

    .cls-1 {
      fill: @base-border-color;
    }

    .cls-2 {
      fill: @gray-color;
    }
  }

  .z-tab {
    height: 40px;

    &-item {
      display: flex;
      align-items: center;
      font-size: 16px;
    }
  }

  @media @mobile, @tablet {
    width: 100%;
  }

  .z-back-title {
    @media @mobile, @tablet {
      margin-top: 0;
    }
  }

  .z-back {
    display: flex;
    align-items: center;

    svg {
      width: 24px;
      height: 24px;
    }
  }

  .z-checkbox {
    svg {
      width: 14px;
      height: 14px;

      .cls-1 {
        fill: white;
      }
    }
  }

  &-head {
    display: flex;
    height: 52px;
    padding-bottom: 12px;

    @media @mobile, @tablet {
      display: block;
      height: auto;
    }

    &-tab {
      @media @mobile, @tablet {
        margin-bottom: 12px;
        overflow-x: auto;
      }

      .z-tab {
        @media @mobile, @tablet {
          width: max-content;
        }
      }
    }

    &-action {
      color: @primary-color;
      cursor: pointer;
    }
  }

  &-table {
    @media @mobile, @tablet {
      overflow-x: auto;
    }

    & > div {
      @media @mobile, @tablet {
        width: 1288px;
      }
    }

    &-row {
      display: flex;
      align-items: flex-start;
      padding: 16px 20px;
      color: @text-color;
      cursor: pointer;
      border-bottom: 1px solid @base-border-color;

      @media @mobile, @tablet {
        padding: 16px 0;
      }

      &-icon {
        margin-top: 2px;
        margin-right: 16px;

        svg {
          width: 24px;
          height: 24px;

          .cls-1, .cls-2 {
            fill: @gray-color;
          }
        }
      }

      &-content {
        font-size: 16px;
      }

      &-status {
        margin-top: 7px;
        margin-right: 16px;
        width: 8px;
        height: 8px;
        background-color: @up-color;
        border-radius: 50%;

        &.seen {
          background-color: @gray-color
        }
      }

      &-desc {
        width: 100%;
        display: inline-block;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      &:hover {
        background-color: rgba(0, 0, 0, 0.05) !important;
      }
    }
  }
}
</style>
