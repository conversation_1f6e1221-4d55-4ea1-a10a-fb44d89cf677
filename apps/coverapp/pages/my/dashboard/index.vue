<script setup lang="ts">
import type { Activity } from '@zsmartex/types'
import { ActivityResult, ActivityTopic } from '@zsmartex/types'
import DashboardCard from '~/layouts/my/dashboard/Card.vue'
import DashboardProfile from '~/layouts/my/dashboard/Profile.vue'
import DashboardRecentLogins from '~/layouts/my/dashboard/RecentLogins.vue'
import ModalUsername from '~~/layouts/my/dashboard/ModalUsername.vue'

const userStore = useUserStore()
let recentRegister: Activity

const modalUsername = ref<InstanceType<typeof ModalUsername>>()

const { data: recentLogins } = await useAsyncData(() => userStore.FetchActivity(
  ActivityTopic.Session,
  ActivityResult.Succeed,
  5,
).then(res => res.data), {
  default: () => ([] as Activity[]),
})

if (recentLogins.value!.length === 0) {
  const { data } = await useAsyncData(() => userStore.FetchActivity(
    ActivityTopic.Account,
    ActivityResult.Succeed,
    1,
  ).then(res => res.data))

  recentRegister = data.value![0]
}

const lastSession = computed(() => {
  if (recentLogins.value!.length === 0) {
    return recentRegister
  } else {
    return recentLogins.value![recentLogins.value!.length - 1]
  }
})
</script>

<template>
  <ZContainer class="my-5">
    <div class="page-my-dashboard-container">
      <DashboardProfile class="col-span-2" :last-login="lastSession" @click="modalUsername?.openModal()" />
      <DashboardCard :title="$t('page.my.dashboard.security_center')" to="/my/security">
        <div class="note flex items-center text-md bold-text my-[14px]">
          <div>
            {{ $t('page.my.dashboard.account_risk_level') }}:
          </div>
          <div class="flex">
            <template v-for="(_, index) in 3" :key="index">
              <span
                v-if="index + 1 <= userStore.level"
                class="level-bar ml-1" :class="[
                  {
                    'bg-up': userStore.level === 3,
                    'bg-warn': userStore.level === 2,
                    'bg-down': userStore.level === 1,
                  },
                ]"
              />
              <span
                v-else
                class="level-bar ml-1"
              />
            </template>
          </div>
        </div>
      </DashboardCard>
      <DashboardCard
        :title="$t('page.my.dashboard.address_book')"
        to="/my/address-book"
      >
        <p class="note text-md bold-text">
          {{ $t('page.my.dashboard.add_edit_withdrawal') }}
        </p>
      </DashboardCard>
      <DashboardCard :title="$t('page.my.dashboard.trading_fees')" to="/fees">
        <p class="note text-md bold-text">
          {{ $t('page.my.dashboard.manage_trading_fee') }}
        </p>
      </DashboardCard>
      <DashboardCard title="Snapshot" to="/my/snapshots">
        <p class="note text-md bold-text">
          Manage your snapshot
        </p>
      </DashboardCard>
      <DashboardRecentLogins class="col-span-2" :data-source="recentLogins!" />
    </div>

    <ModalUsername ref="modalUsername" />
  </ZContainer>
</template>

<style lang="less" scoped>
.page-my-dashboard {
  .z-container {
    @media @mobile, @tablet {
      width: 100%;
    }
  }

  &-container {
    display: grid;
    grid-template-columns: repeat(2, minmax(0, 1fr));
    gap: 1rem;

    @media @mobile {
      display: block;
    }
  }

  .note {
    color: @gray-color;
  }

  .level-bar {
    display: inline-block;
    height: 3px;
    width: 24px;
    background-color: @action-color;
  }
}
</style>
