<script setup lang="ts">
import type { InviteOverview } from '@zsmartex/types'

const userStore = useUserStore()
const publicStore = usePublicStore()
const { data: overview, pending: pendingOverview } = useAsyncData(() => userStore.FetchInviteOverview().then(res => res.data), {
  lazy: true,
  default: () => ({} as InviteOverview),
})

const { data: overviewYesterday, pending: pendingOverviewYesterday } = useAsyncData(() => userStore.FetchInviteOverviewYesterday().then(res => res.data), {
  lazy: true,
  default: () => ({} as InviteOverview),
})
</script>

<template>
  <ZContainer class="page-my-invite-overview-container">
    <ZCard class="page-my-invite-overview-card-1" :loading="pendingOverview">
      <template #head>
        <div class="pb-4 text-base">
          {{ $t('page.my.invite.all_account') }}
        </div>
      </template>

      <div class="flex py-10">
        <div class="flex-1">
          <span class="text-base text-gray-400 block">{{ $t('page.my.invite.total') }}</span>
          <div class="bold-text">
            <I18n tag="div" path="page.my.invite.total_coin">
              <template #total>
                <span class="text-4xl mr-1">{{ (overview!.total * publicStore.priceCurrency).toFixed(4) }}</span>
              </template>
              <template #coin>
                <span>{{ publicStore.convertCurrency }}</span>
              </template>
            </I18n>
          </div>
        </div>
        <div class="flex-1">
          <span class="text-base text-gray-400 block">{{ $t('page.my.invite.number_invitation') }}</span>
          <div>
            <I18n tag="span" path="page.my.invite.invitee" class="bold-text">
              <template #total>
                <span class="text-4xl mr-1">{{ overview!.total_invited }}</span>
              </template>
            </I18n>
            <span class="!font-normal ml-2 text-base">{{ $t('page.my.invite.total_traded', { total: overview!.total_traded }) }}</span>
          </div>
        </div>
      </div>

      <div class="flex mb-6">
        <div class="w-4/12 bg-gray-100 rounded p-4 mr-2">
          <div class="mb-2 page-my-invite-overview-title">
            {{ $t('page.my.invite.total_commission_rebate') }}
          </div>
          <div class="page-my-invite-overview-text bold-text">
            {{ $t('page.my.invite.total_coin', { total: Number(overview!.total_commission_rebate).toFixed(4), coin: publicStore.convertCurrency }) }}
          </div>
        </div>
        <div class="w-4/12 bg-gray-100 rounded p-4 mx-2">
          <div class="page-my-invite-overview-title page-my-invite-overview-title-bottom">
            {{ $t('page.my.invite.total_cash_rebate') }}
          </div>
          <div class="page-my-invite-overview-text bold-text">
            {{ $t('page.my.invite.total_coin', { total: Number(overview!.total_cash_rebate).toFixed(4), coin: publicStore.convertCurrency }) }}
          </div>
        </div>
        <div class="w-4/12 bg-gray-100 rounded p-4 ml-2">
          <div class="mb-2 page-my-invite-overview-title">
            {{ $t('page.my.invite.total_invitee_rebate') }}
          </div>
          <div class="page-my-invite-overview-text bold-text">
            {{ $t('page.my.invite.total_coin', { total: Number(overview!.total_invitee_rebate).toFixed(4), coin: publicStore.convertCurrency }) }}
          </div>
        </div>
      </div>
    </ZCard>
    <ZCard class="page-my-invite-overview-card-2" :loading="pendingOverviewYesterday">
      <template #head>
        <div class="pb-4 text-base">
          {{ $t('page.my.invite.yesterday_title') }}
        </div>
      </template>

      <div class="pt-8 pb-3 flex items-center justify-between">
        <div class="text-base flex items-center page-my-invite-overview-text-width">
          <span class="inline-block w-1 h-4 mr-2 rounded bg-purple-500" />{{ $t('page.my.invite.yesterday_commission_rebate') }}
        </div>
        <div class="text-lg bold-text">
          {{ $t('page.my.invite.total_coin', { total: overviewYesterday!.total_commission_rebate, coin: publicStore.convertCurrency }) }}
        </div>
      </div>
      <div class="py-3 flex items-center justify-between">
        <div class="text-base flex items-center page-my-invite-overview-text-width">
          <span class="inline-block w-1 h-4 mr-2 rounded bg-pink-500" />{{ $t('page.my.invite.yesterday_cash_rebate') }}
        </div>
        <div class="text-lg bold-text">
          {{ $t('page.my.invite.total_coin', { total: overviewYesterday!.total_cash_rebate, coin: publicStore.convertCurrency }) }}
        </div>
      </div>
      <div class="py-3 flex items-center justify-between">
        <div class="text-base flex items-center page-my-invite-overview-text-width">
          <span class="inline-block w-1 h-4 mr-2 rounded bg-green-500" />{{ $t('page.my.invite.yesterday_invitee_rebate') }}
        </div>
        <div class="text-lg bold-text">
          {{ $t('page.my.invite.total_coin', { total: overviewYesterday!.total_invitee_rebate, coin: publicStore.convertCurrency }) }}
        </div>
      </div>
      <div class="py-3 flex items-center justify-between">
        <div class="text-base flex items-center page-my-invite-overview-text-width">
          <span class="inline-block w-1 h-4 mr-2 rounded bg-blue-500" />{{ $t('page.my.invite.yesterday_invitation') }}
        </div>
        <div class="text-lg bold-text">
          {{ overviewYesterday!.total_invited }}
        </div>
      </div>
    </ZCard>
  </ZContainer>
</template>

<style lang="less">
.page-my-invite-overview {
  &-container {
    display: flex;

    @media @tablet {
      padding: 0 12px;
    }
  }

  &-card {
    &-1 {
      width: calc(100% / 3 * 2);

      @media @mobile {
        width: 100%;
        margin-bottom: 16px;
      }
    }

    &-2 {
      width: calc(100% / 3);
      margin-left: 16px;

      @media @mobile {
        width: 100%;
        margin-left: 0;
      }
    }
  }

  &-title {
    @media @mobile {
      font-size: 13px;
    }

    &-bottom {
      margin-bottom: 8px;

      @media @mobile {
        margin-bottom: 24px;
      }

      @media @tablet {
        margin-bottom: 26px;
      }
    }
  }

  &-text {
    font-size: 18px;

    @media @mobile {
      font-size: 13px;
    }

    &-width {
      @media @tablet {
        width: 70%;
      }
    }
  }

  .z-container {
    @media @mobile {
      display: block;
    }
  }

  .z-card {
    &-overall {
      height: auto;
    }
  }
}
</style>
