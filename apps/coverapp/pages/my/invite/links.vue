<script setup lang="ts">
import { copyToClipboard } from '@zsmartex/utils'
import type { ZTableColumn } from '@zsmartex/components/types'
import type { InviteLink } from '@zsmartex/types'

import { Align } from '@zsmartex/types'
import ModalLink from '~/layouts/my/invite/ModalLink.vue'

const total = ref(0)

const userStore = useUserStore()
const tempNote = useState<Record<string, string>>(() => ({}))
const tempNoteBool = useState<Record<string, boolean>>(() => ({}))

const modalLink = ref<InstanceType<typeof ModalLink>>()

const { query, callbacks } = useQuery()

const columns: ZTableColumn[] = [
  {
    title: $t('page.global.table.label'),
    key: 'label',
    scopedSlots: true,
  },
  {
    title: $t('page.global.table.invite_code'),
    key: 'invite_code',
  },
  {
    title: $t('page.global.table.commission_rebate'),
    key: 'commission_rebate',
    scopedSlots: true,
  },
  {
    title: $t('page.global.table.cash_rebate'),
    key: 'cash_rebate',
    align: Align.Left,
    scopedSlots: true,
  },
  {
    title: $t('page.global.table.friend_invited'),
    key: 'total_invited',
    align: Align.Left,
  },
  {
    title: $t('page.global.table.note'),
    key: 'note',
    align: Align.Left,
    scopedSlots: true,
  },
  {
    title: $t('page.global.table.action'),
    key: 'action',
    align: Align.Right,
    scopedSlots: true,
  },
]

const { data: inviteLinks, pending, refresh } = await useAsyncData<InviteLink[]>(async () => {
  const { headers, data } = await userStore.FetchInviteLinks(query.value)

  if (data.length > 0) {
    data.forEach((i) => {
      tempNote.value[i.id] = i.note
    })
  }

  total.value = Number(headers.total)

  return data
}, { lazy: true, default: () => ([]) })

callbacks.push(refresh)

async function UpdateLabelInviteLink(inviteLink: InviteLink) {
  pending.value = true

  await userStore.UpdateInviteLink(inviteLink, {
    id: inviteLink.id,
    default: true,
  }, () => {
    inviteLinks.value!.forEach(i => i.default = false)
    inviteLink.default = true
  })

  pending.value = false
}

async function UpdateNoteInviteLink(inviteLink: InviteLink) {
  pending.value = true

  await userStore.UpdateInviteLink(inviteLink, {
    id: inviteLink.id,
    note: tempNote.value[inviteLink.id],
  }, () => {
    inviteLink.note = tempNote.value[inviteLink.id]
    tempNoteBool.value[inviteLink.id] = false
  })

  pending.value = false
}

function copy(code: string) {
  Message.success({
    message: $t('success'),
  })

  copyToClipboard(`${location.origin}/register?invite_code=${code}`)
}

function CreateInviteLink(invite: InviteLink) {
  if (invite.default) {
    inviteLinks.value!.forEach(i => i.default = false)
  }
  inviteLinks.value!.push(invite)
}
</script>

<template>
  <div class="page-my-invite-links">
    <ZCard class="mt-4" :loading="pending">
      <template #head>
        <div class="text-lg pb-4">
          {{ $t('page.my.invite.invitation_link') }}
        </div>
        <div class="pb-4">
          <ZButton
            class="py-1 rounded page-my-invite-links-button"
            @click="modalLink?.openModal()"
          >
            <div class="flex items-center justify-center">
              <ZIconPlusFilled class="mr-2" />
              <span class="">{{ $t('page.global.action.generate_link') }}</span>
            </div>
          </ZButton>
        </div>
      </template>

      <ZTable
        v-model:query="query"
        class="page-my-invite-links-table"
        :columns="columns"
        :data-source="inviteLinks"
        :loading="pending"
        responsive
      >
        <template #label="{ item }">
          <div v-if="item.default" class="page-my-invite-links-label">
            {{ $t('page.global.default') }}
          </div>
        </template>
        <template #commission_rebate="{ item }">
          <div class="page-my-invite-links-rebate">
            {{ `${$t('page.my.invite.spot')} ${item.commission_rebate ? item.commission_rebate * 100 : 0}%` }}
          </div>
        </template>
        <template #cash_rebate="{ item }">
          <div class="page-my-invite-links-rebate">
            {{ `${$t('page.my.invite.spot')} ${item.cash_rebate * 100}%` }}
          </div>
        </template>
        <template #note="{ item }">
          <div v-if="!tempNoteBool[item.id]">
            {{ item.note }} <ZIconPapersTextDuotone class="cursor-pointer ml-2 note" @click="tempNoteBool[item.id] = true" />
          </div>
          <ZInput v-else v-model="tempNote[item.id]" class="mr-4 w-full" :max-length="10">
            <template #suffix>
              <span
                class="page-my-invite-links-button"
                @click="UpdateNoteInviteLink(item)"
              >
                {{ $t('page.global.action.save') }}
              </span>
            </template>
          </ZInput>
        </template>
        <template #action="{ item }">
          <div class="flex">
            <div class="mr-4 page-my-invite-links-action" @click="UpdateLabelInviteLink(item)">
              {{ $t('page.my.invite.default_setting') }}
            </div>
            <div class="page-my-invite-links-action" @click="copy(item.invite_code)">
              {{ $t('page.my.invite.copy_link') }}
            </div>
          </div>
        </template>
        <template #foot>
          <ZPagination v-model="query.page" v-model:page-size="query.limit" :is-loading="pending" :total="total" />
        </template>
      </ZTable>
    </ZCard>

    <ModalLink ref="modalLink" @submit="CreateInviteLink" />
  </div>
</template>

<style lang="less">
.page-my-invite-links {
  &-button {
    svg {
      width: 18px;
      height: 18px;
      fill: @text-color;
    }

    &:hover {
      svg {
        fill: white;
      }
    }
  }

  .note {
    svg {
      width: 24px;
      height: 24px;

      .cls-1 {
        fill: @gray-color;
      }
    }
  }

  .z-table {
    &-head {
      height: 42px;
      line-height: 42px;
      font-size: 15px;

      @media @tablet {
        padding: 0 !important;
      }
    }

    &-row {
      height: 42px;
      padding: 0 !important;
    }
  }

  .z-card-head {
    border: none;
  }

  &-button {
    color: @text-color;
    cursor: pointer;

    &:hover {
      text-decoration: underline;
    }
  }

  &-action {
    color: @primary-color;
    cursor: pointer;
  }

  &-label {
    padding: 2px 8px;
    color: @primary-color;
    background-color: rgba(@primary-color, 0.3);
    border-radius: 4px;
  }

  &-rebate {
    padding: 2px 8px;
    color: @gray-color;
    background-color: rgba(@gray-color, 0.15);
    border-radius: 4px;
  }

  .label {
    max-width: 100px;
  }

  .invite_code {
    max-width: 120px;
  }

  .commission_rebate {
    max-width: 200px;
  }

  .cash_rebate {
    max-width: 200px;
  }

  .total_invited {
    max-width: 200px;

    @media @tablet {
      display: none;
    }
  }

  .note {
    max-width: 160px;

    @media @tablet {
      display: none;
    }
  }
}
</style>
