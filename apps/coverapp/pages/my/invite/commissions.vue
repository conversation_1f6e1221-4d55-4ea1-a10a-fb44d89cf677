<script setup lang="ts">
import type { ZTableColumn } from '@zsmartex/components/types'
import { Align, Format, ParseType } from '@zsmartex/types'

const total = ref(0)
const userStore = useUserStore()
const publicStore = usePublicStore()

const { query, callbacks } = useQuery()

const { data: totalCommissionYesterday } = useAsyncData( () => userStore.FetchTotalCommissionYesterday().then(res => res.data), {
  default: () => '0',
})

const columns: ZTableColumn[] = [
  {
    title: $t('page.global.table.email'),
    key: 'email',
  },
  {
    title: $t('page.global.table.uid'),
    key: 'uid',
  },
  {
    title: $t('page.global.table.rate'),
    key: 'rate',
    scopedSlots: true,
  },
  {
    title: $t('page.global.table.total'),
    key: 'total',
    sort: true,
  },
  {
    title: $t('page.global.table.date'),
    key: 'created_at',
    formatBy: Format.DateTimeNoYear,
    parse: ParseType.DateTime,
    align: Align.Right,
    sort: true,
  },
]

const { data: commissions, pending, refresh } = await useAsyncData(async () => {
  const { headers, data } = await userStore.FetchCommissions(query.value)

  total.value = Number(headers.total)

  return data
}, { default: () => ([]) })

callbacks.push(refresh)

const totalCommission = computed(() => {
  return commissions.value!.reduce((t, c) => t + Number(c.total), 0)
})
</script>

<template>
  <div>
    <ZCard class="page-my-invite-commissions mt-4" :loading="pending">
      <template #head>
        <div class="page-my-invite-commissions-box-1 mb-4 flex-1 bg-[#f7f7f7] mr-2 px-4 py-6">
          <div class="text-gray-400">
            {{ $t('page.my.invite.total_commission_rebate') }}
          </div>
          <div class="pt-2 text-xl bold-text">
            {{ `${totalCommission * publicStore.priceCurrency} ${publicStore.convertCurrency}` }}
          </div>
        </div>
        <div class="page-my-invite-commissions-box-2 mb-4 flex-1 bg-[#f7f7f7] ml-2 px-4 py-6">
          <div class="text-gray-400">
            {{ $t('page.my.invite.yesterday_commision_rebate') }}
          </div>
          <div class="pt-2 text-xl bold-text">
            {{ `${Number(totalCommissionYesterday) * publicStore.priceCurrency} ${publicStore.convertCurrency}` }}
          </div>
        </div>
      </template>
      <ZTable
        v-model:query="query"
        class="page-my-invite-commissions-table mt-2"
        :columns="columns"
        :data-source="commissions"
        :loading="pending"
        :allow-sort-data="false"
        responsive
      >
        <template #rate="{ item }">
          <div class="page-my-invite-commissions-rebate">
            {{ `${$t('page.my.invite.spot')} ${item.rate * 100}%` }}
          </div>
        </template>
        <template #foot>
          <ZPagination v-model="query.page" v-model:page-size="query.limit" :is-loading="pending" :total="total" />
        </template>
      </ZTable>
    </ZCard>
  </div>
</template>

<style lang="less">
.page-my-invite-commissions {
  .z-table {
    &-head {
      height: 42px;
      line-height: 42px;
      font-size: 15px;
    }

    &-row {
      height: 42px;
    }
  }

  &-box {
    &-1 {
      margin-right: 8px;

      @media @mobile {
        margin-right: 0;
      }
    }

    &-2 {
      margin-left: 8px;

      @media @mobile {
        margin-left: 0;
      }
    }
  }

  .z-card-head {
    border: none;
  }

  &-rebate {
    padding: 2px 8px;
    color: @gray-color;
    background-color: rgba(@gray-color, 0.15);
    border-radius: 4px;
  }
}
</style>
