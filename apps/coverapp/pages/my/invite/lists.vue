<script setup lang="ts">
import type { ZTableColumn } from '@zsmartex/components/types'
import { Align, Format, ParseType } from '@zsmartex/types'

const total = ref(0)
const userStore = useUserStore()

const { query, callbacks } = useQuery()

const { data: totalInvitedToday } = useAsyncData<number>(() => userStore.FetchTotalInvitedToday().then(res => res.data), {
  lazy: true,
  default: () => 0,
})
const { data: totalInvited } = useAsyncData<number>(() => userStore.FetchTotalInvited().then(res => res.data), {
  lazy: true,
  default: () => 0,
})

const columns: ZTableColumn[] = [
  {
    title: $t('page.global.table.email'),
    key: 'email',
  },
  {
    title: $t('page.global.table.uid'),
    key: 'uid',
  },
  {
    title: $t('page.global.table.total'),
    key: 'total',
    formatBy: Format.Price,
    parse: ParseType.Decimal,
  },
  {
    title: $t('page.global.table.invitation_time'),
    key: 'created_at',
    align: Align.Right,
    formatBy: Format.DateTimeNoYear,
    parse: ParseType.DateTime,
  },
]

const { data: inviteLists, pending, refresh } = await useAsyncData(async () => {
  const { headers, data } = await userStore.FetchInviteLists(query.value)
  total.value = Number(headers.total)

  return data
}, { lazy: true, default: () => ([]) })

callbacks.push(refresh)
</script>

<template>
  <div>
    <ZCard class="page-my-invite-list mt-4" :loading="pending">
      <template #head>
        <div class="page-my-invite-list-box-1 mb-4 flex-1 bg-[#f7f7f7] px-4 py-6">
          <div class="text-gray-400">
            {{ $t('page.my.invite.total_friend_invited') }}
          </div>
          <div class="pt-2 text-xl bold-text">
            {{ totalInvited }}
          </div>
        </div>
        <div class="mb-4 flex-1 bg-[#f7f7f7] px-4 py-6 page-my-invite-list-invited">
          <div class="text-gray-400">
            {{ $t('page.my.invite.total_friend_invited_today') }}
          </div>
          <div class="pt-2 text-xl bold-text">
            {{ totalInvitedToday }}
          </div>
        </div>
      </template>
      <ZTable
        v-model:query="query"
        class="page-my-invite-list-table mt-2"
        :columns="columns"
        :data-source="inviteLists"
        :loading="pending"
        responsive
      >
        <template #foot>
          <ZPagination v-model="query.page" v-model:page-size="query.limit" :is-loading="pending" :total="total" />
        </template>
      </ZTable>
    </ZCard>
  </div>
</template>

<style lang="less">
.page-my-invite-list {
  &-invited {
    margin-left: 8px;

    @media @mobile {
      margin-left: 0;
    }
  }

  &-box {
    &-1 {
      margin-right: 8px;

      @media @mobile {
        margin-right: 0;
      }
    }
  }

  .z-table {
    &-head {
      height: 42px;
      line-height: 42px;
      font-size: 15px;
    }

    &-row {
      height: 42px;
    }
  }

  .z-card-head {
    border: none;
  }
}
</style>
