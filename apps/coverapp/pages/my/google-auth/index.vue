<script setup lang="ts">
import type { Ref } from 'vue'
import { InputType } from '@zsmartex/components/types'
import type Button from '#components/Button.vue'

const delayButtonEmail = ref<InstanceType<typeof Button>>()
const delayButtonPhone = ref<InstanceType<typeof Button>>()

const widthQRcode = 130
const otpCode = useState(() => '')
const emailCode = useState(() => '')
const phoneCode = useState(() => '')
const loading = useState(() => false)

const userStore = useUserStore()

const { data: qrcode } = await useAsyncData(() => userStore.GenerateQRCode().then(res => res.data))

function GenerateEmailCode() {
  userStore.GenerateCodeVerifyOTP('email', delayButtonEmail.value?.StartDelay)
}

function GeneratePhoneCode() {
  userStore.GenerateCodeVerifyOTP('phone', delayButtonPhone.value?.StartDelay)
}

async function Enable2FA() {
  loading.value = true
  await userStore.Enable2FA({
    code: otpCode.value,
    email_code: emailCode.value,
    phone_code: userStore.hasPhone ? phoneCode.value : '',
  }, () => {
    useRouter().push('/my/security')
  })
  loading.value = false
}

const emailCodeError = computed(() => {
  if (emailCode.value.length && emailCode.value.length !== 6) {
    return 'page.global.input.error.email_code'
  }
})

const phoneCodeError = computed(() => {
  if (phoneCode.value.length && phoneCode.value.length !== 6) {
    return 'page.global.input.error.phone_code'
  }
})

const otpCodeError = computed(() => {
  if (otpCode.value.length && otpCode.value.length !== 6) {
    return 'page.global.input.error.otp_code'
  }
})

const buttonDisabled = computed(() => {
  if (emailCode.value.length === 0) return true
  if (userStore.hasPhone && userStore.phone && phoneCode.value.length === 0) return true
  if (otpCode.value.length === 0) return true
  if (emailCodeError.value) return true
  if (phoneCodeError.value) return true
  if (otpCodeError.value) return true

  return false
})

function EncryptEmail(email: string) {
  const split = email.split('@')
  const letter1 = split[0].substring(0, 1)
  const letter2 = split[0].substring(split[0].length - 1, split[0].length)
  let newFirst = letter1
  for (let i = 0; i < split[0].length - 2; i++) {
    newFirst += '*'
  }
  newFirst += letter2

  const letter3 = split[1].substring(0, 1)
  let extension = letter3
  for (let i = 0; i < split[1].split('.')[0].length - 1; i++) {
    extension += '*'
  }
  extension = extension.slice(0, 5)
  extension += `.${split[1].split('.')[1]}`
  const result = `${newFirst.slice(0, 5)}@${extension}`

  return result
}
</script>

<template>
  <ZContainer class="my-5 !w-[800px]">
    <ZHeadBack to="/my/security" class="mb-5">
      <ZIconArrowLeftDuotone />
      {{ $t('page.my.google_auth.title') }}
    </ZHeadBack>
    <ZCard :title="$t('page.my.google_auth.enable_2fa')" head-class="justify-center border-b-1 pb-2 mb-6">
      <div class="page-my-google-auth-step">
        <div class="page-my-google-auth-step-head">
          <span class="page-my-google-auth-step-head-number text-color">{{ $t('page.my.google_auth.step') }} <strong>1</strong>.</span>
          <span class="page-my-google-auth-step-head-title">{{ $t('page.my.google_auth.install_google_auth') }}</span>
        </div>
        <div class="page-my-google-auth-step-content">
          <div class="flex">
            <div class="page-my-google-auth-icon" />
            <div class="page-my-google-auth-device flex flex-col justify-center items-start">
              <p class="pb-4 !m-0">
                <i class="z-icon-app-store pr-2" />
                {{ $t('page.my.google_auth.ios') }}
                <a href="https://itunes.apple.com/cn/app/google-authenticator/id388497605" target="_blank">{{ $t('page.my.google_auth.download') }}</a> | <a href="#">{{ $t('page.my.google_auth.user_guide') }}</a>
              </p>
              <p class="!m-0">
                <i class="z-icon-app-store pr-2" />
                {{ $t('page.my.google_auth.android') }}
                <a href="https://play.google.com/store/apps/details?id=com.google.android.apps.authenticator2" target="_blank">{{ $t('page.my.google_auth.download') }}</a> | <a href="#">{{ $t('page.my.google_auth.user_guide') }}</a>
              </p>
            </div>
          </div>
        </div>
      </div>
      <div class="page-my-google-auth-step">
        <div class="page-my-google-auth-step-head">
          <span class="page-my-google-auth-step-head-number text-color">{{ $t('page.my.google_auth.step') }} <strong>2</strong>.</span>
          <span class="page-my-google-auth-step-head-title">{{ $t('page.my.google_auth.scan_code') }}</span>
        </div>
        <div class="page-my-google-auth-step-content page-my-google-auth-step-content-flex">
          <ZQRCode :model-value="qrcode!.url" :width="widthQRcode" :height="widthQRcode" />
        </div>
      </div>
      <div class="page-my-google-auth-step">
        <div class="page-my-google-auth-step-head">
          <span class="page-my-google-auth-step-head-number text-color">{{ $t('page.my.google_auth.step') }} <strong>3</strong>.</span>
          <span class="page-my-google-auth-step-head-title">{{ $t('page.my.google_auth.secret_key') }}:</span>
        </div>
        <div class="page-my-google-auth-step-content">
          <div class="mb-6 text-color page-my-google-auth-step-content-flex">
            <span class="px-4 py-2 bg-gray-100 rounded font-bold page-my-google-auth-step-content-url">
              {{ qrcode!.url.split("&secret=")[1] }}
            </span>
          </div>
          <div class="text-sm">
            <I18n tag="div" path="page.my.google_auth.notification_date_2fa">
              <template #date>
                <span class="text-red-600">{{ $t('page.my.google_auth.business_day') }}</span>
              </template>
            </I18n>
          </div>
        </div>
      </div>
      <div v-if="userStore.hasEmail" class="page-my-google-auth-step step-custom-1">
        <div class="page-my-google-auth-step-head">
          <span class="page-my-google-auth-step-head-number text-color">{{ $t('page.my.google_auth.step') }} <strong>4</strong>.</span>
          <I18n tag="div" path="page.my.google_auth.notify_code" class="page-my-google-auth-step-head-title">
            <template #email>
              <strong v-if="userStore.email">{{ EncryptEmail(userStore.email as string) }}</strong>
            </template>
          </I18n>
        </div>
        <div class="page-my-google-auth-step-content px-2 py-4">
          <div class="page-my-google-auth-step-content-input">
            <ZInput
              v-model="emailCode"
              name="email_code"
              :type="InputType.Number"
              :placeholder="$t('page.global.placeholder.e-confirmation_code')"
              :max-length="6"
              class="px-2 h-32 bg-gray-100"
              :required="true"
              :error="emailCodeError"
            >
              <template #suffix>
                <ZButton
                  ref="delayButtonEmail"
                  :delay="{
                    time: 60,
                    content: 'Get [#{time}] again',
                  }"
                  @click="GenerateEmailCode"
                >
                  {{ $t('page.global.action.get_code') }}
                </ZButton>
              </template>
            </ZInput>
          </div>
        </div>
      </div>
      <div v-if="userStore.hasPhone" class="page-my-google-auth-step step-custom-1">
        <div class="page-my-google-auth-step-head">
          <span class="page-my-google-auth-step-head-number text-color">{{ $t('page.my.google_auth.step') }} <strong>5</strong>.</span>
          <I18n tag="div" path="page.my.google_auth.notify_code" class="page-my-google-auth-step-head-title">
            <template #email>
              <strong>{{ userStore.phone?.number }}</strong>
            </template>
          </I18n>
        </div>
        <div class="page-my-google-auth-step-content px-2 py-4">
          <div class="w-98">
            <ZInput
              v-model="phoneCode"
              name="phone_code"
              :type="InputType.Number"
              :placeholder="$t('page.global.placeholder.phone_confirmation_code')"
              :max-length="6"
              class="px-2 h-32 bg-gray-100"
              :required="true"
              :error="phoneCodeError"
            >
              <template #suffix>
                <ZButton
                  ref="delayButtonPhone"
                  :delay="{
                    time: 60,
                    content: 'Get [#{time}] again',
                  }"
                  @click="GeneratePhoneCode"
                >
                  {{ $t('page.global.action.get_code') }}
                </ZButton>
              </template>
            </ZInput>
          </div>
        </div>
      </div>
      <div class="page-my-google-auth-step -mb-4 step-custom-2">
        <div class="page-my-google-auth-step-head">
          <span class="page-my-google-auth-step-head-number text-color">{{ $t('page.my.google_auth.step') }} <strong>{{ userStore.hasPhone ? '6' : '5' }}</strong>.</span>
          <span class="page-my-google-auth-step-head-title">{{ $t('page.my.google_auth.enter_otp_code') }}:</span>
        </div>
        <div class="page-my-google-auth-step-content flex">
          <div class="w-72">
            <ZInput
              v-model="otpCode"
              name="otp_code"
              :type="InputType.Number"
              :placeholder="$t('page.global.placeholder.google_auth_code')"
              :max-length="6"
              class="px-2 h-32 bg-gray-100"
              :required="true"
              :error="otpCodeError"
            />
          </div>
          <div>
            <ZButton
              :disabled="buttonDisabled"
              :class="buttonDisabled ? '!bg-gray-300' : ''"
              :loading="loading"
              @click="Enable2FA"
            >
              {{ $t('page.global.action.enable') }}
            </ZButton>
          </div>
        </div>
      </div>
    </ZCard>
  </ZContainer>
</template>

<style lang="less">
.page-my-google-auth {
  .z-back {
    display: flex;
    align-items: center;

    svg {
      width: 24px;
      height: 24px;
    }
  }

  &-device {
    padding-left: 20px;

    @media @mobile {
      padding-left: 0;
    }
  }

  .z-container {
    @media @mobile {
      width: 100% !important;
    }
  }

  .z-back-title {
    @media @mobile, @tablet {
      margin-top: 0;
    }
  }

  &-step {
    padding: 12px 0 22px 32px;
    font-size: 16px;

    @media @mobile {
      padding: 12px 0 22px 0;
    }

    &-head {
      margin-bottom: 24px;
      &-number {
        padding-right: 12px;
      }
    }

    &-content {
      padding-left: 67px;

      @media @mobile {
        padding-left: 0;
        padding-right: 0;
      }

      &-flex {
        @media @mobile {
          display: flex;
          justify-content: space-between;
        }
      }

      &-input {
        width: 364px;

        @media @mobile {
          width: 100%;
        }
      }

      &-url {
        font-size: 20px;
        line-height: 1.75rem;

        @media @mobile {
          font-size: 14px;
          line-height: 1.25rem;
        }
      }
    }

    .text-color {
      color: #0095ff;
    }

    &.step-custom-1 {
      padding: 32px 0 12px 56px;
      margin: 0 -24px;

      @media @mobile {
        padding: 12px 0 !important;
        margin: 0;
      }
    }

    &.step-custom-2 {
      padding: 12px 0 52px 56px;
      margin: 0 -24px;

      .z-input {
        margin-right: 24px;
      }

      @media @mobile {
        padding: 12px 0 52px 0 !important;
        margin: 0;
      }
    }

    .z-input {
      height: 52px;

      input {
        padding: 0 8px;
      }
    }

    .z-button {
      height: 52px;
      line-height: 52px;
      border-radius: 4px;
      background-color: #0095ff;
      color: white;
    }
  }

  &-icon {
    position: relative;
    width: 94px;
    height: 94px;
    margin-right: 12px;
    background-image: url(https://b.peatio.com/settings/google-authenticator.svg);
    background-size: contain;
    background-repeat: no-repeat;

    &::after {
      position: absolute;
      bottom: -16px;
      left: 50%;
      transform: translateX(-50%);
      color: #c5c2d1;
      content: "Authenticator";
      font-size: 12px;
    }
  }

  canvas {
    width: 130px !important;
    height: 130px !important;
  }
}
</style>
