<script setup lang="ts">
import type { ZTabItem } from '@zsmartex/components/types'
import config from '@zsmartex/config'
import ModalConvert from '~/layouts/my/wallet/ModalConvert.vue'

const runtimeConfig = useRuntimeConfig()
const route = useRoute()
const activeTab = useState('asset_tab', () => 'trading_account')
const currencyID = useState('asset_currency', () => config.quote_list[0])

const modalConvert = ref<InstanceType<typeof ModalConvert>>()

const tabs: ZTabItem[] = [
  {
    key: 'trading_account',
    text: 'Trading Account',
    slotName: true,
  },
  {
    key: 'deposit',
    slotName: true,
  },
  {
    key: 'withdraw',
    slotName: true,
  },
  {
    key: 'history',
    slotName: true,
  },
]

onMounted(async () => {
  const path = route.path.split('/my/wallet/assets/')[1]
  if (!path) {
    await navigateTo(`/my/wallet/assets/${activeTab.value}`)
    return
  }

  activeTab.value = path.split('/')[0]
})

function onClick(tabKey: string) {
  if (tabKey === 'trading_account') navigateTo('/my/wallet/assets/trading_account/spot')
  else navigateTo(`/my/wallet/assets/${tabKey}`)
}

function clickTab(key: string, url: string) {
  activeTab.value = key
  navigateTo(url)
}
</script>

<template>
  <div class="page-my-wallet-assets">
    <div class="page-my-wallet-assets-tab  h-[48px]">
      <ZContainer class="flex justify-between h-full">
        <ZTab v-model="activeTab" :tabs="tabs" @click="onClick">
          <template v-for="tab in tabs" :key="tab.key" #[tab.key]>
            {{ tab.text }}
          </template>
        </ZTab>
        <div class="page-my-wallet-assets-tab-right flex">
          <div v-if="runtimeConfig.public.p2p" class="page-my-wallet-assets-tab-button mr-8" @click="modalConvert?.openModal()">
            {{ $t('page.my.wallet.assets.transfer') }}
          </div>
          <div class="page-my-wallet-assets-tab-button mr-8" :class="{ 'page-my-wallet-assets-tab-button-selected': activeTab === 'deposit' }" @click="clickTab('deposit', `/my/wallet/assets/deposit/${currencyID}`)">
            {{ $t('page.my.wallet.assets.deposit') }}
          </div>
          <div class="page-my-wallet-assets-tab-button mr-8" :class="{ 'page-my-wallet-assets-tab-button-selected': activeTab === 'withdraw' }" @click="clickTab('withdraw', `/my/wallet/assets/withdraw/${currencyID}`)">
            {{ $t('page.my.wallet.assets.withdraw') }}
          </div>
          <div class="page-my-wallet-assets-tab-button" :class="{ 'page-my-wallet-assets-tab-button-selected': activeTab === 'history' }" @click="clickTab('history', '/my/wallet/assets/history/deposits')">
            {{ $t('page.my.wallet.assets.history') }}
          </div>
        </div>
      </ZContainer>
    </div>
    <ZContainer class="py-4">
      <NuxtPage />
      <ModalConvert ref="modalConvert" />
    </ZContainer>
  </div>
</template>

<style lang="less">
.page-my-wallet-assets {
  font-size: 14px;

  &-tab {
    display: flex;
    justify-content: space-between;
    background-color: white;

    &-right {
      padding-right: 16px;

      @media @tablet {
        padding-right: 24px;
      }
    }

    @media @mobile {
      overflow-x: auto;
    }

    .z-tab {
      height: 100%;

      @media @mobile {
        padding-left: 16px;
      }

      @media @tablet {
        padding-left: 24px;
      }

      &-item {
        line-height: 48px;
        color: @gray-color;

        &-active {
          color: @primary-color;
        }
      }
    }

    &-button {
      line-height: 48px;
      color: @gray-color;
      cursor: pointer;

      &-selected {
        color: @primary-color;
        border-bottom: 3px solid @primary-color;
      }
    }
  }

  .z-container {
    @media @mobile, @tablet {
      width: 100%;
    }
  }

  &-date {
    @media @mobile {
      display: none;
    }

    @media @tablet {
      padding: 0 24px;
    }
  }

  &-charts {
    display: flex;

    @media @mobile {
      display: none;
    }

    @media @tablet {
      padding: 0 24px;
    }
  }

  &-button-day {
    padding: 6px 12px;
    background-color: white;
    border: 1px solid @base-border-color;
    border-radius: 4px;
    cursor: pointer;

    &-first {
      @media @mobile {
        margin-left: 12px;
      }
    }

    &.button-selected {
      border: 1px solid @primary-color;
      color: @primary-color;
    }
  }

  .z-range-picker {
    @media @mobile {
      width: 100%;
      margin-top: 8px;
    }
  }

  .button-selected {
    .z-dropdown-trigger {
      border: 1px solid @primary-color;
      color: @primary-color;
    }
  }

  .title {
    position: relative;

    &::after {
      content: "";
      position: absolute;
      left: 0;
      bottom: -10px;
      height: 3px;
      width: 35px;
      background-color: @text-color;
    }
  }

  .z-table {
    &-head {
      > span {
        padding: 0 12px;

        @media @mobile {
          padding: 0;
        }
      }
    }

    &-row {
      height: 50px;

      > span {
        display: flex;
        padding: 0 12px;

        @media @mobile {
          padding: 0;
        }
      }
    }

    .currency {
      flex: 140 1 0%;

      img {
        position: relative;
        height: 24px;
        margin-right: 12px;
        border-radius: 50%;
      }

      &-code {
        font-weight: 500;
        font-size: 16px;
        line-height: 1.5;
      }

      &-name {
        font-size: 12px;
        color: @gray-color;
        line-height: 1.5;
      }
    }

    .drawer {
      flex: 80 1 0%;
    }

    .total,
    .available,
    .locked {
      flex: 120 1 0%;
    }

    .btc_value {
      flex: 140 1 0%;

      @media @tablet {
        display: none;
      }
    }

    .action {
      flex: 350 1 0%;
    }
  }
}
</style>
