<script setup lang="ts">
import { add, addDays, getUnixTime, isBefore, isSameDay, parse, parseISO } from 'date-fns'
import type { AssetStatistic, PNLDaily } from '@zsmartex/types'
import AssetsDistribution from '~/layouts/my/wallet/assets/Distribution.vue'
import ComulativeChart from '~/layouts/my/wallet/assets/comulative/chart.vue'
import DailyChart from '~/layouts/my/wallet/assets/daily/chart.vue'
import AllocationChart from '~/layouts/my/wallet/assets/allocation/chart.vue'
import NetChart from '~/layouts/my/wallet/assets/net/chart.vue'

const assetStore = useAssetsStore()
const date = useState(() => 'week')
const timeFrom = useState(() => getUnixTime(
  new Date(new Date().setDate(new Date().getDate() - 8)),
).toString())
const timeTo = useState(() => getUnixTime(new Date()).toString())
const dateRange = ref<(Date | null)[]>([null, null])
const showOption = useState(() => false)
const hidden = useState(() => false)

const {
  data: assetStatistics,
  pending: pendingAssetStatistics,
  refresh: refreshAssetStatistics,
} = await useAsyncData<AssetStatistic[]>(async () => {
  const params: Record<string, any> = {}

  if (Number(timeFrom.value) && Number(timeTo.value)) {
    params.time_from = timeFrom.value
    params.time_to = timeTo.value
  }

  const { data } = await assetStore.FetchAssetStatistics(params)

  return data.map((item) => {
    return {
      day: item.day,
      total: Number(item.total),
    }
  })
}, { default: () => ([]) })

const {
  data: pnlDaily,
  pending: pendingPNLDaily,
  refresh: refreshPNLDaily,
} = await useAsyncData<PNLDaily[]>(async () => {
  const params: Record<string, any> = {}

  if (Number(timeFrom.value) && Number(timeTo.value)) {
    params.time_from = timeFrom.value
    params.time_to = timeTo.value
  }

  const { data } = await assetStore.FetchPNLDaily(params)

  return data.map((item) => {
    return {
      day: item.day,
      value: Number(item.value),
    }
  })
},
{ default: () => ([]) })

const pnlComulative = computed(() => {
  let startTotalAssets: AssetStatistic | undefined
  for (const item of assetStatistics.value!) {
    if (Number(item.total) > 0) {
      startTotalAssets = item
      break
    }
  }

  if (!startTotalAssets) return []

  let totalPnl = 0
  return pnlDaily.value!.filter((r) => {
    return !isBefore(
      parseISO(r.day),
      parseISO(startTotalAssets!.day),
    )
  }).map((r, index) => {
    if (index === 0) {
      return {
        day: r.day,
        change: 0,
      }
    }

    totalPnl += Number(r.value)
    const change = Number(Number(totalPnl / startTotalAssets!.total * 100).toFixed(2))

    return {
      day: r.day,
      change,
    }
  })
})

const pnlYesterday = computed(() => {
  const yesterday = add(new Date(), {
    hours: -24,
    minutes: new Date().getTimezoneOffset(),
  })
  const pnl = pnlDaily.value!.find(pnl => isSameDay(parse(pnl.day, 'yyyy-MM-dd', new Date()), yesterday))
  if (pnl) return Number(pnl.value.toFixed(2))
  return 0
})

const pnlYesterdayChange = computed(() => {
  const yesterday = add(new Date(), {
    hours: -24,
    minutes: new Date().getTimezoneOffset(),
  })
  const assetTwoDayAgo = assetStatistics.value!.find(asset => isSameDay(new Date(asset.day), addDays(yesterday, -1)))
  const pnlYesterday = pnlDaily.value!.find(pnl => isSameDay(new Date(pnl.day), yesterday))
  if (assetTwoDayAgo && pnlYesterday) return Number(pnlYesterday.value) / Number(assetTwoDayAgo.total) * 100
  return 0
})

async function click7days() {
  date.value = 'week'
  timeFrom.value = getUnixTime(
    new Date(new Date().setDate(new Date().getDate() - 8)),
  ).toString()
  timeTo.value = getUnixTime(new Date()).toString()
  showOption.value = false
  Promise.all([refreshAssetStatistics(), refreshPNLDaily()])
}

async function click30days() {
  date.value = 'month'
  timeFrom.value = getUnixTime(
    new Date(new Date().setDate(new Date().getDate() - 31)),
  ).toString()
  timeTo.value = getUnixTime(new Date()).toString()
  showOption.value = false
  Promise.all([refreshAssetStatistics(), refreshPNLDaily()])
}

watch(dateRange.value, () => {
  if (dateRange.value[0] && dateRange.value[1] && dateRange.value[0] <= dateRange.value[1]) {
    date.value = 'customize'
    timeFrom.value = getUnixTime(dateRange.value[0]).toString()
    timeTo.value = getUnixTime(dateRange.value[1]).toString()
    Promise.all([refreshAssetStatistics(), refreshPNLDaily()])
    showOption.value = false
  }
})
</script>

<template>
  <div>
    <AssetsDistribution v-model="hidden" :pnl-yesterday="pnlYesterday" :change-yesterday="pnlYesterdayChange" />
    <div class="mt-4 flex flex-wrap page-my-wallet-assets-date">
      <button
        class="page-my-wallet-assets-button-day page-my-wallet-assets-button-day-first"
        :class="[{ 'button-selected': date === 'week' }]"
        @click="click7days"
      >
        {{ $t('page.my.wallet.assets.allocation.week') }}
      </button>
      <button
        class="page-my-wallet-assets-button-day ml-3"
        :class="{ 'button-selected': date === 'month' }"
        @click="click30days"
      >
        {{ $t('page.my.wallet.assets.allocation.month') }}
      </button>
      <button
        v-if="!showOption"
        class="page-my-wallet-assets-button-day ml-3"
        :class="{ 'button-selected': date === 'customize' }"
        @click="showOption = true"
      >
        {{ $t('page.my.wallet.assets.allocation.customize') }}
      </button>
      <ZRangePicker
        v-else
        v-model="dateRange"
        class="ml-3"
        :placement="Placement.BottomLeft"
      />
    </div>
    <div class="my-4 page-my-wallet-assets-charts">
      <div class="w-7/12 pr-2">
        <ZCard class="min-h-[268px]">
          <ComulativeChart :data="pnlComulative" :loading="pendingAssetStatistics" />
        </ZCard>
        <ZCard class="mt-4 min-h-[268px]">
          <DailyChart :data="pnlDaily!" :loading="pendingPNLDaily" />
        </ZCard>
      </div>
      <div class="w-5/12 pl-2">
        <ZCard class="min-h-[268px]">
          <AllocationChart />
        </ZCard>
        <ZCard class="mt-4 min-h-[268px]">
          <NetChart :data="assetStatistics!" :loading="pendingPNLDaily" :hidden-number="hidden" />
        </ZCard>
      </div>
    </div>
  </div>
</template>
