<script setup lang="ts">
import { format as formatDate } from 'date-fns'
import { clearSelectElement, copyToClipboard, parseTemplate, selectElement } from '@zsmartex/utils'
import type { ZTableColumn } from '@zsmartex/components/types'
import type { Currency, Deposit } from '@zsmartex/types'
import { Align, Format, ParseType } from '@zsmartex/types'
import CoinSearch from '~/layouts/my/wallet/CoinSearch.vue'
import { memoList } from '~/constants/index'
import ModalWarn from '~/layouts/my/ModalWarn.vue'
import { roundNumber } from '@zsmartex/utils'

const route = useRoute()
const publicStore = usePublicStore()
const userStore = useUserStore()
const assetsStore = useAssetsStore()
const DepositAddressElement = templateRef('deposit_address')
const currencyID = useState('asset_currency', () => '')
const visible = useState(() => false)
const { query } = useQuery()
const total = useState(() => 0)
const assetTab = useState('asset_tab', () => '')
const isAcceptParentFee = useState(() => false)

const modalWarn = ref<InstanceType<typeof ModalWarn>>()

const useParentFee = ref(false)

const currencies = computed(() => {
  return publicStore.currencies.filter(c => publicStore.getDepositEnabledNetworks(c.id).length)
})

const currency = computed(() => {
  currencyID.value = route.params.currency_id as string

  return publicStore.currencies.find(currency => currency.id === currencyID.value) as Currency
})

const currencyNetworks = computed(() => {
  return publicStore.getDepositEnabledNetworks(currency.value.id)
})

const selectedNetwork = ref(currencyNetworks.value[0].blockchain_key)

const network = computed(() => {
  return currency.value.networks.find(network => network.blockchain_key === selectedNetwork.value)
})

const parentCurrency = computed(() => {
  if (!network.value) return currency.value.id
  if (!network.value.use_parent_fee || !network.value.parent_id) return currency.value.id
  return network.value.parent_id
})

const needAcceptParentFee = computed(() => {
  if (!network.value) return true
  return network.value.use_parent_fee
})

const getAsset = (currency: string) => {
  return assetsStore.spot_assets.find(asset => asset.currency === currency) as Asset
}

const contractAddress = computed(() => {
  if (!network.value) {
    return ''
  }

  for (const key in network.value.options) {
    if (key.includes('contract_address')) {
      return network.value.options[key]
    }
  }

  return ''
})

const contractAddressExplorer = computed(() => {
  if (!network.value || !contractAddress.value) {
    return ''
  }

  if (network.value.explorer_address) {
    return network.value.explorer_address.replace('#{address}', contractAddress.value)
  }

  return ''
})

const depositAddress = computed(() => {
  if (!network.value || !userStore.otp) {
    return ''
  }

  const depositAddresses = assetsStore.deposit_addresses[network.value.blockchain_key]
  if (!depositAddresses) {
    return ''
  }

  return depositAddresses.find(address => address.currencies.includes(currency.value.id))?.address
})

const onAcceptFee = () => {
  localStorage.setItem(`${currency.value.id}_${selectedNetwork.value}_parent_fee`, true)
  isAcceptParentFee.value = true
}

watch(selectedNetwork, async () => {
  if (!depositAddress.value && userStore.otp) {
    await assetsStore.FetchDepositAddress(currency.value.id, selectedNetwork.value)
  }

  if (localStorage.getItem(`${currency.value.id}_${selectedNetwork.value}_parent_fee`)) isAcceptParentFee.value = true
  else isAcceptParentFee.value = false
})

if (!depositAddress.value && process.client && userStore.otp) {
  await useAsyncData(() => assetsStore.FetchDepositAddress(currency.value.id, selectedNetwork.value))
}

const { pause } = useIntervalFn(async () => {
  if (!userStore.otp) {
    return
  }

  await assetsStore.FetchDepositAddress(currency.value.id, selectedNetwork.value)

  if (depositAddress.value) {
    pause()
  }
}, 1000)

const columns = computed(() => {
  const result: ZTableColumn[] = [
    {
      title: $t('page.global.table.date'),
      key: 'date',
      align: Align.Left,
      formatBy: Format.DateTimeNoYear,
      parse: ParseType.DateTime,
      scopedSlots: true,
    },
    {
      title: $t('page.global.table.currency'),
      key: 'currency',
      align: Align.Left,
      scopedSlots: true,
    },
    {
      title: $t('page.global.table.amount'),
      key: 'amount',
      align: Align.Left,
      formatBy: Format.Price,
      parse: ParseType.Decimal,
      scopedSlots: true,
    },
    {
      title: $t('page.global.table.fee'),
      key: 'fee',
      align: Align.Left,
      scopedSlots: true,
    },
    {
      title: $t('page.global.table.credited'),
      key: 'credited',
      align: Align.Left,
      scopedSlots: true,
    },
    {
      title: $t('page.global.table.txid'),
      key: 'txid',
      align: Align.Left,
      scopedSlots: true,
    },
  ]

  return result
})

const { data: deposits, pending: depositsHistoryLoading } = await useAsyncData(async () => {
  const { headers, data } = await assetsStore.FetchDeposits({
    currency: currency.value.id,
    page: query.value.page,
    limit: query.value.limit,
  })

  total.value = Number(headers.total)

  return data
}, { lazy: true, default: () => <Deposit[]>([]) })

onMounted(() => {
  assetTab.value = 'deposit'

  if (!userStore.otp && !userStore.hasPhone) {
    modalWarn.value?.openModal()
  }

  if (process.client) {
    if (localStorage.getItem(`${currency.value.id}_${selectedNetwork.value}_parent_fee`)) isAcceptParentFee.value = true
  }

  useEvent.on('private:deposit', appendDeposit)
})

onBeforeUnmount(() => {
  useEvent.off('private:deposit', appendDeposit)
})

function appendDeposit(deposit: Deposit) {
  const index = deposits.value.findIndex(d => d.id === deposit.id)
  if (index !== -1) {
    deposits.value[index] = deposit
  } else if (index === -1 && query.value.page === 1 && deposit.currency === currency.value.id) {
    deposits.value.unshift(deposit)
  }
}

function ExplorerAddress(deposit: Deposit, address?: string) {
  const currency = publicStore.currencies.find(c => c.id === deposit.currency)
  if (!currency) return ''
  const network = currency.networks.find(n => n.blockchain_key === deposit.blockchain_key)
  if (network && network.explorer_address) {
    return network.explorer_address.replace('#{address}', address || deposit.address)
  }

  return ''
}

function ExplorerTransaction(deposit: Deposit) {
  const currency = publicStore.currencies.find(c => c.id === deposit.currency)
  if (!currency) return ''
  const network = currency.networks.find(n => n.blockchain_key === deposit.blockchain_key)
  if (network && network.explorer_transaction) {
    return parseTemplate(network.explorer_transaction, deposit)
  }

  return ''
}

function copyDepositAddress() {
  Message.success({
    message: $t('success'),
  })

  copyToClipboard(depositAddress.value as string)
  selectElement(DepositAddressElement.value as HTMLElement)
}

function copyMemo() {
  Message.success({
    message: $t('success'),
  })

  copyToClipboard(depositAddress.value?.split('?memo=')[1] as string)
  // TODO: sửa
  selectElement(DepositAddressElement.value as HTMLElement)
}

function onSearchBoxClicked(currency: Currency) {
  navigateTo(`/my/wallet/assets/deposit/${currency.id}`)
}

watch(selectedNetwork, () => {
  clearSelectElement()
})
</script>

<template>
  <ZLayoutContent>
    <ZContainer>
      <ZCard class="mt-4" content-class="flex flex-col items-center">
        <div class="bold-text text-2xl">
          {{ currency.id.toUpperCase() }}
        </div>

        <CoinSearch v-model="visible" :data-source="currencies" :currency-id="currency.id" @click="onSearchBoxClicked" />

        <ZRow v-if="network" class="bg-gray-100 rounded p-5 mt-5 w-full">
          <ZRow class="w-full border-b border-b-gray-300 pb-6">
            <ZCol class="text-sm">
              {{ $t('page.my.wallet.deposit.select_network') }}
            </ZCol>
            <ZRow>
              <ZButton
                v-for="(net, index) in currencyNetworks"
                :key="net.id" :class="[{ 'ml-2': index !== 0 }]"
                :selected="selectedNetwork === net.blockchain_key"
                @click="selectedNetwork = net.blockchain_key"
              >
                {{ net.protocol }}
              </ZButton>
            </ZRow>
          </ZRow>
          <ZRow v-if="!needAcceptParentFee || (needAcceptParentFee && isAcceptParentFee)" class="w-full my-6">
            <div class="w-full grid grid-cols-5 gap-2 page-my-wallet-assets-deposit-block">
              <ZCol class="col-span-4">
                <div class="text-sm flex items-center">
                  {{ $t('page.my.wallet.deposit.your') }} {{ currency.id.toUpperCase() }} {{ network.parent_id ? `(${network.protocol.toUpperCase()})` : '' }} {{ $t('page.my.wallet.deposit.deposit') }}
                  <ZIconCopyAltFilled class="cursor-pointer ml-1" @click="copyDepositAddress" />
                </div>
                <div ref="deposit_address" class="text-sm text-gray-500 mt-1 break-words">
                  <span v-if="depositAddress && depositAddress.length > 153" class="cursor-pointer" @click="copyDepositAddress">
                    address too long, click to copy
                  </span>
                  <span v-else class="cursor-pointer" @click="copyDepositAddress">
                    {{ depositAddress?.split("?memo=")[0] }}
                  </span>
                </div>
                <div v-if="network && depositAddress?.includes('?memo=') && memoList.includes(network.client)" class="mt-2">
                  <div class="text-sm flex items-center">
                    {{ $t('page.my.wallet.deposit.memo') }}
                    <ZIconCopyAltFilled class="cursor-pointer ml-1" @click="copyMemo" />
                  </div>
                  <div ref="deposit_address" class="text-sm text-gray-500 mt-1 break-words">
                    {{ depositAddress?.split("?memo=")[1] }}
                  </div>
                </div>
                <div class="mt-2 text-sm flex items-center">
                  Deposit Fee: <span class="mx-1 bold-text">{{network?.deposit_fee}}</span> {{ `${parentCurrency.toUpperCase()}` }}
                  <ZTooltip v-if="network && network.use_parent_fee && network.options.deposit_note" :title="network.options.deposit_note" :placement="TooltipPlacement.TopCenter">
                    <div class="w-[14px] h-[14px] mt-[1px] ml-[3px] icon-info">
                      <ZIconInfoFilled class="absolute w-full h-full text-sm cursor-pointer" />
                    </div>
                  </ZTooltip>
                </div>
                <div class="mt-2 text-sm flex items-center">
                  Minimum Deposit Amount: <span class="mx-1 bold-text">{{network?.min_deposit_amount}}</span> {{ `${currency.id.toUpperCase()}` }}
                </div>
              </ZCol>
              <div class="col-span-1 qr-code">
                <ZQRCode v-if="depositAddress?.length" v-model="depositAddress" :width="80" />
              </div>
            </div>
          </ZRow>
          <div v-else class="w-full">
            <div>
              <div class="flex align-center">
                <ZCheckbox v-model="useParentFee" />
                {{ currency.id.toUpperCase() }} deposits require a fee of {{ `${network?.deposit_fee} ${parentCurrency.toUpperCase()}` }}
              </div>
              <div class="mt-2 flex justify-end">
                <ZButton @click="onAcceptFee" :disabled="!useParentFee">
                  Submit
                </ZButton>
              </div>
            </div>
          </div>
        </ZRow>

        <ZRow class="note w-full py-6">
          <p class="text-base mb-2">
            {{ $t('page.global.note') }}
          </p>
          <ul>
            <li>Min Confirmations: <span class="page-my-wallet-assets-deposit-note-text">{{ network?.min_confirmations }}</span></li>
            <li v-if="contractAddress && contractAddressExplorer">Contract Address: <span class="text-black decoration-underline">{{ `${contractAddress.slice(0, 6)}...${contractAddress.slice(-6)}` }}</span></li>
            <li>{{ $t('page.my.wallet.deposit.note-2', { currency: currency.id.toUpperCase() }) }}</li>
            <li>{{ $t('page.my.wallet.deposit.note-3', { currency: currency.id.toUpperCase() }) }}</li>
          </ul>
        </ZRow>
      </ZCard>

      <ZTablePro
        class="page-my-wallet-history-deposit-table my-2"
        :loading="depositsHistoryLoading"
        :columns="columns"
        :data-source="deposits"
        :title="$t('page.my.wallet.deposit.deposit_history')"
        hover
        responsive
      >
        <template #date="{ item }">
          <div class="h-full flex-col justify-end">
            <span v-if="item.credited" class="inline-block date-status-green">
              {{ $t('page.global.status.succeed') }}
            </span>
            <span v-else-if="!item.fee_paid && item.fee_currency !== item.currency" class="inline-block date-status-orange">
              {{ $t('page.global.status.fee_required') }}
            </span>
            <span v-else class="inline-block date-status-orange">
              {{ $t('page.global.status.processing') }}
            </span>
            <div class="mt-[4px]">
              {{ formatDate(new Date(item.created_at), "MM-dd HH:mm:ss") }}
            </div>
          </div>
        </template>
        <template #currency="{ item }">
          <div class="flex justify-center items-center h-full">
            <img :src="currency.icon_url">
            <div>
              <div class="currency-code">
                {{ item.currency.toUpperCase() }}
              </div>
              <div class="currency-name">
                {{ currency.name }}
              </div>
            </div>
          </div>
        </template>
        <template #amount="{ item }">
          <div class="flex justify-center items-center h-full">
            {{ item.amount }}
          </div>
        </template>
        <template #fee="{ item }">
          <div v-if="item.fee_paid" class="flex justify-center items-center h-full">
            {{ `${item.fee} ${item.fee_currency.toUpperCase()}` }}
          </div>
          <ZTooltip v-else :title="`You need ${item.fee} ${item.fee_currency.toUpperCase()} to process deposit`" :placement="TooltipPlacement.TopCenter">
            <div class="flex justify-center items-center h-full underline decoration-dashed decoration-gray-400">
              {{ `${item.fee} ${item.fee_currency.toUpperCase()}` }}
            </div>
          </ZTooltip>
        </template>
        <template #credited="{ item }">
          <div v-if="item.credited" class="flex justify-center items-center h-full text-green-500">
            TRUE
          </div>
          <ZTooltip v-else-if="!item.fee_paid && Number(item.fee) > 0" :title="`You need ${item.amount} ${item.fee_currency.toUpperCase()} to process deposit`" :placement="TooltipPlacement.TopCenter">
            <div class="flex justify-center items-center h-full underline decoration-dashed decoration-amber-400 text-amber-500">
              FALSE
            </div>
          </ZTooltip>
          <ZTooltip v-else-if="Number(network?.min_deposit_amount) > Number(item.amount)" :title="`You need deposit more ${roundNumber(Number(network?.min_deposit_amount) - Number(item.amount), currency.precision)} ${item.currency.toUpperCase()} to process deposit`" :placement="TooltipPlacement.TopCenter">
            <div class="flex justify-center items-center h-full underline decoration-dashed decoration-amber-400 text-amber-500">
              FALSE
            </div>
          </ZTooltip>
          <div v-else class="flex justify-center items-center h-full text-amber-500">
            FALSE
          </div>
        </template>
        <template #txid="{ item }">
          <div
            class="txid-container"
          >
            <div class="txid-container-txid flex items-start gap-[4px]">
              {{ $t('page.my.wallet.history.txid') }} <NuxtLink :to="ExplorerTransaction(item)" class="txid-container-text" target="_blank">
                {{ item.txid }}
              </NuxtLink>
            </div>
            <div class="txid-container-address flex items-start gap-[4px]">
              {{ $t('page.my.wallet.history.address') }} <NuxtLink :to="ExplorerAddress(item, item.from_address)" class="txid-container-text" target="_blank">
                {{ item.from_address }}
              </NuxtLink>
            </div>
          </div>
        </template>
        <template #foot>
          <ZPagination v-model="query.page" v-model:page-size="query.limit" :is-loading="depositsHistoryLoading" :total="total" />
        </template>
      </ZTablePro>
    </ZContainer>
    <ModalWarn ref="modalWarn" @skip="navigateTo('/my/wallet/assets/trading_account/spot')" />
  </ZLayoutContent>
</template>

<style lang="less">
.page-my-wallet-assets-deposit {
  .z-container {
    @media @mobile, @tablet {
      width: 100%;
    }
  }
  .icon-info {
    svg {
      width: 14px;
      height: 14px;
    }

    .cls-1 {
      fill: @gray-color;
    }
  }

  .z-table {
    .z-loading {
      z-index: 1;
    }
  }

  .fee {
    max-width: 140px;
  }

  .credited {
    max-width: 120px;
  }

  &-block {
    svg {
      width: 24px;
      height: 24px;
      fill: @gray-color;
    }

    @media @mobile {
      display: block !important;
    }
  }

  .page-my-wallet-assets-deposit-note-text {
    color: @text-color;
  }

  .z-card-content {
    width: 100%;
    max-width: 520px;
    margin: auto;
  }

  &-table {
    .z-card-content {
      max-width: 100%;
    }

  }

  .z-pagination {
    padding: 16px;
  }

  >:not(.z-header) .z-dropdown {
    width: 100%;

    &-overlay {
      width: 100%;
    }
  }

  .search-box-trigger {
    position: relative;
    width: 100%;
    cursor: pointer;
  }

  .icon {
    width: 20px;
    height: 20px;
    margin-right: 8px;
    border-radius: 50%;
  }

  .z-search-box {
    width: 100%;
  }

  .tooltip {
    position: absolute;
    color: @primary-color;
    background-color: @dropdown-text-color;
    max-width: 360px;
    font-size: 12px;

    &-arrow {
      position: absolute;
      width: 5px;
      height: 5px;
      background-color: @dropdown-text-color;
      top: -3px;
      left: 20px;
      transform: rotate(45deg);
    }
  }

  .qr-code {
    width: 90px;
    height: 90px;

    @media @mobile {
      margin: auto;
      margin-top: 64px;
    }

    canvas {
      width: 90px !important;
      height: 90px !important;
    }
  }

  .note {
    ul {
      color: @gray-color;

      // li {
      //   &::before {
      //     margin-right: 4px;
      //     content: "-";
      //   }
      // }
    }
  }

  .z-table-pro {
    &-head {
      @media @mobile {
        padding: 16px;
      }
    }

    .z-table {
      &-head {
        @media @mobile {
          padding: 0 16px;
        }
      }

      &-row {
        height: auto;
        line-height: 1.5;

        @media @mobile {
          padding: 0 16px;
        }

        &-col {
          align-items: flex-start;

          &-content {
            align-items: flex-start;
          }
        }
      }
    }
  }

  .date {
    max-width: 140px;
    display: flex;
    align-items: center !important;

    &-status {
      max-width: 140px;
      &-green {
        text-transform: capitalize;
        margin-top: 5px;
        padding: 4px 12px;
        line-height: 1;
        border-radius: 4px;
        color: @up-color;
        background-color: rgba(@up-color, 0.15);
      }

      &-orange {
        text-transform: capitalize;
        margin-top: 5px;
        padding: 4px 12px;
        line-height: 1;
        border-radius: 4px;
        color: @warn-color;
        background-color: rgba(@warn-color, 0.15);
      }

      &-red {
        text-transform: capitalize;
        margin-top: 5px;
        padding: 4px 12px;
        line-height: 1;
        border-radius: 4px;
        color: @error-color;
        background-color: rgba(@error-color, 0.15);
      }

      &-gray {
        text-transform: capitalize;
        margin-top: 5px;
        padding: 4px 12px;
        line-height: 1;
        border-radius: 4px;
        color: @gray-color;
        background-color: rgba(@gray-color, 0.15);
      }
    }
  }

  .currency {
    max-width: 145px;

    @media @mobile {
      display: none !important;
    }

    img {
      position: relative;
      height: 24px;
      margin-right: 12px;
      border-radius: 50%;
    }

    &-code {
      margin-top: 6px;
      line-height: 1;
      font-weight: 500;
      font-size: 16px;
    }

    &-name {
      font-size: 12px;
      color: @gray-color;
      line-height: 1.5;
    }
  }

  .amount {
    max-width: 150px;
  }

  .txid {
    &-container {
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;

      &-text {
        display: inline-block;
        color: @primary-color;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        max-width: 460px;

        &:hover {
          text-decoration: underline;
        }
      }
    }
  }
}
</style>
