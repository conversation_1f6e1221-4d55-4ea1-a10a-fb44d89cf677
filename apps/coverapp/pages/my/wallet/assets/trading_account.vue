<script setup lang="ts">
import type { ZTabItem } from '@zsmartex/components/types'
import ModalConvert from '~/layouts/my/wallet/ModalConvert.vue'

const route = useRoute()
const assetTab = useState('asset_tab', () => '')
const activeTab = useState(() => 'spot')
const config = useRuntimeConfig()

const modalConvert = ref<InstanceType<typeof ModalConvert>>()

const tabs: ZTabItem[] = [
  {
    key: 'spot',
    text: 'Spot Account',
    slotName: true,
  },
]

if (config.public.p2p) {
  tabs.push({
    key: 'p2p',
    text: 'P2P Account',
    slotName: true,
  })
}

onMounted(() => {
  assetTab.value = 'trading_account'
})

onBeforeMount(async () => {
  const path = route.path.split('/my/wallet/assets/trading_account/')[1]
  if (!path) {
    await navigateTo(`/my/wallet/assets/trading_account/${activeTab.value}`)
    return
  }

  activeTab.value = path
})

function onClick(tabKey: string) {
  navigateTo(`/my/wallet/assets/trading_account/${tabKey}`)
}
</script>

<template>
  <div class="page-my-wallet-assets-trading-account">
    <div class="text-2xl py-4 page-my-wallet-assets-trading-account-title">
      {{ $t('page.my.wallet.assets.trading_account.title') }}
    </div>
    <div class="page-my-wallet-assets-trading-account-tab  h-[48px]">
      <ZContainer class="flex justify-between h-full">
        <ZTab v-model="activeTab" :tabs="tabs" @click="onClick">
          <template v-for="tab in tabs" #[tab.key] :key="tab.key">
            {{ tab.text }}
          </template>
        </ZTab>
      </ZContainer>
    </div>
    <ZContainer class="py-4">
      <NuxtPage />
      <ModalConvert ref="modalConvert" />
    </ZContainer>
  </div>
</template>

<style lang="less">
.page-my-wallet-assets-trading-account {
  font-size: 14px;

  &-title {
    @media @mobile {
      padding-left: 16px;
    }

    @media @tablet {
      padding-left: 24px;
    }
  }

  &-tab {
    display: flex;
    justify-content: space-between;

    .z-tab {
      height: 100%;

      @media @mobile {
        padding-left: 16px;
      }

      @media @tablet {
        padding-left: 24px;
      }

      &-item {
        line-height: 48px;
        color: @gray-color;

        &-active {
          color: @primary-color;
        }
      }
    }

    &-button {
      line-height: 48px;
      color: @gray-color;
      cursor: pointer;
    }
  }

  .z-container {
    @media @mobile, @tablet {
      width: 100%;
    }
  }

  &-date {
    @media @mobile {
      display: none;
    }

    @media @tablet {
      padding: 0 16px;
    }
  }

  &-charts {
    display: flex;

    @media @mobile {
      display: none;
    }

    @media @tablet {
      padding: 0 16px;
    }
  }

  &-button-day {
    padding: 6px 12px;
    background-color: white;
    border: 1px solid @base-border-color;
    border-radius: 4px;
    cursor: pointer;

    &-first {
      @media @mobile {
        margin-left: 12px;
      }
    }

    &.button-selected {
      border: 1px solid @primary-color;
      color: @primary-color;
    }
  }

  .z-range-picker {
    @media @mobile {
      width: 100%;
      margin-top: 8px;
    }
  }

  .button-selected {
    .z-dropdown-trigger {
      border: 1px solid @primary-color;
      color: @primary-color;
    }
  }

  .title {
    position: relative;

    &::after {
      content: "";
      position: absolute;
      left: 0;
      bottom: -10px;
      height: 3px;
      width: 35px;
      background-color: @text-color;
    }
  }

  .z-table {
    &-head {
      > span {
        padding: 0 12px;

        @media @mobile, @tablet {
          padding: 0;
        }
      }
    }

    &-row {
      height: 50px;

      > span {
        display: flex;
        padding: 0 12px;

        @media @mobile, @tablet {
          padding: 0;
        }
      }
    }

    .currency {
      flex: 140 1 0%;

      img {
        position: relative;
        height: 24px;
        margin-right: 12px;
        border-radius: 50%;
      }

      &-code {
        font-weight: 500;
        font-size: 16px;
        line-height: 1.5;
      }

      &-name {
        font-size: 12px;
        color: @gray-color;
        line-height: 1.5;
      }
    }

    .drawer {
      flex: 80 1 0%;
    }

    .total,
    .available,
    .locked {
      flex: 120 1 0%;
    }

    .btc_value {
      flex: 140 1 0%;

      @media @tablet {
        display: none;
      }
    }

    .action {
      flex: 350 1 0%;
    }
  }
}
</style>
