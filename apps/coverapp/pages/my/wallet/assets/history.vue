<script setup lang="ts">
import type { ZTabItem } from '@zsmartex/components/types'

const route = useRoute()

const activeTab = useState(() => 'deposits')
const assetTab = useState('asset_tab', () => '')

const tabs: ZTabItem[] = [
  {
    key: 'deposits',
    text: $t('page.my.wallet.history.deposits'),
  },
  {
    key: 'withdraws',
    text: $t('page.my.wallet.history.withdraws'),
  },
]

onMounted(async () => {
  assetTab.value = 'history'
  const path = route.path.split('/my/wallet/assets/history/')[1]
  if (!path) {
    await navigateTo('/my/wallet/assets/history/deposits')
  }
  activeTab.value = path
})

function onClick(tabKey: string) {
  navigateTo(`/my/wallet/assets/history/${tabKey}`)
}
</script>

<template>
  <ZContainer class="my-5 page-my-wallet-history">
    <ZCard>
      <template #head>
        <ZTab v-model="activeTab" :tabs="tabs" @click="onClick" />
      </template>
      <NuxtPage />
    </ZCard>
  </ZContainer>
</template>

<style lang="less">
@import "~/assets/styles/layouts/filter.less";

.page-my-wallet-history {
  .z-back {
    display: flex;
    align-items: center;

    svg {
      width: 24px;
      height: 24px;
    }
  }

  @media @mobile, @tablet {
    width: 100%;
  }

  .z-back-title {
    @media @mobile, @tablet {
      margin-top: 0;
    }
  }

  &-table {
    &-item {
      &-side {
        padding: 4px 12px;
        line-height: 1;
        border-radius: 4px;

        &.page-my-orders-table-item-side-red {
          background-color: rgba(#ea4d4d, 0.15);
        }

        &.page-my-orders-table-item-side-green {
          background-color: rgba(#16c39342, 0.15);
        }
      }
    }
  }

  .z-table {
    &-row {
      line-height: 1.5;
    }

    &-head {
      padding: 0;
    }

    &-empty {
      min-height: 200px;
    }
  }

  &-filter {
    .z-filter();
  }

  .z-card {
    overflow: visible;

    &-head {
      display: flex;
      justify-content: space-between;
      margin: 0 -24px;
      padding: 0 24px;
      border-bottom: 1px solid @base-border-color;

      @media @mobile {
        margin: 0;
        padding: 0;
      }
    }

    .z-tab {
      display: flex;
      align-items: center;

      &-item {
        font-size: 16px;
      }
    }
  }

  .z-pagination {
    .z-dropdown {
      &-trigger {
        display: flex;
        justify-content: center;
        background-color: #fff;
      }
    }
  }
}
</style>
