<script setup lang="ts">
import type { ZTableColumn } from '@zsmartex/components/types'
import { Align, SortBy } from '@zsmartex/types'
import { roundNumber } from '@zsmartex/utils'
import ModalConvert from '~/layouts/my/wallet/ModalConvert.vue'

defineProps<{
  hidden?: boolean
}>()

const assetsStore = useAssetsStore()
const publicStore = usePublicStore()
const hiddenEmpty = useState(() => false)
const drawer = ref(false)
const query = useState<Record<string, string>>(() => ({}))
const currencyID = ref('')

const modalConvert = ref<InstanceType<typeof ModalConvert>>()

const columns = computed(() => {
  const result: ZTableColumn[] = [
    {
      title: $t('page.global.table.currency'),
      key: 'currency',
      scopedSlots: true,
      sort: true,
      sortBy: SortBy.String,
    },
    {
      title: $t('page.global.table.total'),
      key: 'total',
      align: Align.Right,
      sort: true,
      sortBy: SortBy.Number,
      scopedSlots: true,
    },
    {
      title: $t('page.global.table.available'),
      key: 'available',
      align: Align.Right,
      sort: true,
      sortBy: SortBy.Number,
      scopedSlots: true,
    },
    {
      title: $t('page.global.table.locked'),
      key: 'locked',
      align: Align.Right,
      sort: true,
      sortBy: SortBy.Number,
      scopedSlots: true,
    },
    {
      title: $t('page.global.table.btc_value'),
      key: 'btc_value',
      align: Align.Right,
      scopedSlots: true,
      sort: true,
      sortBy: SortBy.Number,
    },
    {
      title: $t('page.global.table.action'),
      key: 'action',
      align: Align.Right,
      scopedSlots: true,
    },
    {
      title: $t('page.global.table.action'),
      key: 'drawer',
      align: Align.Right,
      scopedSlots: true,
    },
  ]

  return result
})

const assets = computed(() => {
  const currency = publicStore.currencies.find(c => c.id === 'btc')
  const btcPrice = currency ? Number(currency.price) : 1
  const result = publicStore.currencies.map((currency) => {
    const a = assetsStore.p2p_assets.find(a => a.currency === currency.id)

    if (!a) {
      return {
        currency: currency.id,
        name: currency.name,
        total: 0,
        available: 0,
        locked: 0,
        btc_value: 0,
      }
    }

    const total = Number(a.balance) + Number(a.locked)

    return {
      currency: a.currency,
      name: currency?.name,
      available: Number(a.balance),
      locked: Number(a.locked),
      total,
      btc_value: (Number(currency?.price) * total / btcPrice) || 0,
    }
  })

  if (hiddenEmpty.value) {
    return result.filter(r => r.total > 0)
  }

  return result
})

const balanceBTC = computed(() => {
  const result = assets.value.reduce((a, b) => {
    return a + b.btc_value
  }, 0)

  return `${result} BTC`
})

const balanceCurrency = computed(() => {
  const currency = publicStore.currencies.find(c => c.id === 'btc')
  const btcPrice = currency ? Number(currency.price) : 1

  const btc = assets.value.reduce((a, b) => {
    return a + b.total
  }, 0)

  return `${roundNumber(btc * btcPrice * publicStore.global_price[publicStore.convert_currency], 2)} ${publicStore.convert_currency.toUpperCase()}`
})

// const getCurrency = (currencyID: string) => {
//   return publicStore.currencies.find(c => c.id === currencyID) as Currency
// }

function handleShowConvert(currency?: string) {
  if (currency) {
    currencyID.value = currency
  } else {
    currencyID.value = ''
  }
  drawer.value = false
  modalConvert.value?.openModal("p2p", currencyID.value)
}

function handleShowDrawer(currency: string) {
  drawer.value = true
  currencyID.value = currency
}
</script>

<template>
  <div>
    <ZCard>
      <div class="flex justify-between">
        <div>
          <div class="flex-1">
            <div class="pb-1 flex items-center">
              {{ $t('page.my.wallet.assets.distribution.estimated_balance') }}
            </div>
            <div class="inline-block bold-text text-2xl flex items-end leading-none">
              {{ balanceBTC }}
              <span class="text-xs pl-2"> ~ {{ balanceCurrency }}</span>
            </div>
          </div>
        </div>
        <div class="flex items-start">
          <ZButton class="mr-3" @click="handleShowConvert()">
            {{ $t('page.my.wallet.assets.transfer') }}
          </ZButton>
          <ZButton @click="navigateTo('/my/orders/p2p')">
            {{ $t('page.my.wallet.assets.history') }}
          </ZButton>
        </div>
      </div>
    </ZCard>
    <ZTablePro
      class="mt-5 mb-5"
      :title="$t('page.my.wallet.assets.balances.title_table')"
      :columns="columns"
      :data-source="assets"
      :search-enabled="true"
      :find-by="['currency', 'name']"
      :query="query"
      hover
    >
      <template #head>
        <div class="flex flex-1 justify-end mr-4">
          <div class="flex items-center  select-none">
            <div class="cursor-pointer flex items-center">
              <ZCheckbox v-model="hiddenEmpty" />
              <span @click="hiddenEmpty = !hiddenEmpty">{{ $t('page.global.hide') }}</span>
            </div>
          </div>
        </div>
      </template>
      <template #currency="{ item }">
        <img
          :src="publicStore.getCurrencyByID(item.currency)?.icon_url"
        >
        <div>
          <div class="currency-code">
            {{ item.currency.toUpperCase() }}
          </div>
          <div class="currency-name">
            {{ item.name }}
          </div>
        </div>
      </template>
      <template #total="{ item }">
        {{ hidden ? '****' : item.total === 0 ? '--' : Number(item.total).toFixed(8) }}
      </template>
      <template #available="{ item }">
        {{ hidden ? '****' : item.available === 0 ? '--' : Number(item.available).toFixed(8) }}
      </template>
      <template #locked="{ item }">
        {{ hidden ? '****' : item.locked === 0 ? '--' : Number(item.locked).toFixed(8) }}
      </template>
      <template #btc_value="{ item }">
        {{ hidden ? '****' : item.btc_value === 0 ? '--' : Number(item.btc_value).toFixed(8) }}
      </template>
      <template #action="{ item }">
        <ZButton
          class="mr-4"
          @click="handleShowConvert(item.currency)"
        >
          {{ $t('page.my.wallet.assets.transfer') }}
        </ZButton>
        <ZButton :to="`/p2p?coin_currency=${item.currency}`" is-router-link>
          {{ $t('page.global.action.trade') }}
        </ZButton>
      </template>
      <template #drawer="{ item }">
        <div class="flex items-center bold-text text-[20px]" @click="handleShowDrawer(item.currency)">
          ...
        </div>
      </template>
    </ZTablePro>
    <ZDrawer v-model="drawer" :title="currencyID.toUpperCase()" height="340" :footer="false" :position="Position.Bottom" @close="drawer = false">
      <div class="page-my-wallet-assets-drawer">
        <div
          class="page-my-wallet-assets-drawer-item bold-text"
          @click="handleShowConvert(currencyID)"
        >
          <ZIcon type="person" class="mr-2" />
          {{ $t('page.my.wallet.assets.transfer') }}
        </div>
        <div class="page-my-wallet-assets-drawer-item bold-text" @click="navigateTo(`/p2p?coin_currency=${currencyID}`)">
          <ZIcon type="person" class="mr-2" />
          {{ $t('page.my.wallet.assets.trade') }}
        </div>
      </div>
    </ZDrawer>
    <ModalConvert ref="modalConvert" />
  </div>
</template>

<style lang="less">
.page-my-wallet-assets-p2p {
  .z-table-pro {
    &-head {
      &-slot {
        flex: 1;
        display: flex;
        justify-items: flex-end;
      }
    }
  }

  &-drawer {
    margin-top: 8px;
    padding: 0 16px;

    &-item {
      padding: 16px 0;
    }
  }

  .drawer {
    display: none !important;

    @media @mobile {
      display: flex !important;
      justify-content: flex-end;
    }
  }

  .available {
    @media @mobile {
      display: none !important;
    }
  }

  .locked {
    @media @mobile {
      display: none !important;
    }
  }

  .btc_value {
    @media @mobile {
      display: none !important;
    }
  }

  .action {
    @media @mobile {
      display: none !important;
    }
  }
}
</style>
