<script setup lang="ts">
import type { ZTableColumn } from '@zsmartex/components/types'
import { Align, SortBy } from '@zsmartex/types'

defineProps<{
  hidden?: boolean
}>()

const assetsStore = useAssetsStore()
const publicStore = usePublicStore()
const hiddenEmpty = useState(() => false)
const query = useState<Record<string, string>>(() => ({}))

const columns = computed(() => {
  const result: ZTableColumn[] = [
    {
      title: $t('page.global.table.currency'),
      key: 'currency',
      scopedSlots: true,
      sort: true,
      sortBy: SortBy.String,
    },
    {
      title: $t('page.global.table.total'),
      key: 'total',
      align: Align.Right,
      sort: true,
      sortBy: SortBy.Number,
      scopedSlots: true,
    },
    {
      title: $t('page.global.table.available'),
      key: 'available',
      align: Align.Right,
      sort: true,
      sortBy: SortBy.Number,
      scopedSlots: true,
    },
    {
      title: $t('page.global.table.locked'),
      key: 'locked',
      align: Align.Right,
      sort: true,
      sortBy: SortBy.Number,
      scopedSlots: true,
    },
    {
      title: $t('page.global.table.btc_value'),
      key: 'btc_value',
      align: Align.Right,
      scopedSlots: true,
      sort: true,
      sortBy: SortBy.Number,
    },
    {
      title: $t('page.global.table.action'),
      key: 'action',
      align: Align.Right,
      scopedSlots: true,
    },
  ]

  return result
})

const assets = computed(() => {
  const currency = publicStore.currencies.find(c => c.id === 'btc')
  const btcPrice = currency ? Number(currency.price) : 1
  const result = publicStore.currencies.map((currency) => {
    const a = assetsStore.p2p_assets.find(a => a.currency === currency.id)

    if (!a) {
      return {
        currency: currency.id,
        name: currency.name,
        total: 0,
        available: 0,
        locked: 0,
        btc_value: 0,
      }
    }

    const total = Number(a.balance) + Number(a.locked)

    return {
      currency: a.currency,
      name: currency?.name,
      available: Number(a.balance),
      locked: Number(a.locked),
      total,
      btc_value: (Number(currency?.price) * total / btcPrice) || 0,
    }
  })

  if (hiddenEmpty.value) {
    return result.filter(r => r.total > 0)
  }

  return result
})
</script>

<template>
  <div>
    <ZTablePro
      class="mt-5 mb-5"
      :title="$t('page.my.wallet.assets.balances.title_table')"
      :columns="columns"
      :data-source="assets"
      :search-enabled="true"
      :find-by="['currency', 'name']"
      :query="query"
      hover
    >
      <template #head>
        <div class="flex flex-1 justify-end mr-4">
          <div class="flex items-center  select-none">
            <div class="cursor-pointer flex items-center">
              <ZCheckbox v-model="hiddenEmpty" />
              <span @click="hiddenEmpty = !hiddenEmpty">{{ $t('page.global.hide') }}</span>
            </div>
          </div>
        </div>
      </template>
      <template #currency="{ item }">
        <img
          :src="publicStore.getCurrencyByID(item.currency)?.icon_url"
        >
        <div>
          <div class="currency-code">
            {{ item.currency.toUpperCase() }}
          </div>
          <div class="currency-name">
            {{ item.name }}
          </div>
        </div>
      </template>
      <template #total="{ item }">
        {{ hidden ? '****' : item.total === 0 ? '--' : Number(item.total).toFixed(8) }}
      </template>
      <template #available="{ item }">
        {{ hidden ? '****' : item.available === 0 ? '--' : Number(item.available).toFixed(8) }}
      </template>
      <template #locked="{ item }">
        {{ hidden ? '****' : item.locked === 0 ? '--' : Number(item.locked).toFixed(8) }}
      </template>
      <template #btc_value="{ item }">
        {{ hidden ? '****' : item.btc_value === 0 ? '--' : Number(item.btc_value).toFixed(8) }}
      </template>
      <template #action>
        <ZButton to="/p2p" is-router-link>
          {{ $t('page.global.action.trade') }}
        </ZButton>
      </template>
    </ZTablePro>
  </div>
</template>

<style lang="less">
.page-my-wallet-assets-p2p {
  .z-table-pro {
    &-head {
      &-slot {
        flex: 1;
        display: flex;
        justify-items: flex-end;
      }
    }
  }

  &-drawer {
    margin-top: 8px;
    padding: 0 16px;

    &-item {
      padding: 16px 0;
    }
  }

  .drawer {
    display: none !important;

    @media @mobile {
      display: flex !important;
      justify-content: flex-end;
    }
  }

  .available {
    @media @mobile {
      display: none !important;
    }
  }

  .locked {
    @media @mobile {
      display: none !important;
    }
  }

  .btc_value {
    @media @mobile {
      display: none !important;
    }
  }

  .action {
    @media @mobile {
      display: none !important;
    }
  }
}
</style>
