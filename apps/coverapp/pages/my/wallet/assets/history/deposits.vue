<script setup lang="ts">
import { format as formatDate, fromUnixTime, getUnixTime } from 'date-fns'
import type { ZTableColumn } from '@zsmartex/components/types'
import type { Deposit } from '@zsmartex/types'
import { Align, Format, ParseType } from '@zsmartex/types'
import { parseTemplate, roundNumber } from '@zsmartex/utils'

const total = useState(() => 0)

const publicStore = usePublicStore()
const assetsStore = useAssetsStore()

const dateRange = ref<(Date | null)[]>([null, null])

const { query, callbacks } = useQuery()

const columns = computed(() => {
  const result: ZTableColumn[] = [
    {
      title: $t('page.global.table.date'),
      key: 'date',
      align: Align.Left,
      formatBy: Format.DateTimeNoYear,
      parse: ParseType.DateTime,
      scopedSlots: true,
    },
    {
      title: $t('page.global.table.currency'),
      key: 'currency',
      align: Align.Left,
      scopedSlots: true,
    },
    {
      title: $t('page.global.table.amount'),
      key: 'amount',
      align: Align.Left,
      formatBy: Format.Price,
      parse: ParseType.Decimal,
      scopedSlots: true,
    },
    {
      title: $t('page.global.table.fee'),
      key: 'fee',
      align: Align.Left,
      scopedSlots: true,
    },
    {
      title: $t('page.global.table.credited'),
      key: 'credited',
      align: Align.Left,
      scopedSlots: true,
    },
    {
      title: $t('page.global.table.txid'),
      key: 'txid',
      align: Align.Left,
      scopedSlots: true,
    },
  ]

  return result
})

const { data: deposits, pending: loading, refresh } = await useAsyncData(async () => {
  const { headers, data } = await assetsStore.FetchDeposits(query.value)

  total.value = Number(headers.total)

  return data
}, { default: () => <Deposit[]>([]) })

callbacks.push(refresh)

if (query.value.time_from) dateRange.value[0] = fromUnixTime(Number(query.value.time_from as string))
if (query.value.time_to) dateRange.value[1] = fromUnixTime(Number(query.value.time_to as string))

watch(dateRange.value, () => {
  if (dateRange.value[0] && dateRange.value[1] && dateRange.value[0] <= dateRange.value[1]) {
    query.value = {
      ...query.value,
      time_from: String(getUnixTime(dateRange.value[0])),
      time_to: String(getUnixTime(dateRange.value[1])),
    }
  }
})

function clearDate() {
  query.value.time_from = ''
  query.value.time_to = ''

  query.value = {
    ...query.value,
    page: 1,
    limit: 15,
  }
}

function getCurrency(currencyID: string) {
  return publicStore.currencies.find(c => c.id === currencyID) as Currency
}

function getCurrencyNetwork(currencyID: string, blockchainKey: string) {
  const currency = getCurrency(currencyID)
  console.log(currency)
  return currency.networks.find(n => n.blockchain_key === blockchainKey)
}

function clearParams() {
  query.value = {
    page: 1,
    limit: query.value.limit,
  }

  dateRange.value[0] = null
  dateRange.value[1] = null
}

const showClear = computed(() => {
  if (query.value.currency || dateRange.value[0] || dateRange.value[1]) {
    return true
  }
  return false
})

const currencyColumns = [
  {
    key: 'id',
    scopedSlots: true,
  },
]

onMounted(() => {
  useEvent.on('private:deposit', appendDeposit)
})

onBeforeUnmount(() => {
  useEvent.off('private:deposit', appendDeposit)
})

function appendDeposit(deposit: Deposit) {
  const index = deposits.value.findIndex(d => d.id === deposit.id)
  if (index !== -1) {
    deposits.value[index] = deposit
  }
}

function uppercase(text: string) {
  return text.toUpperCase()
}

function ExplorerAddress(deposit: Deposit, address?: string) {
  const currency = publicStore.currencies.find(c => c.id === deposit.currency)
  if (!currency) return ''
  const network = currency.networks.find(n => n.blockchain_key === deposit.blockchain_key)
  if (network && network.explorer_address) {
    return network.explorer_address.replace('#{address}', address || deposit.address)
  }

  return ''
}

function ExplorerTransaction(deposit: Deposit) {
  const currency = publicStore.currencies.find(c => c.id === deposit.currency)
  if (!currency) return ''
  const network = currency.networks.find(n => n.blockchain_key === deposit.blockchain_key)
  if (network && network.explorer_transaction) {
    return parseTemplate(network.explorer_transaction, deposit)
  }

  return ''
}
</script>

<template>
  <div class="page-my-wallet-history-deposits">
    <div class="page-my-wallet-history-filter">
      <ZSelect
        v-model="query.currency"
        :data-source="publicStore.currencies"
        :search="true"
        :columns="currencyColumns"
        :find-by="['id']"
        value-key="id"
        label-key="id"
        :scroll="true"
        placeholder="Currency"
        :replace-func="uppercase"
        class="mr-4 w-[140px]"
      >
        <template #id="{ item }">
          <span>{{ item.id.toUpperCase() }}</span>
        </template>
      </ZSelect>
      <ZRangePicker v-model="dateRange" class="mr-4" :placement="Placement.BottomLeft" @clear="clearDate" />
      <ZButton v-if="showClear" @click="clearParams">
        {{ $t('page.global.action.clear') }}
      </ZButton>
    </div>
    <ZTable
      class="page-my-wallet-history-deposits-table"
      :columns="columns"
      :data-source="deposits"
      :loading="loading"
      :hover="true"
      responsive
    >
      <template #date="{ item }">
        <div class="h-full flex-col justify-end">
          <span v-if="item.credited" class="inline-block date-status-green">
            {{ $t('page.global.status.succeed') }}
          </span>
          <span v-else-if="!item.fee_paid && item.fee_currency !== item.currency" class="inline-block date-status-orange">
            {{ $t('page.global.status.fee_required') }}
          </span>
          <span v-else class="inline-block date-status-orange">
            {{ $t('page.global.status.processing') }}
          </span>
          <div class="mt-[4px]">
            {{ formatDate(new Date(item.created_at), "MM-dd HH:mm:ss") }}
          </div>
        </div>
      </template>
      <template #currency="{ item }">
        <div class="flex justify-center items-center h-full">
          <img :src="getCurrency(item.currency).icon_url">
          <div>
            <div class="currency-code">
              {{ item.currency.toUpperCase() }}
            </div>
            <div class="currency-name">
              {{ getCurrency(item.currency).name }}
            </div>
          </div>
        </div>
      </template>
      <template #amount="{ item }">
        <div class="flex justify-center items-center h-full">
          {{ item.amount }}
        </div>
      </template>
      <template #fee="{ item }">
        <div v-if="item.fee_paid" class="flex justify-center items-center h-full">
          {{ `${item.fee} ${item.fee_currency.toUpperCase()}` }}
        </div>
        <ZTooltip v-else :title="`You need ${item.fee} ${item.fee_currency.toUpperCase()} to process deposit`" :placement="TooltipPlacement.TopCenter">
          <div class="flex justify-center items-center h-full underline decoration-dashed decoration-gray-400">
            {{ `${item.fee} ${item.fee_currency.toUpperCase()}` }}
          </div>
        </ZTooltip>
      </template>
      <template #credited="{ item }">
        <div v-if="item.credited" class="flex justify-center items-center h-full text-green-500">
          TRUE
        </div>
        <div v-else class="flex justify-center items-center h-full text-amber-500">
          FALSE
        </div>
      </template>
      <template #txid="{ item }">
        <div
          class="txid-container"
        >
          <div class="txid-container-txid flex items-start gap-[4px]">
            {{ $t('page.my.wallet.history.txid') }} <NuxtLink :to="ExplorerTransaction(item)" class="txid-container-text" target="_blank">
              {{ item.txid }}
            </NuxtLink>
          </div>
          <div class="txid-container-address flex items-start gap-[4px]">
            {{ $t('page.my.wallet.history.address') }} <NuxtLink :to="ExplorerAddress(item, item.from_address)" class="txid-container-text" target="_blank">
              {{ item.from_address }}
            </NuxtLink>
          </div>
        </div>
      </template>
      <template #foot>
        <ZPagination v-model="query.page" v-model:page-size="query.limit" :is-loading="loading" :total="total" />
      </template>
    </ZTable>
  </div>
</template>

<style lang="less">
.page-my-wallet-history-deposits {
  .fee {
    max-width: 120px;
  }

  .credited {
    max-width: 120px;
  }

  .z-table {
    .z-loading {
      z-index: 1;
    }

    &-row {
      padding: 0 !important;
      border-top: 1px solid @base-border-color;
    }
  }

  .z-select {
    .z-table-row {
      height: 42px;
    }
  }

  &-table {
    .z-card-content {
      max-width: 100%;
    }
  }

  .z-pagination-left {
    padding-left: 0;
  }

  .z-table-pro {
    &-head {
      @media @mobile {
        padding: 16px;
      }
    }

    .z-table {
      &-head {
        @media @mobile {
          padding: 0 16px;
        }
      }

      &-row {
        height: auto;
        line-height: 1.5;

        @media @mobile {
          padding: 0 16px;
        }

        &-col {
          align-items: flex-start;

          &-content {
            align-items: flex-start;
          }
        }
      }
    }
  }

  .date {
    max-width: 160px;
    display: flex;
    align-items: center !important;

    &-status {
      max-width: 140px;
      &-green {
        text-transform: capitalize;
        margin-top: 5px;
        padding: 4px 12px;
        line-height: 1;
        border-radius: 4px;
        color: @up-color;
        background-color: rgba(@up-color, 0.15);
      }

      &-orange {
        text-transform: capitalize;
        margin-top: 5px;
        padding: 4px 12px;
        line-height: 1;
        border-radius: 4px;
        color: @warn-color;
        background-color: rgba(@warn-color, 0.15);
      }

      &-red {
        text-transform: capitalize;
        margin-top: 5px;
        padding: 4px 12px;
        line-height: 1;
        border-radius: 4px;
        color: @error-color;
        background-color: rgba(@error-color, 0.15);
      }

      &-gray {
        text-transform: capitalize;
        margin-top: 5px;
        padding: 4px 12px;
        line-height: 1;
        border-radius: 4px;
        color: @gray-color;
        background-color: rgba(@gray-color, 0.15);
      }
    }
  }

  .currency {
    max-width: 160px;

    @media @mobile {
      display: none !important;
    }

    img {
      position: relative;
      height: 24px;
      margin-right: 12px;
      border-radius: 50%;
    }

    &-code {
      margin-top: 6px;
      line-height: 1;
      font-weight: 500;
      font-size: 16px;
    }

    &-name {
      font-size: 12px;
      color: @gray-color;
      line-height: 1.5;
    }
  }

  .amount {
    max-width: 120px;
  }

  .txid {
    &-container {
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;

      &-text {
        display: inline-block;
        color: @primary-color;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        max-width: 460px;

        &:hover {
          text-decoration: underline;
        }
      }
    }
  }
}
</style>
