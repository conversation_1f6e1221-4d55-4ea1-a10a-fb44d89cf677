<script setup lang="ts">
import { format as formatDate, fromUnixTime, getUnixTime } from 'date-fns'
import type { ZTableColumn } from '@zsmartex/components/types'
import type { Deposit, Withdraw } from '@zsmartex/types'
import { Align, Format, ParseType } from '@zsmartex/types'
import { parseTemplate } from '@zsmartex/utils'

const total = useState(() => 0)

const publicStore = usePublicStore()
const assetsStore = useAssetsStore()

const dateRange = ref<(Date | null)[]>([null, null])
const tempShow = ref<Record<string, boolean>>({})

const { query, callbacks } = useQuery()

const columns: ZTableColumn[] = [
  {
    title: $t('page.global.table.date'),
    key: 'date',
    align: Align.Left,
    formatBy: Format.DateTimeNoYear,
    parse: ParseType.DateTime,
    scopedSlots: true,
  },
  {
    title: $t('page.global.table.currency'),
    key: 'currency',
    align: Align.Left,
    scopedSlots: true,
  },
  {
    title: $t('page.global.table.amount'),
    key: 'amount',
    align: Align.Left,
    formatBy: Format.Price,
    parse: ParseType.Decimal,
    scopedSlots: true,
  },
  {
    title: $t('page.global.table.fee'),
    key: 'fee',
    align: Align.Left,
    scopedSlots: true,
  },
  {
    title: $t('page.global.table.txid'),
    key: 'txid',
    align: Align.Left,
    scopedSlots: true,
  },
]

const { data: withdraws, pending: loading, refresh } = await useAsyncData(async () => {
  const { headers, data } = await assetsStore.FetchWithdraws(query.value)

  total.value = Number(headers.total)

  return data
}, { default: () => <Withdraw[]>([]) })
callbacks.push(refresh)

if (query.value.time_from) dateRange.value[0] = fromUnixTime(Number(query.value.time_from as string))
if (query.value.time_to) dateRange.value[1] = fromUnixTime(Number(query.value.time_to as string))

watch(dateRange.value, () => {
  if (dateRange.value[0] && dateRange.value[1] && dateRange.value[0] <= dateRange.value[1]) {
    query.value = {
      ...query.value,
      time_from: String(getUnixTime(dateRange.value[0])),
      time_to: String(getUnixTime(dateRange.value[1])),
    }
  }
})

function clearDate() {
  query.value.time_from = ''
  query.value.time_to = ''

  query.value = {
    ...query.value,
    page: 1,
    limit: 15,
  }
}

function clearParams() {
  query.value = {
    page: 1,
    limit: query.value.limit,
  }

  dateRange.value[0] = null
  dateRange.value[1] = null
}

const showClear = computed(() => {
  if (query.value.currency || dateRange.value[0] || dateRange.value[1]) {
    return true
  }
  return false
})

function ChangeShowAddress(item: Deposit) {
  tempShow.value[item.id] = !tempShow.value[item.id]
}

const currencyColumns = [
  {
    key: 'id',
    scopedSlots: true,
  },
]

onMounted(() => {
  useEvent.on('private:withdraw', appendWithdraw)
})

onBeforeUnmount(() => {
  useEvent.off('private:withdraw', appendWithdraw)
})

function appendWithdraw(withdraw: Withdraw) {
  const index = withdraws.value.findIndex(w => w.id === withdraw.id)
  if (index !== -1) {
    withdraws.value[index] = withdraw
  }
}

function GetCurrency(currencyID: string) {
  return publicStore.currencies.find(c => c.id === currencyID) as Currency
}

function uppercase(text: string) {
  return text.toUpperCase()
}

function ExplorerAddress(withdraw: Withdraw) {
  const currency = publicStore.currencies.find(c => c.id === withdraw.currency_id)
  if (!currency) return ''
  const network = currency.networks.find(n => n.blockchain_key === withdraw.blockchain_key)
  if (network && network.explorer_address) {
    return network.explorer_address.replace('#{address}', withdraw.rid)
  }

  return ''
}

function ExplorerTransaction(withdraw: Withdraw) {
  const currency = publicStore.currencies.find(c => c.id === withdraw.currency_id)
  if (!currency) return ''
  const network = currency.networks.find(n => n.blockchain_key === withdraw.blockchain_key)
  if (network && network.explorer_transaction && withdraw.txid) {
    return parseTemplate(network.explorer_transaction, withdraw)
  }

  return ''
}
</script>

<template>
  <div class="page-my-wallet-history-withdraws">
    <div class="page-my-wallet-history-filter">
      <ZSelect
        v-model="query.currency"
        :data-source="publicStore.currencies"
        :search="true"
        :columns="currencyColumns"
        :find-by="['id']"
        value-key="id"
        label-key="id"
        :scroll="true"
        placeholder="Currency"
        :replace-func="uppercase"
        class="mr-4 w-[140px]"
      >
        <template #id="{ item }">
          <span>{{ item.id.toUpperCase() }}</span>
        </template>
      </ZSelect>
      <ZRangePicker v-model="dateRange" class="mr-4" :placement="Placement.BottomLeft" @clear="clearDate" />
      <ZButton v-if="showClear" @click="clearParams">
        {{ $t('page.global.action.clear') }}
      </ZButton>
    </div>
    <ZTable
      class="page-my-wallet-history-withdraws-table"
      :columns="columns"
      :data-source="withdraws"
      :hover="true"
      :loading="loading"
      responsive
    >
      <template #date="{ item }">
        <div class="h-full flex-col justify-end">
          <span
            class="inline-block"
            :class="[
              { 'date-status-green': item.status === WithdrawStatus.Succeed },
              { 'date-status-orange': [WithdrawStatus.Accepted, WithdrawStatus.Processing, WithdrawStatus.Confirming, WithdrawStatus.Prepared].includes(item.status) },
              { 'date-status-gray': [WithdrawStatus.Skipped, WithdrawStatus.UnderReview].includes(item.status) },
              { 'date-status-red': [WithdrawStatus.Errored, WithdrawStatus.Failed, WithdrawStatus.Canceled, WithdrawStatus.Rejected, WithdrawStatus.ToReject].includes(item.status) },
            ]"
          >
            {{ $t(`page.global.status.${item.status}`) }}
          </span>
          <div class="mt-1">
            {{ formatDate(new Date(item.created_at), "MM-dd HH:mm:ss") }}
          </div>
        </div>
      </template>
      <template #currency="{ item }">
        <div class="flex justify-center items-center h-full">
          <img :src="GetCurrency(item.currency_id).icon_url">
          <div>
            <div class="currency-code">
              {{ item.currency_id.toUpperCase() }}
            </div>
            <div class="currency-name">
              {{ GetCurrency(item.currency_id).name }}
            </div>
          </div>
        </div>
      </template>
      <template #amount="{ item }">
        <div class="flex justify-center items-center h-full">
          {{ item.amount }}
        </div>
      </template>
      <template #fee="{ item }">
        <div class="flex justify-center items-center h-full">
          {{ `${item.fee} ${item.fee_currency_id.toUpperCase()}` }}
        </div>
      </template>
      <template #txid="{ item }">
        <div
          class="txid-container"
        >
          <template v-if="item.type == 'internal'">
            <div class="txid-container-txid flex items-start gap-[4px]">
              {{ $t('page.my.wallet.history.destination') }} <NuxtLink class="txid-container-text">
                {{ item.rid.split(':')[1] }}
              </NuxtLink>
            </div>
            <div class="txid-container-address flex items-start gap-[4px]">
              {{ $t('page.my.wallet.history.tid') }} <NuxtLink class="txid-container-text">
                {{ item.txid }}
              </NuxtLink>
            </div>
          </template>
          <template v-else>
            <div class="txid-container-txid flex items-start gap-[4px]">
              {{ $t('page.my.wallet.history.txid') }} <NuxtLink :to="ExplorerTransaction(item)" class="txid-container-text" target="_blank">
                {{ item.txid }}
              </NuxtLink>
            </div>
            <div class="txid-container-address flex items-start gap-[4px]">
              {{ $t('page.my.wallet.history.address') }} <NuxtLink :to="ExplorerAddress(item)" class="txid-container-text" target="_blank">
                {{ item.rid }}
              </NuxtLink>
            </div>
          </template>
        </div>
      </template>
      <template #foot>
        <ZPagination v-model="query.page" v-model:page-size="query.limit" :is-loading="loading" :total="total" />
      </template>
    </ZTable>
  </div>
</template>

<style lang="less">
.page-my-wallet-history-withdraws {
  .z-table {
    .z-loading {
      z-index: 1;
    }

    &-row {
      padding: 0 !important;
      border-top: 1px solid @base-border-color;
    }
  }

  .z-pagination {
    padding: 16px;
  }

  .fee {
    max-width: 120px;
  }

  .z-pagination-left {
    padding-left: 0;
  }

  .z-select {
    .z-table-row {
      height: 42px;
    }
  }

  .z-table {
    &-head {
      @media @mobile {
        padding: 0 16px;
      }
    }

    &-row {
      padding: 0;
      height: auto;
      line-height: 1.5;
      border-top: 1px solid @base-border-color;

      @media @mobile {
        padding: 0 16px;
      }

      &-col {
        align-items: flex-start;

        &-content {
          align-items: flex-start;
        }
      }
    }
  }

  .date {
    max-width: 160px;
    display: flex;
    align-items: center !important;

    &-status {
      max-width: 140px;
      &-green {
        text-transform: capitalize;
        margin-top: 5px;
        padding: 4px 12px;
        line-height: 1;
        border-radius: 4px;
        color: @up-color;
        background-color: rgba(@up-color, 0.15);
      }

      &-orange {
        text-transform: capitalize;
        margin-top: 5px;
        padding: 4px 12px;
        line-height: 1;
        border-radius: 4px;
        color: @warn-color;
        background-color: rgba(@warn-color, 0.15);
      }

      &-red {
        text-transform: capitalize;
        margin-top: 5px;
        padding: 4px 12px;
        line-height: 1;
        border-radius: 4px;
        color: @error-color;
        background-color: rgba(@error-color, 0.15);
      }

      &-gray {
        text-transform: capitalize;
        margin-top: 5px;
        padding: 4px 12px;
        line-height: 1;
        border-radius: 4px;
        color: @gray-color;
        background-color: rgba(@gray-color, 0.15);
      }
    }
  }

  .currency {
    max-width: 160px;
    img {
      position: relative;
      height: 24px;
      margin-right: 12px;
      border-radius: 50%;
    }

    &-code {
      margin-top: 6px;
      line-height: 1;
      font-weight: 500;
      font-size: 16px;
    }

    &-name {
      font-size: 12px;
      color: @gray-color;
      line-height: 1.5;
    }
  }

  .amount {
    max-width: 120px;
  }

  .txid {
    &-container {
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;

      &-text {
        display: inline-block;
        color: @primary-color;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        max-width: 540px;

        &:hover {
          text-decoration: underline;
        }
      }
    }
  }
}
</style>
