<script setup lang="ts">
import { format as formatDate } from 'date-fns'
import Decimal from 'decimal.js'
import type { Asset, Currency, Withdraw } from '@zsmartex/types'
import { Align, Format, ParseType, WithdrawType } from '@zsmartex/types'
import { InputType } from '@zsmartex/components/types'
import type { ZTableColumn } from '@zsmartex/components/types'
import ModalWarn from '~/layouts/my/ModalWarn.vue'
import ModalNetwork from '~/layouts/my/address-book/ModalNetwork.vue'
import ModalAddressBook from '~/layouts/my/wallet/withdraw/ModalAddressBook.vue'
import CoinSearch from '~/layouts/my/wallet/CoinSearch.vue'
import type Button from '#components/Button.vue'
import Validate from '~/validation/validate'
import { memoList } from '~/constants/index'
import { parseTemplate } from '@zsmartex/utils'

const runtimeConfig = useRuntimeConfig()
const route = useRoute()
const publicStore = usePublicStore()
const assetsStore = useAssetsStore()
const userStore = useUserStore()
const tradeStore = useTradeStore()
const delayButtonEmail = ref<InstanceType<typeof Button>>()
const delayButtonPhone = ref<InstanceType<typeof Button>>()
const { query } = useQuery()
const total = useState(() => 0)
const currencyID = useState('asset_currency', () => '')
const assetTab = useState('asset_tab', () => '')
const withdrawType = useState<WithdrawType>('withdraw_type', () => WithdrawType.OnChain)

const modalAddressBook = ref<InstanceType<typeof ModalAddressBook>>()
const modalNetwork = ref<InstanceType<typeof ModalNetwork>>()
const modalWarn = ref<InstanceType<typeof ModalWarn>>()

const loading = ref(false)
const step = ref(1)

const destination = ref('')
const address = ref('')
const memo = ref('')
const amount = ref('')
const otpCode = ref('')
const emailCode = ref('')
const phoneCode = ref('')
const beneficiaryID = ref<number>()
const note = ref('')
const addressError = ref<string>()
const visible = ref(false)

const selectAddressBook = ref(false)

const currencies = computed(() => {
  return publicStore.currencies.filter(c => publicStore.getWithdrawalEnabledNetworks(c.id).length)
})

const currency = computed(() => {
  const id = route.params.currency_id as string
  currencyID.value = id

  return publicStore.currencies.find(currency => currency.id === id) as Currency
})

const currencyNetworks = computed(() => {
  return publicStore.getWithdrawalEnabledNetworks(currency.value.id)
})

const blockchainKey = ref(currencyNetworks.value[0].blockchain_key)

const network = computed(() => {
  return currency.value.networks.find(network => network.blockchain_key === unref(blockchainKey) && unref(withdrawType) === WithdrawType.OnChain)
})

const beneficiary = computed(() => {
  return userStore.beneficiaries.find(b => b.id === beneficiaryID.value)
})

const parentCurrency = computed(() => {
  if (!network.value) return currency.value.id
  if (!network.value.use_parent_fee || !network.value.parent_id) return currency.value.id
  return network.value.parent_id
})

const contractAddress = computed(() => {
  if (!network.value) {
    return ''
  }

  for (const key in network.value.options) {
    if (key.includes('contract_address')) {
      return network.value.options[key]
    }
  }

  return ''
})

const contractAddressExplorer = computed(() => {
  if (!network.value || !contractAddress.value) {
    return ''
  }

  if (network.value.explorer_address) {
    return network.value.explorer_address.replace('#{address}', contractAddress.value)
  }

  return ''
})

const asset = computed(() => {
  return assetsStore.spot_assets.find(asset => asset.currency === currency.value.id) as Asset
})

function getAsset(currency: string) {
  return assetsStore.spot_assets.find(asset => asset.currency === currency) as Asset
}

async function onSearchBoxClicked(currency: Currency) {
  navigateTo(`/my/wallet/assets/withdraw/${currency.id}`)
}

function generateCode(type: 'email' | 'phone', callback: () => void) {
  if (unref(withdrawType) === WithdrawType.OnChain) {
    const withdrawAddress = memo.value ? `${unref(address)}?memo=${unref(memo)}` : unref(address)

    tradeStore.GenerateWithdrawalCode(type, withdrawAddress, unref(currency).id, unref(blockchainKey), Number(unref(amount)), callback)
  } else {
    tradeStore.GenerateInternalTransferCode(type, unref(destination), unref(currency).id, unref(amount), callback)
  }
}

function generateEmailCode() {
  generateCode('email', delayButtonEmail.value!.StartDelay)
}

function generatePhoneCode() {
  generateCode('phone', delayButtonPhone.value!.StartDelay)
}

const withdrawFee = computed(() => {
  if (!network.value || !network.value.withdraw_fee) return 0
  if (network.value.withdraw_fee_percentage) return Number(network.value.withdraw_fee) * Number(amount.value || '0')
  return Number(network.value.withdraw_fee)
})

const received = computed(() => {
  if (!amount.value) return '0'
  if (parentCurrency.value !== currency.value.id) return Decimal.max(0, new Decimal(amount.value))
  return Decimal.max(0, new Decimal(amount.value).sub(new Decimal(withdrawFee.value)))
})

const amountError = computed(() => {
  if (Number(amount.value) <= 0) return undefined
  if (Number(amount.value) < Number(network.value?.min_withdraw_amount) && amount.value.length !== 0) return 'Amount must greater min withdraw amount'
  if (Number(amount.value) > Number(asset.value.balance)) return 'Amount must less than balance'
  return undefined
})

const emailCodeError = computed(() => {
  if (emailCode.value.length && emailCode.value.length !== 6) {
    return 'page.global.input.error.email_code'
  }
})

const phoneCodeError = computed(() => {
  if (phoneCode.value.length && phoneCode.value.length !== 6) {
    return 'page.global.input.error.phone_code'
  }
})

const otpCodeError = computed(() => {
  if (otpCode.value.length && otpCode.value.length !== 6) {
    return 'page.global.input.error.otp_code'
  }
})

const disabledButton1 = computed(() => {
  if (amountError.value || amount.value.length === 0) return true
  if (withdrawType.value === WithdrawType.OnChain) {
    if (addressError.value || address.value.length === 0) return true
    if (parentCurrency.value !== currency.value.id && network.value) {
      const parentAsset = getAsset(parentCurrency.value)
      if (!parentAsset) return true
      else if (Number(parentAsset.balance) < withdrawFee.value) return true
    }
  } else {
    if (destination.value.length === 0) return true
  }

  return false
})

const disabledButton2 = computed(() => {
  if (!beneficiary.value && (emailCodeError.value || emailCode.value.length === 0)) return true
  if (userStore.hasPhone && (phoneCodeError.value || phoneCode.value.length === 0)) return true
  if (userStore.otp && (otpCodeError.value || otpCode.value.length === 0)) return true
  return false
})
const { data: withdraws, pending: withdrawsHistoryLoading } = await useAsyncData(async () => {
  const { headers, data } = await assetsStore.FetchWithdraws({
    currency: currency.value.id,
    page: query.value.page,
    limit: query.value.limit,
  })

  total.value = Number(headers.total)

  return data
}, { lazy: true, default: () => Array<Withdraw>() })

async function createWithdrawal() {
  const callback = async () => {
    Message.success({
      message: $t('success.create_withdraw'),
    })
    step.value = 1
    destination.value = ''
    address.value = ''
    memo.value = ''
    amount.value = ''
    otpCode.value = ''
    emailCode.value = ''
    phoneCode.value = ''
    beneficiaryID.value = undefined

    await userStore.FetchTradeProfile()
  }

  loading.value = true
  switch (unref(withdrawType)) {
    case WithdrawType.OnChain:
      const payload = {
        currency: unref(currency).id,
        amount: Number(unref(amount)),
        otp_code: unref(otpCode),
        email_code: unref(emailCode),
        phone_code: unref(phoneCode),
        note: unref(note),
      }

      if (beneficiary.value) {
        payload.beneficiary_id = beneficiary.value.id
      } else {
        payload.address = memo.value ? `${unref(address)}?memo=${unref(memo)}` : unref(address)
        payload.blockchain_key = unref(blockchainKey)
      }

      await tradeStore.CreateWithdrawal(payload, callback)
      break
    case WithdrawType.Internal:
      await tradeStore.CreateInternalTransfer({
        destination: unref(destination),
        amount: Number(unref(amount)),
        currency: unref(currency).id,
        email_code: unref(emailCode),
        phone_code: unref(phoneCode),
        otp_code: unref(otpCode),
        note: unref(note),
      }, callback)
      break
  }
  loading.value = false
}

watch(step, () => {
  if (unref(step) === 2) {
    Message.warn({
      message: $t('warning.otp_enabled', { name: 'withdraw' }),
    })
  }
})

const columns: ZTableColumn[] = [
  {
    title: $t('page.global.table.date'),
    key: 'date',
    align: Align.Left,
    formatBy: Format.DateTimeNoYear,
    parse: ParseType.DateTime,
    scopedSlots: true,
  },
  {
    title: $t('page.global.table.currency'),
    key: 'currency',
    align: Align.Left,
    scopedSlots: true,
  },
  {
    title: $t('page.global.table.amount'),
    key: 'amount',
    align: Align.Left,
    formatBy: Format.Price,
    parse: ParseType.Decimal,
    scopedSlots: true,
  },
  {
    title: $t('page.global.table.fee'),
    key: 'fee',
    align: Align.Left,
    scopedSlots: true,
  },
  {
    title: $t('page.global.table.txid'),
    key: 'txid',
    align: Align.Left,
    scopedSlots: true,
  },
]

function onClickNetwork(key: string) {
  blockchainKey.value = key
}

const networkText = computed(() => {
  const network = currencyNetworks.value.find(c => c.blockchain_key === unref(blockchainKey))
  if (!network) return ''

  let currencyNetworkName: string
  if (network.parent_id) {
    const parentCurrency = publicStore.currencies.find(c => c.id === network.parent_id)!
    currencyNetworkName = `${parentCurrency.name} (${network.protocol})`
  } else {
    const currency = publicStore.currencies.find(c => c.id === network.currency_id)!
    currencyNetworkName = `${currency.name} (${network.protocol})`
  }

  return currencyNetworkName
})

function GetParrentCurrencyName(currencyID: string, blockchainKey: string) {
  const currency = publicStore.currencies.find(c => c.id === currencyID)!
  const network = currency.networks.find(c => c.blockchain_key === blockchainKey)!
  const parentID = network.parent_id ? network.parent_id : network.currency_id
  return `${publicStore.currencies.find(c => c.id === parentID)!.name} (${network.protocol})`
}

onMounted(() => {
  assetTab.value = 'withdraw'
  const query = route.query
  if (query.beneficiary_id) {
    selectAddressBook.value = true
    const beneficiary = userStore.beneficiaries.find(b => b.id === Number(query.beneficiary_id as string))!
    blockchainKey.value = beneficiary.blockchain_key
    address.value = beneficiary.address
    beneficiaryID.value = beneficiary.id
  }

  if (!userStore.otp && !userStore.hasPhone) {
    modalWarn.value?.openModal()
  }

  useEvent.on('private:withdraw', appendWithdraw)
})

onBeforeUnmount(() => {
  useEvent.off('private:withdraw', appendWithdraw)
})

function appendWithdraw(withdraw: Withdraw) {
  const index = withdraws.value.findIndex(w => w.id === withdraw.id)
  if (index !== -1) {
    withdraws.value[index] = withdraw
  } else if (index === -1 && query.value.page === 1 && withdraw.currency_id === currency.value.id) {
    withdraws.value.unshift(withdraw)
  }
}

function onClickAddressBook(beneficiary: Beneficiary) {
  blockchainKey.value = beneficiary.blockchain_key
  address.value = beneficiary.address
  beneficiaryID.value = beneficiary.id
  navigateTo(route.path, {
    replace: true,
  })
}

function changeSelectAddressBook(value: boolean) {
  selectAddressBook.value = value
  address.value = ''
  memo.value = ''
  beneficiaryID.value = undefined
  blockchainKey.value = value ? '' : currency.value.networks[0].blockchain_key
  navigateTo(route.path)
}

function ExplorerAddress(withdraw: Withdraw) {
  const currency = publicStore.currencies.find(c => c.id === withdraw.currency_id)
  if (!currency) return ''
  const network = currency.networks.find(n => n.blockchain_key === withdraw.blockchain_key)
  if (network && network.explorer_address) {
    return network.explorer_address.replace('#{address}', withdraw.rid)
  }

  return ''
}

function ExplorerTransaction(withdraw: Withdraw) {
  const currency = publicStore.currencies.find(c => c.id === withdraw.currency_id)
  if (!currency) return ''
  const network = currency.networks.find(n => n.blockchain_key === withdraw.blockchain_key)
  if (network && network.explorer_transaction && withdraw.txid) {
    return parseTemplate(network.explorer_transaction, withdraw)
  }

  return ''
}

function NextStep() {
  if (disabledButton1.value) return
  step.value++
}

watch([address, network], async () => {
  if (process.server) return
  if (!globalThis.Buffer) return

  if (network.value) {
    const passedValidate = await Validate.address(address.value, currency.value.id, network.value.client)

    addressError.value = passedValidate ? '' : 'input.error.address_format'
  }
}, {
  immediate: true,
})
</script>

<template>
  <ZLayoutContent>
    <ZContainer>
      <ZCard class="mt-4">
        <div class="bold-text text-center text-2xl">
          {{ `${$t('page.my.wallet.withdraw.title')} ${currency.id.toUpperCase()}` }}
        </div>
        <div class="mt-4 page-my-wallet-assets-withdraw-flex">
          <div class="page-my-wallet-assets-withdraw-coins">
            <CoinSearch v-model="visible" :data-source="currencies" class="mb-4" :currency-id="currency.id" @click="onSearchBoxClicked" />
            <div class="page-my-wallet-assets-withdraw-balance">
              <div class="page-my-wallet-assets-withdraw-balance-item">
                <label>{{ $t('page.my.wallet.withdraw.total_balance') }}</label>
                <strong>{{ (Number(asset.balance) + Number(asset.locked)).toFixed(8) }} {{ currency.id.toUpperCase() }}</strong>
              </div>
              <div class="page-my-wallet-assets-withdraw-balance-item">
                <label>{{ $t('page.my.wallet.withdraw.available_balance') }}</label>
                <strong>{{ Number(asset.balance).toFixed(8) }} {{ currency.id.toUpperCase() }}</strong>
              </div>
              <div class="page-my-wallet-assets-withdraw-balance-item">
                <label>{{ $t('page.my.wallet.withdraw.locked_balance') }}</label>
                <strong>{{ Number(asset.locked).toFixed(8) }} {{ currency.id.toUpperCase() }}</strong>
              </div>
              <!-- <div class="page-my-wallet-assets-withdraw-balance-item">
                <label>Withdraw Limit</label>
                <strong>{{ `${userStore.withdraw_limit?.filled}/${userStore.withdraw_limit?.limit} USDT` }}</strong>
              </div> -->
            </div>

            <div class="note note-top bg-gray-100 p-4 mt-12">
              <div class="text-base mb-2">
                {{ $t('page.global.note') }}
              </div>
              <div>
                <div v-if="contractAddress && contractAddressExplorer">
                  Contract Address: <span class="text-black decoration-underline">{{ `${contractAddress.slice(0, 6)}...${contractAddress.slice(-6)}` }}</span>
                </div>
                <div v-if="network && network.use_parent_fee && network.options.withdraw_note">
                  - Withdraw Note: {{ network.options.withdraw_note }}
                </div>
                <div v-if="network && network.withdraw_fee_percentage">
                  - Fee is calculated at {{ Number(network.withdraw_fee) * 100 }}% of withdraw amount
                </div>
                <I18n tag="div" path="page.my.wallet.withdraw.note">
                  <template #br>
                    <br>
                  </template>
                  <template #exchange_name>
                    <span>{{ runtimeConfig.public.exchangeName }}</span>
                  </template>
                  <template #min_withdraw_amount>
                    <span>{{ network?.min_withdraw_amount || 0 }}</span>
                  </template>
                  <template #currency_id>
                    <span>{{ currency.id.toUpperCase() }}</span>
                  </template>
                </I18n>
              </div>
            </div>
          </div>
          <div class="page-my-wallet-assets-withdraw-step-1">
            <template v-if="step === 1">
              <form @submit.prevent="NextStep">
                <div class="page-my-wallet-assets-withdraw-type-selector">
                  <button v-for="type in WithdrawType" :key="type" class="page-my-wallet-assets-withdraw-type-button" :class="{ 'page-my-wallet-assets-withdraw-type-button-selected': withdrawType === type }" @click="withdrawType = type">
                    {{ $t(`page.my.wallet.withdraw.type.${type}`) }}
                  </button>
                </div>

                <template v-if="withdrawType === 'onchain'">
                  <div v-if="selectAddressBook">
                    <div class="flex justify-between">
                      <label>{{ $t('page.my.wallet.withdraw.address_book') }}<span class="text-down">*</span></label>
                      <div class="page-my-wallet-assets-withdraw-button mb-1" @click="changeSelectAddressBook(false)">
                        <ZIconArrowRepeatDuotone /> {{ $t('page.my.wallet.withdraw.new_address') }}
                      </div>
                    </div>
                    <div class="page-my-wallet-assets-withdraw-select" :class="{ 'page-my-wallet-assets-withdraw-selected': beneficiary?.label }" @click="modalAddressBook?.openModal(currency)">
                      <span :class="{ 'text-gray-400 text-[13px]': !beneficiary?.label }">
                        {{ beneficiary?.label || 'Select Network' }}
                      </span>
                      <ZIconAngleDownFilled />
                    </div>
                    <div v-if="beneficiary" class="page-my-wallet-assets-withdraw-address-book">
                      <div class="pb-4">
                        <span class="w-24 inline-block">{{ $t('page.my.wallet.withdraw.address') }}</span>
                        <span>{{ beneficiary.address.split('?memo=')[0].length > 50 ? `${beneficiary.address.split('?memo=')[0].slice(0, 22)}...${beneficiary.address.split('?memo=')[0].slice(-25)}` : beneficiary.address.split('?memo=')[0] }}</span>
                      </div>
                      <div v-if="beneficiary.address.includes('?memo=')" class="pb-4">
                        <span class="w-24 inline-block">{{ $t('page.my.wallet.withdraw.memo') }}</span>
                        <span>{{ beneficiary.address.split('?memo=')[1] }}</span>
                      </div>
                      <div>
                        <span class="w-24 inline-block">{{ $t('page.global.placeholder.network') }}</span>
                        <span>{{ GetParrentCurrencyName(beneficiary.currency_id, beneficiary.blockchain_key) }}</span>
                      </div>
                    </div>
                  </div>
                  <div v-else class="form-row w-full">
                    <div class="form-row w-full">
                      <div class="flex justify-between">
                        <label>{{ $t('page.my.wallet.withdraw.address') }} <span class="text-down">*</span></label>
                        <div class="page-my-wallet-assets-withdraw-button" @click="changeSelectAddressBook(true)">
                          <ZIconArrowRepeatDuotone /> {{ $t('page.my.wallet.withdraw.address_book') }}
                        </div>
                      </div>
                      <ZInput v-model="address" :error="addressError" :placeholder="$t('page.my.wallet.withdraw.placeholder.withdrawal_address')" :trim-space="true" />
                    </div>
                    <div v-if="memoList.includes(network!.client)" class="form-row w-full">
                      <label>{{ $t('page.my.wallet.withdraw.memo') }}</label>
                      <ZInput v-model="memo" :placeholder="$t('page.my.wallet.withdraw.placeholder.memo')" :trim-space="true" />
                    </div>
                    <div class="mt-6">
                      <div class="form-row w-full">
                        <label>{{ $t('page.global.placeholder.network') }} <span class="text-down">*</span></label>
                        <div class="page-my-wallet-assets-withdraw-select mt-1" @click="modalNetwork?.openModal(currencyNetworks)">
                          <span :class="{ 'text-gray-400': !blockchainKey }">
                            {{ networkText || 'Select Network' }}
                          </span>
                          <ZIconAngleDownFilled />
                        </div>
                      </div>
                    </div>
                  </div>
                </template>
                <template v-else>
                  <div class="form-row w-full mt-4">
                    <label>{{ $t('page.my.wallet.withdraw.destination') }} <span class="text-down">*</span></label>
                    <ZInput v-model="destination" :placeholder="$t('page.my.wallet.withdraw.placeholder.withdraw_destination')" />
                  </div>
                </template>
                <div class="form-row w-full mt-4">
                  <label>{{ $t('page.my.wallet.withdraw.amount') }} <span class="text-down">*</span></label>
                  <ZInput v-model="amount" :type="InputType.Decimal" :precision="currency.precision" :placeholder="$t('page.my.wallet.withdraw.placeholder.withdrawal_amount', { min_withdraw_amount: network?.min_withdraw_amount || 0 })" :error="amountError">
                    <template #suffix>
                      <ZButton class="z-button-delay" @click="amount = asset.balance">
                        {{ $t('page.global.action.select_all') }}
                      </ZButton>
                    </template>
                  </ZInput>
                </div>
                <div class="form-row w-full !mt-8">
                  <label>{{ $t('page.my.wallet.withdraw.note_title') }}</label>
                  <textarea v-model="note" maxlength="255" rows="4" class="page-my-wallet-assets-withdraw-note block border-[1px] rounded w-full max-w-full mt-1 p-2 outline-0 focus:border-blue-300 duration-200" :placeholder="$t('page.my.wallet.withdraw.placeholder.withdrawal_note')" />
                </div>
                <div class="flex justify-between mt-2" :class="{ '!mt-8': amountError }">
                  <div class="flex items-center">
                    {{ $t('page.my.wallet.withdraw.fee', { fee: withdrawFee }) }} {{ parentCurrency ? parentCurrency.toUpperCase() : currency.id.toUpperCase() }} {{ network && network.withdraw_fee_percentage ? `(${Number(network.withdraw_fee) * 100}%)` : '' }}
                    <ZTooltip v-if="network && network.use_parent_fee && network.options.withdraw_note" :title="network.options.withdraw_note" :placement="TooltipPlacement.TopCenter">
                      <div class="w-[14px] h-[14px] mt-[1px] ml-[3px] icon-info">
                        <ZIconInfoFilled class="absolute w-full h-full text-sm cursor-pointer" />
                      </div>
                    </ZTooltip>
                    <ZTooltip v-if="network && network.withdraw_fee_percentage" :title="`Fee is calculated at ${Number(network.withdraw_fee) * 100}% of withdraw amount`" :placement="TooltipPlacement.TopCenter">
                      <div class="w-[14px] h-[14px] mt-[1px] ml-[3px] icon-info">
                        <ZIconInfoFilled class="absolute w-full h-full text-sm cursor-pointer" />
                      </div>
                    </ZTooltip>
                  </div>
                  <div>
                    {{ $t('page.my.wallet.withdraw.received', { received }) }}
                  </div>
                </div>
                <ZButton class="w-full h-10 text-sm font-medium mt-4 rounded" type="primary" html-type="submit" :disabled="disabledButton1">
                  {{ $t('page.global.action.submit') }}
                </ZButton>
              </form>
            </template>
            <template v-else>
              <div class="page-my-wallet-assets-withdraw-step-back relative mb-8">
                <ZIconAngerLeftFilled class="absolute text-lg cursor-pointer" @click="step--" />
              </div>
              <form @submit.prevent="createWithdrawal">
                <div v-if="userStore.hasEmail && !beneficiary" class="form-row w-full">
                  <label>{{ $t('page.global.placeholder.email_code') }} <span class="text-down">*</span></label>
                  <ZInput
                    v-model="emailCode"
                    :type="InputType.Number"
                    :max-length="6"
                    :required="true"
                    :error="emailCodeError"
                    :placeholder="$t('page.global.placeholder.email_code')"
                  >
                    <template #suffix>
                      <ZButton
                        ref="delayButtonEmail"
                        :delay="{
                          time: 60,
                          content: 'Get [#{time}] again',
                        }"
                        @click="generateEmailCode"
                      >
                        {{ $t('page.global.action.get_code') }}
                      </ZButton>
                    </template>
                  </ZInput>
                </div>

                <div v-if="userStore.hasPhone && !beneficiary" class="form-row w-full">
                  <label>{{ $t('page.global.placeholder.phone_code') }} <span class="text-down">*</span></label>
                  <ZInput
                    v-model="phoneCode"
                    :type="InputType.Number"
                    :max-length="6"
                    :required="true"
                    :error="phoneCodeError"
                    :placeholder="$t('page.global.placeholder.phone_code')"
                  >
                    <template #suffix>
                      <ZButton
                        ref="delayButtonPhone"
                        :delay="{
                          time: 60,
                          content: 'Get [#{time}] again',
                        }"
                        @click="generatePhoneCode"
                      >
                        {{ $t('page.global.action.get_code') }}
                      </ZButton>
                    </template>
                  </ZInput>
                </div>

                <div v-if="userStore.otp" class="form-row w-full">
                  <label>{{ $t('page.global.placeholder.otp_code') }} <span class="text-down">*</span></label>
                  <ZInput v-model="otpCode" :type="InputType.Number" :placeholder="$t('page.global.placeholder.otp_code')" :max-length="6" :error="otpCodeError" />
                </div>

                <ZButton class="w-full h-10 text-sm font-medium mt-10" type="primary" html-type="submit" :loading="loading" :disabled="disabledButton2">
                  {{ $t('page.global.action.submit') }}
                </ZButton>
              </form>
            </template>
          </div>
          <div class="note note-bottom bg-gray-100 p-4 mt-4">
            <div class="text-base mb-2">
              {{ $t('page.global.note') }}
            </div>
            <div>
              <div>1234</div>
              <I18n tag="div" path="page.my.wallet.withdraw.note">
                <template #br>
                  <br>
                </template>
                <template #exchange_name>
                  <span>{{ runtimeConfig.public.exchangeName }}</span>
                </template>
                <template #min_withdraw_amount>
                  <span>{{ network?.min_withdraw_amount || 0 }}</span>
                </template>
                <template #currency_id>
                  <span>{{ currency.id.toUpperCase() }}</span>
                </template>
              </I18n>
            </div>
          </div>
        </div>
      </ZCard>

      <ZTablePro
        class="page-my-wallet-assets-withdraw-table my-2"
        :columns="columns"
        :data-source="withdraws"
        :title="$t('page.my.wallet.withdraw.withdraw_history')"
        :loading="withdrawsHistoryLoading"
        responsive
      >
        <template #date="{ item }">
          <div class="h-full flex-col justify-end">
            <span
              class="inline-block"
              :class="[
                { 'date-status-green': item.status === WithdrawStatus.Succeed },
                { 'date-status-orange': [WithdrawStatus.Accepted, WithdrawStatus.Processing, WithdrawStatus.Confirming, WithdrawStatus.Prepared].includes(item.status) },
                { 'date-status-gray': [WithdrawStatus.Skipped, WithdrawStatus.UnderReview].includes(item.status) },
                { 'date-status-red': [WithdrawStatus.Errored, WithdrawStatus.Failed, WithdrawStatus.Canceled, WithdrawStatus.Rejected, WithdrawStatus.ToReject].includes(item.status) },
              ]"
            >
              {{ $t(`page.global.status.${item.status}`) }}
            </span>
            <div class="mt-1">
              {{ formatDate(new Date(item.created_at), "MM-dd HH:mm:ss") }}
            </div>
          </div>
        </template>
        <template #currency="{ item }">
          <div class="flex justify-center items-center h-full">
            <img :src="currency.icon_url">
            <div>
              <div class="currency-code">
                {{ item.currency_id.toUpperCase() }}
              </div>
              <div class="currency-name">
                {{ currency.name }}
              </div>
            </div>
          </div>
        </template>
        <template #amount="{ item }">
          <div class="flex justify-center items-center h-full">
            {{ item.amount }}
          </div>
        </template>
        <template #fee="{ item }">
          <div class="flex justify-center items-center h-full">
            {{ `${item.fee} ${item.fee_currency_id.toUpperCase()}` }}
          </div>
        </template>
        <template #txid="{ item }">
          <div
            class="txid-container"
          >
            <template v-if="item.type == 'internal'">
              <div class="txid-container-txid flex items-start gap-[4px]">
                {{ $t('page.my.wallet.history.destination') }} <NuxtLink class="txid-container-text">
                  {{ item.rid.split(':')[1] }}
                </NuxtLink>
              </div>
              <div class="txid-container-address flex items-start gap-[4px]">
                {{ $t('page.my.wallet.history.tid') }} <NuxtLink class="txid-container-text">
                  {{ item.txid }}
                </NuxtLink>
              </div>
            </template>
            <template v-else>
              <div class="txid-container-txid flex items-start gap-[4px]">
                {{ $t('page.my.wallet.history.txid') }} <NuxtLink :to="ExplorerTransaction(item)" class="txid-container-text" target="_blank">
                  {{ item.txid }}
                </NuxtLink>
              </div>
              <div class="txid-container-address flex items-start gap-[4px]">
                {{ $t('page.my.wallet.history.address') }} <NuxtLink :to="ExplorerAddress(item)" class="txid-container-text" target="_blank">
                  {{ item.rid }}
                </NuxtLink>
              </div>
            </template>
          </div>
        </template>
        <template #foot>
          <ZPagination v-model="query.page" v-model:page-size="query.limit" :is-loading="withdrawsHistoryLoading" :total="total" />
        </template>
      </ZTablePro>
    </ZContainer>
    <ModalAddressBook ref="modalAddressBook" @click="onClickAddressBook" />
    <ModalNetwork ref="modalNetwork" @click="onClickNetwork" />
    <ModalWarn ref="modalWarn" @skip="navigateTo('/my/wallet/assets/trading_account/spot')" />
  </ZLayoutContent>
</template>

<style lang="less">
.page-my-wallet-assets-withdraw {
  .icon-info {
    .cls-1 {
      fill: @gray-color;
    }
  }

  .z-container {
    @media @mobile, @tablet {
      width: 100%;
    }
  }

  .z-table {
    .z-loading {
      z-index: 1;
    }
  }

  .z-pagination {
    padding: 16px;
  }

  &-step {
    &-1 {
      width: 50%;
      padding-left: 40px;
      padding-right: 32px;

      @media @mobile {
        width: 100%;
        padding: 0;
      }
    }

    &-back {
      svg {
        width: 20px;
        height: 20px;
        fill: @gray-color;
      }

      @media @mobile {
        height: 32px;
      }
    }
  }

  &-flex {
    display: flex;

    @media @mobile {
      display: block;
    }
  }

  &-type {
    &-selector {
      width: 100%;
      background-color: rgba(@primary-color, 0.1);
      margin-bottom: 30px;
      padding: 4px;
      border-radius: 8px;
    }

    &-button {
      width: 50%;
      background-color: transparent;
      padding: 12px 16px 12px 12px;
      border-radius: 4px;
      cursor: pointer;

      &-selected {
        background-color: @primary-color;
        border-color: @primary-color;
        color: white;
        font-weight: 500;
      }
    }
  }

  &-coins {
    width: 50%;
    padding-right: 40px;
    padding-left: 32px;

    @media @mobile {
      width: 100%;
      margin-bottom: 12px;
      padding: 0;
    }
  }

  &-balance {
    @media @mobile {
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
    }

    &-item {
      @media @mobile {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 50%;
      }

      label {
        @media @mobile {
          font-size: 13px;
        }
      }

      strong {
        @media @mobile {
          font-size: 14px !important;
        }
      }
    }
  }

  &-note {
    border: 1px solid @base-border-color;
  }

  &-select {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 12px;
    height: 40px;
    border: 1px solid @base-border-color;
    border-radius: 4px;
    cursor: pointer;

    svg {
      width: 20px;
      height: 20px;
      fill: @gray-color;
    }
  }

  &-selected {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 8px;
    height: 40px;
    background-color: rgba(@base-border-color, 0.05);
    border: 1px solid @base-border-color;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
    cursor: pointer;
  }

  &-address-book {
    // px-2 py-4 bg-gray-100 border-gray-300 border-[1px]
    padding: 16px 8px;
    background-color: rgba(@base-border-color, 0.05);
    border: 1px solid @base-border-color;
    border-top: none;
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
  }

  &-button {
    color: @primary-color;
    cursor: pointer;

    svg {
      width: 16px;
      height: 16px;
      fill: @primary-color;
    }
  }

  .z-card-content {
    width: 100%;
    margin: auto;

    .z-dropdown {
      &-overlay {
        width: 100%;
      }
    }
  }

  .search-box-trigger {
    position: relative;
    width: 100%;
    cursor: pointer;
  }

  .fee {
    max-width: 120px;
  }

  .icon {
    width: 20px;
    height: 20px;
    margin-right: 8px;
    border-radius: 50%;
  }

  .z-search-box {
    width: 100%;
  }

  .z-input {
    height: 40px;
    line-height: 40px;
    border: 1px solid @base-border-color;
    background-color: transparent;
    margin-top: 4px;

    .z-button {
      width: 106px;
      justify-content: flex-end;
      padding-right: 0;
    }

    .z-button:disabled {
      background-color: white !important;
    }
  }

  .z-input-error-content {
    margin-top: 4px;
    height: 14px;
    line-height: 1;
  }

  .form-row {
    position: relative;

    & + .form-row {
      margin-top: 28px;
    }
  }

  .z-button {
    width: 100%;
    color: white;
  }

  strong {
    font-size: 16px;
    margin-bottom: 12px;
    display: block;
  }

  .note {
    ul li {
      color: @gray-color;

      &::before {
        margin-right: 4px;
        content: "-";
      }
    }

    &.note-top {
      @media @mobile {
        display: none !important;
      }
    }

    &.note-bottom {
      display: none;

      @media @mobile {
        display: block !important;
      }
    }
  }

  .z-table-pro {
    &-head {
      @media @mobile {
        padding: 16px;
      }
    }

    .z-table {
      &-head {
        @media @mobile {
          padding: 0 16px;
        }
      }

      &-row {
        height: auto;
        line-height: 1.5;

        @media @mobile {
          padding: 0 16px;
        }

        &-col {
          align-items: flex-start;

          &-content {
            align-items: flex-start;
          }
        }
      }
    }
  }

  .date {
    max-width: 160px;
    display: flex;
    align-items: center !important;

    &-status {
      max-width: 140px;
      &-green {
        text-transform: capitalize;
        margin-top: 5px;
        padding: 4px 12px;
        line-height: 1;
        border-radius: 4px;
        color: @up-color;
        background-color: rgba(@up-color, 0.15);
      }

      &-orange {
        text-transform: capitalize;
        margin-top: 5px;
        padding: 4px 12px;
        line-height: 1;
        border-radius: 4px;
        color: @warn-color;
        background-color: rgba(@warn-color, 0.15);
      }

      &-red {
        text-transform: capitalize;
        margin-top: 5px;
        padding: 4px 12px;
        line-height: 1;
        border-radius: 4px;
        color: @error-color;
        background-color: rgba(@error-color, 0.15);
      }

      &-gray {
        text-transform: capitalize;
        margin-top: 5px;
        padding: 4px 12px;
        line-height: 1;
        border-radius: 4px;
        color: @gray-color;
        background-color: rgba(@gray-color, 0.15);
      }
    }
  }

  .currency {
    max-width: 160px;
    img {
      position: relative;
      height: 24px;
      margin-right: 12px;
      border-radius: 50%;
    }

    &-code {
      margin-top: 6px;
      line-height: 1;
      font-weight: 500;
      font-size: 16px;
    }

    &-name {
      font-size: 12px;
      color: @gray-color;
      line-height: 1.5;
    }
  }

  .amount {
    max-width: 160px;
  }

  .txid {
    &-container {
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;

      &-text {
        display: inline-block;
        color: @primary-color;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        max-width: 540px;

        &:hover {
          text-decoration: underline;
        }
      }
    }
  }
}
</style>
