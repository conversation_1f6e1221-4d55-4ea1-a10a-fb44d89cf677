<script setup lang="ts">
import { InputType, ZAuthFormFieldType } from '@zsmartex/components/types'
import type { ZAuthForm<PERSON>ield } from '@zsmartex/components/types'
import Validate from '~/validation/validate'
import type Button from '#components/Button.vue'

definePageMeta({
  middleware: ['confirm-code'],
})

const userStore = useUserStore()
const extendUserStore = useExtendUserStore()

const delayButtonEmail = ref<InstanceType<typeof Button>>()

const fields: ZAuthFormField[] = [
  {
    key: 'code',
    name: 'code',
    type: ZAuthFormFieldType.Input,
    inputType: InputType.Text,
    label: $t('page.global.placeholder.confirm_code'),
    placeholder: $t('page.global.placeholder.confirm_code'),
    required: true,
    maxLength: 6,
    validate: [Validate.minLength(6)],
    transformErrors: {
      'input.error.min_length': $t('page.global.error.min_length', { min: 6 }),
    },
  },
]

async function confirmCode({ code }: { code: string }) {
  if (userStore.email) {
    await userStore.ConfirmCode(userStore.email, '', '', code, '', extendUserStore.CallbackLogin)
  } else {
    await userStore.ConfirmCode('', userStore.phone!.number, userStore.phone!.region, '', code, extendUserStore.CallbackLogin)
  }
}

async function GenerateConfirmCode() {
  if (userStore.email) {
    await userStore.GenerateCodeConfirmEmail(userStore.email)
    delayButtonEmail.value?.StartDelay()
  }
}

onMounted(async () => {
  delayButtonEmail.value?.StartDelay()
})
</script>

<template>
  <ZLayoutContent class="page-confirm-email-layout">
    <ZAuthForm
      :fields="fields"
      :loading="userStore.loginLoading"
      :title="$t('page.confirm-code.form.title')"
      :submit-text="$t('page.global.action.confirm')"
      @submit="confirmCode"
    >
      <template #code-suffix>
        <ZButton
          ref="delayButtonEmail"
          :delay="{
            time: 60,
            content: 'Get [#{time}] again',
          }"
          @click="GenerateConfirmCode()"
        >
          {{ $t('page.global.action.get_code') }}
        </ZButton>
      </template>
    </ZAuthForm>
  </ZLayoutContent>
</template>

<style lang="less">
@import '~/assets/styles/layouts/auth.less';
.page-confirm-email {
  &-layout {
    @media @mobile {
      background-color: white;
    }
  }

  .z-auth-form {
    @media @mobile {
      padding: 0;
    }
  }

  .z-auth-form-title {
    @media @mobile {
      text-align: left !important;
      color: @text-color;
      margin-left: 24px;
      margin-bottom: 24px;
      width: calc(100% - 48px);
    }
  }

  .z-container {
    @media @mobile {
      padding-top: 80px;
      width: 100% !important;
    }
  }
}
</style>
