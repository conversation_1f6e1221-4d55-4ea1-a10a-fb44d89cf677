<script setup lang="ts">
import { InputType, type ZAuthFormField, ZAuthFormFieldType } from '@zsmartex/components/types'
import Validate from '~/validation/validate'

definePageMeta({
  middleware: ['guest-only', 'reset-password'],
})

const userStore = useUserStore()

const loading = ref(false)
const password = ref('')

const fields = computed(() => [
  {
    key: 'password',
    name: 'password',
    type: ZAuthFormFieldType.Input,
    inputType: InputType.Password,
    label: $t('page.global.label.password'),
    placeholder: $t('page.global.placeholder.password'),
    required: true,
    validate: [Validate.pattern(/^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])(?=.*?[$&+,:;=?@#|'"<>.^*()%!-]).{8,}$/)],
    value: password,
    transformErrors: {
      'input.error.pattern': $t('page.global.error.password'),
    },
  },
  {
    key: 'confirm_password',
    name: 'confirm_password',
    type: ZAuthFormFieldType.Input,
    inputType: InputType.Password,
    label: $t('page.global.label.confirm_password'),
    placeholder: $t('page.global.placeholder.confirm_password'),
    required: true,
    validate: [Validate.equal(password.value)],
    transformErrors: {
      'input.errors.equal': $t('page.global.error.password_confirmation'),
    },
  },
  {
    key: 'otp_code',
    name: 'otp_code',
    type: ZAuthFormFieldType.Input,
    inputType: InputType.Number,
    hidden: !userStore.need_otp,
    label: $t('page.global.label.otp_code'),
    placeholder: $t('page.global.placeholder.otp_code'),
    required: userStore.need_otp,
    maxLength: 6,
    validate: [Validate.minLength(6)],
    transformErrors: {
      'input.error.min_length': $t('page.global.error.min_length', { min: 6 }),
    },
    autoFocus: !userStore.need_email,
  },
] as ZAuthFormField[])

async function resetPassword(payload: { password: string; confirm_password: string; otp_code?: string }) {
  loading.value = true
  await userStore.ResetPassword({
    email: userStore._cache.email,
    phone_number: userStore._cache.phone_number,
    region: userStore._cache.region,
    password: payload.password,
    confirm_password: payload.confirm_password,
    email_code: userStore._cache.email_code,
    phone_code: userStore._cache.phone_code,
    otp_code: payload.otp_code,
  })
  loading.value = false
}
</script>

<template>
  <ZLayoutContent class="page-reset-password-layout">
    <ZAuthForm
      :fields="fields"
      :loading="loading"
      :title="$t('page.reset-password.form.title')"
      :submit-text="$t('page.global.action.confirm')"
      @submit="resetPassword"
    />
  </ZLayoutContent>
</template>

<style lang="less">
@import '~/assets/styles/layouts/auth.less';
.page-reset-password {
  &-layout {
    @media @mobile {
      background-color: white;
    }
  }

  &-layout {
    @media @mobile {
      background-color: white;
    }
  }

  .z-auth-form {
    @media @mobile {
      padding: 0;
    }
  }

  .z-auth-form-title {
    @media @mobile {
      text-align: left !important;
      color: @text-color;
      margin-left: 24px;
      margin-bottom: 24px;
      width: calc(100% - 48px);
    }
  }

  .z-container {
    @media @mobile {
      padding-top: 80px;
      width: 100% !important;
    }
  }
}
</style>
