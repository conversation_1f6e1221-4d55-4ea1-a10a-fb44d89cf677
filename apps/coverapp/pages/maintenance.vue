<template>
  <div class="page-maintenance">
    <div class="relative w-[420px]">
      <NuxtLink class="page-maintenance-logo" to="/">
        <img src="@/assets/img/rectangular_logo.png">
      </NuxtLink>
    </div>

    <div class="flex items-center">
      <div class="text-">
        <div class="bold-text text-2xl page-maintenance-text">
          {{ $t('page.maintenance.oops') }}
        </div>
        <I18n class="page-maintenance-text text-base" tag="div" path="page.maintenance.note">
          <template #br>
            <br>
          </template>
        </I18n>
      </div>
    </div>
  </div>
</template>

<style lang="less">
.page-maintenance {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
  height: 100vh;
  width: 100%;
  padding-top: 50px;
  background-color: @gray-color;
  background-image: url("~/assets/img/auth-bg.png") !important;
  background-repeat: no-repeat !important;
  background-size: 100% 100% !important;
  text-align: center;

  &-text {
    color: @exchange-text-color;
  }

  &-logo {
    position: absolute;
    top: -145px;
    display: block;
    width: 420px;
    margin-bottom: 42px;

    img {
      width: 100%;
    }
  }
}
</style>
