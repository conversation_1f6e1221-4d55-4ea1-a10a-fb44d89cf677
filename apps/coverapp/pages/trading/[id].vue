<script setup lang="ts">
const publicStore = usePublicStore()
const route = useRoute()

const marketId = route.params.id as string
const market = computed(() => publicStore.markets.find(m => m.id === marketId.toLowerCase()))

await navigateTo(`/exchange/${market.value?.base_unit?.toUpperCase()}-${market.value?.quote_unit?.toUpperCase()}?type=basic`)
</script>

<template>
  <div>Redirect...</div>
</template>
