<script setup lang="ts">
import { ScreenDevice, WebSocketType } from '@zsmartex/types'
import { roundNumber, screenDevice } from '@zsmartex/utils'
import ExchangeDesktop from '~/layouts/exchange/ExchangeDesktop.vue'
import ExchangeMobile from '~/layouts/exchange/ExchangeMobile.vue'
import ExchangeTablet from '~/layouts/exchange/ExchangeTablet.vue'

const tradeStore = useTradeStore()
const webSocketStore = useWebSocketStore()
const currentMarketID = ref(tradeStore.market_id)
const { isTablet, isDesktop } = useDevice()
const device = screenDevice()
const reloadDevice = ref<ScreenDevice>(ScreenDevice.LargeDesktop)
const runtimeConfig = useRuntimeConfig()

const market = computed(() => tradeStore.market)
const ticker = computed(() => tradeStore.ticker)

defineOgImageComponent('BlogPost')

useSeoMeta({
  title: `${roundNumber(ticker.value.last, market.value.price_precision)} | ${market.value.base_unit.toUpperCase()}/${market.value.quote_unit.toUpperCase()} | SafeTrade`,
  ogTitle: `${roundNumber(ticker.value.last, market.value.price_precision)} | ${market.value.base_unit.toUpperCase()}/${market.value.quote_unit.toUpperCase()} | SafeTrade`,
  ogDescription: `Trade ${tradeStore.market.base_unit.toUpperCase()} to ${tradeStore.market.quote_unit.toUpperCase()} and a variety of other cryptocurrencies on SafeTrade exchange. Access real-time live prices alongside technical indicators to assist you in analyzing ${tradeStore.market.base_unit.toUpperCase()}/${tradeStore.market.quote_unit.toUpperCase()} fluctuations.`,
  description: `Trade ${tradeStore.market.base_unit.toUpperCase()} to ${tradeStore.market.quote_unit.toUpperCase()} and a variety of other cryptocurrencies on SafeTrade exchange. Access real-time live prices alongside technical indicators to assist you in analyzing ${tradeStore.market.base_unit.toUpperCase()}/${tradeStore.market.quote_unit.toUpperCase()} fluctuations.`,
  ogUrl: runtimeConfig.public.mainSiteUrl,
  twitterTitle: `${roundNumber(ticker.value.last, market.value.price_precision)} | ${market.value.base_unit.toUpperCase()}/${market.value.quote_unit.toUpperCase()} | SafeTrade`,
  twitterSite: 'SafeTrade',
})

if (process.server) {
  if (isTablet) {
    reloadDevice.value = ScreenDevice.Tablet
  } else if (isDesktop) {
    reloadDevice.value = ScreenDevice.LargeDesktop
  } else {
    reloadDevice.value = ScreenDevice.Mobile
  }
}

function checkWidth() {
  if (reloadDevice.value === ScreenDevice.Desktop) {
    navigateTo(`/exchange/${tradeStore.market.name.replace('/', '-')}?type=basic`)
  }
}

onMounted(() => {
  if (process.server) return
  webSocketStore.subscribe(WebSocketType.Public, ...['depth', 'trades'].map(c => [tradeStore.market_id, c].join('.')))

  reloadDevice.value = screenDevice().value
  checkWidth()
})

onBeforeUnmount(() => {
  webSocketStore.unsubscribe(WebSocketType.Public, ...['depth', 'trades'].map(c => [currentMarketID.value, c].join('.')))
})

watch(device, () => {
  reloadDevice.value = device.value
  checkWidth()
})
</script>

<template>
  <Title>{{ tradeStore.ticker.last }} | {{ tradeStore.market.name }} | SafeTrade</Title>
  <ExchangeTablet v-if="reloadDevice == ScreenDevice.Tablet || isTablet" />
  <ExchangeDesktop v-else-if="reloadDevice == ScreenDevice.Desktop || reloadDevice == ScreenDevice.LargeDesktop" />
  <ExchangeMobile v-else />
</template>

<style lang="less">
.page-exchange {
  background-color: @exchange-layout-background-color !important;
  color: @exchange-text-color !important;
  font-size: 12px !important;
  font-weight: 500 !important;
  user-select: none;

  &-desktop {
    @media @mobile, @tablet {
      display: none !important;
    }
  }

  &-mobile {
    display: none;

    @media @mobile {
      display: block !important;
    }

    .z-drawer {
      &-overlay {
        background-color: rgba(33, 47, 79, 0.5);
      }
    }
  }

  &-tablet {
    min-height: 1088px;
    grid-template-columns: 1fr 1fr minmax(300px, 330px);
    grid-template-rows: 64px 1fr 1.5fr 1fr;
    grid-template-areas:
        "ticker ticker ticker"
        "chart chart trade-action"
        "trades orderbook trade-action"
        "mine-control mine-control trade-action";
    overflow: hidden;

    &-side {
      display: flex;
      padding: 12px;
      background-color: @input-background-color;
      border-radius: 2px;
      overflow: hidden;

      .z-button {
        position: relative;
        flex: 1;
        height: 32px;
        line-height: 32px;
        background-color: @input-background-color;
        border: none;
        color: @white-color;
        transition: none;

        &.buy {
          background-color: @up-color;
          color: @white-color;

          &::after {
            content: '';
            position: absolute;
            right: -8px;
            width: 24px;
            height: 24px;
            background-color: @up-color;
            border-radius: 2px;
            transform: rotate(45deg);
            z-index: 999;
          }
        }

        &.sell {
          background-color: @down-color;
          color: @white-color;

          &::after {
            content: '';
            position: absolute;
            left: -8px;
            width: 24px;
            height: 24px;
            background-color: @down-color;
            border-radius: 2px;
            transform: rotate(45deg);
            z-index: 999;
          }
        }
      }
    }
  }

  &-mobile-buttons {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    display: flex;
    padding: 12px 16px;
    background-color: @exchange-card-background;

    .z-button {
      flex: 1;
      height: 40px;
    }

    &-green {
      color: @white-color;
      background-color: @up-color !important;
      border-color: @up-color !important;
    }

    &-red {
      color: @white-color;
      background-color: @down-color !important;
      border-color: @down-color !important;
    }
  }

  &-tab {
    @media @mobile {
      background-color: @exchange-card-background;
      padding-left: 16px;
    }
  }

  .z-tab {
    height: 24px;
    line-height: 24px;

    &-item {
      font-size: 14px !important;
    }
  }

  .z-button {
    &:disabled {
      background-color: @exchange-layout-background-color !important;
      border-color: @exchange-layout-background-color !important;
    }
  }

  .z-header {
    background-color: @exchange-card-background;
    border-bottom: none;
  }

  .z-dropdown .z-menu {
    background-color: @exchange-dropdown-background;

    &-item {
      &-selected, &:hover {
        background-color: rgba(0, 0, 0, 0.15) !important;
      }
    }
  }

  .z-footer {
    background-color: transparent;
  }

  .z-card {
    background-color: @exchange-card-background;
    padding: 0;
    border-radius: 0;
    box-shadow: none;

    &-head {
      padding: 0 16px;
      height: 32px;
      line-height: 32px;
      font-size: 12px;
      margin: 0;

      @media @mobile {
        padding: 0;
      }
    }
  }

  .z-table {
    &-head {
      color: @exchange-gray-color;
      font-weight: 500;
      font-family: URWDIN-Medium,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol;

      @media @mobile {
        padding: 0 !important;
      }
    }

    &-empty {
      color: @exchange-gray-color;
      opacity: 0.5;
    }

    &-row {
      @media @mobile {
        padding: 0;
      }
    }
  }

  .z-dropdown .z-menu-item {
    color: inherit;
  }

  .z-select {
    .z-overlay {
      margin: 4px 0;
    }

    .z-menu {
      width: 125px;
      background-color: @exchange-dropdown-background;
      border: 1px solid @exchange-border-color;
      border-radius: 4px;
      text-align: left;
      padding: 4px 0;

      &-item {
        padding: 0 8px;
        margin-left: 0;

        &-selected, &:hover {
          background-color: rgba(0, 0, 0, 0.15);
        }
      }
    }
  }

  .z-checkbox {
    &-inner {
      border-color: @exchange-border-color;

      i {
        color: rgba(@exchange-gray-color, 0.5);
      }
    }

    &-selected {
      .z-checkbox-inner {
        border-color: @primary-color;

        i {
          color: @exchange-text-color;
        }
      }
    }
  }

  .z-loading {
    background-color: @exchange-card-background;
  }

  .z-notification {
    color: @text-color;
  }

  .z-layout-content {
    display: grid;
    padding: 4px;
    gap: 4px;

    &.basic {
      height: auto;
      grid-template-columns: 1fr minmax(253px,320px) minmax(500px,870px) minmax(253px,320px) 1fr;
      grid-template-rows: 64px 380px 80px 1.15fr 320px;
      grid-template-areas:
        "left ticker ticker trade-pairs right"
        "left orderbook chart trade-pairs right"
        "left orderbook chart trades right"
        "left orderbook trade-action trades right"
        "left mine-control mine-control mine-control right";

      .page-exchange-orderbook-overlay-content {
        transform: translateX(101%) translateY(-50%);
      }
    }

    &.pro {
      height: calc(100vh - 50px);
      grid-template-columns: minmax(220px,320px) minmax(600px, 1fr) minmax(260px,320px) minmax(260px,320px);
      grid-template-rows: 64px 1fr auto 260px;
      grid-template-areas:
          "trade-pairs ticker orderbook trades"
          "trade-pairs chart orderbook trades"
          "trade-pairs chart trade-action trade-action"
          "mine-control mine-control trade-action trade-action";
      overflow: hidden;

      & + .z-footer {
        display: none;
      }
    }
  }

  &-ticker {
    height: 100%;
    width: 100%;
    grid-area: ticker/ticker/ticker/ticker;

    @media @tablet {
      height: auto;
    }
  }

  &-orderbook {
    height: 100%;
    width: 100%;
    grid-area: orderbook/orderbook/orderbook/orderbook;

    .z-table-row {
      height: 20px;
      line-height: 20px;
    }
  }

  &-chart {
    grid-area: chart/chart/chart/chart;
  }

  &-trades {
    height: 100%;
    width: 100%;
    grid-area: trades/trades/trades/trades;

    .z-table-row {
      height: 20px;
      line-height: 20px;
    }
  }

  &-mine-control {
    height: 100%;
    width: 100%;
    grid-area: mine-control/mine-control/mine-control/mine-control;
  }

  &-trade-pairs {
    height: 100%;
    width: 100%;
    grid-area: trade-pairs/trade-pairs/trade-pairs/trade-pairs;

    .z-table {
      &-row {
        &-selected, &:hover {
          background-color: @exchange-border-color;

          &::before {
            position: absolute;
            content: "";
            width: 4px;
            height: 100%;
            left: 0;
            background-color: @primary-color;
          }
        }
      }
    }
  }

  &-trade-action {
    grid-area: trade-action/trade-action/trade-action/trade-action;
  }
}
</style>
