@import "z-icon";

@font-face {
  font-display: fallback;
  font-display: swap;
  font-family: URWDIN-Regular;
  font-style: normal;
  font-weight: regular;
  src: local("URWDIN Regular"), local("URWDIN-Regular"), url('~/assets/fonts/URWDIN-v1-Regular.woff') format("woff")
}

@font-face {
  font-display: fallback;
  font-display: swap;
  font-family: URWDIN-Medium;
  font-style: normal;
  font-weight: regular;
  src: local("URWDIN Regular"), local("URWDIN-Regular"), url('~/assets/fonts/URWDIN-v1-Medium.woff') format("woff")
}

.font-family-regular() {
  font-size: 14px;
  font-family: URWDIN-Regular,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol;
  font-weight: 400;
}

body {
  margin: 0;
  color: @text-color;
  font-size: 14px;
  line-height: normal;
  overflow: overlay;
  .font-family-regular;

  button {
    background-color: white;
    border: none;

    .font-family-regular;
  }

  textarea {
    .font-family-regular;
  }

  &, * {
    outline: none;
    box-sizing: border-box;
  }

  @media @desktop, @large-desktop {
    ::-webkit-scrollbar,
    &::-webkit-scrollbar {
      width: 5px;
      height: 0px;
    }

    ::-webkit-scrollbar-track-piece,
    &::-webkit-scrollbar-track-piece {
      background-color: transparent;
      border-radius: 6px;
    }

    ::-webkit-scrollbar-track,
    &:-webkit-scrollbar-track {
      background-color: @layout-background-color;
    }

    ::-webkit-scrollbar-thumb,
    &::-webkit-scrollbar-thumb {
      height: 5px;
      background-color: rgba(@gray-color, 0.5);
      border-radius: 6px;
    }
  }

  @media @tablet, @mobile {
    ::-webkit-scrollbar,
    &::-webkit-scrollbar {
      width: 0px;
      height: 0px;
      display: none;
    }
  }
}

.bold-text {
  font-family: URWDIN-Medium,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol;
  font-weight: 500;
}

.h1, .h2, .h3, .h4, .h5, .h6, h1, h2, h3, h4, h5, h6 {
  margin-bottom: 0.5rem;
  font-family: URWDIN-Medium,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol;
  font-weight: 500;
  line-height: 1.2;
}

a, button {
  cursor: pointer;
}

input {
  border: none;

  &:focus, &:focus-visible {
    box-shadow: none;
    outline: none;
  }
}

*:disabled {
  cursor: not-allowed;
}

.text {
  &-up {
    color: @up-color;
  }

  &-down {
    color: @down-color;
  }

  &-warn {
    color: @warn-color;
  }

  &-left {
    text-align: left;
  }

  &-center {
    text-align: center;
  }

  &-right {
    text-align: right;
  }
}

.bg {
  &-up {
    background-color: @up-color !important;
  }

  &-down {
    background-color: @down-color !important;
  }

  &-warn {
    background-color: @warn-color !important;
  }
}

a {
  color: @primary-color;
  text-decoration: none;
  background-color: transparent;
  outline: none;
  cursor: pointer;
  transition: color 0.3s;

  &:hover {
    color: @blue-color;
  }
}

@import "./components/index.less";
