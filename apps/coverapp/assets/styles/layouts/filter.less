.z-filter() {
  display: flex;
  align-items: center;
  padding: 16px 0;

  @media @mobile {
    flex-wrap: wrap;
  }

  & > div {
    @media @mobile {
      margin-bottom: 8px;
    }
  }

  .z-date-picker {
    background-color: rgba(@gray-color, 0.15);

    input {
      background-color: transparent;
    }
  }

  &-dropdown {
    &-item {
      display: block;
      cursor: pointer;
      padding: 8px 12px;
      color: @text-color !important;

      &:hover {
        background-color: #f5f5f5;
      }
    }

    &-item + &-item {
      border-top: 1px solid @base-border-color;
    }
  }
}

.z-card {
  overflow: visible;

  &-head {
    display: flex;
    justify-content: space-between;
    margin: 0 -24px;
    padding: 0 24px;
    border-bottom: 1px solid @base-border-color;

    @media @mobile {
      margin: 0;
      padding: 0;
    }
  }

  .z-tab {
    display: flex;
    align-items: center;

    @media @mobile {
      display: block;
      width: max-content;
    }

    &-item {
      font-size: 16px;
    }
  }
}