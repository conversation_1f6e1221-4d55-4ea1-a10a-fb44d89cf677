import { acceptHMRUpdate, defineStore } from 'pinia'
import type { TradingViewStream } from '~/types'

export const useTradingViewStore = defineStore('tradingviewStream', () => {
  const streams = reactive<TradingViewStream[]>([])

  useEvent.on('new:trade', (trade) => {
    for (const stream of streams) {
      if (trade.market !== stream.market.id) continue

      let resolution = Number(stream.resolution)

      if (stream.resolution.includes('D')) {
        resolution = 1440
      } else if (stream.resolution.includes('W')) {
        resolution = 10080
      }

      const coeff = resolution * 60
      const unix = new Date(trade.created_at).getTime() / 1000
      const rounded = Math.floor(unix / coeff) * coeff

      const lastBar = stream.lastBar
      const price = Number(trade.price)
      const amount = Number(trade.amount)

      if (!lastBar || rounded > lastBar.time / 1000) {
        lastBar.time = rounded * 1000
        lastBar.open = price
        lastBar.high = price
        lastBar.low = price
        lastBar.close = price
        lastBar.volume = amount
      } else {
        if (!lastBar.time) lastBar.time = rounded * 1000
        if (!lastBar.open) lastBar.open = price
        if (!lastBar.high) lastBar.high = price
        if (!lastBar.low) lastBar.low = price

        lastBar.close = price

        if (price > lastBar.high) lastBar.high = price
        if (price < lastBar.low) lastBar.low = price

        if (lastBar.volume) {
          lastBar.volume += amount
        } else {
          lastBar.volume = amount
        }
      }

      stream.listener(lastBar)
    }
  })

  return {
    streams,
  }
})

if (import.meta.hot)
  import.meta.hot.accept(acceptHMRUpdate(useTradingViewStore, import.meta.hot))
