import { acceptHMRUpdate, defineStore } from 'pinia'

export const useExtendUserStore = defineStore({
  id: 'extendUserStore',
  actions: {
    async CallbackLogin() {
      const userStore = useUserStore()
      if (userStore.state === UserState.Active) {
        await this.FetchData()
        if (process.client) {
          const websocketStore = useWebSocketStore()

          websocketStore.subscribe(WebSocketType.Private, 'balance', 'order', 'trade')
          websocketStore.connect(WebSocketType.Private)
          await navigateTo('/')
        }
      }
    },
    async FetchData() {
      const publicStore = usePublicStore()
      const assetsStore = useAssetsStore()
      const userStore = useUserStore()

      const calls = [
        assetsStore.GetAllAssets(),
        userStore.FetchBeneficiaries(),
        userStore.FetchTradeProfile(),
      ]

      if (publicStore.config.public.p2p) {
        calls.push(assetsStore.GetP2PAssets())
        calls.push(userStore.FetchPayments())
        calls.push(userStore.FetchNotifies())
      }

      await Promise.all(calls)
    },
  },
})

if (import.meta.hot)
  import.meta.hot.accept(acceptHMRUpdate(useExtendUserStore, import.meta.hot))
