export default defineNuxtRouteMiddleware(async () => {
  const publicStore = usePublicStore()
  const userStore = useUserStore()
  const userExtendStore = useExtendUserStore()
  const config = useRuntimeConfig()

  const baseURL = {
    server: config.public.apiUrl as string,
    client: '',
  }

  if (process.client) {
    baseURL.client = `${globalThis.location.origin}/api/v2/`
  }

  setBaseURL(baseURL)
  if (!publicStore.first_route) return

  publicStore.first_route = false
  publicStore.headers = useRequestHeaders()
  publicStore.config = config

  try {
    await Promise.all([
      publicStore.FetchConfig(),
      publicStore.FetchAnnouncements(),
      publicStore.FetchTradingFees(),
      publicStore.FetchBanners(),
      publicStore.FetchCoins(),
      publicStore.FetchFiatCurrencies(),
    ])
    await Promise.all([
      publicStore.FetchTickers(),
      publicStore.FetchSparklines(),
      userStore.GetLogged(userExtendStore.CallbackLogin),
    ])
  } catch (error) {
    console.log(error)
  }
})
