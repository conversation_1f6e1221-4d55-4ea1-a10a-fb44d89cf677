export default defineNuxtRouteMiddleware(() => {
  const userStore = useUserStore()
  const config = useRuntimeConfig()

  if (!userStore.isAuthenticated) {
    return navigateTo('/login')
  }

  if (!config.public.phone && !userStore.pendingEmail && userStore.labels.length !== 0) {
    return
  }

  if (userStore.isPending && userStore.email && (userStore.pendingEmail || userStore.labels.length === 0)) {
    return navigateTo('/confirm-code')
  }
})
