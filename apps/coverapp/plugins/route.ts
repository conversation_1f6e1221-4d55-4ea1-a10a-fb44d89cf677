import { ExchangeLayout } from '@zsmartex/types'
import { defineNuxtPlugin } from '#app'

export default defineNuxtPlugin(() => {
  const router = useRouter()
  const tradeStore = useTradeStore()
  const publicStore = usePublicStore()

  router.beforeEach((to, _, next) => {
    const exchangeMatch = to.path.match(/^\/exchange\/(?:([^/]+?))\/?$/i) || to.path.match(/^\/(?:([^/]+?))\/exchange\/(?:([^/]+?))\/?$/i)
    if (exchangeMatch) {
      const type = to.query.type

      if (type !== ExchangeLayout.Basic && type !== ExchangeLayout.Pro) {
        return next({
          path: to.path,
          query: {
            type: ExchangeLayout.Basic,
          },
        })
      }

      tradeStore.SetExchangeLayout(type)

      let market = publicStore.enabledMarkets.find(m => m.id === String(to.params.market_id).split('-').join('').toLowerCase())
      if (market) {
        tradeStore.SetMarketID(market.id)
      } else {
        market = publicStore.enabledMarkets[0]

        return next({
          path: to.path.replace(String(to.params.market_id), `${market.base_unit}-${market.quote_unit}`.toUpperCase()),
          query: {
            type: to.query.type,
          },
        })
      }
    }

    const depositMatch = to.path.includes('assets/deposit')
    if (depositMatch) {
      let isEnable = false
      let currency = publicStore.currencies.find(c => c.id === String(to.params.currency_id))

      if (currency && currency.networks.length !== 0) {
        for (const network of currency.networks) {
          if (network.deposit_enabled) {
            isEnable = true
            break
          }
        }
      }

      if (!isEnable) {
        currency = publicStore.currencies[0]
        return next({
          path: '/',
        })
      }
    }

    const withdrawMatch = to.path.includes('assets/withdraw')
    if (withdrawMatch) {
      let isEnable = false
      let currency = publicStore.currencies.find(c => c.id === String(to.params.currency_id))

      if (currency && currency.networks.length !== 0) {
        for (const network of currency.networks) {
          if (network.withdraw_enabled) {
            isEnable = true
            break
          }
        }
      }

      if (!isEnable) {
        currency = publicStore.currencies[0]
        return next({
          path: '/',
        })
      }
    }

    next()
  })
})
