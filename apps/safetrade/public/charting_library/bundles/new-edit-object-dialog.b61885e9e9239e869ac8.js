(window.webpackJsonp=window.webpackJsonp||[]).push([["new-edit-object-dialog"],{"+ByK":function(e,t,n){e.exports={itemWrap:"itemWrap-3qF9ynvx",item:"item-112BZuXZ",icon:"icon-2y6cSg4c",selected:"selected-3tUrY97Z",label:"label-1uw3rZaL"}},"/YRR":function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M5.5 16.5l5-5a1.414 1.414 0 0 1 2 0m11-1l-5 5a1.414 1.414 0 0 1-2 0"/><path fill="currentColor" d="M14 5h1v2h-1zM14 10h1v2h-1zM14 15h1v2h-1zM14 20h1v2h-1z"/></svg>'},"01Ho":function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M14.354 6.646L14 6.293l-.354.353-7 7-.353.354.353.354 7 7 .354.353.354-.353 7-7 .353-.354-.353-.354-7-7z"/></svg>'},"4Njr":function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M14 21l7.424-6.114a.5.5 0 0 0-.318-.886H18.5V7h-9v7H6.894a.5.5 0 0 0-.318.886L14 21z"/></svg>'},"4ZyK":function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M8.5 22v-5.5m0 0v-8L12 7l4 2.5 3.5-1v8l-3.5 1-4-2.5-3.5 1.5z"/></svg>'},"4pMH":function(e,t,n){},"5ijr":function(e){e.exports=JSON.parse('{"switcherWrapper":"switcherWrapper-1wFH-_jm","size-small":"size-small-1gT-kZYO","size-large":"size-large-MOSirnj_","intent-select":"intent-select-2kut8F29","switcherThumbWrapper":"switcherThumbWrapper-2u191lDO","input":"input-J7QIcTTo","switcherTrack":"switcherTrack-2XruDVTa","intent-default":"intent-default-3soo5rvS","switcherThumb":"switcherThumb-2yuEucci","focus":"focus-uZMRkCO0"}')},"9FXF":function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M6.5 12.5v8h3v-8h-3zM12.5 7.5v13h3v-13h-3zM18.5 15.5v5h3v-5h-3z"/></svg>'},CHgb:function(e,t,n){"use strict";n.d(t,"c",(function(){return p})),n.d(t,"a",(function(){return d})),n.d(t,"b",(function(){return h}));var r=n("mrSG"),l=n("q1tI"),a=n.n(l),o=n("TSYQ"),i=n.n(o),c=n("H172"),s=n("Iivm"),u=n("+ByK");function p(e){var t=e.menuItemClassName,n=Object(r.e)(e,["menuItemClassName"]);return a.a.createElement(c.a,Object(r.a)({},n,{menuItemClassName:i()(t,u.itemWrap)}))}function d(e){return a.a.createElement("div",{className:i()(u.item,u.selected)},a.a.createElement(s.a,{className:u.icon,icon:e.icon}))}function h(e){return a.a.createElement("div",{className:u.item},a.a.createElement(s.a,{className:i()(u.icon,e.iconClassName),icon:e.icon}),a.a.createElement("div",{className:u.label},e.label))}},D2im:function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28"><path stroke="currentColor" d="M17 8.5h7M20.5 12V5M10 19.5h7M13.5 23v-7M3 12.5h7M6.5 16V9"/></svg>'},Dj0x:function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M11 18.5h-.5V8.793l.146-.147 3-3L14 5.293l.354.353 3 3 .146.147V18.5H11z"/></svg>'},HWhk:function(e,t){
e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path fill="currentColor" fillRule="evenodd" clipRule="evenodd" d="M7.5 13a1.5 1.5 0 1 0 0 3 1.5 1.5 0 0 0 0-3zM5 14.5a2.5 2.5 0 1 1 5 0 2.5 2.5 0 0 1-5 0zm9.5-1.5a1.5 1.5 0 1 0 0 3 1.5 1.5 0 0 0 0-3zM12 14.5a2.5 2.5 0 1 1 5 0 2.5 2.5 0 0 1-5 0zm9.5-1.5a1.5 1.5 0 1 0 0 3 1.5 1.5 0 0 0 0-3zM19 14.5a2.5 2.5 0 1 1 5 0 2.5 2.5 0 0 1-5 0z"/></svg>'},J4oI:function(e,t,n){e.exports={lineStyleSelect:"lineStyleSelect-1s1ap44b"}},KacW:function(e,t,n){"use strict";n.d(t,"a",(function(){return v}));var r=n("mrSG"),l=(n("YFKU"),n("q1tI")),a=n.n(l),o=n("TSYQ"),i=n.n(o),c=n("8Uy/"),s=n("CHgb"),u=n("bQEj"),p=n("UXdH"),d=n("ZSM+"),h=n("J4oI"),m=[{type:c.LINESTYLE_SOLID,icon:u,label:window.t("Line")},{type:c.LINESTYLE_DASHED,icon:p,label:window.t("Dashed Line")},{type:c.LINESTYLE_DOTTED,icon:d,label:window.t("Dotted Line")}];var v=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return Object(r.c)(t,e),t.prototype.render=function(){var e,t,n=this.props,l=n.lineStyle,o=n.className,c=n.lineStyleChange,u=n.disabled,p=n.additionalItems,d=n.allowedLineStyles,v=(e=d,t=Object(r.f)(m),void 0!==e&&(t=t.filter((function(t){return e.includes(t.type)}))),t.map((function(e){return{value:e.type,selectedContent:a.a.createElement(s.a,{icon:e.icon}),content:a.a.createElement(s.b,{icon:e.icon,label:e.label})}})));return p&&(v=Object(r.f)([{readonly:!0,content:p}],v)),a.a.createElement(s.c,{disabled:u,className:i()(h.lineStyleSelect,o),hideArrowButton:!0,items:v,value:l,onChange:c,"data-name":"line-style-select"})},t}(a.a.PureComponent)},Ly1u:function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M7.5 7.5h13v13h-13z"/></svg>'},MB0Y:function(e,t,n){"use strict";var r=n("mrSG"),l=n("q1tI"),a=n.n(l),o=n("TSYQ"),i=n.n(o),c=n("5ijr");n("4pMH");function s(e){const{className:t="",intent:n="default",size:r="small",disabled:l}=e;return o(t,c.switcherWrapper,c["size-"+r],!l&&c["intent-"+n])}class u extends l.PureComponent{render(){const e=this.props,{reference:t,size:n,intent:a}=e,i=Object(r.e)(e,["reference","size","intent"]),u=o(c.input,-1!==this.props.tabIndex&&c.focus);return l.createElement("div",{className:s(this.props)},l.createElement("input",Object.assign({},i,{type:"checkbox",className:u,ref:t})),l.createElement("div",{className:c.switcherThumbWrapper},l.createElement("div",{className:c.switcherTrack}),l.createElement("div",{className:c.switcherThumb})))}}var p=n("QpNh"),d=n("OP2o");n.d(t,"a",(function(){return h})),n.d(t,"b",(function(){return m}));var h=d;function m(e){var t=e.className,n=e.checked,l=e.id,o=e.label,c=e.labelDescription,s=e.value,h=e.preventLabelHighlight,m=e.reference,v=e.switchReference,y=e.theme,f=void 0===y?d:y,b=i()(f.label,n&&!h&&f.labelOn),w=i()(t,f.wrapper,n&&f.wrapperWithOnLabel);return a.a.createElement("label",{className:w,htmlFor:l,ref:m},a.a.createElement("div",{className:f.labelRow},a.a.createElement("div",{className:b
},o),c&&a.a.createElement("div",{className:f.labelHint},c)),a.a.createElement(u,Object(r.a)({className:f.switch,reference:v,checked:n,onChange:function(t){var n=t.target.checked;void 0!==e.onChange&&e.onChange(n)},value:s,tabIndex:-1,id:l},Object(p.a)(e))))}},OP2o:function(e,t,n){e.exports={wrapper:"wrapper-3Sj-FzgR",hovered:"hovered-1G0yygIe",labelRow:"labelRow-3h7cSJ_L",label:"label-3iLxp29M",labelHint:"labelHint-3qxeiVfa",labelOn:"labelOn-10QGwv2n"}},UXdH:function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28"><path fill="currentColor" d="M4 13h5v1H4v-1zM12 13h5v1h-5v-1zM20 13h5v1h-5v-1z"/></svg>'},UXjO:function(e,t,n){"use strict";n.d(t,"a",(function(){return p}));var r=n("mrSG"),l=n("q1tI"),a=n.n(l),o=n("TSYQ"),i=n.n(o),c=n("H172"),s=n("QpNh"),u=n("z1Uu");function p(e){var t,n=e.fontSize,l=e.fontSizes,o=void 0===l?[]:l,p=e.className,d=e.disabled,h=e.fontSizeChange;return a.a.createElement(c.a,Object(r.a)({disabled:d,className:i()(p,u.defaultSelect),menuClassName:u.defaultSelect,items:(t=o,t.map((function(e){return{value:e.value,content:e.title}}))),value:n,onChange:h},Object(s.a)(e)))}},V1YL:function(e,t,n){e.exports={recalculateCheckbox:"recalculateCheckbox-1Xa1TR7D",descriptionCell:"descriptionCell-3oIbGAm4"}},W7Dn:function(e,t,n){e.exports={scrollable:"scrollable-mKj9lAM_"}},Y5hB:function(e,t,n){"use strict";n.r(t);var r=n("mrSG"),l=(n("YFKU"),n("i8i4")),a=n("q1tI"),o=n.n(a),i=n("Eyy1"),c=(n("bSeV"),n("CLNU")),s=n("Vdly"),u=n("Kxc7"),p=n("FQhm"),d=n("JWMC"),h=n("aDg1"),m=n("vHME"),v=n("ycFu"),y=n("tWVy"),f=n("tmL0"),b=n("3ClC"),w=n("W7Dn"),g=function(e){function t(t){var n=e.call(this,t)||this;n._renderFooterLeft=function(e){var t=n.props,r=t.source,l=t.model;if(Object(b.isStudy)(r))return a.createElement(m.a,{model:l,source:r,mode:e?"compact":"normal"});throw new TypeError("Unsupported source type.")},n._handleSelect=function(e){n.setState({activeTabId:e},(function(){n._requestResize&&n._requestResize()})),n.props.onActiveTabChanged&&n.props.onActiveTabChanged(e)},n._handleScroll=function(){y.a.fire()},n._handleSubmit=function(){n.props.onSubmit(),n.props.onClose()};var r=n.props,l=r.pages,o=r.initialActiveTab;return n.state={activeTabId:l.allIds.includes(o)?o:l.allIds[0]},n}return Object(r.c)(t,e),t.prototype.render=function(){var e=this.props,t=e.title,n=e.onCancel,r=e.onClose,l=this.state.activeTabId;return a.createElement(v.a,{dataName:"indicator-properties-dialog",title:t,isOpened:!0,onSubmit:this._handleSubmit,onCancel:n,onClickOutside:r,onClose:r,footerLeftRenderer:this._renderFooterLeft,render:this._renderChildren(l),submitOnEnterKey:!1})},t.prototype._renderChildren=function(e){var t=this;return function(n){var r=n.requestResize;t._requestResize=r;var l=t.props,o=l.pages,i=l.source,c=l.model,s=o.byId[e];"Component"in s||s.page;return a.createElement(a.Fragment,null,a.createElement(h.a,{activeTabId:e,onSelect:t._handleSelect,tabs:o}),a.createElement(f.a,{className:w.scrollable,onScroll:t._handleScroll},"Component"in s&&a.createElement(s.Component,{source:i,model:c})))}
},t}(a.PureComponent),C=n("PjdP"),E=n("HfwS"),S=n("HGyE"),_=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return Object(r.c)(t,e),t.prototype.render=function(){var e=this.props,t=e.input,n=e.value,l=e.onChange,o=e.onBlur,i=e.onKeyDown,c=t.options.reduce((function(e,t){return e[t]="NONE"===t?window.t("Default"):t,e}),{}),s=Object(r.a)(Object(r.a)({},t),{optionsTitles:c});return a.createElement(S.b,{input:s,value:n,onChange:l,onBlur:o,onKeyDown:i})},t}(a.PureComponent),P=Object(E.a)(_),O=n("h5Dg"),x=n("rJEJ"),j=n("XDrA"),k=n("+8gn"),L=n("Q+1u"),N=(n("HbRj"),a.createContext(null)),T=window.t("{currency} per order"),M=window.t("{currency} per contract"),I=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return Object(r.c)(t,e),t.prototype.render=function(){var e,t=this.props.input,n=Object(i.ensureNotNull)(this.context),l=((e={}).percent="%",e.cash_per_order=T.format({currency:n}),e.cash_per_contract=M.format({currency:n}),e),o=Object(r.a)(Object(r.a)({},t),{optionsTitles:l});return a.createElement(S.a,{input:o})},t.contextType=N,t}(a.PureComponent),V=window.t("Contracts"),R=window.t("% of equity"),z=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return Object(r.c)(t,e),t.prototype.render=function(){var e,t=this.props.input,n=Object(i.ensureNotNull)(this.context),l=((e={}).fixed=V,e.cash_per_order=n,e.percent_of_equity=R,e),o=Object(r.a)(Object(r.a)({},t),{optionsTitles:l});return a.createElement(S.a,{input:o})},t.contextType=N,t}(a.PureComponent),D=n("V1YL"),B=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return Object(r.c)(t,e),t.prototype.render=function(){var e=this.props.inputs;return a.createElement(L.a,null,a.createElement(x.a,{label:window.t("Initial capital")},a.createElement(C.a,{input:e.initial_capital})),a.createElement(x.a,{label:window.t("Base currency")},a.createElement(P,{input:e.currency})),a.createElement(x.a,{label:window.t("Order size"),labelAlign:"adaptive"},a.createElement(j.a,null,a.createElement(C.a,{input:e.default_qty_value}),a.createElement(z,{input:e.default_qty_type}))),a.createElement(x.a,{label:window.t("Pyramiding")},a.createElement("span",null,a.createElement(C.a,{input:e.pyramiding})),a.createElement("span",{className:D.descriptionCell},window.t("orders",{context:"Pyramiding: count orders"}))),a.createElement(L.a.Separator,null),a.createElement(x.a,{label:window.t("Commission"),labelAlign:"adaptive"},a.createElement(j.a,null,a.createElement(C.a,{input:e.commission_value}),a.createElement(I,{input:e.commission_type}))),a.createElement(x.a,{label:window.t("Verify Price for Limit Orders")},a.createElement("span",null,a.createElement(C.a,{input:e.backtest_fill_limits_assumption})),a.createElement("span",{className:D.descriptionCell},window.t("ticks",{context:"slippage ... ticks"}))),a.createElement(x.a,{label:window.t("Slippage")},a.createElement("span",null,a.createElement(C.a,{input:e.slippage})),a.createElement("span",{className:D.descriptionCell},window.t("ticks",{
context:"slippage ... ticks"}))),a.createElement(L.a.Separator,null),a.createElement(x.a,{label:window.t("Recalculate"),labelAlign:"top"},a.createElement("div",null,a.createElement("div",{className:D.recalculateCheckbox},a.createElement(O.a,{label:window.t("After Order is Filled"),input:e.calc_on_order_fills})),a.createElement("div",{className:D.recalculateCheckbox},a.createElement(O.a,{label:window.t("On Every Tick"),input:e.calc_on_every_tick})))))},t.contextType=k.b,t}(a.PureComponent);function H(e){var t=e.property,n=e.model,r=e.inputs,l=e.study;return a.createElement(k.a,{property:t.inputs,model:n,study:l},a.createElement(B,{inputs:r}))}var W,A=n("z61+"),F=n("txPx"),G=Object(F.getLogger)("Platform.GUI.PropertyDialog.Indicators.StrategyPage"),U=function(e){function t(t){var n=e.call(this,t)||this;n._handleWatchedDataChange=function(){n.setState({currency:n._getCurrency()})};var r=n.props.source;if(n._source=r,!Object(b.isStudy)(n._source))throw new TypeError("Strategy page works only for study.");n._properties=r.properties();var l=r.metaInfo(),a=new A.a(l);return n._inputs=a.getStrategyProperties(),n.state={currency:n._getCurrency()},n}return Object(r.c)(t,e),t.prototype.componentDidMount=function(){this._source.watchedData.subscribe(this._handleWatchedDataChange)},t.prototype.componentWillUnmount=function(){this._source.watchedData.unsubscribe(this._handleWatchedDataChange)},t.prototype.render=function(){return a.createElement(N.Provider,{value:this.state.currency},a.createElement(H,{inputs:this._inputs,property:this._properties,model:this.props.model,study:this.props.source}))},t.prototype._getCurrency=function(){var e=this._source.reportData();return null===e||void 0===e.currency?(void 0!==this.state&&null===this.state.currency||G.logWarn("Can't obtain currency from strategy report"),null):e.currency},t}(a.PureComponent),Y=n("5Ssy"),q=function(e){function t(t){var n=e.call(this,t)||this;return n._properties=n.props.source.properties(),n._inputs=new A.a(n.props.source.metaInfo()).getUserEditableInputs(),n}return Object(r.c)(t,e),t.prototype.render=function(){return a.createElement(Y.a,{property:this._properties,model:this.props.model,study:this.props.source,inputs:this._inputs})},t}(a.PureComponent),Q=n("23IT"),K=n("0YCj"),J=n.n(K),X=n("Z1Tk"),Z=n("S0KV"),$=window.t("Change Visibility"),ee=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t._onChange=function(e){var n=t.context.setValue,r=t.props.visible;r&&Object(Z.b)(r,(function(t){return n(t,e,$)}))},t}return Object(r.c)(t,e),t.prototype.render=function(){var e=this.props,t=e.id,n=e.title,r=e.visible,l=e.disabled,o=Object(c.clean)(window.t(n,{context:"input"}),!0);return a.createElement(O.b,{label:o,disabled:l,input:{id:t,type:"bool",defval:!0,name:"visible"},value:!r||Object(Z.a)(r),onChange:this._onChange})},t.contextType=X.b,t
}(a.PureComponent),te=n("KKsp"),ne=n("MB0Y"),re=n("CHgb"),le=n("xHjM"),ae=n("/YRR"),oe=n("rlj/"),ie=n("ZtdB"),ce=n("D2im"),se=n("tH7p"),ue=n("tQCG"),pe=n("9FXF"),de=n("sPU+"),he=((W={})[Q.LineStudyPlotStyle.Line]={type:Q.LineStudyPlotStyle.Line,order:0,icon:le,label:window.t("Line")},W[Q.LineStudyPlotStyle.LineWithBreaks]={type:Q.LineStudyPlotStyle.LineWithBreaks,order:1,icon:ae,label:window.t("Line With Breaks")},W[Q.LineStudyPlotStyle.StepLine]={type:Q.LineStudyPlotStyle.StepLine,order:2,icon:oe,label:window.t("Step Line")},W[Q.LineStudyPlotStyle.Histogram]={type:Q.LineStudyPlotStyle.Histogram,order:3,icon:ie,label:window.t("Histogram")},W[Q.LineStudyPlotStyle.Cross]={type:Q.LineStudyPlotStyle.Cross,order:4,icon:ce,label:window.t("Cross",{context:"chart_type"})},W[Q.LineStudyPlotStyle.Area]={type:Q.LineStudyPlotStyle.Area,order:5,icon:se,label:window.t("Area")},W[Q.LineStudyPlotStyle.AreaWithBreaks]={type:Q.LineStudyPlotStyle.AreaWithBreaks,order:6,icon:ue,label:window.t("Area With Breaks")},W[Q.LineStudyPlotStyle.Columns]={type:Q.LineStudyPlotStyle.Columns,order:7,icon:pe,label:window.t("Columns")},W[Q.LineStudyPlotStyle.Circles]={type:Q.LineStudyPlotStyle.Circles,order:8,icon:de,label:window.t("Circles")},W),me=Object.values(he).sort((function(e,t){return e.order-t.order})).map((function(e){return{value:e.type,selectedContent:o.a.createElement(re.a,{icon:e.icon}),content:o.a.createElement(re.b,{icon:e.icon,label:e.label})}})),ve=window.t("Price Line"),ye=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return Object(r.c)(t,e),t.prototype.render=function(){var e=this.props,t=e.plotType,n=e.className,l=e.priceLine,a=e.plotTypeChange,i=e.priceLineChange,c=e.disabled,s={readonly:!0,content:o.a.createElement(o.a.Fragment,null,o.a.createElement(ne.b,{id:"PlotTypePriceLineSwitch",checked:l,label:ve,preventLabelHighlight:!0,value:"priceLineSwitcher",onChange:i}),o.a.createElement(te.a,null))};return o.a.createElement(re.c,{disabled:c,className:n,hideArrowButton:!0,items:Object(r.f)([s],me),value:t,onChange:a})},t}(o.a.PureComponent),fe=n("lkVX"),be=n("wwEg"),we=window.t("Change Plot Type"),ge=window.t("Change Price Line"),Ce=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t._onPlotTypeChange=function(e){var n=t.context.setValue,r=t.props.styleProp.plottype;r&&n(r,e,we)},t._onPriceLineChange=function(e){var n=t.context.setValue,r=t.props.styleProp.trackPrice;r&&n(r,e,ge)},t}return Object(r.c)(t,e),t.prototype.render=function(){var e=this.props,t=e.paletteColor,n=e.paletteColorProps,r=e.styleProp,l=e.isLine,o=e.hasPlotTypeSelect,i=e.grouped,c=n.childs();return a.createElement(x.a,{grouped:i,label:a.createElement("div",{className:be.childRowContainer},window.t(t.name,{context:"input"}))},a.createElement(fe.a,{disabled:!r.visible.value(),color:c.color,transparency:r.transparency,thickness:l?c.width:void 0,isPaletteColor:!0}),l&&o&&r.plottype&&r.trackPrice?a.createElement(ye,{disabled:!r.visible.value(),className:be.smallStyleControl,plotType:r.plottype.value(),
priceLine:r.trackPrice.value(),plotTypeChange:this._onPlotTypeChange,priceLineChange:this._onPriceLineChange}):null)},t.contextType=X.b,t}(a.PureComponent);var Ee=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return Object(r.c)(t,e),t.prototype.render=function(){var e=this.props,t=e.plot,n=e.area,r=e.palette,l=e.paletteProps,o=e.hideVisibilitySwitch,c=e.styleProp,s=t?t.id:Object(i.ensureDefined)(n).id,u=!s.startsWith("fill")&&t&&Object(Q.isLinePlot)(t);return a.createElement(a.Fragment,null,!o&&a.createElement(L.a.Row,null,a.createElement(L.a.Cell,{placement:"first",colSpan:2,grouped:!0},a.createElement(ee,{id:s,title:n?n.title:c.title.value(),visible:c.visible}))),function(e,t,n,r){var l=e.colors,o=t.colors;return Object.keys(l).map((function(e,t){return a.createElement(Ce,{key:e,grouped:!0,paletteColor:Object(i.ensureDefined)(l[e]),paletteColorProps:Object(i.ensureDefined)(o[e]),styleProp:n,isLine:r,hasPlotTypeSelect:0===t})}))}(r,l,c,u),a.createElement(L.a.GroupSeparator,null))},t.contextType=X.b,t}(a.PureComponent),Se=window.t("Change Plot Type"),_e=window.t("Change Price Line"),Pe=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t._onPlotTypeChange=function(e){var n=t.context.setValue,r=t.props.property.plottype;r&&n(r,e,Se)},t._onPriceLineChange=function(e){var n=t.context.setValue,r=t.props.property.trackPrice;r&&n(r,e,_e)},t}return Object(r.c)(t,e),t.prototype.render=function(){var e=this.props,t=e.id,n=e.property,r=n.title,l=n.color,o=n.plottype,i=n.linewidth,c=n.transparency,s=n.trackPrice,u=n.visible;return a.createElement(x.a,{label:a.createElement(ee,{id:t,title:r.value(),visible:u})},a.createElement(fe.a,{disabled:!u.value(),color:l,transparency:c,thickness:i}),a.createElement(ye,{disabled:!u.value(),className:be.smallStyleControl,plotType:o.value(),priceLine:s.value(),plotTypeChange:this._onPlotTypeChange,priceLineChange:this._onPriceLineChange}))},t.contextType=X.b,t}(a.PureComponent),Oe=a.createContext(null),xe=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return Object(r.c)(t,e),t.prototype.render=function(){var e=this.props,t=e.id,n=e.property,r=n.colorup,l=n.colordown,o=n.transparency,c=n.visible;return a.createElement(Oe.Consumer,null,(function(e){return a.createElement(x.a,{label:a.createElement(ee,{id:t,title:je(Object(i.ensureNotNull)(e),t),visible:c})},a.createElement(fe.a,{disabled:!c.value(),color:r,transparency:o}),a.createElement("span",{className:be.additionalSelect},a.createElement(fe.a,{disabled:!c.value(),color:l,transparency:o})))}))},t.contextType=X.b,t}(a.PureComponent);function je(e,t){var n=Object(i.ensureDefined)(e.metaInfo().styles),r=Object(i.ensureDefined)(n[t]).title;return Object(i.ensureDefined)(r)}var ke,Le,Ne=n("/SnT"),Te=n.n(Ne),Me=n("TSYQ"),Ie=n.n(Me),Ve=n("3G1X"),Re=n("H172"),ze=n("972a"),De=((ke={})[ze.MarkLocation.AboveBar]={value:ze.MarkLocation.AboveBar,content:window.t("Above Bar"),order:0},ke[ze.MarkLocation.BelowBar]={value:ze.MarkLocation.BelowBar,content:window.t("Below Bar"),
order:1},ke[ze.MarkLocation.Top]={value:ze.MarkLocation.Top,content:window.t("Top"),order:2},ke[ze.MarkLocation.Bottom]={value:ze.MarkLocation.Bottom,content:window.t("Bottom"),order:3},ke[ze.MarkLocation.Absolute]={value:ze.MarkLocation.Absolute,content:window.t("Absolute"),order:4},ke),Be=Object.values(De).sort((function(e,t){return e.order-t.order})),He=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return Object(r.c)(t,e),t.prototype.render=function(){var e=this.props,t=e.shapeLocation,n=e.className,r=e.menuItemClassName,l=e.shapeLocationChange,o=e.disabled;return a.createElement(Re.a,{disabled:o,className:n,menuItemClassName:r,items:Be,value:t,onChange:l})},t}(a.PureComponent),We=window.t("Change Char"),Ae=window.t("Change Location"),Fe=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t._onCharChange=function(e){var n=t.context.setValue,r=e.currentTarget.value.trim(),l=Te()(r),a=0===l.length?"":l[l.length-1];n(t.props.property.char,a,We)},t._onLocationChange=function(e){(0,t.context.setValue)(t.props.property.location,e,Ae)},t}return Object(r.c)(t,e),t.prototype.render=function(){var e=this.props,t=e.id,n=e.property,r=n.title,l=n.color,o=n.transparency,i=n.char,c=n.location,s=n.visible,u=e.hasPalette;return a.createElement(x.a,{grouped:u,label:a.createElement(ee,{id:t,title:r.value(),visible:s})},!u&&a.createElement(fe.a,{disabled:!s.value(),color:l,transparency:o}),a.createElement(Ve.a,{disabled:!s.value(),className:be.smallStyleControl,value:i.value(),onChange:this._onCharChange}),a.createElement(He,{disabled:!s.value(),className:Me(be.defaultSelect,be.additionalSelect),menuItemClassName:be.defaultSelectItem,shapeLocation:c.value(),shapeLocationChange:this._onLocationChange}))},t.contextType=X.b,t}(a.PureComponent),Ge=n("Nu4p"),Ue=n("4Njr"),Ye=n("lOpG"),qe=n("br6c"),Qe=n("m+Gx"),Ke=n("01Ho"),Je=n("4ZyK"),Xe=n("kMtk"),Ze=n("Dj0x"),$e=n("Ly1u"),et=n("leq5"),tt=n("flzi"),nt=n("iB0j"),rt=((Le={}).arrow_down=Ue,Le.arrow_up=Ye,Le.circle=qe,Le.cross=Qe,Le.diamond=Ke,Le.flag=Je,Le.label_down=Xe,Le.label_up=Ze,Le.square=$e,Le.triangle_down=et,Le.triangle_up=tt,Le.x_cross=nt,Le);function lt(e){return rt[e]}var at=[];Object.keys(Ge.plotShapesData).forEach((function(e){var t=Ge.plotShapesData[e];at.push({value:t.id,selectedContent:o.a.createElement(re.a,{icon:lt(t.icon)}),content:o.a.createElement(re.b,{icon:lt(t.icon),label:t.guiName})})}));var ot=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return Object(r.c)(t,e),t.prototype.render=function(){var e=this.props,t=e.shapeStyleId,n=e.className,r=e.shapeStyleChange,l=e.disabled;return o.a.createElement(re.c,{disabled:l,className:n,hideArrowButton:!0,items:at,value:t,onChange:r})},t}(o.a.PureComponent),it=window.t("Change Shape"),ct=window.t("Change Location"),st=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t._onPlotTypeChange=function(e){(0,t.context.setValue)(t.props.property.plottype,e,it)},t._onLocationChange=function(e){(0,
t.context.setValue)(t.props.property.location,e,ct)},t}return Object(r.c)(t,e),t.prototype.render=function(){var e=this.props,t=e.id,n=e.hasPalette,r=e.property,l=r.title,o=r.color,i=r.transparency,c=r.plottype,s=r.location,u=r.visible;return a.createElement(x.a,{grouped:n,label:a.createElement(ee,{id:t,title:l.value(),visible:u})},!n&&a.createElement(fe.a,{disabled:!u.value(),color:o,transparency:i}),a.createElement(ot,{disabled:!u.value(),className:be.smallStyleControl,shapeStyleId:c.value(),shapeStyleChange:this._onPlotTypeChange}),a.createElement(He,{disabled:!u.value(),className:Me(be.defaultSelect,be.additionalSelect),menuItemClassName:be.defaultSelectItem,shapeLocation:s.value(),shapeLocationChange:this._onLocationChange}))},t.contextType=X.b,t}(a.PureComponent),ut=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return Object(r.c)(t,e),t.prototype.render=function(){var e=this.props,t=e.id,n=e.title,r=e.visible,l=e.color,o=e.transparency,i=e.thickness,c=e.children,s=e.switchable,u=void 0===s||s;return a.createElement(x.a,{label:u?a.createElement(ee,{id:t,title:n,visible:r}):n},a.createElement(fe.a,{disabled:r&&!(Array.isArray(r)?r[0].value():r.value()),color:l,transparency:o,thickness:i}),c)},t.contextType=X.b,t}(a.PureComponent),pt=Object(F.getLogger)("Chart.Study.PropertyPage"),dt=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return Object(r.c)(t,e),t.prototype.render=function(){var e=this.props,t=e.plot,n=e.palette,r=e.paletteProps,l=e.study,o=t.id,c=l.properties().styles[o],s=t.type;if("line"===s||"bar_colorer"===s||"bg_colorer"===s)return n&&r?a.createElement(Ee,{plot:t,palette:n,paletteProps:r,styleProp:c}):a.createElement(Pe,{id:o,property:c});if("arrows"===s)return a.createElement(xe,{id:o,property:c});if("chars"===s||"shapes"===s)return a.createElement(a.Fragment,null,"chars"===s?a.createElement(Fe,{id:o,property:c,hasPalette:Boolean(n)}):a.createElement(st,{id:o,property:c,hasPalette:Boolean(n)}),n&&r&&a.createElement(Ee,{plot:t,palette:n,paletteProps:r,hideVisibilitySwitch:!0,styleProp:c}));if(Object(Q.isOhlcPlot)(t)){var u=t.target,p=Object(i.ensureDefined)(l.metaInfo().defaults.ohlcPlots)[u],d=l.properties().ohlcPlots[u],h=void 0;h=n&&r?a.createElement(Ee,{plot:t,palette:n,paletteProps:r,styleProp:d}):a.createElement(ut,{id:u,title:d.title.value(),color:d.color,visible:d.visible,transparency:d.transparency});var m=void 0;return void 0!==p&&Object(Q.isOhlcPlotStyleCandles)(p)&&(m=a.createElement(a.Fragment,null,a.createElement(ut,{id:u,title:window.t("Wick"),visible:d.drawWick,color:d.wickColor,transparency:d.transparency}),a.createElement(ut,{id:u,title:window.t("Border"),visible:d.drawBorder,color:d.borderColor,transparency:d.transparency}))),a.createElement(a.Fragment,null,h,m)}return pt.logError("Unknown plot type: "+s),null},t}(a.PureComponent),ht=n("YS4w"),mt=n("KacW"),vt=window.t("Change Line Style"),yt=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t._onLineStyleChange=function(e){(0,
t.context.setValue)(t.props.lineStyle,e,vt)},t}return Object(r.c)(t,e),t.prototype.render=function(){var e=this.props,t=e.lineStyle,n=Object(r.e)(e,["lineStyle"]);return o.a.createElement(mt.a,Object(r.a)({},n,{lineStyle:t.value(),lineStyleChange:this._onLineStyleChange}))},t.contextType=X.b,t}(o.a.PureComponent),ft=window.t("Change Value"),bt=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t._onValueChange=function(e){(0,t.context.setValue)(t.props.property.value,e,ft)},t}return Object(r.c)(t,e),t.prototype.render=function(){var e=this.props,t=e.id,n=e.property,r=n.name,l=n.color,o=n.linestyle,i=n.linewidth,c=n.transparency,s=n.value,u=n.visible;return a.createElement(x.a,{labelAlign:"adaptive",label:a.createElement(ee,{id:t,title:r.value(),visible:u})},a.createElement("div",{className:be.block},a.createElement("div",{className:be.group},a.createElement(fe.a,{disabled:!u.value(),color:l,transparency:c,thickness:i}),a.createElement(yt,{disabled:!u.value(),className:be.smallStyleControl,lineStyle:o})),a.createElement("div",{className:Me(be.wrapGroup,be.defaultSelect,be.additionalSelect)},a.createElement(ht.b,{input:{id:"",name:"",type:"float",defval:0},value:s.value(),disabled:!u.value(),onChange:this._onValueChange}))))},t.contextType=X.b,t}(a.PureComponent),wt=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return Object(r.c)(t,e),t.prototype.render=function(){var e=this.props.orders,t=e.visible,n=e.showLabels,r=e.showQty;return a.createElement(a.Fragment,null,a.createElement(L.a.Row,null,a.createElement(L.a.Cell,{placement:"first",colSpan:2},a.createElement(ee,{id:"chart-orders-switch",title:window.t("Trades on Chart"),visible:t}))),a.createElement(L.a.Row,null,a.createElement(L.a.Cell,{placement:"first",colSpan:2},a.createElement(ee,{id:"chart-orders-labels-switch",title:window.t("Signal Labels"),visible:n}))),a.createElement(L.a.Row,null,a.createElement(L.a.Cell,{placement:"first",colSpan:2},a.createElement(ee,{id:"chart-orders-qty-switch",title:window.t("Quantity"),visible:r}))))},t.contextType=X.b,t}(a.PureComponent),gt=n("KG+6"),Ct=n("kk0y"),Et=[{value:gt.a.LeftToRight,content:window.t("Left")},{value:gt.a.RightToLeft,content:window.t("Right")}],St=window.t("Width (% of the Box)"),_t=window.t("Placement"),Pt=window.t("Show Values"),Ot=window.t("Text Color"),xt=window.t("Change Percent Width"),jt=window.t("Change Placement"),kt=window.t("Change Show Values"),Lt=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t._onPercentWidthChange=function(e){(0,t.context.setValue)(t.props.property.childs().percentWidth,e,xt)},t._onPlacementChange=function(e){(0,t.context.setValue)(t.props.property.childs().direction,e,jt)},t._onShowValuesChange=function(e){(0,t.context.setValue)(t.props.property.childs().showValues,e,kt)},t}return Object(r.c)(t,e),t.prototype.render=function(){var e=this.props.property.childs(),t=e.title,n=e.percentWidth,r=e.direction,l=e.showValues,o=e.valuesColor,i=e.visible
;return a.createElement(a.Fragment,null,a.createElement(L.a.Row,null,a.createElement(L.a.Cell,{placement:"first",colSpan:2,grouped:!0},a.createElement(ee,{id:t.value(),title:t.value(),visible:i}))),a.createElement(x.a,{label:a.createElement("div",{className:be.childRowContainer},St),grouped:!0},a.createElement(Ct.b,{input:{id:"",name:"",type:"integer",defval:0},value:n.value(),disabled:!i.value(),onChange:this._onPercentWidthChange})),a.createElement(x.a,{label:a.createElement("div",{className:be.childRowContainer},_t),grouped:!0},a.createElement(Re.a,{disabled:!i.value(),className:be.defaultSelect,menuItemClassName:be.defaultSelectItem,items:Et,value:r.value(),onChange:this._onPlacementChange})),a.createElement(L.a.Row,null,a.createElement(L.a.Cell,{className:be.childRowContainer,placement:"first",colSpan:2,grouped:!0},a.createElement(O.b,{label:Pt,input:{id:t.value()+"_showValues",type:"bool",defval:!0,name:"visible"},value:!l||l.value(),disabled:!i.value(),onChange:this._onShowValuesChange}))),a.createElement(x.a,{label:a.createElement("div",{className:be.childRowContainer},Ot),grouped:!0},a.createElement(fe.a,{disabled:i&&!i.value(),color:o})),this._renderColors(),a.createElement(L.a.GroupSeparator,null))},t.prototype._renderColors=function(){var e=this.props.property.childs(),t=e.colors,n=e.titles,r=e.transparencies,l=e.visible;return t.childNames().map((function(e){return a.createElement(x.a,{key:e,grouped:!0,label:a.createElement("div",{className:be.childRowContainer},n.childs()[e].value())},a.createElement(fe.a,{disabled:!l.value(),color:t.childs()[e],transparency:r.childs()[e]}))}))},t.contextType=X.b,t}(a.PureComponent),Nt=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return Object(r.c)(t,e),t.prototype.render=function(){var e=this.props.title,t=this.props.property.childs(),n=t.color,r=t.transparency,l=t.width,o=t.style,i=t.visible;return a.createElement(x.a,{label:a.createElement(ee,{id:e.value(),title:e.value(),visible:i})},a.createElement(fe.a,{disabled:!i.value(),color:n,transparency:r,thickness:l}),a.createElement(yt,{disabled:!i.value(),className:be.smallStyleControl,lineStyle:o}))},t.contextType=X.b,t}(a.PureComponent),Tt=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return Object(r.c)(t,e),t.prototype.render=function(){var e=this.props,t=e.graphicType,n=e.study,r=n.metaInfo().graphics,l=n.properties().graphics,o=Object(i.ensureDefined)(r[t]);return Object.keys(o).map((function(e,n){var r=l[t][e];return"horizlines"===t||"vertlines"===t||"lines"===t?a.createElement(Nt,{key:e,title:"lines"===t?r.title:r.name,property:r}):"hhists"===t?a.createElement(Lt,{key:e,property:r}):null}))},t}(a.PureComponent),Mt=window.t("Change Font"),It=["Verdana","Courier New","Times New Roman","Arial"].map((function(e){return{value:e,content:e}})),Vt=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t._onFontFamilyChange=function(e){(0,t.context.setValue)(t.props.fontFamily,e,Mt)},t}return Object(r.c)(t,e),t.prototype.render=function(){
var e=this.props,t=e.fontFamily,n=e.className,r=e.disabled;return a.createElement(Re.a,{disabled:r,className:Ie()(n,be.defaultSelect),menuItemClassName:be.defaultSelectItem,items:It,value:t.value(),onChange:this._onFontFamilyChange})},t.contextType=X.b,t}(a.PureComponent),Rt=n("UXjO"),zt=window.t("Change Font Size"),Dt=[10,11,12,14,16,20,24,28,32,40].map((function(e){return{value:e,title:e.toString()}})),Bt=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t._onFontSizeChange=function(e){(0,t.context.setValue)(t.props.fontSize,e,zt)},t}return Object(r.c)(t,e),t.prototype.render=function(){var e=this.props,t=e.fontSize,n=Object(r.e)(e,["fontSize"]);return a.createElement(Rt.a,Object(r.a)({},n,{fontSizes:Dt,fontSize:t.value(),fontSizeChange:this._onFontSizeChange}))},t.contextType=X.b,t}(a.PureComponent),Ht=window.t("Change Visibility"),Wt=window.t("Labels Font"),At=window.t("Show Labels"),Ft={Traditional:new Set(["S5/R5","S4/R4","S3/R3","S2/R2","S1/R1","P"]),Fibonacci:new Set(["S3/R3","S2/R2","S1/R1","P"]),Woodie:new Set(["S4/R4","S3/R3","S2/R2","S1/R1","P"]),Classic:new Set(["S4/R4","S3/R3","S2/R2","S1/R1","P"]),DM:new Set(["S1/R1","P"]),DeMark:new Set(["S1/R1","P"]),Camarilla:new Set(["S4/R4","S3/R3","S2/R2","S1/R1","P"])},Gt=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t._onChange=function(e){(0,t.context.setValue)(t.props.property.childs().levelsStyle.childs().showLabels,e,Ht)},t}return Object(r.c)(t,e),t.prototype.render=function(){var e=this.props.property.childs(),t=e.font,n=e.fontsize,r=e.levelsStyle;return o.a.createElement(o.a.Fragment,null,o.a.createElement(x.a,{labelAlign:"adaptive",label:o.a.createElement("span",null,Wt)},o.a.createElement("div",{className:be.block},o.a.createElement("div",{className:be.group},o.a.createElement(Vt,{fontFamily:t})),o.a.createElement("div",{className:Me(be.wrapGroup,be.additionalSelect)},o.a.createElement(Bt,{fontSize:n})))),o.a.createElement(L.a.Row,null,o.a.createElement(L.a.Cell,{placement:"first",colSpan:2},o.a.createElement(O.b,{label:At,input:{id:"ShowLabels",type:"bool",defval:!0,name:"visible"},value:r.childs().showLabels.value(),onChange:this._onChange}))),this._renderColors())},t.prototype._renderColors=function(){var e=this.props.property.childs(),t=e.levelsStyle,n=e.inputs,r=t.childs(),l=r.colors,a=r.widths,c=r.visibility,s=n.childs().kind,u=Object(i.ensureDefined)(Ft[s.value()]);return l.childNames().filter((function(e){return u.has(e)})).map((function(e){return o.a.createElement(ut,{key:e,id:e,title:e,color:l.childs()[e],visible:c.childs()[e],thickness:a.childs()[e]})}))},t.contextType=X.b,t}(o.a.PureComponent);var Ut=window.t("Change Visibility"),Yt=window.t("Volume Profile"),qt=window.t("Show Values"),Qt=window.t("Width (% of the Box)"),Kt=window.t("Placement"),Jt=window.t("Developing VA"),Xt=[{value:gt.a.RightToLeft,content:window.t("Right")},{value:gt.a.LeftToRight,content:window.t("Left")}],Zt=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t._onChange=function(e){
t._setHhistsProperty("visible",e)},t._onShowValuesChange=function(e){t._setHhistsProperty("showValues",e)},t._onValueChange=function(e){t._setHhistsProperty("percentWidth",e)},t._onDirectionChange=function(e){t._setHhistsProperty("direction",e)},t}return Object(r.c)(t,e),t.prototype.render=function(){var e=this.props.metaInfo,t=this.props.property.childs(),n=t.graphics,r=t.styles,l=n.childs(),a=l.hhists,c=l.horizlines,s=l.polygons,u=Object.keys(Object(i.ensureDefined)(e.graphics.hhists)),p=a.childs()[u[0]],d=p.childs().visible,h=u.map((function(e){return a.childs()[e].childs().showValues})),m=p.childs().percentWidth,v=p.childs().direction,y=u.map((function(e){return a.childs()[e].childs().valuesColor})),f=c.childs().pocLines,b=r.childs().developingPoc,w=r.childs().developingVAHigh,g=r.childs().developingVALow;return o.a.createElement(o.a.Fragment,null,o.a.createElement(L.a.Row,null,o.a.createElement(L.a.Cell,{placement:"first",colSpan:2},o.a.createElement(O.b,{label:Yt,input:{id:"VolumeProfile",type:"bool",defval:!0,name:"visible"},value:d.value(),onChange:this._onChange}))),o.a.createElement(L.a.Row,null,o.a.createElement(L.a.Cell,{placement:"first"},o.a.createElement("div",{className:be.childRowContainer},o.a.createElement(O.b,{disabled:!d.value(),label:qt,input:{id:"ShowValues",type:"bool",defval:!0,name:"visible"},value:h[0].value(),onChange:this._onShowValuesChange}))),o.a.createElement(L.a.Cell,{placement:"last"},o.a.createElement(fe.a,{disabled:!d.value()||!h[0].value(),color:y}))),o.a.createElement(L.a.Row,null,o.a.createElement(L.a.Cell,{placement:"first"},o.a.createElement("div",{className:be.childRowContainer},Qt)),o.a.createElement(L.a.Cell,{placement:"last"},o.a.createElement(Ct.b,{disabled:!d.value(),input:{id:"",name:"",type:"integer",defval:0},value:m.value(),onChange:this._onValueChange}))),o.a.createElement(L.a.Row,null,o.a.createElement(L.a.Cell,{placement:"first"},o.a.createElement("div",{className:be.childRowContainer},Kt)),o.a.createElement(L.a.Cell,{placement:"last"},o.a.createElement(Re.a,{disabled:!d.value(),className:be.defaultSelect,menuItemClassName:be.defaultSelectItem,items:Xt,value:v.value(),onChange:this._onDirectionChange}))),u.map((function(e){return o.a.createElement(o.a.Fragment,{key:e},a.childs()[e].childs().colors.childNames().map((function(t,n){return o.a.createElement(x.a,{key:n,label:o.a.createElement("div",{className:be.childRowContainer},window.t(a.childs()[e].childs().titles.childs()[n].value()))},o.a.createElement(fe.a,{disabled:!d.value(),color:a.childs()[e].childs().colors.childs()[n],transparency:a.childs()[e].childs().transparencies.childs()[n]}))})))})),o.a.createElement(ut,{id:"pocLines",title:f.childs().name.value(),color:f.childs().color,visible:f.childs().visible,thickness:f.childs().width},o.a.createElement(yt,{disabled:!f.childs().visible.value(),className:be.smallStyleControl,lineStyle:f.childs().style})),b&&o.a.createElement(ut,{id:"developingPoc",title:window.t(b.childs().title.value()),color:b.childs().color,visible:b.childs().visible,
thickness:b.childs().linewidth},o.a.createElement(yt,{disabled:!b.childs().visible.value(),className:be.smallStyleControl,lineStyle:b.childs().linestyle})),w&&g&&o.a.createElement(ut,{id:"developingPoc",title:Jt,color:[w.childs().color,g.childs().color],visible:[w.childs().visible,g.childs().visible],thickness:[w.childs().linewidth,g.childs().linewidth]},o.a.createElement(yt,{disabled:!w.childs().visible.value(),className:be.smallStyleControl,lineStyle:w.childs().linestyle})),s&&o.a.createElement(x.a,{label:o.a.createElement("div",null,window.t(s.childs().histBoxBg.childs().name.value()))},o.a.createElement(fe.a,{color:s.childs().histBoxBg.childs().color,transparency:s.childs().histBoxBg.childs().transparency})))},t.prototype._setHhistsProperty=function(e,t){for(var n=this.context.setValue,r=this.props,l=r.metaInfo,a=r.property.childs().graphics.childs().hhists,o=Object.keys(Object(i.ensureDefined)(l.graphics.hhists)),c=0;c<o.length;c++){var s=a.childs()[o[c]].child(e);n(Object(i.ensureDefined)(s),t,Ut)}},t.contextType=X.b,t}(o.a.PureComponent);for(var $t=n("KJt4"),en={PivotPointsStandard:function(){var e=Object(i.ensureNotNull)(Object(a.useContext)(Oe)).properties();return o.a.createElement(Gt,{property:e})},VbPVisible:function(){var e=Object(i.ensureNotNull)(Object(a.useContext)(Oe)),t=e.metaInfo(),n=e.properties();return o.a.createElement(Zt,{metaInfo:t,property:n})}},tn=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return Object(r.c)(t,e),t.prototype.render=function(){var e=this,t=Object(i.ensureNotNull)(this.context);return a.createElement(Oe.Consumer,null,(function(n){return a.createElement(X.a,{property:Object(i.ensureNotNull)(n).properties(),model:t},a.createElement(L.a,null,e._renderCustomContent(Object(i.ensureNotNull)(n).metaInfo().shortId)))}))},t.prototype._renderCustomContent=function(e){if(e in en){var t=en[e];return a.createElement(t,null)}return null},t.contextType=$t.a,t}(a.PureComponent),nn=n("Ecpn"),rn=window.t("Default"),ln=window.t("Precision"),an=window.t("Change Precision"),on=[{value:"default",content:rn}],cn=0;cn<=8;cn++)on.push({value:cn,content:cn.toString()});for(var sn=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t._onChange=function(e){(0,t.context.setValue)(t.props.precision,e,an)},t}return Object(r.c)(t,e),t.prototype.render=function(){var e=this.props.precision;return a.createElement(x.a,{label:ln},a.createElement(Re.a,{className:be.defaultSelect,menuItemClassName:be.defaultSelectItem,items:on,value:e.value(),onChange:this._onChange}))},t.contextType=X.b,t}(a.PureComponent),un=window.t("Default"),pn=window.t("Override Min Tick"),dn=window.t("Change Min Tick"),hn=[{priceScale:1,minMove:1,frac:!1},{priceScale:10,minMove:1,frac:!1},{priceScale:100,minMove:1,frac:!1},{priceScale:1e3,minMove:1,frac:!1},{priceScale:1e4,minMove:1,frac:!1},{priceScale:1e5,minMove:1,frac:!1},{priceScale:1e6,minMove:1,frac:!1},{priceScale:1e7,minMove:1,frac:!1},{priceScale:1e8,minMove:1,frac:!1},{priceScale:2,minMove:1,frac:!0},{priceScale:4,minMove:1,
frac:!0},{priceScale:8,minMove:1,frac:!0},{priceScale:16,minMove:1,frac:!0},{priceScale:32,minMove:1,frac:!0},{priceScale:64,minMove:1,frac:!0},{priceScale:128,minMove:1,frac:!0},{priceScale:320,minMove:1,frac:!0}],mn=[{value:"default",content:un}],vn=0;vn<hn.length;vn++){var yn=hn[vn];mn.push({value:yn.priceScale+","+yn.minMove+","+yn.frac,content:yn.minMove+"/"+yn.priceScale})}var fn=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t._onChange=function(e){(0,t.context.setValue)(t.props.minTick,e,dn)},t}return Object(r.c)(t,e),t.prototype.render=function(){var e=this.props.minTick;return a.createElement(x.a,{label:pn},a.createElement(Re.a,{className:be.defaultSelect,menuItemClassName:be.defaultSelectItem,items:mn,value:e.value(),onChange:this._onChange}))},t.contextType=X.b,t}(a.PureComponent),bn=n("5YG5"),wn=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t._findPlotPalette=function(e){var n=t.props.study,r=n.metaInfo(),l=Object(i.ensureDefined)(r.palettes);return Object(Q.isBarColorerPlot)(e)||Object(Q.isBgColorerPlot)(e)?{palette:l[e.palette],paletteProps:n.properties().palettes[e.palette]}:t._findPaletteByTargetId(e.id)},t}return Object(r.c)(t,e),t.prototype.render=function(){var e=this,t=this.props.study,n=t.metaInfo();if(Object(nn.a)(n.shortId))return a.createElement(tn,null);var l=new A.a(n).getUserEditablePlots(),o=t.properties(),c=o.bands,s=o.bandsBackground,u=o.areaBackground,p=o.precision,d=o.strategy,h=o.minTick,m=n.filledAreas,v=n.graphics,y=l.length>0,f=Object(bn.a)(t).canOverrideMinTick();return a.createElement(L.a,null,l.map((function(n){var l=Object(Q.isOhlcPlot)(n)?Object(r.a)(Object(r.a)({},n),{id:n.target}):n,o=e._findPlotPalette(l),i=o.palette,c=o.paletteProps;return a.createElement(dt,{key:n.id,plot:n,palette:i,paletteProps:c,study:t})})),c&&c.childNames().map((function(e,t){var n=c.child(e);if(!n.isHidden||!n.isHidden.value())return a.createElement(bt,{key:t,id:n.name.value(),property:n})})),s&&a.createElement(ut,{id:"bandsBackground",title:"Background",visible:s.fillBackground,color:s.backgroundColor,transparency:s.transparency}),u&&a.createElement(ut,{id:"areaBackground",title:"Background",visible:u.fillBackground,color:u.backgroundColor,transparency:u.transparency}),m&&m.map((function(n){if(!n.isHidden){var r=t.properties().filledAreasStyle[n.id],l=n.title||"Background";if(n.palette){var o=e._findPaletteByTargetId(n.id);return a.createElement(Ee,{key:n.id,area:n,palette:Object(i.ensureDefined)(o.palette),paletteProps:Object(i.ensureDefined)(o.paletteProps),styleProp:r})}return a.createElement(ut,{key:n.id,id:n.id,title:l,color:r.color,visible:r.visible,transparency:r.transparency})}})),v&&Object.keys(v).map((function(e,n){return a.createElement(Tt,{key:e,graphicType:e,study:t})})),y&&a.createElement(sn,{precision:p}),f&&a.createElement(fn,{minTick:h}),J.a.isScriptStrategy(n)&&a.createElement(wt,{orders:d.orders}))},t.prototype._findPaletteByTargetId=function(e){
for(var t=this.props.study,n=t.metaInfo(),r=n.plots,l=Object(i.ensureDefined)(n.palettes),a=0,o=r;a<o.length;a++){var c=o[a];if((Object(Q.isColorerPlot)(c)||Object(Q.isOhlcColorerPlot)(c))&&c.target===e)return{palette:l[c.palette],paletteProps:t.properties().palettes[c.palette]}}return{}},t}(a.PureComponent);function gn(e){return Object(X.c)(wn,Object(r.a)(Object(r.a)({},e),{property:e.study.properties()}))}var Cn=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return Object(r.c)(t,e),t.prototype.render=function(){return a.createElement($t.a.Provider,{value:this.props.model},a.createElement(Oe.Provider,{value:this.props.source},a.createElement(gn,{study:this.props.source})))},t}(a.PureComponent),En=n("CW80");n.d(t,"EditObjectDialogRenderer",(function(){return Sn}));var Sn=function(){function e(e,t,n,r){var a=this;this._container=document.createElement("div"),this._isVisible=!1,this._timeout=null,this._handleClose=function(){l.unmountComponentAtNode(a._container),a._isVisible=!1,a._subscription.unsubscribe(a,a._handleCollectionChanged)},this._handleCancel=function(){a._model.undoToCheckpoint(a._checkpoint)},this._handleSubmit=function(){},this._handleActiveTabChanged=function(e){s.setValue(a._activeTabSettingsName(),e)},this._source=e,this._model=t,this._propertyPages=r,this._checkpoint=this._ensureCheckpoint(n),this._subscription=this._model.model().dataSourceCollectionChanged(),this._subscription.subscribe(this,this._handleCollectionChanged)}return e.prototype.hide=function(e){e?this._handleCancel():this._handleSubmit(),this._handleClose()},e.prototype.isVisible=function(){return this._isVisible},e.prototype.focusOnText=function(){},e.prototype.show=function(e){if(void 0===e&&(e={}),u.enabled("property_pages")){var t=this._source.metaInfo();if(Object(En.isLineTool)(this._source)&&Object(d.trackEvent)("GUI","Drawing Properties",this._source.name()),Object(b.isStudy)(this._source)){var n=!this._source.isPine()||this._source.isStandardPine()?t.description:"Custom Pine";Object(d.trackEvent)("GUI","Study Properties",n)}var r={byId:{inputs:{title:window.t("Inputs"),Component:q},style:{title:window.t("Style"),Component:Cn},properties:{title:window.t("Properties"),Component:U}},allIds:[]},o=new A.a(t);o.hasUserEditableInputs()&&r.allIds.push("inputs"),o.hasUserEditableProperties()&&r.allIds.push("properties"),o.hasUserEditableStyles()&&r.allIds.push("style"),r=this._getPagesForStudyLineTool(r);var i=e.initialTab||s.getValue(this._activeTabSettingsName())||"inputs",h=Object(c.clean)(t.shortDescription,!0);0,l.render(a.createElement(g,{title:h,model:this._model,source:this._source,initialActiveTab:r.allIds.includes(i)?i:r.allIds[0],pages:r,onSubmit:this._handleSubmit,onCancel:this._handleCancel,onClose:this._handleClose,onActiveTabChanged:this._handleActiveTabChanged}),this._container),this._isVisible=!0,p.emit("edit_object_dialog",{objectType:"study",scriptTitle:this._source.title()})}},e.prototype._activeTabSettingsName=function(){return"properties_dialog.active_tab.study"},
e.prototype._ensureCheckpoint=function(e){return void 0===e&&(e=this._model.createUndoCheckpoint()),e},e.prototype._getPagesForStudyLineTool=function(e){if(this._propertyPages){var t=this._propertyPages.filter((function(e){return"coordinates"===e.id||"visibility"===e.id})),n={allIds:t.map((function(e){return e.id})),byId:t.reduce((function(e,t){var n;return Object(r.a)(Object(r.a)({},e),((n={})[t.id]={title:t.title,page:t},n))}),{})};return{allIds:Object(r.f)(e.allIds,n.allIds),byId:Object(r.a)(Object(r.a)({},e.byId),n.byId)}}return e},e.prototype._handleCollectionChanged=function(){var e=this;null===this._timeout&&(this._timeout=setTimeout((function(){e._closeDialogIfSourceIsDeleted(),e._timeout=null})))},e.prototype._closeDialogIfSourceIsDeleted=function(){null===this._model.model().dataSourceForId(this._source.id())&&this._handleClose()},e}()},"ZSM+":function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="currentColor"><circle cx="9" cy="14" r="1"/><circle cx="4" cy="14" r="1"/><circle cx="14" cy="14" r="1"/><circle cx="19" cy="14" r="1"/><circle cx="24" cy="14" r="1"/></svg>'},ZtdB:function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28"><path stroke="currentColor" d="M4.5 20v-7m3 7V10m3 10V8m3 12V10m3 10v-8m3 8V10m3 10V8"/></svg>'},bQEj:function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28"><path stroke="currentColor" d="M4 13.5h20"/></svg>'},br6c:function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><circle stroke="currentColor" cx="14" cy="14" r="6.5"/></svg>'},flzi:function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M19.424 16.735l.478.765H8.098l.478-.765 5-8L14 8.057l.424.678 5 8z"/></svg>'},iB0j:function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28"><path stroke="currentColor" d="M9 9l11 11M9 20L20 9"/></svg>'},kMtk:function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M11 8.5h-.5v9.707l.146.147 3 3 .354.353.354-.353 3-3 .146-.147V8.5H11z"/></svg>'},lOpG:function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M14 7l7.424 6.114a.5.5 0 0 1-.318.886H18.5v7h-9v-7H6.894a.5.5 0 0 1-.318-.886L14 7z"/></svg>'},leq5:function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M19.424 11.265l.478-.765H8.098l.478.765 5 8 .424.678.424-.678 5-8z"/></svg>'},"m+Gx":function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28"><path stroke="currentColor" d="M9 14.5h11M14.5 20V9"/></svg>'},"rlj/":function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M5.5 17v5.5h4v-18h4v12h4v-9h4V21"/></svg>'},"sPU+":function(e,t){
e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M10.5 13a2.5 2.5 0 1 1-5 0 2.5 2.5 0 0 1 5 0zM16.5 19a2.5 2.5 0 1 1-5 0 2.5 2.5 0 0 1 5 0zM22.5 8a2.5 2.5 0 1 1-5 0 2.5 2.5 0 0 1 5 0z"/></svg>'},tH7p:function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M5.5 13.52v4.98a1 1 0 0 0 1 1h15a1 1 0 0 0 1-1V8.914c0-.89-1.077-1.337-1.707-.707l-4.66 4.66a1 1 0 0 1-1.332.074l-3.716-2.973a1 1 0 0 0-1.198-.039l-3.96 2.772a1 1 0 0 0-.427.82z"/></svg>'},tQCG:function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M13 11.5l-1.915-1.532a1 1 0 0 0-1.198-.039l-3.96 2.772a1 1 0 0 0-.427.82V18.5a1 1 0 0 0 1 1H13m3.5-7l4.293-4.293c.63-.63 1.707-.184 1.707.707V18.5a1 1 0 0 1-1 1H16"/><path fill="currentColor" d="M14 6h1v2h-1zM14 11h1v2h-1zM14 16h1v2h-1zM14 21h1v2h-1z"/></svg>'},wwEg:function(e,t,n){e.exports={smallStyleControl:"smallStyleControl-1XGqoHgA",additionalSelect:"additionalSelect-1RoWzlTA",childRowContainer:"childRowContainer-_iCnmDPI",defaultSelect:"defaultSelect-DeTJWnAh",defaultSelectItem:"defaultSelectItem-1jN74NCa",block:"block-3Tp_jRog",group:"group-2HQIdqE5",wrapGroup:"wrapGroup-3gHGJIrr",textMarkGraphicBlock:"textMarkGraphicBlock-1nDopgxR",textMarkGraphicWrapGroup:"textMarkGraphicWrapGroup-3QaIoY03"}},xHjM:function(e,t){e.exports='<svg xmlns="http://www.w3.org/2000/svg" width="28" height="28" fill="none"><path stroke="currentColor" d="M5.5 16.5l4.586-4.586a2 2 0 0 1 2.828 0l3.172 3.172a2 2 0 0 0 2.828 0L23.5 10.5"/></svg>'},z1Uu:function(e,t,n){e.exports={defaultSelect:"defaultSelect-2RDyqwu4"}}}]);