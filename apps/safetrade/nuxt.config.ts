import { themeDark, themeLight } from "./app/config/colorThemesNaive";
import { localesMarkets } from "./app/config/lang";

const imagePreload = [
    "/image/home-hero-banner-dark.png",
    "/image/home-hero-banner-light.png",
    "/image/black_logo.png",
    "/image/white_logo.png",
] as const;

export default defineNuxtConfig({
    srcDir: "app",

    naiveui: {
        themeConfig: {
            light: themeLight,
            dark: themeDark,
        },
    },

    site: {
        url: process.env.NUXT_PUBLIC_BASE_URL || "https://safetrade.com",
        name: "Safe Trade",
    },

    future: {
        compatibilityVersion: 4,
    },

    experimental: {
        payloadExtraction: false,
    },

    runtimeConfig: {
        public: {
            p2p: false,
            qrLogin: false,
            phone: false,
            exchangeName: "Safe Trade",
            apiUrl: "http://localhost:8080/api/v2/",
            mainSiteUrl: "https://safetrade.com/",
        },
        backend: {
            apiUrl: process.env.NUXT_CMS_URL || "http://localhost:8055",
        },
    },

    devtools: { enabled: true },

    app: {
        head: {
            link: [
                { rel: "icon", href: "/favicon.ico" },
                ...imagePreload.map((image) => ({ rel: "preload", as: "image" as const, href: image })),
            ],

            meta: [{ name: "keywords", content: "Safetrade, Crypto, Secure Safe, Trade, Crypto Trading, Buy Crypto" }],
        },
    },

    css: ["@unocss/reset/tailwind.css", "assets/styles/base.css", "assets/styles/override-naive.css"],

    modules: [
        "@vueuse/nuxt",
        "@bg-dev/nuxt-naiveui",
        "@nuxt/image",
        "@pinia/nuxt",
        "@unocss/nuxt",
        "@nuxtjs/sitemap",
        "@nuxtjs/robots",
        "@nuxtjs/i18n",
        "@zsmartex/components",
        "@zsmartex/core",
        "@zsmartex/types/nuxt",
        "nuxt-marquee",
        "@nuxtjs/seo",
    ],

    // https://zsmartex.huuhait.me/api/v2/
    // https://safetrade.huuhait.me/api/v2/
    // https://safe.trade/api/v2/
    nitro: {
        devProxy: {
            "/api/v2": {
                target: process.env.NUXT_PUBLIC_API_URL || "https://zsmartex.huuhait.me/api/v2/",
                changeOrigin: true,
                ws: true,
                // headers: {
                //     "user-agent": "safetrademobile3dmd33cl32h2jfw3233812lkdyvdoi3wvl4wo3od9w",
                // },
            },
        },
    },

    image: {
        domains: ["safetrade.com", "localhost:8055"],
        alias: {
            safetrade: "https://safetrade.com",
            directus: process.env.NUXT_CMS_URL + "/assets" || "http://localhost:8055/assets",
        },
        provider: "none",
    },

    compatibilityDate: "2025-03-29",

    i18n: {
        baseUrl: process.env.NUXT_PUBLIC_BASE_URL || "https://safetrade.com",
        locales: localesMarkets,
        defaultLocale: "en",
        detectBrowserLanguage: false,
        langDir: "../locales",
        strategy: "prefix_except_default",
        debug: false,
        lazy: true,
        bundle: {
            optimizeTranslationDirective: false,
        },
    },
});
