<template>
    <NDataTable :columns="columns" :data="transformDataHistory(data)" :loading="loading" :pagination="pagination">
        <template #empty>
            <AppEmptyData />
        </template>
    </NDataTable>
</template>

<script setup lang="ts" generic="T extends Deposit | Withdraw">
import { type Deposit, type Withdraw, WithdrawStatus } from "@zsmartex/types";
import DepositCredited from "../spot-deposit/DepositCredited.vue";
import { NTag, NTooltip, type DataTableBaseColumn, type PaginationProps, type TagProps } from "naive-ui";
import { useTransformData } from "../composables/useTransformData";
import { sliceAddress } from "~/common";
const { transformDataHistory } = useTransformData();
const localePath = useLocalePath();
const {
    data = [],
    loading,
    pagination,
    type,
} = defineProps<{
    data?: T[];
    loading: boolean;
    pagination: PaginationProps | false;
    type: "deposit" | "withdraw";
}>();

const publicStore = usePublicStore();
const { currencies } = publicStore;
const { t } = useI18n();

const columns = computed<DataTableBaseColumn<T>[]>(() => {
    const baseColumns: DataTableBaseColumn<T>[] = [
        {
            title: t("wallet.date"),
            key: "dataCreatedAt",
            minWidth: 190,
        },

        {
            title: t("wallet.currency"),
            key: "currency",
            render(row: T) {
                return (row as Deposit).currency?.toUpperCase() || (row as Withdraw).currency_id?.toUpperCase();
            },
            minWidth: 90,
        },
        {
            title: t("wallet.network"),
            key: "network",
            minWidth: 160,
            maxWidth: 200,
        },
        {
            title: t("wallet.amount"),
            key: "amount",
            minWidth: 90,
        },
        {
            title: t("wallet.fee"),
            key: "fee",
            minWidth: 90,
        },
    ];

    const TxidColumn = [
        {
            title: t("wallet.txid"),
            key: "txid",
            minWidth: 110,
            render(row: T) {
                if ("txid" in row) {
                    const explorerUrl = getExplorerTransaction(row as Deposit);
                    if (explorerUrl) {
                        return h(
                            NTooltip,
                            { trigger: "hover", maxWidth: 600 },
                            {
                                default: () => (row as Deposit).txid,
                                trigger: () =>
                                    h(
                                        "a",
                                        {
                                            href: explorerUrl,
                                            target: "_blank",
                                            class: "text-primary hover:underline",
                                        },
                                        {
                                            default: () => sliceAddress((row as Deposit).txid, 4),
                                        },
                                    ),
                            },
                        );
                    }
                }
                return null;
            },
        },
    ];

    if (type === "deposit") {
        return [
            ...baseColumns,
            {
                title: t("wallet.depositAddress"),
                key: "from_address",
                minWidth: 120,
                render(row: T) {
                    return h(
                        NTooltip,
                        { trigger: "hover", maxWidth: 600 },
                        {
                            default: () => (row as Deposit).from_address,
                            trigger: () => sliceAddress((row as Deposit).from_address, 6),
                        },
                    );
                },
            },
            {
                title: t("wallet.credited"),
                key: "credited",
                minWidth: 110,
                render(row: T) {
                    return h(DepositCredited, { item: row as Deposit });
                },
            },
            ...TxidColumn,
        ];
    }

    if (type === "withdraw") {
        return [
            ...baseColumns,
            {
                title: t("wallet.status"),
                key: "status",
                minWidth: 110,
                render(row: T) {
                    return h(
                        NTag,
                        {
                            type: getStatus((row as Withdraw).status),
                            size: "small",
                            round: true,
                            class: "capitalize",
                        },
                        {
                            default: () => row.status,
                        },
                    );
                },
            },
            {
                title: t("wallet.type"),
                key: "type",
                minWidth: 110,
                render(row: T) {
                    const type = (row as Withdraw).type;
                    return h("span", { class: ` capitalize font-medium` }, type);
                },
            },
            ...TxidColumn,
        ];
    }

    return [...baseColumns, ...TxidColumn];
});

const getExplorerTransaction = (deposit: Deposit): string => {
    const currency = currencies.find((c) => c.id === deposit.currency);
    if (!currency) return "";

    const network = currency.networks.find((n) => n.blockchain_key === deposit.blockchain_key);
    if (!network?.explorer_transaction) return "";

    return network.explorer_transaction.replace("#{txid}", deposit.txid);
};

function getStatus(status: WithdrawStatus): TagProps["type"] {
    const statusMap: Record<WithdrawStatus, TagProps["type"]> = {
        [WithdrawStatus.Succeed]: "success",
        [WithdrawStatus.Accepted]: "success",
        [WithdrawStatus.Processing]: "warning",
        [WithdrawStatus.Confirming]: "warning",
        [WithdrawStatus.UnderReview]: "warning",
        [WithdrawStatus.Prepared]: "warning",
        [WithdrawStatus.Failed]: "error",
        [WithdrawStatus.Rejected]: "error",
        [WithdrawStatus.Canceled]: "error",
        [WithdrawStatus.Banned]: "error",
        [WithdrawStatus.Errored]: "error",
        [WithdrawStatus.Skipped]: "error",
        [WithdrawStatus.ToReject]: "error",
    };
    return statusMap[status] || "default";
}
</script>

<style scoped>
:deep(.n-data-table .n-data-table-wrapper) {
    @apply border-b-none;
}
</style>
