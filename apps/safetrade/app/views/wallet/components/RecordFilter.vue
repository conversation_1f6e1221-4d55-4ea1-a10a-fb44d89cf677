<template>
    <div v-if="dataFilter" class="space-y-4">
        <div class="grid grid-cols-2 gap-6 md:gap-10 md:w-1/2">
            <div class="flex-items gap-2 sm:gap-5">
                <AppText look="helpText" tag="label" class="min-w-10"> {{ $t("wallet.assets") }} </AppText>
                <NSelect
                    clearable
                    :placeholder="$t('wallet.allAssets')"
                    class="size-tiny"
                    :options="optionsCurrencies"
                    v-model:value="dataFilter.currency"
                />
            </div>
            <div class="flex-items gap-2 sm:gap-5">
                <AppText look="helpText" tag="label" class="min-w-10"> {{ $t("wallet.sortBy") }} </AppText>
                <NSelect
                    :placeholder="$t('wallet.sortBy')"
                    class="size-tiny"
                    :options="optionsSortBy"
                    v-model:value="dataFilter.order_by"
                />
            </div>
        </div>

        <div class="flex flex-col sm:flex-row gap-x-10 gap-y-4">
            <div class="flex-items gap-2 sm:gap-5">
                <AppText look="helpText" tag="label" class="min-w-10"> {{ $t("wallet.time") }} </AppText>
                <div class="flex-1 grid grid-cols-3 md:flex-center gap-2">
                    <NButton
                        v-for="option in optionsTime"
                        :key="option.value"
                        size="small"
                        ghost
                        class="min-w-25"
                        :type="dataFilter.time === option.value ? 'primary' : 'default'"
                        @click="handleChangeTime(option.value)"
                    >
                        {{ option.label }}
                    </NButton>
                </div>
            </div>

            <div class="flex-items gap-2 sm:gap-5">
                <AppText look="helpText" tag="label" class="min-w-10"> {{ $t("wallet.date") }} </AppText>
                <NDatePicker
                    class="w-full md:max-w-55"
                    type="daterange"
                    clearable
                    v-model:value="dataFilter.range"
                    :size="sizeDatePicker"
                    :is-date-disabled="disablePreviousDate"
                    @update:value="handleChangeDate"
                >
                </NDatePicker>
                <NButton size="small" secondary type="primary" class="min-w-25"> {{ $t("wallet.export") }} </NButton>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
const sizeDatePicker = ref<any>("tiny");

export type DataFilter = {
    currency: string | null;
    type: string | null;
    time: (typeof optionsTime)[number]["value"] | null;
    range: [number, number] | null;
    order_by: string | null;
};

const dataFilter = defineModel<DataFilter>({
    default: {
        currency: null,
        type: "all",
        time: "all",
        range: [],
        order_by: "created_at",
    },
});

const publicStore = usePublicStore();
const { currencies } = publicStore;

const optionsCurrencies = computed(() => {
    return currencies.map((c) => ({ label: `${c.id.toUpperCase()} - ${c.name}`, value: c.id }));
});

const { t } = useI18n();

const optionsSortBy = [
    { label: t("wallet.createdAt"), value: "created_at" },
    { label: t("wallet.amount"), value: "amount" },
    { label: t("wallet.fee"), value: "fee" },
];

const optionsTime = [
    { label: "All", value: "all" },
    { label: "7d", value: "7d" },
    { label: "30d", value: "30d" },
];

const handleChangeTime = (value: string) => {
    dataFilter.value.time = value;

    if (value === "all") {
        dataFilter.value.range = null;
        return;
    }

    const NUMBER_OF_DAYS = {
        "7d": 7 * 1000 * 60 * 60 * 24,
        "30d": 30 * 1000 * 60 * 60 * 24,
    };
    const numberOfDays = NUMBER_OF_DAYS[value as keyof typeof NUMBER_OF_DAYS];
    dataFilter.value.range = [Date.now() - numberOfDays, Date.now()];
};

const handleChangeDate = (value: [number, number]) => {
    dataFilter.value.range = value;
};

const disablePreviousDate = (ts: number) => {
    return ts > Date.now();
};
</script>

<style scoped>
:deep(.n-data-table .n-data-table-wrapper) {
    @apply border-b-none;
}

:deep(.n-button.n-button--primary-type.n-button--small-type.n-button--ghost),
:deep(.n-button.n-button--default-type.n-button--small-type.n-button--ghost) {
    --n-font-weight: 400 !important;
    --n-font-size: 12px !important;
}
</style>
