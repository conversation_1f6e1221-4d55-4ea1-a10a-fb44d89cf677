<template>
    <div class="rounded-2xl bg-bg-2 p-6 sm:p-8 max-w-110 w-full space-y-8">
        <div class="space-y-3">
            <div class="flex-items gap-2 text-text-heading">
                <span class="size-6 text-text-secondary i-solar:shield-keyhole-bold-duotone"></span>
                <AppText look="heading5" tag="h2"> {{ $t("wallet.verifyOtp") }} </AppText>
                <div
                    @click="emit('onClose')"
                    class="i-custom-close size-6 ml-auto cursor-pointer text-text-secondary hover:text-text-heading duration-100"
                ></div>
            </div>
            <AppText look="body2SmRegular" tag="p" class="text-text-secondary">
                {{ $t("wallet.verifyOtpDescription") }}
            </AppText>
        </div>

        <NForm
            ref="formRefVerifyCode"
            :rules="{ email_code: ruleEmailCode, otp_code: ruleOtpCode }"
            :model="{ email_code: dataForm.email_code, otp_code: dataForm.otp_code }"
            :disabled="loading"
            @submit.prevent="handleSubmitVerifyCode"
        >
            <NFormItem :label="$t('wallet.enterEmailCode')" path="email_code" required>
                <NInput
                    clearable
                    size="large"
                    type="text"
                    name="email_code"
                    :placeholder="$t('wallet.enterVerificationCode')"
                    @update-value="dataForm.email_code = $event"
                >
                    <template #suffix>
                        <NButton
                            size="small"
                            text
                            @click="generateWithdrawalCode('email')"
                            :disabled="isDelaying"
                            class="!pr-0 !pl-2 ml-2"
                            :class="{ '!text-primary': !isDelaying }"
                        >
                            {{ isDelaying ? t("wallet.resendCode", { time: remainingTime }) : t("wallet.sendCode") }}
                        </NButton>
                    </template>
                </NInput>
            </NFormItem>

            <NFormItem :label="$t('wallet.enterCodeTwoFactorAuthentication')" path="otp_code" required>
                <AppInputOtp
                    inputmode="numeric"
                    input-type="number"
                    :num-inputs="6"
                    :should-focus-order="true"
                    @update:value="dataForm.otp_code = $event"
                />
            </NFormItem>
            <NButton type="primary" block size="large" class="mt-8" :loading="loading" @click="handleSubmitVerifyCode">
                {{ $t("wallet.verify") }}
            </NButton>
        </NForm>
    </div>
</template>

<script setup lang="ts">
import AppInputOtp from "~/components/AppInputOtp.vue";
import type { WithdrawData } from "../spot-withdraw/WithdrawBaseForm.vue";
import { ruleOtpCode, ruleEmailCode } from "@/common/rules";

const { handleError } = useErrorHandler();
const { dataWithdraw, currencyId, withdrawAddress, loading, typeWithdraw } = defineProps<{
    currencyId: string;
    withdrawAddress: string | null;
    loading: boolean;
    typeWithdraw: "internal" | "normal";
    dataWithdraw: WithdrawData;
}>();

const { t } = useI18n();

const emit = defineEmits<{
    onSubmit: [];
    onClose: [];
}>();

const formRefVerifyCode = ref<HTMLFormElement | null>(null);
const message = useMessage();

const { GenerateWithdrawalCode, GenerateInternalTransferCode } = useTradeStore();
const { remainingTime, isDelaying, startDelay, clearDelay } = useTimeLeft();

const dataForm = defineModel<Pick<WithdrawData, "otp_code" | "email_code">>({ required: true });

async function generateWithdrawalCode(type: "email" | "phone") {
    clearDelay();
    const { account, amount, network } = dataWithdraw;

    try {
        if (typeWithdraw === "internal" && account && amount) {
            await GenerateInternalTransferCode(type, account, currencyId, Number(amount));
        }

        if (typeWithdraw === "normal" && withdrawAddress && network) {
            await GenerateWithdrawalCode(type, withdrawAddress, currencyId, network, Number(amount));
        }

        startDelay(60);
        message.success(t("wallet.notificationCodeSent"));
    } catch (error: any) {
        handleError(error);
    }
}

async function handleSubmitVerifyCode() {
    await formRefVerifyCode.value?.validate(async (errors: any) => {
        if (errors) return;
        emit("onSubmit");
    });
}
</script>
