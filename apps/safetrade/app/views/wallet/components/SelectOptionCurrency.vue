<template>
    <span class="flex-items gap-2 w-full min-h-11">
        <AppImage
            :width="36"
            :height="36"
            :src="option?.icon_url"
            loading="lazy"
            class="size-8 rounded-full border border-bg-4 object-cover"
        />
        <div class="flex-1 flex-items gap-2">
            <p class="font-semibold uppercase">{{ option.id }}</p>
            <p class="text-text-secondary text-sm">{{ option?.name }}</p>
        </div>
    </span>
</template>

<script setup lang="ts">
import type { Currency } from "@zsmartex/types";

defineProps<{
    option: Currency;
}>();
</script>
