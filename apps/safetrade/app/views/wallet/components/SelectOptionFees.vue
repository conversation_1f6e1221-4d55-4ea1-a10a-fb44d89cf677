<template>
    <span class="flex-items gap-2 w-full min-h-11">
        <p>{{ option?.label }}</p>
        <p class="text-text-secondary text-sm">
            {{ $t("wallet.txFee") }} {{ option.withdraw_fee }} {{ option.currency_id?.toUpperCase() }}
        </p>
    </span>
</template>

<script setup lang="ts">
import type { CurrencyNetwork } from "@zsmartex/types";

defineProps<{
    option: CurrencyNetwork & { label: string; value: string };
}>();
</script>
