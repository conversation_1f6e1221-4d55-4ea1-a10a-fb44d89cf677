<template>
    <div>
        <div class="n-form-item">
            <NInput v-model:value="inputText" type="text" size="small" placeholder="Search" clearable>
                <template #prefix>
                    <span class="i-solar:magnifer-linear text-text-secondary mr-2 size-4.5"></span>
                </template>
            </NInput>
        </div>
        <NTabs
            animated
            v-model:value="tabSelectedUnit"
            default-value="all"
            size="small"
            type="line"
            :bar-width="20"
            class="mt-2 mb-1"
        >
            <NTab v-for="tab in tabsFilterCoins" :key="tab.name" :name="tab.name" :tab="tab.tab">
                <div class="flex items-center gap-2 px-2">
                    <span v-if="tab.icon" :class="tab.icon" class="size-4" />
                    <span v-if="tab.tab">{{ tab.tab }}</span>
                </div>
            </NTab>
        </NTabs>

        <NDataTable :columns="columnsRef" :data="tickersRender" size="small" :max-height="310" :bordered="false">
            <template #empty>
                <AppEmptyData />
            </template>
        </NDataTable>
    </div>
</template>

<script setup lang="ts">
import type { Ticker } from "@zsmartex/types";
import type { DataTableBaseColumn } from "naive-ui";
import { storeToRefs } from "pinia";
import { ref, computed, h } from "vue";
import AppPercentValue from "~/components/AppPercentValue.vue";
import AppEmptyData from "~/components/AppEmptyData.vue";

const publicStore = usePublicStore();
const tradeStore = useTradeStore();

const message = useMessage();
const { ChangeFavorite } = tradeStore;
const { favorites } = storeToRefs(tradeStore);
const { quote_list, enabledMarkets } = storeToRefs(publicStore);
const { t } = useI18n();
const currentMarketId = computed(() => tradeStore.market_id);

const inputText = ref("");
const emit = defineEmits<{
    onClickRow: [Ticker | undefined];
}>();

const extraQuoteList = computed(() => {
    const extra: string[] = [];
    for (const market of enabledMarkets.value) {
        if (!quote_list.value.includes(market.quote_unit) && !extra.includes(market.quote_unit)) {
            extra.push(market.quote_unit);
        }
    }
    return extra;
});

const tabsFilterCoins = computed(() => [
    {
        tab: "",
        name: "favorites",
        icon: "i-solar:star-bold",
    },
    ...quote_list.value.map((quote) => ({
        tab: quote.toUpperCase(),
        name: quote,
        icon: "",
    })),
    ...extraQuoteList.value.map((quote) => ({
        tab: quote.toUpperCase(),
        name: quote,
        icon: "",
    })),
]);

const tabSelectedUnit = ref("all");

const tickersRender = computed(() => {
    return publicStore.enabledTickers
        .filter((ticker: Ticker) => {
            if (inputText.value) {
                return ticker.market.name.toLocaleUpperCase().includes(inputText.value.toLocaleUpperCase());
            }
            if (tabSelectedUnit.value === "favorites") {
                return favorites.value.includes(ticker.id);
            }
            if (tabSelectedUnit.value === "all") {
                return true;
            }
            return tabSelectedUnit.value?.toLocaleUpperCase() === ticker.market.quote_unit.toLocaleUpperCase();
        })
        .filter((ticker: Ticker) => ticker.market.id !== currentMarketId.value);
});

const columnsRef: DataTableBaseColumn<Ticker>[] = [
    {
        key: "selected",
        width: 20,
        render(row: Ticker) {
            return h("div", {
                class: `i-solar:star-bold size-3 cursor-pointer active:scale-70 duration-100
                ${favorites.value.includes(row.id) ? "text-primary opacity-100" : "opacity-20"}`,
                onClick: () => handleClickStar(row),
            });
        },
    },
    {
        title: t("exchange.currency"),
        key: "name",
        sorter: (row1: Ticker, row2: Ticker) => row1.market.name.localeCompare(row2.market.name),
        cellProps(rowData) {
            return { onClick: () => handleClickRow(rowData) };
        },
    },
    {
        title: t("exchange.price"),
        key: "last",
        align: "right",
        sorter: (row1: Ticker, row2: Ticker) => +row1.last - +row2.last,
        cellProps(rowData) {
            return { onClick: () => handleClickRow(rowData), class: "whitespace-nowrap" };
        },
    },
    {
        title: t("exchange.change"),
        key: "change",
        align: "right",
        sorter: (row1: Ticker, row2: Ticker) => {
            const value1 = row1.price_change_percent.replace("%", "");
            const value2 = row2.price_change_percent.replace("%", "");
            return +value1 - +value2;
        },
        render(row: Ticker) {
            return h(AppPercentValue, { value: row.price_change_percent });
        },
        cellProps(rowData) {
            return { onClick: () => handleClickRow(rowData) };
        },
    },
];

function handleClickStar(coin: Ticker) {
    ChangeFavorite(coin.id);
    message.success(t("exchange.favorite_updated"));
}

function handleClickRow(coin: Ticker) {
    emit("onClickRow", coin);
    useGoToTrade(coin.market);
}
</script>
<style scoped>
:deep(.n-data-table thead tr th:not(:nth-child(2)) .n-data-table-th__title-wrapper) {
    @apply justify-end;
}
</style>
