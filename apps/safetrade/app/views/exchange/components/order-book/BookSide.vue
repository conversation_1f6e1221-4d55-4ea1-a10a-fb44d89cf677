<template>
    <div class="relative">
        <NDataTable
            size="small"
            :bordered="false"
            :single-line="false"
            :data="data"
            :columns="columns"
            :pagination="false"
            :show-header="headEnabled"
        />
    </div>
</template>

<script setup lang="ts">
import { OrderSide } from "@zsmartex/types";
import type { DepthRow } from "@zsmartex/types";
import type { DataTableColumn } from "naive-ui";

const {
    headEnabled,
    reverseColumn,
    reverseData,
    index,
    side,
    count: propsCount,
    dataSource,
} = defineProps<{
    side: OrderSide;
    reverseColumn?: boolean;
    reverseData?: boolean;
    headEnabled?: boolean;
    count: number;
    dataSource: DepthRow[];
    index?: boolean;
}>();

const upColor = "var(--color-success)";
const downColor = "var(--color-error)";

const columns = computed(() => {
    const result: DataTableColumn[] = [
        {
            key: "price",
            title: "Price",
            align: reverseColumn ? "right" : "left",
            className: `text-${side === OrderSide.Buy ? "success" : "error"} p${reverseColumn ? "r" : "l"}-2`,
            render: (row: any) => {
                return h(
                    "span",
                    {
                        class: side === OrderSide.Buy ? "text-success" : "text-error",
                    },
                    row.price,
                );
            },
        },
        {
            key: "amount",
            title: "Amount",
            align: reverseColumn ? "left" : "right",
            render: (row: any) => {
                return h("span", {}, row.amount);
            },
        },
        {
            key: "style",
            render: (row: any) => {
                return h("div", {
                    class: "absolute h-full opacity-15",
                    style: {
                        [reverseColumn ? "right" : "left"]: 0,
                        backgroundColor: side === OrderSide.Buy ? upColor : downColor,
                        width: row.width,
                        height: "20px",
                    },
                });
            },
        },
    ];

    if (index) {
        result.push({
            key: "index",
            title: "Index",
            align: reverseColumn ? "left" : "right",
            width: 50,
            render: (row: any) => {
                return h("span", { class: "text-gray-400" }, row.index);
            },
        });
    }

    if (reverseColumn) {
        result.reverse();
    }

    return result;
});

const data = computed(() => {
    const result: Record<string, any>[] = [];
    let data = [...dataSource].splice(0, propsCount);
    const totalAmount = data.reduce((a, b) => a + b.amount, 0);
    let count = 1;

    if (reverseData) {
        let currentAmount = totalAmount;
        data = data.reverse();
        for (const item of data) {
            currentAmount -= item.amount;
            result.push({
                ...item,
                index: count,
                width: `${Math.round((currentAmount / totalAmount) * 100)}%`,
            });
            count++;
        }
    } else {
        let currentAmount = 0;
        for (const item of data) {
            currentAmount += item.amount;
            result.push({
                ...item,
                index: count,
                width: `${Math.round((currentAmount / totalAmount) * 100)}%`,
            });
            count++;
        }
    }

    const length = result.length;
    for (let i = 0; i < count - length; i++) {
        if (reverseData) {
            result.unshift({
                index: count,
                price: "---",
                amount: "---",
            });
        } else {
            result.push({
                index: count,
                price: "---",
                amount: "---",
            });
        }
        count++;
    }

    return result;
});
</script>
