<template>
    <NDataTable :columns="columns" :data="filteredData" :loading="loading">
        <template #empty>
            <div v-if="!isAuthenticated" class="text-center text-sm text-gray-500">
                <NuxtLink :to="useGetLinkLoginRedirect()" class="text-primary font-medium">
                    {{ $t("orders.login") }}
                </NuxtLink>
                {{ $t("orders.or") }}
                <NuxtLink :to="useGetLinkRegisterRedirect()" class="text-primary font-medium">
                    {{ $t("orders.register") }}
                </NuxtLink>
                {{ $t("orders.toTrade") }}
            </div>
            <AppEmptyData v-else />
        </template>
    </NDataTable>
</template>

<script setup lang="ts">
import type { TypePageBySlug } from "@/views/orders/composables/useOrderTable";
import { useOrderTable } from "@/views/orders/composables/useOrderTable";

const { page: pageProps } = defineProps<{
    page: TypePageBySlug;
}>();

const ordersManagerOpenOrders = useOrdersManager("open_orders");
const ordersManagerOrdersHistory = useOrdersManager("orders_history");
const tradesManager = useTradesManager("trades_history");

const tradeStore = useTradeStore();
const marketId = computed(() => tradeStore.market_id);

const authStore = useUserStore();
const { isAuthenticated } = storeToRefs(authStore);
const { getColumns, transformOrder, transformTrade } = useOrderTable();

const currentManager = computed(() => {
    if (pageProps === "open") return ordersManagerOpenOrders;
    if (pageProps === "history") return ordersManagerOrdersHistory;
    return tradesManager;
});

const columns = computed(() => getColumns(pageProps));
const loading = computed(() => currentManager.value?.config.loading || false);

const transformedData = computed(() => {
    if (!isAuthenticated.value) return [];

    const manager = currentManager.value;
    if (!manager) return [];

    if (pageProps === "trades" && tradesManager) {
        return transformTrade(tradesManager.trades);
    } else if (
        (pageProps === "open" || pageProps === "history") &&
        (ordersManagerOpenOrders || ordersManagerOrdersHistory)
    ) {
        const orders = pageProps === "open" ? ordersManagerOpenOrders?.orders : ordersManagerOrdersHistory?.orders;
        return transformOrder(orders || []);
    }
    return [];
});

const filteredData = computed(() => {
    return transformedData.value.filter((item) => item.marketData?.id === marketId.value);
});

await useLazyAsyncData("layouts_exchange_mind_control", fetchAll, {
    watch: [() => pageProps],
});

function fetchAll() {
    return Promise.all([
        ordersManagerOpenOrders.GetData(),
        ordersManagerOrdersHistory.GetData(),
        tradesManager.GetData(),
    ]);
}
</script>

<style scoped>
.exchange .n-data-table .n-data-table-wrapper,
:deep(.n-data-table .n-data-table-wrapper) {
    @apply border-b-none;
}
</style>
