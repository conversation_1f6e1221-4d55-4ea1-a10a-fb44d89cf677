import type { Market } from "@zsmartex/types";
import { routesName } from "@/config/page";

export function useGoToTrade(market?: Market) {
    if (!market) return;

    const localePath = useLocalePath();
    return navigateTo(
        localePath({
            name: routesName.exchange,
            params: {
                base_unit: market.base_unit.toUpperCase(),
                quote_unit: market.quote_unit.toUpperCase(),
            },
        }),
    );
}

export function useGetLinkTrade(market?: Market) {
    if (!market) return;
    const localePath = useLocalePath();

    return localePath({
        name: routesName.exchange,
        params: {
            base_unit: market.base_unit.toUpperCase(),
            quote_unit: market.quote_unit.toUpperCase(),
        },
    });
}

export function useGetLinkLoginRedirect() {
    const route = useRoute();
    const localePath = useLocalePath();
    const redirect = route.meta?.layout === "auth" ? undefined : route.fullPath;

    return localePath({
        name: routesName.login,
        query: { redirect },
    });
}

export function useGetLinkRegisterRedirect() {
    const route = useRoute();

    const localePath = useLocalePath();
    const redirect = route.meta?.layout === "auth" ? undefined : route.fullPath;

    return localePath({
        name: routesName.register,
        query: { redirect },
    });
}
