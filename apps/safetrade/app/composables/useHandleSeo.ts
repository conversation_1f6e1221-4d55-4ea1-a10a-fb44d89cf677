import { routesName } from "@/config/page";
import type { UseSeoMetaInput } from "@unhead/vue/types";
import type { ExtensionSeoMetadata } from "#shared/types/directus-types";

export function useHandleSeo(seoProps?: MaybeRefOrGetter<ExtensionSeoMetadata | null>) {
    const { t, localeProperties } = useI18n();
    const route = useRoute();
    const localePath = useLocalePath();

    const dataMappingBreadcrumb: Record<string, { breadcrumbs: string[] }> = {
        [routesName.home]: {
            breadcrumbs: [routesName.home],
        },
        [routesName.marketsCoin]: {
            breadcrumbs: [routesName.home, routesName.marketsCoin],
        },
        [routesName.marketsData]: {
            breadcrumbs: [routesName.home, routesName.marketsData],
        },
        [routesName.exchange]: {
            breadcrumbs: [routesName.home, routesName.exchange],
        },
        [routesName.swap]: {
            breadcrumbs: [routesName.home, routesName.swap],
        },
        [routesName.walletOverview]: {
            breadcrumbs: [routesName.home, routesName.walletOverview],
        },
        [routesName.login]: {
            breadcrumbs: [routesName.home, routesName.login],
        },
        [routesName.register]: {
            breadcrumbs: [routesName.home, routesName.register],
        },
        [routesName.accountConfirmCode]: {
            breadcrumbs: [routesName.home, routesName.accountConfirmCode],
        },
    };

    const breadcrumbs = computed(() => {
        const routeName = route.name?.toString().split("___")[0] as keyof typeof dataMappingBreadcrumb;
        const breadcrumbData = dataMappingBreadcrumb[routeName];

        if (!breadcrumbData) {
            return [];
        }

        return breadcrumbData.breadcrumbs.map((item) => {
            return {
                name: t(`breadcrumb.${item}`),
                item: localePath(item),
            };
        });
    });

    const seo = computed<UseSeoMetaInput>(() => {
        const seoPropsValue = toValue(seoProps);

        const pageName = route.name?.toString().split("___")[0] as string;

        const ignorePage = [routesName.exchange, routesName.blogDetail, routesName.announcementsDetail];

        if (ignorePage.includes(pageName)) return {};

        return {
            title: seoPropsValue?.title ?? t(`seo.${pageName}.title`),
            description: seoPropsValue?.meta_description ?? t(`seo.${pageName}.description`),
            ogImage: seoPropsValue?.og_image ?? "https://safetrade.com/image/white_logo.png",
            ogTitle: seoPropsValue?.title ?? t(`seo.${pageName}.title`),
            ogDescription: seoPropsValue?.meta_description ?? t(`seo.${pageName}.description`),
            ogUrl: "https://safetrade.com",
            ogType: "website",
            ogLocale: localeProperties.value.name,
            ogSiteName: "Safe Trade",
            ogImageWidth: 1200,
            keywords: "Safetrade, Crypto, Secure Safe, Trade, Crypto Trading, Buy Crypto",
        };
    });

    useHead({
        title: seo.value.title,
        meta: [{ name: "description", content: seo.value.description }],
    });

    useSeoMeta(seo.value);

    watch(seo, (newVal) => {
        useSeoMeta(newVal);
    });

    defineBreadcrumb({ breadcrumbs: breadcrumbs.value });
}
