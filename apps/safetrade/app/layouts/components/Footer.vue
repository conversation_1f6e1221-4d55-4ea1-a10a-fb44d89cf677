<template>
    <footer>
        <div class="bg-bg-2 py-10">
            <div class="container grid grid-cols-2 sm:grid-cols-3 md:grid-cols-5 md:gap-5 gap-14">
                <div class="col-span-2 sm:col-span-1 md:col-span-2 sm:col-span-3 flex flex-col">
                    <AppLogo />
                    <AppText look="heading5" tag="p" class="mt-6 text-text-secondary">
                        {{ globalData?.title }}
                    </AppText>

                    <ul class="mt-auto pt-10 flex-items gap-3 flex-wrap">
                        <li v-for="item in networks" :key="item.label">
                            <NuxtLink :to="item.url" target="_blank" external>
                                <span
                                    :class="item.icon"
                                    class="size-8 rounded-full hover:opacity-80 duration-200"
                                ></span>
                            </NuxtLink>
                        </li>
                    </ul>
                    <AppText look="helpText" tag="p" class="mt-4 text-text-secondary">
                        {{ globalData?.copyright }}
                    </AppText>
                </div>

                <div v-for="col in dataFooter">
                    <AppText look="body2Semibold" tag="p">{{ $t(col.title) }} </AppText>
                    <ul class="mt-6 space-y-[18px] text-text-secondary">
                        <li v-for="item in col.children">
                            <NuxtLink :to="item.url" class="hover:text-primary duration-200">
                                <AppText look="body2Regular" tag="span">
                                    {{ $t(item.title) }}
                                </AppText>
                            </NuxtLink>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="bg-primary py-4 text-white">
            <div class="container flex-between">
                <ClientOnly>
                    <AppText look="helpText" tag="p"> {{ currentTime }} </AppText>
                </ClientOnly>
            </div>
        </div>
    </footer>
</template>

<script setup lang="ts">
import type { Global, GlobalTranslation } from "#shared/types/directus-types";
const localePath = useLocalePath();
const { localeProperties } = useI18n();

const { data: globalData } = await useLazyFetch<Global & GlobalTranslation>(
    `/api/content/global/singleton/${localeProperties.value.name}`,
    {
        params: { fields: ["*", "translations.*"] },
    },
);

const networks = useNetworks();

watch(
    globalData,
    () => {
        networks.value =
            globalData.value?.socials
                ?.filter((item) => item.social)
                .map((item) => ({
                    icon: `i-logo-${item.social}`,
                    url: item.url,
                    label: item.social,
                })) ?? [];
    },
    { immediate: true },
);

const dataFooter = [
    {
        title: "footer.company",
        children: [
            { title: "footer.contactUs", url: "/contact" },
            { title: "footer.discord", url: "https://discord.com/invite/safetrade" },
            { title: "footer.support", url: "https://support.safetrade.com/hc/en-us" },
            { title: "footer.faq", url: "/support/faq" },
        ],
    },
    {
        title: "footer.exchange",
        children: [
            { title: "footer.exchange", url: localePath({ name: "exchange", params: { base_unit: "BTC", quote_unit: "USDT" } }) },
            { title: "footer.wallets", url: localePath({ name: "wallet-spot-overview" }) },
            { title: "footer.fees", url: localePath({ name: "fees" }) },
            {
                title: "footer.api",
                url: "https://support.safetrade.com/hc/en-us/articles/360002787112-Announcements",
            },
        ],
    },
    {
        title: "footer.helpCenter",
        children: [
            { title: "footer.academy", url: "https://bitcointalk.org/index.php?topic=3240246.0" },
            { title: "footer.terms", url: localePath({ name: "terms" }) },
            { title: "footer.status", url: localePath({ name: "status" }) },
        ],
    },
];

const currentTime = computed(() => {
    return useDateFormat(new Date(), "YYYY-MM-DD HH:mm:ss zzz").value;
});
</script>
