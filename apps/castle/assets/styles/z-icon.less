@import "z-icon-variables";

@font-face {
  font-family: '@{icomoon-font-family}';
  src:  url('@{icomoon-font-path}/@{icomoon-font-family}.eot?csizry');
  src:  url('@{icomoon-font-path}/@{icomoon-font-family}.eot?csizry#iefix') format('embedded-opentype'),
    url('@{icomoon-font-path}/@{icomoon-font-family}.ttf?csizry') format('truetype'),
    url('@{icomoon-font-path}/@{icomoon-font-family}.woff?csizry') format('woff'),
    url('@{icomoon-font-path}/@{icomoon-font-family}.svg?csizry#@{icomoon-font-family}') format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

[class^="z-icon-"], [class*=" z-icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: '@{icomoon-font-family}' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.z-icon-upload {
  &:before {
    content: @z-icon-upload; 
  }
}
.z-icon-maipan {
  &:before {
    content: @z-icon-maipan; 
  }
}
.z-icon-maipan1 {
  &:before {
    content: @z-icon-maipan1; 
  }
}
.z-icon-maimaipan {
  &:before {
    content: @z-icon-maimaipan; 
  }
}
.z-icon-pie-chart {
  &:before {
    content: @z-icon-pie-chart; 
  }
}
.z-icon-minus {
  &:before {
    content: @z-icon-minus; 
  }
}
.z-icon-list {
  &:before {
    content: @z-icon-list; 
  }
}
.z-icon-mixin {
  &:before {
    content: @z-icon-mixin; 
  }
}
.z-icon-telegram {
  &:before {
    content: @z-icon-telegram; 
  }
}
.z-icon-twitter {
  &:before {
    content: @z-icon-twitter; 
  }
}
.z-icon-Instagram {
  &:before {
    content: @z-icon-Instagram; 
  }
}
.z-icon-sidebar {
  &:before {
    content: @z-icon-sidebar; 
  }
}
.z-icon-speaker {
  &:before {
    content: @z-icon-speaker; 
  }
}
.z-icon-skype {
  &:before {
    content: @z-icon-skype; 
  }
}
.z-icon-heart {
  &:before {
    content: @z-icon-heart; 
  }
}
.z-icon-star {
  &:before {
    content: @z-icon-star; 
  }
}
.z-icon-contract {
  &:before {
    content: @z-icon-contract; 
  }
}
.z-icon-visible {
  &:before {
    content: @z-icon-visible; 
  }
}
.z-icon-invisible {
  &:before {
    content: @z-icon-invisible; 
  }
}
.z-icon-home {
  &:before {
    content: @z-icon-home; 
  }
}
.z-icon-mistake {
  &:before {
    content: @z-icon-mistake; 
  }
}
.z-icon-copy {
  &:before {
    content: @z-icon-copy; 
  }
}
.z-icon-question {
  &:before {
    content: @z-icon-question; 
  }
}
.z-icon-edit {
  &:before {
    content: @z-icon-edit; 
  }
}
.z-icon-share {
  &:before {
    content: @z-icon-share; 
  }
}
.z-icon-filter {
  &:before {
    content: @z-icon-filter; 
  }
}
.z-icon-3x {
  &:before {
    content: @z-icon-3x; 
  }
}
.z-icon-plus {
  &:before {
    content: @z-icon-plus; 
  }
}
.z-icon-success {
  &:before {
    content: @z-icon-success; 
  }
}
.z-icon-trend-up {
  &:before {
    content: @z-icon-trend-up; 
  }
}
.z-icon-investment {
  &:before {
    content: @z-icon-investment; 
  }
}
.z-icon-download {
  &:before {
    content: @z-icon-download; 
  }
}
.z-icon-bookmarks {
  &:before {
    content: @z-icon-bookmarks; 
  }
}
.z-icon-facebook {
  &:before {
    content: @z-icon-facebook; 
  }
}
.z-icon-weibo {
  &:before {
    content: @z-icon-weibo; 
  }
}
.z-icon-Rectangle50 {
  &:before {
    content: @z-icon-Rectangle50; 
  }
}
.z-icon-group {
  &:before {
    content: @z-icon-group; 
  }
}
.z-icon-user {
  &:before {
    content: @z-icon-user; 
  }
}
.z-icon-member-3 {
  &:before {
    content: @z-icon-member-3; 
  }
}
.z-icon-paste {
  &:before {
    content: @z-icon-paste; 
  }
}
.z-icon-Rectangle51 {
  &:before {
    content: @z-icon-Rectangle51; 
  }
}
.z-icon-calendar {
  &:before {
    content: @z-icon-calendar; 
  }
}
.z-icon-person {
  &:before {
    content: @z-icon-person; 
  }
}
.z-icon-new {
  &:before {
    content: @z-icon-new; 
  }
}
.z-icon-defi {
  &:before {
    content: @z-icon-defi; 
  }
}
.z-icon-short-btc {
  &:before {
    content: @z-icon-short-btc; 
  }
}
.z-icon-print {
  &:before {
    content: @z-icon-print; 
  }
}
.z-icon-coupon {
  &:before {
    content: @z-icon-coupon; 
  }
}
.z-icon-bar-chart {
  &:before {
    content: @z-icon-bar-chart; 
  }
}
.z-icon-vip {
  &:before {
    content: @z-icon-vip; 
  }
}
.z-icon-bank {
  &:before {
    content: @z-icon-bank; 
  }
}
.z-icon-app-store {
  &:before {
    content: @z-icon-app-store; 
  }
}
.z-icon-Mixin {
  &:before {
    content: @z-icon-Mixin; 
  }
}
.z-icon-medal {
  &:before {
    content: @z-icon-medal; 
  }
}
.z-icon-fund-security {
  &:before {
    content: @z-icon-fund-security; 
  }
}
.z-icon-referral {
  &:before {
    content: @z-icon-referral; 
  }
}
.z-icon-guandian {
  &:before {
    content: @z-icon-guandian; 
  }
}
.z-icon-Info {
  &:before {
    content: @z-icon-Info; 
  }
}
.z-icon-member-2 {
  &:before {
    content: @z-icon-member-2; 
  }
}
.z-icon-cancel {
  &:before {
    content: @z-icon-cancel; 
  }
}
.z-icon-lock {
  &:before {
    content: @z-icon-lock; 
  }
}
.z-icon-check {
  &:before {
    content: @z-icon-check; 
  }
}
.z-icon-add {
  &:before {
    content: @z-icon-add; 
  }
}
.z-icon-rejected {
  &:before {
    content: @z-icon-rejected; 
  }
}
.z-icon-safety {
  &:before {
    content: @z-icon-safety; 
  }
}
.z-icon-transaction {
  &:before {
    content: @z-icon-transaction; 
  }
}
.z-icon-contribute {
  &:before {
    content: @z-icon-contribute; 
  }
}
.z-icon-member-1 {
  &:before {
    content: @z-icon-member-1; 
  }
}
.z-icon-wallet {
  &:before {
    content: @z-icon-wallet; 
  }
}
.z-icon-license {
  &:before {
    content: @z-icon-license; 
  }
}
.z-icon-switch {
  &:before {
    content: @z-icon-switch; 
  }
}
.z-icon-global {
  &:before {
    content: @z-icon-global; 
  }
}
.z-icon-link {
  &:before {
    content: @z-icon-link; 
  }
}
.z-icon-arrow {
  &:before {
    content: @z-icon-arrow; 
  }
}
.z-icon-close {
  &:before {
    content: @z-icon-close; 
  }
}
.z-icon-coingecko {
  &:before {
    content: @z-icon-coingecko; 
  }
}
.z-icon-instagram {
  &:before {
    content: @z-icon-instagram; 
  }
}
.z-icon-medium {
  &:before {
    content: @z-icon-medium; 
  }
}
.z-icon-tiktok {
  &:before {
    content: @z-icon-tiktok; 
  }
}
.z-icon-reddit {
  &:before {
    content: @z-icon-reddit; 
  }
}
.z-icon-youtube {
  &:before {
    content: @z-icon-youtube; 
  }
}
.z-icon-gift {
  &:before {
    content: @z-icon-gift; 
  }
}
.z-icon-system {
  &:before {
    content: @z-icon-system; 
  }
}
.z-icon-setup {
  &:before {
    content: @z-icon-setup; 
  }
}
.z-icon-gour-glass {
  &:before {
    content: @z-icon-gour-glass; 
  }
}
.z-icon-trash {
  &:before {
    content: @z-icon-trash; 
  }
}
.z-icon-clear {
  &:before {
    content: @z-icon-clear; 
  }
}
.z-icon-notify {
  &:before {
    content: @z-icon-notify; 
  }
}
.z-icon-coinmarketcap {
  &:before {
    content: @z-icon-coinmarketcap; 
  }
}
.z-icon-percentage {
  &:before {
    content: @z-icon-percentage; 
  }
}
.z-icon-increase {
  &:before {
    content: @z-icon-increase; 
  }
}
.z-icon-arrow-down {
  &:before {
    content: @z-icon-arrow-down; 
  }
}
.z-icon-arrow-up {
  &:before {
    content: @z-icon-arrow-up; 
  }
}
.z-icon-search {
  &:before {
    content: @z-icon-search; 
  }
}
.z-icon-associate {
  &:before {
    content: @z-icon-associate; 
  }
}
.z-icon-export-data {
  &:before {
    content: @z-icon-export-data; 
  }
}
.z-icon-more {
  &:before {
    content: @z-icon-more; 
  }
}
.z-icon-company {
  &:before {
    content: @z-icon-company; 
  }
}
.z-icon-icon_play {
  &:before {
    content: @z-icon-icon_play; 
  }
}
.z-icon-ic_back {
  &:before {
    content: @z-icon-ic_back; 
  }
}
.z-icon-warn {
  &:before {
    content: @z-icon-warn; 
  }
}
