@import "z-icon";
@import "override";

@font-face {
  font-family: 'URWDIN-Regular';
  src: url('~/assets/fonts/URWDIN-Regular.eot');
  src: local('URW DIN Regular'), local('URWDIN-Regular'),
      url('~/assets/fonts/URWDIN-Regular.eot?#iefix') format('embedded-opentype'),
      url('~/assets/fonts/URWDIN-Regular.woff2') format('woff2'),
      url('~/assets/fonts/URWDIN-Regular.woff') format('woff'),
      url('~/assets/fonts/URWDIN-Regular.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'URWDIN-Medium';
  src: url('~/assets/fonts/URWDIN-Medium.eot');
  src: local('URW DIN Medium'), local('URWDIN-Medium'),
      url('~/assets/fonts/URWDIN-Medium.eot?#iefix') format('embedded-opentype'),
      url('~/assets/fonts/URWDIN-Medium.woff2') format('woff2'),
      url('~/assets/fonts/URWDIN-Medium.woff') format('woff'),
      url('~/assets/fonts/URWDIN-Medium.ttf') format('truetype');
  font-weight: 500;
  font-style: normal;
}

.font-family-regular() {
  font-size: 14px;
  font-family: URWDIN-Regular,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol;
  font-weight: 400;
}

body {
  margin: 0;
  color: @text-color;
  font-size: 14px;

  .font-family-regular;

  button {
    .font-family-regular;

    background-color: white;
    border: none;
  }

  &, * {
    box-sizing: border-box;
  }

  ::-webkit-scrollbar,
  &::-webkit-scrollbar {
    width: 5px;
    height: 0;
  }

  ::-webkit-scrollbar-track-piece,
  &::-webkit-scrollbar-track-piece {
    background-color: transparent;
    border-radius: 6px;
  }

  ::-webkit-scrollbar-track,
  &:-webkit-scrollbar-track {
    background-color: @layout-background-color;
  }

  ::-webkit-scrollbar-thumb,
  &::-webkit-scrollbar-thumb {
    height: 5px;
    background-color: @dropdown-border-color;
    border-radius: 6px;
  }
}

.bold-text {
  font-family: URWDIN-Medium,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol;
  font-weight: 500;
}

.h1, .h2, .h3, .h4, .h5, .h6, h1, h2, h3, h4, h5, h6 {
  margin-bottom: 0.5rem;
  font-family: URWDIN-Medium,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol;
  font-weight: 500;
  line-height: 1.2;
}

a, button {
  cursor: pointer;
}

input {
  border: none;

  &:focus, &:focus-visible {
    box-shadow: none;
    outline: none;
  }
}

*:disabled {
  cursor: not-allowed;
}

.text {
  &-up {
    color: @up-color;
  }

  &-down {
    color: @down-color;
  }

  &-warn {
    color: @warn-color;
  }

  &-left {
    text-align: left;
  }

  &-center {
    text-align: center;
  }

  &-right {
    text-align: right;
  }
}

a {
  color: @primary-color;
  text-decoration: none;
  background-color: transparent;
  outline: none;
  cursor: pointer;
  transition: color 0.3s;

  &:hover {
    color: @blue-color;
  }
}
