import colors from './colors'

const storages: Record<string, any> = {}

if (process.env.STORAGE) {
  storages.cache = {
    driver: 'redis',
    host: process.env.REDIS_HOST,
    port: process.env.REDIS_PORT || 6379,
    username: process.env.REDIS_USERNAME,
    password: process.env.REDIS_PASSWORD,
    db: process.env.REDIS_DB || 0,
  }
}
else {
  storages.cache = {
    driver: 'memory',
  }
}

export default defineNuxtConfig({
  telemetry: false,
  runtimeConfig: {
    public: {
      apiUrl: 'https://safetrade.com/api/v2/',
      mainSiteUrl: 'https://safetrade.com/',
      backupAlertEnabled: true,
    },
    p2p: true,
  },
  imports: {
    // dirs: ['types'],
  },
  app: {
    head: {
      title: 'ZSmartex v3',
      link: [
        { rel: 'icon', href: '/favicon.ico' },
      ],
    },
  },
  modules: [
    '@vueuse/nuxt',
    '@pinia/nuxt',
    '@zsmartex/i18n',
    '@zsmartex/components',
    '@zsmartex/core',
    '@zsmartex/types/nuxt',
    '@unocss/nuxt',
  ],
  vueuse: {
    ssrHandlers: true,
  },
  build: {
    transpile: [
      '@vue/babel-plugin-jsx',
      '@babel/highlight',
    ],
  },
  vite: {
    build: {
      chunkSizeWarningLimit: 1500,
    },
    css: {
      preprocessorOptions: {
        less: {
          lessOptions: {
            javascriptEnabled: true,
          },
          additionalData: (function () {
            let variables = ''

            for (const key in colors) {
              const variable = `@${key}`
              const color = colors[key as keyof typeof colors]

              variables += `${variable}: ${color};\n`
            }

            return variables
          }()),
        },
      },
    },
    server: {
      fs: {
        allow: ['../..'],
      },
    },
  },
  nitro: {
    devStorage: {
      cache: {
        driver: 'memory',
      },
    },
    storage: { ...storages },
    devProxy: {
      '/api/v2': {
        target: process.env.NUXT_PUBLIC_API_URL || 'https://safetrade.com/api/v2/',
        changeOrigin: true,
        ws: true,
        headers: {
          // 'user-agent': 'do98vy39p4vth934vthd9l4ueth9uebhfgff222222_huu',
        },
      },
    },
  },
})
