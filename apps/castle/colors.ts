const cardBackground = '#20283c'
const exchangeGrayColor = '#9eaed6'
const grayColor = '#a9aebb'
const actionColor = '#7387a7'
const headerBackground = '#1c2330'

export default {
  'text-color': '#212f4f',
  'exchange-text-color': '#e1e7ef',
  'white-color': '#e1e7ef',
  'primary-color': '#0095ff',
  'action-color': actionColor,
  'gray-color': '#8490aa',
  'exchange-gray-color': exchangeGrayColor,
  'exchange-error-color': '#fe6262',
  'up-color': '#16c393',
  'down-color': '#ea4d4d',
  'warn-color': '#dea959',
  'error-color': '#fe6262',
  'exchange-border-color': '#314363',
  'exchange-border-error-color': '#fe6262',
  'base-border-color': 'rgba(33,47,79,.1)',
  'placeholder-color': '#515f79',

  'blue-color': '#52a7f8',

  'up-bg-color': 'rgba(23, 67, 78, 0.5)',
  'down-bg-color': 'rgba(69, 46, 67, 0.5)',

  'active-background-color': '#2e3f5f',

  'line-height-base': 1.5,

  'layout-background-color': '#eff2f5',
  'layout-header-background-color': headerBackground,
  'layout-footer-background-color': headerBackground,

  'exchange-layout-background-color': '#141c2a',

  'btn-primary-background': '#1d3863',
  'btn-primary-border-color': '#295e9f',
  'btn-disabled-color': '#e9eff3',

  'exchange-dropdown-background': '#2a344c',
  'exchange-card-background': cardBackground,
  'card-background': '#fff',

  'dropdown-background': '#25344e',
  'dropdown-border-color': '#3b4d6b',
  'dropdown-text-color': '#c6d8f3',

  'sparkline-background': '#213150',

  'tv-color-pane-background': cardBackground,
  'tv-color-toolbar-button-text': grayColor,
  'tv-color-toolbar-button-text-hover': actionColor,
  'tv-color-platform-background': cardBackground,
}
