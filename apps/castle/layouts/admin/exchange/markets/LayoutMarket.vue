<script setup lang="ts">
import type { ZAdminFormColumn } from '~/types'
import { ZAdminFormFieldType } from '~/types'
import Validate from '~/validation/validate'

const props = defineProps<{
  id?: string
  modelValue: Market
}>()

const emit = defineEmits(['update:modelValue'])

const router = useRouter()
const adminStore = useAdminStore()
const menuActionStore = useMenuActionStore()

const market = useVModel(props, 'modelValue', emit)

const pricePrecision = useState(() => (market.value.price_precision || 8))
const amountPrecision = useState(() => (market.value.amount_precision || 8))
const loading = useState(() => false)
const pending = useState(() => false)

const baseCurrencyColumns: ZTableColumn[] = [
  {
    key: 'id',
    scopedSlots: true,
  },
]

const quoteCurrencyColumns: ZTableColumn[] = [
  {
    key: 'id',
    scopedSlots: true,
  },
]

function isUpdateAction() {
  if (props.id) return true
  return false
}

if (isUpdateAction()) {
  menuActionStore.setMenu([
    {
      key: 'refresh',
      title: 'Refresh',
      callback: async () => {
        pending.value = true
        await useAdminStore().FetchMarkets()
        pending.value = false
      },
    },
  ])
}

function changePricePrecision(value: string) {
  pricePrecision.value = Number(value)
}

function changeAmountPrecision(value: string) {
  amountPrecision.value = Number(value)
}

const columns = computed(() => {
  const result: ZAdminFormColumn[] = [
    {
      fields: [
        {
          key: 'name',
          type: ZAdminFormFieldType.Text,
          label: 'Market name',
          scopedSlots: true,
          class: '!w-6/12',
        },
        {
          key: 'state',
          type: ZAdminFormFieldType.Switch,
          label: 'State',
          value: market.value.state === MarketState.Enabled,
          class: '!w-6/12 flex justify-end items-center mt-0!',
        },
        {
          key: 'min_price',
          type: ZAdminFormFieldType.Input,
          label: 'Min price(*)',
          value: market.value.min_price,
          validate: [Validate.required, Validate.double(pricePrecision.value), Validate.lte(0)],
        },
        {
          key: 'price_precision',
          type: ZAdminFormFieldType.Input,
          label: 'Price precision(*)',
          value: market.value.price_precision,
          validate: [Validate.required, Validate.integer, Validate.max(12), Validate.min(0)],
          onInput: changePricePrecision,
        },
        {
          key: 'position',
          type: ZAdminFormFieldType.Input,
          label: 'Position(*)',
          value: market.value.position,
          validate: [Validate.required, Validate.integer, Validate.min(0)],
        },
      ],
    },
    {
      fields: [
        {
          key: 'base_unit',
          type: ZAdminFormFieldType.Select,
          label: 'Base Currency(*)',
          value: market.value.base_unit,
          dataSource: adminStore.currencies,
          search: true,
          scroll: true,
          replaceFunc: (text: string) => text.toUpperCase(),
          columns: baseCurrencyColumns,
          findBy: ['id'],
          valueKey: 'id',
          labelKey: 'id',
          validate: [Validate.required],
        },
        {
          key: 'max_price',
          type: ZAdminFormFieldType.Input,
          label: 'Max Price(*)',
          value: market.value.max_price,
          validate: [Validate.required, Validate.double(pricePrecision.value), Validate.lte(0)],
        },
        {
          key: 'amount_precision',
          type: ZAdminFormFieldType.Input,
          label: 'Amount Precision(*)',
          value: market.value.amount_precision,
          validate: [Validate.required, Validate.integer, Validate.max(12), Validate.min(0)],
          onInput: changeAmountPrecision,
        },
      ],
    },
    {
      fields: [
        {
          key: 'quote_unit',
          type: ZAdminFormFieldType.Select,
          label: 'Quote Currency(*)',
          dataSource: adminStore.currencies,
          value: market.value.quote_unit,
          search: true,
          scroll: true,
          replaceFunc: (text: string) => text.toUpperCase(),
          columns: quoteCurrencyColumns,
          findBy: ['id'],
          valueKey: 'id',
          labelKey: 'id',
          validate: [Validate.required],
        },
        {
          key: 'min_amount',
          type: ZAdminFormFieldType.Input,
          label: 'Min Amount(*)',
          value: market.value.min_amount,
          validate: [Validate.required, Validate.double(amountPrecision.value), Validate.lte(0)],
        },
        {
          key: 'total_precision',
          type: ZAdminFormFieldType.Input,
          label: 'Total Precision(*)',
          value: market.value.total_precision,
          validate: [Validate.required, Validate.integer, Validate.max(8), Validate.min(0)],
        },
      ],
    },
  ]

  if (isUpdateAction()) {
    result[1].fields[0] = {
      key: 'quote_unit',
      type: ZAdminFormFieldType.Text,
      label: 'Quote Currency',
      value: market.value.base_unit.toUpperCase(),
      class: '!w-6/12',
    }

    result[2].fields[0] = {
      key: 'base_unit',
      type: ZAdminFormFieldType.Text,
      label: 'Base Currency',
      value: market.value.quote_unit.toUpperCase(),
      class: '!w-6/12',
    }
  }

  return result
})

async function ActionMarket(market: Market) {
  loading.value = true
  if (isUpdateAction()) {
    await adminStore.UpdateMarket(props.id as string, {
      position: Number(market.position),
      min_price: Number(market.min_price),
      min_amount: Number(market.min_amount),
      amount_precision: Number(market.amount_precision),
      price_precision: Number(market.price_precision),
      total_precision: Number(market.total_precision),
      max_price: Number(market.max_price),
      state: market.state ? MarketState.Enabled : MarketState.Disabled,
    }, () => {
      router.push('/exchange/markets')
    })
  } else {
    await adminStore.CreateMarket({
      base_unit: market.base_unit as string,
      quote_unit: market.quote_unit as string,
      position: Number(market.position),
      min_price: Number(market.min_price),
      min_amount: Number(market.min_amount),
      amount_precision: Number(market.amount_precision),
      price_precision: Number(market.price_precision),
      total_precision: Number(market.total_precision),
      max_price: Number(market.max_price),
      state: market.state ? MarketState.Enabled : MarketState.Disabled,
    }, () => {
      router.push('/exchange/markets')
    })
  }

  loading.value = false
}
</script>

<template>
  <ZCard>
    <ZAdminForm :columns="columns" :loading="loading" :pending="pending" @submit="ActionMarket">
      <template #name="{ columns: c, values }">
        Market name: {{ values[c[1].fields[0].key] ? values[c[1].fields[0].key].toUpperCase() : '' }} / {{ values[c[2].fields[0].key] ? values[c[2].fields[0].key].toUpperCase() : '' }}
      </template>
    </ZAdminForm>
  </ZCard>
</template>

<style lang="less">
.page-dashboard-exchange-markets-action {
  &-dropdown {
    &-item {
      display: block;
      cursor: pointer;
      padding: 8px 12px;
      color: @text-color !important;
      background-color: rgba(@gray-color, 0.1);

      &:hover {
        background-color: white;
      }
    }

    &-item + &-item {
      border-top: 1px solid @base-border-color;
    }
  }

  .z-dropdown {
    &-trigger {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 12px;
      width: 100%;
      height: 38px;
      border: 1px solid rgba(33, 47, 79, 0.1);
      border-radius: 4px;
      transition: all 0.3s;
      &:hover {
        border: 1px solid @primary-color;
      }
    }

    &-triggered {
      border: 1px solid @primary-color;
      box-shadow: 0 0 0 2px rgba(@primary-color, 0.2);
    }

    &-overlay {
      width: 100%;
      background-color: #fff;
      box-shadow: 0 3px 6px -4px #0000001f, 0 6px 16px #00000014, 0 9px 28px 8px #0000000d;
    }
  }

  .z-input {
    background-color: rgba(@gray-color, 0.05);
  }

  .z-button {
    width: 68px;
    height: 32px;
    background-color: @primary-color !important;
    color: white !important;
  }
}
</style>
