<script setup lang="ts">
import type { ZAdminFormColumn } from '~/types'
import Validate from '~/validation/validate'

const emit = defineEmits(['click'])

const visible = ref(false)
const loading = ref(false)
const adminStore = useAdminStore()

function openModal() {
  visible.value = true
}

const markets = adminStore.markets.map((m) => {
  return {
    key: `${m.base_unit.toUpperCase()}/${m.quote_unit.toUpperCase()}`,
    value: m.id,
  }
})

function ReplaceFunc(text: string) {
  return text[0].toUpperCase() + text.slice(1)
}

const columns = computed(() => {
  const result: ZAdminFormColumn[] = [
    {
      fields: [
        {
          key: 'market',
          type: ZAdminFormFieldType.Select,
          label: 'Market(*)',
          dataSource: markets,
          columns: [
            {
              key: 'key',
            },
          ],
          search: true,
          findBy: ['key'],
          valueKey: 'value',
          labelKey: 'key',
          validate: [Validate.required],
        },
        {
          key: 'side',
          type: ZAdminFormFieldType.Select,
          label: 'Side',
          dataSource: [
            {
              value: 'buy',
            },
            {
              value: 'sell',
            },
          ],
          columns: [
            {
              key: 'value',
              scopedSlots: true,
            },
          ],
          search: true,
          findBy: ['value'],
          valueKey: 'value',
          labelKey: 'value',
          replaceFunc: ReplaceFunc,
        },
      ],
    },
  ]

  return result
})

function onClick(params: {
  market: string
  side: string
}) {
  CancelOrders(params.market, params.side)
}

async function CancelOrders(market: string, side: string) {
  loading.value = true

  await adminStore.CancelOrders({
    market,
    side,
  }, () => {
    emit('click', market, side)
  })

  loading.value = false
  visible.value = false
}

defineExpose({
  openModal,
})
</script>

<template>
  <ZModal
    v-model="visible"
    class="layouts-admin-exchange-cancels-modal"
    title="Cancel Orders"
  >
    <ZAdminForm :columns="columns" :loading="loading" @submit="(item: any) => onClick(item)" />
  </ZModal>
</template>

<style lang="less">
.layouts-admin-exchange-cancels-modal {
  overflow: auto;

  .z-modal {
    &-overlay {
      width: 400px;
    }
  }

  .z-overlay {
    width: 400px;
  }

  .z-input {
    height: 40px;
  }

  .z-dropdown-overlay {
    width: 200px;
  }

  .z-button {
    height: 40px;
    font-size: 16px;
    color: #fff;

    &:disabled {
      background-color: #e9eff3 !important;
      border: 1px solid #e9eff3 !important;
    }
  }

  .z-dropdown {
    &-trigger {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 12px;
      width: 100%;
      height: 38px;
      border: 1px solid rgba(33, 47, 79, 0.1);
      border-radius: 4px;
      transition: all 0.3s;
      &:hover {
        border: 1px solid @primary-color;
      }
    }

    &-triggered {
      border: 1px solid @primary-color;
      box-shadow: 0 0 0 2px rgba(@primary-color, 0.2);
    }

    &-overlay {
      width: 100%;
      background-color: #fff;
      box-shadow: 0 3px 6px -4px #0000001f, 0 6px 16px #00000014, 0 9px 28px 8px #0000000d;
    }
  }

  .z-admin-form-button {
    display: block;

    .z-button {
      width: 100%;
      color: white;
      background-color: @primary-color;
    }
  }

  .z-form-col-item {
    padding: 0 !important;
  }
}
</style>
