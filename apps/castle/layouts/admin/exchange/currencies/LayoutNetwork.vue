<script setup lang="ts">
import type { AsyncDataExecuteOptions } from 'nuxt/dist/app/composables/asyncData'
import { capitalize } from '@zsmartex/utils'
import { BlockchainClient } from '@zsmartex/types'
import type { ZAdminFormColumn } from '~/types'
import { BlockchainCurrencyStatus, GasPriceRate, ZAdminFormFieldType } from '~/types'
import Validate from '~/validation/validate'

const props = defineProps<{
  id?: string
  modelValue: BlockchainCurrency
  pending?: boolean
  currency: string
  refresh?: (opts?: AsyncDataExecuteOptions) => Promise<any>
}>()

const emit = defineEmits(['update:modelValue'])

const adminStore = useAdminStore()
const menuActionStore = useMenuActionStore()

const blockchainCurrency = useVModel(props, 'modelValue', emit)
const { data: blockchainCurrencies } = await useAsyncData(() => adminStore.FetchBlockchainCurrencies().then(res => res.data))
const loading = ref(false)

const blockchainKey = ref('')
const isNewUpdate = ref(false)
const useParentFee = ref(false)
const parentId = ref('')
const options = reactive<Record<string, string | boolean>>({ ...blockchainCurrency.value.options })
const oldOptions = reactive<Record<string, string | boolean>>({ ...blockchainCurrency.value.options })

onMounted(() => {
  if (blockchainCurrency.value) {
    blockchainKey.value = blockchainCurrency.value.blockchain_key || ''
    parentId.value = blockchainCurrency.value.parent_id || ''
    useParentFee.value = blockchainCurrency.value.use_parent_fee
  }
})

const client = computed(() => {
  const blockchain = adminStore.blockchains.find(b => b.key === blockchainKey.value)
  if (blockchain) return blockchain.client
})

const supportedSmartContractClients = [
  BlockchainClient.Ethereum,
  BlockchainClient.Tron,
  BlockchainClient.Solana,
  BlockchainClient.Space,
  BlockchainClient.Qubic,
  BlockchainClient.Verus,
  BlockchainClient.Raven,
]

const supportedApprovalClients = [
  BlockchainClient.Ethereum,
]

const contractFields = computed(() => {
  switch (client.value) {
    case BlockchainClient.Ethereum:
      return [
        {
          key: 'erc20_contract_address',
          value: 'ERC20 Contract Address',
        },
      ]
    case BlockchainClient.Tron:
      return [
        {
          key: 'trc20_contract_address',
          value: 'TRC20 Contract Address',
        },
      ]
    case BlockchainClient.Solana:
      return [
        {
          key: 'sol20_contract_address',
          value: 'SOL20 Contract Address',
        },
      ]
    case BlockchainClient.Qubic:
      return [
        {
          key: 'qubic_contract_address',
          value: 'QUBIC Contract Address',
        },
      ]
    case BlockchainClient.Raven:
      return [
        {
          key: 'raven_asset_name',
          value: 'Raven Asset Name',
        },
      ]
    case BlockchainClient.Verus:
      return [
        {
          key: 'verus_contract_address',
          value: 'Verus Contract Address',
        },
        {
          key: 'verus_asset_name',
          value: 'Verus Asset Name',
        },
      ]
    case BlockchainClient.Space:
      return [
        {
          key: 'code_hash',
          value: 'Code Hash',
        },
        {
          key: 'genesis',
          value: 'Genesis',
        },
      ]
  }
})

const blockchainCurrenciesByKey = computed(() => {
  return blockchainCurrencies.value!
    .filter(item => item.blockchain_key === blockchainKey.value && item.parent_id === null)
    .map((item) => {
      const currency = adminStore.currencies.find(c => c.id === item.currency_id)

      return {
        name: currency?.name,
        blockchain_key: item.blockchain_key,
        currency_id: item.currency_id,
      }
    })
})

function UpdateBlockchainKey(value: string) {
  parentId.value = ''
  blockchainKey.value = value
}

function isUpdateAction() {
  if (props.id) return true
  return false
}

if (isUpdateAction()) {
  menuActionStore.setMenu([
    {
      key: 'refresh',
      title: 'Refresh',
      callback: async () => {
        loading.value = true
        props.refresh!()
        loading.value = false
      },
    },
  ])
}

const columns = computed(() => {
  const result: ZAdminFormColumn[] = [
    {
      fields: [
        {
          key: 'name',
          type: ZAdminFormFieldType.Text,
          label: 'Name currency',
          value: props.currency.toUpperCase(),
          class: '!w-6/12',
        },
        {
          key: 'status',
          type: ZAdminFormFieldType.Switch,
          label: 'Status',
          value: blockchainCurrency.value.status === BlockchainCurrencyStatus.Active,
          class: '!w-6/12 flex justify-end items-center mt-0!',
        },
        {
          key: 'blockchain_key',
          type: ZAdminFormFieldType.Select,
          dataSource: adminStore.blockchains,
          search: true,
          scroll: true,
          columns: [{
            key: 'name',
          }],
          findBy: ['key', 'name'],
          label: 'Blockchain Key(*)',
          valueKey: 'key',
          labelKey: 'name',
          onChange: UpdateBlockchainKey,
          value: blockchainKey.value,
          validate: [Validate.required],
          class: supportedApprovalClients.includes(client.value!) && !parentId.value.length && isUpdateAction() ? '!w-6/12' : '',
        },
        {
          key: 'min_deposit_amount',
          type: ZAdminFormFieldType.Input,
          typeInput: InputType.Decimal,
          label: 'Min Deposit Amount(*)',
          value: blockchainCurrency.value.min_deposit_amount,
          validate: [Validate.required, Validate.double(8), Validate.min(0)],
        },
        {
          key: 'min_record_deposit_amount',
          type: ZAdminFormFieldType.Input,
          typeInput: InputType.Decimal,
          label: 'Min Record Deposit Amount(*)',
          value: blockchainCurrency.value.min_record_deposit_amount,
          validate: [Validate.required, Validate.double(8), Validate.min(0)],
        },
      ],
    },
    {
      fields: [
        {
          key: 'deposit_enabled',
          type: ZAdminFormFieldType.Switch,
          label: 'Deposit',
          labelStyle: 'min-w-[80px]',
          value: blockchainCurrency.value.deposit_enabled,
          class: '!w-6/12 flex justify-start items-center',
        },
        {
          key: 'withdraw_enabled',
          type: ZAdminFormFieldType.Switch,
          label: 'Withdraw',
          value: blockchainCurrency.value.withdraw_enabled,
          class: '!w-6/12 flex justify-end items-center mt-0!',
        },
        {
          key: 'visible_deposit_enabled',
          type: ZAdminFormFieldType.Switch,
          label: 'Visible Deposit',
          labelStyle: 'min-w-[80px]',
          value: blockchainCurrency.value.visible_deposit_enabled,
          class: '!w-6/12 flex justify-start items-center mt-0!',
        },
        {
          key: 'untrusted',
          type: ZAdminFormFieldType.Switch,
          label: 'Untrusted',
          value: blockchainCurrency.value.untrusted,
          class: '!w-6/12 flex justify-end items-center mt-0!',
        },
        {
          key: 'subunits',
          type: ZAdminFormFieldType.Input,
          label: 'Subunits(*)',
          typeInput: InputType.Number,
          value: blockchainCurrency.value.subunits,
          validate: [Validate.required, Validate.integer, Validate.min(0)],
        },
        {
          key: 'min_collection_amount',
          type: ZAdminFormFieldType.Input,
          label: 'Min Collection Amount(*)',
          typeInput: InputType.Decimal,
          value: blockchainCurrency.value.min_collection_amount,
          validate: [Validate.required, Validate.double(8), Validate.min(0)],
        },
        {
          key: 'min_withdraw_amount',
          type: ZAdminFormFieldType.Input,
          label: 'Min Withdraw Amount(*)',
          typeInput: InputType.Decimal,
          value: blockchainCurrency.value.min_withdraw_amount,
          validate: [Validate.required, Validate.double(8), Validate.min(0)],
        },
        {
          key: 'deposit_fee',
          type: ZAdminFormFieldType.Input,
          label: `Deposit Fee (${useParentFee.value ? parentId.value.toUpperCase() : props.currency.toUpperCase()})(*)`,
          typeInput: InputType.Decimal,
          value: blockchainCurrency.value.deposit_fee,
          validate: [Validate.required, Validate.double(8), Validate.min(0)],
        },
        {
          key: 'withdraw_fee',
          type: ZAdminFormFieldType.Input,
          label: `Withdraw Fee (${useParentFee.value ? parentId.value.toUpperCase() : props.currency.toUpperCase()})(*)`,
          typeInput: InputType.Decimal,
          value: blockchainCurrency.value.withdraw_fee,
          validate: [Validate.required, Validate.double(8), Validate.min(0)],
          class: '!w-6/12',
        },
        {
          key: 'withdraw_fee_percentage',
          type: ZAdminFormFieldType.Switch,
          label: 'Withdraw Fee Percentage',
          value: blockchainCurrency.value.withdraw_fee_percentage,
          class: '!w-6/12 flex justify-end !mt-12 items-center',
        },
      ],
    },
  ]

  if (client.value === BlockchainClient.Neptune) {
    result[0].fields.push({
      key: 'fee',
      type: ZAdminFormFieldType.Input,
      label: 'Fee(*)',
      value: options.fee,
      validate: [Validate.required, Validate.double(8), Validate.min(0)],
      onInput: (value: string) => { changeOptionsValue('fee', value) },
    })
  }

  if (supportedSmartContractClients.includes(client.value!)) {
    result[0].fields.splice(3, 0, {
      key: 'parent_id',
      type: ZAdminFormFieldType.Select,
      dataSource: blockchainCurrenciesByKey.value,
      search: true,
      scroll: true,
      columns: [{
        key: 'name',
      }],
      findBy: ['currency_id', 'blockchain_key'],
      label: 'Parent Currency (optional)',
      valueKey: 'currency_id',
      labelKey: 'name',
      disabled: blockchainCurrenciesByKey.value.length === 0,
      value: parentId.value,
      onChange: (value: string) => { parentId.value = value },
      class: '!w-6/12',
    })
  }

  if (parentId.value.length > 0) {
    result[0].fields.splice(4, 0, {
      key: 'use_parent_fee',
      type: ZAdminFormFieldType.Switch,
      label: 'Use Parent Fee',
      value: useParentFee.value,
      onChange: (value: boolean) => { useParentFee.value = value },
      class: '!w-6/12 flex justify-end !mt-12 items-center',
    })
  }

  if (supportedApprovalClients.includes(client.value!) && !parentId.value.length && isUpdateAction()) {
    result[0].fields.splice(3, 0, {
      key: 'approval',
      type: ZAdminFormFieldType.Switch,
      label: 'Approval',
      value: options.approval,
      class: '!w-6/12 flex justify-end !mt-12 items-center',
      onChange: (value: boolean) => changeOptionsValue('approval', value),
    })
  }

  if (parentId.value && supportedSmartContractClients.includes(client.value!)) {
    for (const field of contractFields.value!) {
      result[0].fields.push({
        key: field.key,
        type: ZAdminFormFieldType.Input,
        label: `${field.value}(*)`,
        value: options[field.key],
        validate: [Validate.required],
        onInput: (value: string) => changeOptionsValue(field.key, value),
      })
    }
  }

  if (parentId.value && client.value === BlockchainClient.Qubic) {
    result[0].fields.push({
      key: 'fee',
      type: ZAdminFormFieldType.Input,
      label: 'Network Fee (*)',
      value: options.fee,
      validate: [Validate.required, Validate.double(0), Validate.min(1), Validate.max(100)],
      onInput: (value: string) => changeOptionsValue('fee', value),
    })
  }

  return result
})

function checkNewUpdate() {
  for (const key in options) {
    if (options[key] !== oldOptions[key]) {
      isNewUpdate.value = true
      return
    }
  }

  isNewUpdate.value = false
}

function changeOptionsValue(key: string, value: string | boolean) {
  options[key] = value
  checkNewUpdate()
}

const optionsColumns = computed(() => {
  const result: ZAdminFormColumn[] = [{
    fields: [],
  }, {
    fields: [],
  }]

  result[1].fields.push({
    key: 'message',
    type: ZAdminFormFieldType.Input,
    label: 'Message (optional)',
    value: options.message,
    onInput: (value: string) => changeOptionsValue('message', value),
  })

  if (parentId.value.length && useParentFee.value) {
    result[1].fields.push({
      key: 'deposit_note',
      type: ZAdminFormFieldType.Input,
      label: 'Deposit Note (optional)',
      value: options.deposit_note,
      onInput: (value: string) => { changeOptionsValue('deposit_note', value) },
    }, {
      key: 'withdraw_note',
      type: ZAdminFormFieldType.Input,
      label: 'Withdraw Note (optional)',
      value: options.withdraw_note,
      onInput: (value: string) => { changeOptionsValue('withdraw_note', value) },
    })
  }

  switch (client.value) {
    case BlockchainClient.Ethereum:
      result[0].fields.push(
        {
          key: 'gas_limit',
          type: ZAdminFormFieldType.Input,
          label: 'Gas Limit (optional)',
          value: options.gas_limit,
          onInput: (value: string) => { changeOptionsValue('gas_limit', value) },
        },
        {
          key: 'gas_rate',
          type: ZAdminFormFieldType.Select,
          label: 'Gas Rate (optional)',
          dataSource: [
            {
              value: GasPriceRate.Standard,
            },
            {
              value: GasPriceRate.Fast,
            },
            {
              value: GasPriceRate.Turbo,
            },
            {
              value: GasPriceRate.DoubleSpeed,
            },
          ],
          columns: [
            {
              key: 'value',
              scopedSlots: true,
            },
          ],
          search: true,
          findBy: ['value'],
          valueKey: 'value',
          replaceFunc: capitalize,
          labelKey: 'value',
          value: options.gas_rate,
          onChange: (value: string) => { changeOptionsValue('gas_rate', value) },
        },
        {
          key: 'legacy_version',
          type: ZAdminFormFieldType.Select,
          label: 'Legacy Version (optional)',
          dataSource: [
            {
              value: true,
            },
            {
              value: false,
            },
          ],
          columns: [
            {
              key: 'value',
              scopedSlots: true,
            },
          ],
          search: true,
          findBy: ['value'],
          valueKey: 'value',
          value: options.legacy_version,
          replaceFunc: (text: string) => text.toString().toUpperCase(),
          labelKey: 'value',
          onChange: (value: string) => { changeOptionsValue('legacy_version', value) },
        },
      )

      if (!options.gas_rate) {
        result[0].fields.push({
          key: 'gas_price',
          type: ZAdminFormFieldType.Input,
          label: 'Gas Price (optional)',
          value: options.gas_price,
          onInput: (value: string) => { changeOptionsValue('gas_price', value) },
        })
      }

      if (!parentId.value.length) {
        result[0].fields.unshift({
          key: 'multi_send_contract_address',
          type: ZAdminFormFieldType.Input,
          label: 'Multi Send Contract Address (optional)',
          value: options.multi_send_contract_address,
          onInput: (value: string) => { changeOptionsValue('multi_send_contract_address', value) },
        })
      }
      break
    case BlockchainClient.Tron:
      result[1].fields.push({
        key: 'fee_limit',
        type: ZAdminFormFieldType.Input,
        label: 'Fee limit (optional)',
        value: options.fee_limit,
        onInput: (value: string) => { changeOptionsValue('fee_limit', value) },
      })
      break
    case BlockchainClient.SUI:
      result[1].fields.push({
        key: 'gas_budget',
        type: ZAdminFormFieldType.Input,
        label: 'Gas Budget (optional)',
        value: options.gas_budget,
        onInput: (value: string) => { changeOptionsValue('gas_budget', value) },
      })
      break
    case BlockchainClient.Solana:
    case BlockchainClient.Monero:
    case BlockchainClient.Yadacoin:
      result[1].fields.push({
        key: 'fee',
        type: ZAdminFormFieldType.Input,
        label: 'Fee (optional)',
        value: options.fee,
        onInput: (value: string) => { changeOptionsValue('fee', value) },
      })
      break
  }

  return result
})

async function ActionBlockchainCurrency(bc: any) {
  loading.value = true
  const opts: Record<string, any> = {}
  if (options.message) opts.message = options.message
  if (useParentFee.value && options.withdraw_note) opts.withdraw_note = options.withdraw_note
  if (useParentFee.value && options.deposit_note) opts.deposit_note = options.deposit_note
  if (client.value === BlockchainClient.Tron) {
    if (options.fee_limit) opts.fee_limit = Number(options.fee_limit)
    if (parentId.value && options.trc20_contract_address) opts.trc20_contract_address = options.trc20_contract_address
  } else if (client.value === BlockchainClient.Ethereum) {
    if (options.approval) opts.approval = options.approval
    if (options.gas_limit) opts.gas_limit = Number(options.gas_limit)
    if (options.gas_rate) opts.gas_rate = options.gas_rate
    if (options.gas_price && !options.gas_rate) opts.gas_price = Number(options.gas_price)
    if (parentId.value && options.erc20_contract_address) opts.erc20_contract_address = options.erc20_contract_address
    if (options.multi_send_contract_address) opts.multi_send_contract_address = options.multi_send_contract_address
    if (options.legacy_version) opts.legacy_version = options.legacy_version
  } else if (client.value === BlockchainClient.Solana) {
    if (parentId.value && options.sol20_contract_address) opts.sol20_contract_address = options.sol20_contract_address
  } else if (client.value === BlockchainClient.Qubic) {
    if (parentId.value && options.qubic_contract_address) {
      opts.qubic_contract_address = options.qubic_contract_address
      opts.fee = Number(options.fee)
    }
  } else if (client.value === BlockchainClient.Raven) {
    if (parentId.value && options.raven_asset_name) opts.raven_asset_name = options.raven_asset_name
  } else if (client.value === BlockchainClient.Verus) {
    if (parentId.value && options.verus_contract_address) opts.verus_contract_address = options.verus_contract_address
    if (parentId.value && options.verus_asset_name) opts.verus_asset_name = options.verus_asset_name
  } else if (client.value === BlockchainClient.Space) {
    if (parentId.value && options.code_hash) {
      opts.code_hash = options.code_hash
      opts.genesis = options.genesis
    }
  } else if (client.value === BlockchainClient.Neptune) {
    if (options.fee) opts.fee = Number(options.fee)
  } else if (client.value === BlockchainClient.BitcoinFork) {
    if (options.private_getblock_params !== undefined) opts.private_getblock_params = options.private_getblock_params
    if (options.private_getrawtransaction_params !== undefined) opts.private_getrawtransaction_params = options.private_getrawtransaction_params
  }

  for (const option in opts) {
    if (typeof opts[option] === 'string' && opts[option].length === 0) delete opts[option]
  }

  if (isUpdateAction()) {
    await adminStore.UpdateBlockchainCurrency({
      id: Number(props.id),
      currency_id: props.currency as string,
      blockchain_key: bc.blockchain_key,
      subunits: Number(bc.subunits),
      parent_id: bc.parent_id || null,
      min_deposit_amount: Number(bc.min_deposit_amount),
      min_record_deposit_amount: Number(bc.min_record_deposit_amount),
      min_collection_amount: Number(bc.min_collection_amount),
      min_withdraw_amount: Number(bc.min_withdraw_amount),
      deposit_fee: Number(bc.deposit_fee),
      withdraw_fee: Number(bc.withdraw_fee),
      withdraw_fee_percentage: bc.withdraw_fee_percentage,
      visible_deposit_enabled: bc.visible_deposit_enabled,
      deposit_enabled: bc.deposit_enabled,
      withdraw_enabled: bc.withdraw_enabled,
      untrusted: bc.untrusted,
      status: bc.status ? BlockchainCurrencyStatus.Active : BlockchainCurrencyStatus.Disabled,
      use_parent_fee: bc.use_parent_fee,
      options: opts,
    })
  } else {
    await adminStore.CreateBlockchainCurrency({
      currency_id: props.currency as string,
      blockchain_key: bc.blockchain_key as string,
      subunits: Number(bc.subunits),
      parent_id: bc.parent_id || null,
      min_deposit_amount: Number(bc.min_deposit_amount),
      min_record_deposit_amount: Number(bc.min_record_deposit_amount),
      min_collection_amount: Number(bc.min_collection_amount),
      min_withdraw_amount: Number(bc.min_withdraw_amount),
      deposit_fee: Number(bc.deposit_fee),
      withdraw_fee: Number(bc.withdraw_fee),
      withdraw_fee_percentage: bc.withdraw_fee_percentage,
      visible_deposit_enabled: bc.visible_deposit_enabled,
      deposit_enabled: bc.deposit_enabled,
      withdraw_enabled: bc.withdraw_enabled,
      untrusted: bc.untrusted,
      use_parent_fee: bc.use_parent_fee,
      status: bc.status ? BlockchainCurrencyStatus.Active : BlockchainCurrencyStatus.Disabled,
      options: opts,
    })
  }

  loading.value = false
}
</script>

s
<template>
  <div>
    <ZCard class="mb-4">
      <ZAdminForm :columns="columns" :is-new-update="isNewUpdate" :loading="loading" :pending="pending" @submit="ActionBlockchainCurrency" />
    </ZCard>
    <ZCard class="mb-4">
      <ZAdminForm :columns="optionsColumns" :loading="loading" hidden-submit :pending="pending" />
    </ZCard>
  </div>
</template>
