<script setup lang="ts">
import type { AsyncDataExecuteOptions } from 'nuxt/dist/app/composables/asyncData'
import type { ZAdminFormColumn } from '~/types'
import { CurrencyStatus, ZAdminFormFieldType } from '~/types'
import Validate from '~/validation/validate'

const props = defineProps<{
  id?: string
  modelValue: Currency
  pending?: boolean
  refresh?: (opts?: AsyncDataExecuteOptions) => Promise<void>
}>()

const emit = defineEmits(['update:modelValue'])

const router = useRouter()
const adminStore = useAdminStore()
const menuActionStore = useMenuActionStore()

const currency = useVModel(props, 'modelValue', emit)

const loading = useState(() => false)

function isUpdateAction() {
  if (props.id) return true
  return false
}

function uppercase(text: string) {
  return text.toUpperCase()
}

if (isUpdateAction()) {
  menuActionStore.setMenu([
    {
      key: 'refresh',
      title: 'Refresh',
      callback: async () => {
        loading.value = true
        props.refresh!()
        loading.value = false
      },
    },
  ])
}

const columns: ZAdminFormColumn[] = [
  {
    fields: [
      {
        key: 'id',
        type: isUpdateAction() ? ZAdminFormFieldType.Text : ZAdminFormFieldType.Input,
        label: 'Code(*)',
        value: currency.value.id,
        class: '!w-6/12',
        replaceFunc: uppercase,
        validate: [Validate.required],
      },
      {
        key: 'status',
        type: ZAdminFormFieldType.Switch,
        label: 'Status',
        value: currency.value.status === CurrencyStatus.Enabled,
        class: '!w-6/12 flex justify-end items-center mt-0!',
      },
      {
        key: 'name',
        type: ZAdminFormFieldType.Input,
        label: 'Name(*)',
        value: currency.value.name,
        validate: [Validate.required, Validate.maxLength(32)],
      },
      {
        key: 'precision',
        type: ZAdminFormFieldType.Input,
        label: 'Precision(*)',
        value: currency.value.precision,
        validate: [Validate.required, Validate.integer, Validate.min(0)],
      },
    ],
  },
  {
    fields: [
      {
        key: 'position',
        type: ZAdminFormFieldType.Input,
        label: 'Position(*)',
        value: currency.value.position,
        validate: [Validate.required, Validate.integer, Validate.min(0)],
      },
      {
        key: 'icon_url',
        type: ZAdminFormFieldType.Input,
        label: 'Icon URL',
        value: currency.value.icon_url,
        validate: [Validate.url],
      },
    ],
  },
]

async function ActionCurrency(currency: Currency) {
  loading.value = true

  if (isUpdateAction()) {
    await adminStore.UpdateCurrency({
      id: currency.id as string,
      name: currency.name as string,
      position: Number(currency.position),
      status: currency.status ? CurrencyStatus.Enabled : CurrencyStatus.Disabled,
      icon_url: currency.icon_url,
      precision: Number(currency.precision),
    }, () => {
      router.push('/exchange/currencies')
    })
  } else {
    await adminStore.CreateCurrency({
      id: currency.id as string,
      name: currency.name as string,
      position: Number(currency.position),
      status: currency.status ? CurrencyStatus.Enabled : CurrencyStatus.Disabled,
      icon_url: currency.icon_url,
      precision: Number(currency.precision),
    }, () => {
      router.push('/exchange/currencies')
    })
  }

  loading.value = false
}
</script>

<template>
  <ZCard :title="isUpdateAction() ? 'Update Currency' : 'Create Currency'" class="layout-admin-exchange-currency">
    <ZAdminForm :columns="columns" :loading="loading" :pending="pending" @submit="ActionCurrency" />
  </ZCard>
</template>

<style lang="less">
.layout-admin-exchange-currency {
  .z-admin-form {
    padding-top: 0;
  }
}
</style>
