<script setup lang="ts">
const visible = ref(false)
const img = ref('')

function openModal(image: string) {
  visible.value = true
  img.value = image
}

defineExpose({
  openModal,
})
</script>

<template>
  <ZModal
    v-model="visible"
    class="layout-p2p-preview-modal"
    title="Preview Image"
  >
    <img class="w-full" :src="img">
  </ZModal>
</template>

<style lang="less">
.layout-p2p-preview-modal {
  // font-size: 16px;
  .img {
    width: 100%;
  }
}
</style>
