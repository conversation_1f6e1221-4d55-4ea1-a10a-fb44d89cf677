<script setup lang="ts">
import type { ZAdminFormColumn } from '~/types'
import Validate from '~/validation/validate'

const emit = defineEmits(['click'])

const visible = ref(false)
const loading = ref(false)
const type = ref('')
const form = ref<Record<string, any>>({})

function openModal(typeModal: string, p2pTrade: P2PTrade | null) {
  visible.value = true
  type.value = typeModal

  if (p2pTrade != null) {
    form.value = p2pTrade
  }
}

const columns: ZAdminFormColumn[] = [
  {
    fields: [
      {
        key: 'unpaid_id',
        type: ZAdminFormFieldType.Select,
        label: 'Unpaid(*)',
        value: '',
        dataSource: [
          {
            value: form.value.maker_id,
            label: 'Maker',
          },
          {
            value: form.value.taker_id,
            label: 'Taker',
          },
        ],
        columns: [
          {
            key: 'label',
          },
        ],
        search: true,
        findBy: ['label'],
        valueKey: 'value',
        labelKey: 'label',
        validate: [Validate.required],
      },
      {
        key: 'response',
        type: ZAdminFormFieldType.Textarea,
        label: 'Response(*)',
        value: '',
        validate: [Validate.required],
        class: 'h-[160px]',
      },
    ],
  },
]

// TODO: Fix type of P2PTrade
async function onClick({ unpaid_id: unpaidId, response }: any) {
  emit('click', type.value, unpaidId, response)
}

defineExpose({
  openModal,
})
</script>

<template>
  <ZModal
    v-model="visible"
    class="layouts-admin-exchange-p2p-fail-modal"
    title="Handle P2P Trade"
  >
    <ZAdminForm :columns="columns" :loading="loading" @submit="(item: P2PTrade) => onClick(item)" />
  </ZModal>
</template>

<style lang="less">
.layouts-admin-exchange-p2p-fail-modal {
  .z-overlay {
    width: 400px;
  }

  .z-input {
    height: 40px;
  }

  .z-dropdown-overlay {
    width: 250px;
  }

  .z-dropdown {
    &-trigger {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 12px;
      width: 100%;
      height: 38px;
      border: 1px solid rgba(33, 47, 79, 0.1);
      border-radius: 4px;
      transition: all 0.3s;
      &:hover {
        border: 1px solid @primary-color;
      }
    }

    &-triggered {
      border: 1px solid @primary-color;
      box-shadow: 0 0 0 2px rgba(@primary-color, 0.2);
    }

    &-overlay {
      width: 100%;
      background-color: #fff;
      box-shadow: 0 3px 6px -4px #0000001f, 0 6px 16px #00000014, 0 9px 28px 8px #0000000d;
    }
  }

  .z-admin-form-button {
    display: block;

    .z-button {
      width: 100%;
      color: white;
      background-color: @primary-color;
    }
  }

  .z-form-col-item {
    padding: 0 !important;
  }
}
</style>
