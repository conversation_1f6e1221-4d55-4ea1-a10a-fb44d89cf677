<script setup lang="ts">
import getSymbolFromCurrency from 'currency-symbol-map'
import { RoundNumber } from '~/mixins'

const props = defineProps<{
  advertisement: Advertisement
  side: string
}>()

const visible = ref(false)
const ads = ref<Advertisement>(props.advertisement)

onMounted(() => {
  const instance = getCurrentInstance()
  onClickOutside(instance?.proxy?.$el, () => {
    visible.value = false
  })
})
</script>

<template>
  <div class="layout-advertisement">
    <div class="layout-advertisement-row">
      <div class="layout-advertisement-row-currency flex items-start">
        <span class="text-base bold-text mr-2">{{ ads.coin_currency.toUpperCase() }}</span>
      </div>
      <div class="layout-advertisement-row-currency flex items-start">
        <span class="text-base bold-text mr-2">{{ ads.fiat_currency.toUpperCase() }}</span>
      </div>
      <div
        class="layout-advertisement-row-side capitalize flex items-start"
        :class="[
          { 'text-green-500': ads.side === AdvertisementSide.Buy },
          { 'text-red-500': ads.side === AdvertisementSide.Sell },
        ]"
      >
        <span class="text-base bold-text mr-2">{{ ads.side }}</span>
      </div>
      <div class="layout-advertisement-row-price flex items-start">
        <span class="text-xl bold-text mr-2">{{ RoundNumber(ads.price, 2) }}</span>
        <span class="text-[12px] bold-text mt-[6px]">{{ ads.fiat_currency.toUpperCase() }}</span>
      </div>
      <div class="layout-advertisement-row-limit flex flex-col justify-between">
        <div>
          <span class="text-gray mr-2">Available</span>
          <span class="bold-text">{{ `${RoundNumber(ads.origin_amount - ads.filled_amount - ads.self_unlock_amount, 2)} ${ads.coin_currency.toUpperCase()}` }}</span>
        </div>
        <div>
          <span class="text-gray mr-2">Limit</span>
          <span class="bold-text">{{ `${RoundNumber(ads.min, 2)} ${getSymbolFromCurrency(ads.fiat_currency.toUpperCase())}` }} - {{ `${RoundNumber(ads.max, 2)} ${getSymbolFromCurrency(ads.fiat_currency.toUpperCase())}` }}</span>
        </div>
      </div>
      <div class="layout-advertisement-row-state flex justify-end items-start">
        <span
          class="capitalize"
          :class="[
            { 'text-red-500': ads.state === AdvertisementState.Banned || ads.state === AdvertisementState.Disabled },
            { 'text-green-500': ads.state === AdvertisementState.Enabled },
          ]"
        >
          {{ ads.state }}
        </span>
      </div>
    </div>
  </div>
</template>

<style lang="less">
.layout-advertisement-row {
  display: flex;
  padding: 12px 24px;
  height: 92px;

  &-currency {
    font-size: 14px;
  }

  & > div {
    flex: 1;
  }

  &-payments {
    display: flex;
    flex-wrap: wrap;
    max-width: 280px;

    & > div {
      margin-bottom: 6px;
      padding: 2px 6px;
      height: fit-content;
      line-height: normal;
      background-color: rgba(@gray-color, 0.1);
      border-radius: 4px;
      color: @primary-color;
      cursor: pointer;

      & ~ div {
        margin-left: 6px;
      }
    }
  }
}
</style>
