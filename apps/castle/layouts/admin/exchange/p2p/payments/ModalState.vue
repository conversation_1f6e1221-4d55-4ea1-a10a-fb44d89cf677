<script setup lang="ts">
import type { ZAdminFormColumn } from '~/types'
import Validate from '~/validation/validate'

const visible = ref(false)
const form = ref<Record<string, any>>({})

function openModal(payment: Payment) {
  visible.value = true
  form.value = payment
}

const columns = computed(() => {
  const result: ZAdminFormColumn[] = [
    {
      fields: [
        {
          key: 'id',
          type: ZAdminFormFieldType.Text,
          label: 'ID',
          value: form.value.id,
        },
        {
          key: 'uid',
          type: ZAdminFormFieldType.Text,
          label: 'UID',
          value: form.value.uid,
        },
        {
          key: 'type',
          type: ZAdminFormFieldType.Text,
          label: 'Type',
          value: form.value.type,
          class: 'capitalize',
        },
        {
          key: 'name_account',
          type: ZAdminFormFieldType.Text,
          label: 'Name Account',
          value: form.value.name_account,
        },
        {
          key: 'state',
          type: ZAdminFormFieldType.Select,
          label: 'State(*)',
          value: form.value.state,
          dataSource: [
            {
              value: PaymentState.Enabled,
            },
            {
              value: PaymentState.Disabled,
            },
            {
              value: PaymentState.Denied,
            },
          ],
          columns: [
            {
              key: 'value',
              scopedSlots: true,
            },
          ],
          replaceFunc: (text: string) => text[0].toUpperCase() + text.slice(1),
          findBy: ['value'],
          valueKey: 'value',
          labelKey: 'value',
          validate: [Validate.required],
        },
      ],
    },
  ]

  if (form.value.type === 'bank') {
    result[0].fields.splice(4, 0,
      {
        key: 'number_account',
        type: ZAdminFormFieldType.Text,
        label: 'Number Account',
        value: form.value.data.number_account,
      },
      {
        key: 'bank_name',
        type: ZAdminFormFieldType.Text,
        label: 'Bank Name',
        value: form.value.data.bank_name,
      },
      {
        key: 'bank_address',
        type: ZAdminFormFieldType.Text,
        label: 'Bank Address',
        value: form.value.data.bank_address,
      },
    )
  }

  if (form.value.type === 'momo') {
    result[0].fields.splice(4, 0,
      {
        key: 'phone_account',
        type: ZAdminFormFieldType.Text,
        label: 'Phone Account',
        value: form.value.data.phone,
      },
    )
  }

  return result
})

defineExpose({
  openModal,
})
</script>

<template>
  <ZModal
    v-model="visible"
    class="layouts-admin-exchange-p2p-payment-modal w-[300px]"
    title="Update State Payment"
  >
    <ZAdminForm :columns="columns" :loading="loading" />
  </ZModal>
</template>

<style lang="less">
.layouts-admin-exchange-p2p-payment-modal {
  .z-overlay {
    width: 400px;
  }

  .z-modal-overlay {
    width: 380px;
  }

  .z-input {
    height: 40px;
  }

  .z-dropdown-overlay {
    width: 250px;
  }

  .z-form-row {
    margin-bottom: 20px;
  }

  .z-dropdown {
    &-trigger {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 12px;
      width: 100%;
      height: 38px;
      border: 1px solid rgba(33, 47, 79, 0.1);
      border-radius: 4px;
      transition: all 0.3s;
      &:hover {
        border: 1px solid @primary-color;
      }
    }

    &-triggered {
      border: 1px solid @primary-color;
      box-shadow: 0 0 0 2px rgba(@primary-color, 0.2);
    }

    &-overlay {
      width: 100%;
      background-color: #fff;
      box-shadow: 0 3px 6px -4px #0000001f, 0 6px 16px #00000014, 0 9px 28px 8px #0000000d;
    }
  }

  .z-admin-form-button {
    display: block;

    .z-button {
      width: 100%;
      color: white;
      background-color: @primary-color;
    }
  }

  .z-form-col-item {
    padding: 0 !important;
  }
}
</style>
