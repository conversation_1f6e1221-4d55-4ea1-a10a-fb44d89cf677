<script setup lang="ts">
import type { ZAdminFormColumn } from '~/types'
import Validate from '~/validation/validate'

const emit = defineEmits(['click'])

const adminStore = useAdminStore()
const visible = ref(false)
const loading = ref(false)
const id = ref(0)

function openModal(idNumber: number) {
  visible.value = true
  id.value = idNumber
}

const columns = computed(() => {
  const result: ZAdminFormColumn[] = [
    {
      fields: [
        {
          key: 'response',
          type: ZAdminFormFieldType.Textarea,
          label: 'Response(*)',
          value: '',
          validate: [Validate.required],
        },
      ],
    },
  ]

  return result
})

async function onClick({ response }: any) {
  DenyComplain(response)
}

async function DenyComplain(response: string) {
  await adminStore.DenyComplain(id.value, response, () => {
    emit('click', response)

    visible.value = false
  })
}

defineExpose({
  openModal,
})
</script>

<template>
  <ZModal
    v-model="visible"
    class="layouts-admin-exchange-p2p-complain-modal"
    title="Deny P2P Complain"
  >
    <ZAdminForm :columns="columns" :loading="loading" @submit="(item: P2PComplain) => onClick(item)" />
  </ZModal>
</template>

<style lang="less">
.layouts-admin-exchange-p2p-complain-modal {
  .z-overlay {
    width: 400px;
  }

  .z-input {
    height: 40px;
  }

  .z-dropdown-overlay {
    width: 250px;
  }

  .z-dropdown {
    &-trigger {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 12px;
      width: 100%;
      height: 38px;
      border: 1px solid rgba(33, 47, 79, 0.1);
      border-radius: 4px;
      transition: all 0.3s;
      &:hover {
        border: 1px solid @primary-color;
      }
    }

    &-triggered {
      border: 1px solid @primary-color;
      box-shadow: 0 0 0 2px rgba(@primary-color, 0.2);
    }

    &-overlay {
      width: 100%;
      background-color: #fff;
      box-shadow: 0 3px 6px -4px #0000001f, 0 6px 16px #00000014, 0 9px 28px 8px #0000000d;
    }
  }

  .z-admin-form {
    .z-form-row-content {
      height: 160px;
    }
  }

  .z-admin-form-button {
    display: block;

    .z-button {
      width: 100%;
      color: white;
      background-color: @primary-color;
    }
  }

  .z-form-col-item {
    padding: 0 !important;
  }
}
</style>
