<script setup lang="ts">
import { format as formatDate, formatDistanceToNow, parseISO } from 'date-fns'

const props = defineProps<{
  user: User
}>()

const emit = defineEmits(['update:modelValue', 'updateNote'])

const adminStore = useAdminStore()
const note = useState(() => props.user.note || '')
const isEditNote = useState(() => false)
const loading = useState(() => false)

function CancelEdit() {
  isEditNote.value = false
  note.value = props.user.note || ''
}

async function UpdateUserNote() {
  if (note.value === props.user.note) return

  loading.value = true
  await adminStore.UpdateUser({
    uid: props.user.uid,
    note: note.value,
  })

  emit('updateNote', note.value)
  loading.value = false
  isEditNote.value = false
}
</script>

<template>
  <ZCard class="user-profile">
    <div class="user-profile-name font-semibold text-xl ">
      <p>{{ user.email }}</p>
    </div>
    <span class="inline-block mb-8 text-gray-400 text-xs">
      <ZTooltip :title="formatDistanceToNow(new Date(user.updated_at))" :placement="TooltipPlacement.TopCenter">
        Last activity: {{ formatDate(parseISO(user.updated_at), 'MMM dd, yyyy hh:mm a') }}
      </ZTooltip>
    </span>
    <div class="flex flex-wrap">
      <div class="user-profile-box-item pb-6">
        <span class="text-gray-400 block mb-2">UID</span>
        <p>{{ user.uid }}</p>
      </div>
      <div class="user-profile-box-item pb-6">
        <span class="text-gray-400 block mb-2">
          Created At
        </span>
        <span class="inline-block">
          <ZTooltip :title="formatDistanceToNow(new Date(user.updated_at))" :placement="TooltipPlacement.TopCenter">
            {{ 1 }}
          </ZTooltip>
        </span>
      </div>
      <div v-if="user.username" class="user-profile-box-item pb-6">
        <span class="text-gray-400 block mb-2">Username</span>
        <p>{{ user.username }}</p>
      </div>
      <div class="w-full">
        <span class="text-gray-400 block mb-2">Note</span>
        <div v-if="!isEditNote">
          {{ user.note }}
        </div>
        <textarea v-else v-model="note" class="user-profile-note" />
      </div>
      <!-- <div v-if="user.profile && user.profile.state === 'verified' && user.profile.full_name" class="user-profile-box-item pb-6">
        <span class="text-gray-400 block mb-2">Fullname</span>
        <p>{{ user.profile.full_name }}</p>
      </div>
      <div v-if="user.profile && user.profile.state === 'verified' && user.profile.country" class="user-profile-box-item pb-6">
        <span class="text-gray-400 block mb-2">Country</span>
        <p>{{ user.profile.country }}</p>
      </div>
      <div class="user-profile-box-item pb-6">
        <span class="text-gray-400 block mb-2">Level</span>
        <p>{{ user.level }}</p>
      </div>
      <div v-if="user.role" class="user-profile-box-item pb-6">
        <span class="text-gray-400 block mb-2">Role</span>
        <p class="capitalize">
          {{ user.role }}
        </p>
      </div> -->
    </div>
    <div class="flex flex-1 w-full justify-end items-end">
      <div v-if="isEditNote" class="flex">
        <ZButton
          type="primary"
          class="mt-[16px] h-8! leading-8! mr-4"
          @click="CancelEdit"
        >
          Cancel
        </ZButton>
        <ZButton
          type="primary"
          class="mt-[16px] h-8! leading-8!"
          :disabled="note === user.note"
          :loading="loading"
          @click="UpdateUserNote"
        >
          Submit
        </ZButton>
      </div>
      <div v-else>
        <ZButton
          type="primary"
          class="mt-[16px] h-8! leading-8!"
          @click="isEditNote = true"
        >
          Edit
        </ZButton>
      </div>
    </div>
  </ZCard>
</template>

<style lang="less">
.user-profile {
  height: 100%;
  border-radius: 4px;
  padding: 22px;
  color: @text-color;

  .z-card-content {
    display: flex;
    flex-direction: column;
  }

  &-note {
    padding: 8px 12px;
    width: 100%;
    height: 100px;
    transition: border 0.3s ease-in-out, box-shadow 0.3s ease-in-out;
    border: 1px solid @base-border-color;
    border-radius: 4px;
    font-size: 14px;
    outline: none;
    font-family: 'URWDIN-Regular';

    &:hover {
      border-color: @primary-color;
    }

    &:focus {
      border-color: @primary-color;
      box-shadow: 0 0 0 2px rgba(@primary-color, 0.2);
    }
  }

  &-box {
    &-item {
      width: 50%;
    }
  }

  &-role {
    background-color: rgba(@gray-color, 0.2);
    color: rgb(@text-color, 0.6);
    text-align: center;
    margin: auto;
    width: 60px;
  }

  &-title {
    padding-bottom: 8px;
    margin-bottom: 24px;
    border-bottom: 1px solid rgba(@text-color, 0.2);
  }

  &-dropdown {
    &-item {
      display: block;
      cursor: pointer;
      padding: 8px 12px;
      color: @text-color !important;

      &:hover {
        background-color: #f5f5f5;
      }
    }

    &-item + &-item {
      border-top: 1px solid @base-border-color;
    }
  }

  .z-dropdown {
    &-trigger {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 12px;
      width: 100%;
      height: 32px;
      border: 1px solid rgba(33, 47, 79, 0.1);
      border-radius: 2px;
      transition: all 0.3s;
      &:hover {
        border: 1px solid @primary-color;
      }
    }

    &-triggered {
      border: 1px solid @primary-color;
      box-shadow: 0 0 0 2px rgba(@primary-color, 0.2);
    }

    &-overlay {
      width: 100%;
      background-color: #fff;
      box-shadow: 0 3px 6px -4px #0000001f, 0 6px 16px #00000014, 0 9px 28px 8px #0000000d;
    }
  }
}
</style>
