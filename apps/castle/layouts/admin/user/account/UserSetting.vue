<script setup lang="ts">
import { Placement, UserRole, UserState } from '@zsmartex/types'

const props = defineProps<{
  modelValue: Member
  user: User
}>()

const emit = defineEmits(['update:modelValue', 'updateState'])
const member = useVModel(props, 'modelValue', emit)

const adminStore = useAdminStore()

const states = [UserState.Active, UserState.Deleted, UserState.Banned, UserState.Pending, UserState.Locked]
const roles = [UserRole.SuperAdmin, UserRole.Admin, UserRole.Support1, UserRole.Support2, UserRole.Member]
const groups = adminStore.groups

const state = useState(() => '')
const role = useState(() => '')
const otp = useState(() => false)
const loading = useState(() => false)
const group = useState(() => member.value.group)

onMounted(() => {
  state.value = props.user.state
  role.value = props.user.role
  otp.value = props.user.otp
  // inviteCode.value = props.user.data ? props.user.data.invite_code : ''
})

async function UpdateUserState(item: string) {
  state.value = item
  await adminStore.UpdateUser({
    uid: props.user.uid,
    state: state.value,
  })
  emit('updateState', item)
}

async function UpdateUserRole(item: string) {
  role.value = item
  await adminStore.UpdateUserRole({
    uid: props.user.uid,
    role: role.value,
  })
}

async function UpdateUserOTP() {
  loading.value = true
  await adminStore.UpdateUserOTP(props.user.uid, false, () => {
    otp.value = false
  })
  loading.value = false
}

async function UpdateUserGroup(text: string) {
  loading.value = true
  await adminStore.UpdateUserGroup(props.user.uid, text, () => {
    group.value = text
  })
  loading.value = false
}

const Capitalize = (text: string) => text[0].toUpperCase() + text.slice(1)
</script>

<template>
  <div class="pt-2">
    <ZCard title="Setting" class="user-setting">
      <div class="flex justify-between">
        <div class="mb-3 w-5/12">
          <span class="bold-text mr-2 pb-1 block">State:</span>
          <ZDropdown trigger="click" :placement="Placement.BottomCenter" class="mr-4 w-full">
            <span class="capitalize">{{ state }}</span> <ZIcon type="arrow-down" class="text-xs" />
            <template #overlay>
              <div v-for="item in states" :key="item" class="page-dashboard-users-user-dropdown-item" @click="UpdateUserState(item)">
                <span class="capitalize">{{ item }}</span>
              </div>
            </template>
          </ZDropdown>
        </div>
        <div class="mb-3 w-5/12">
          <span class="bold-text mr-2 pb-1 block">Role:</span>
          <ZDropdown trigger="click" :placement="Placement.BottomCenter" class="mr-4 w-full">
            <span class="capitalize">{{ role }}</span> <ZIcon type="arrow-down" class="text-xs" />
            <template #overlay>
              <div v-for="item in roles" :key="item" class="page-dashboard-users-user-dropdown-item" @click="UpdateUserRole(item)">
                <span class="capitalize">{{ item }}</span>
              </div>
            </template>
          </ZDropdown>
        </div>
      </div>
      <div class="flex justify-between">
        <div class="mb-3 w-5/12">
          <span class="bold-text mr-2 pb-1 block">Fee/Withdraw Group:</span>
          <ZDropdown trigger="click" :placement="Placement.BottomCenter" class="mr-4 w-full">
            {{ Capitalize(group) }} <ZIcon type="arrow-down" class="text-xs" />
            <template #overlay>
              <div v-for="item in groups" :key="item" class="page-dashboard-users-user-dropdown-item" @click="UpdateUserGroup(item)">
                {{ Capitalize(item) }}
              </div>
            </template>
          </ZDropdown>
        </div>
      </div>
      <div class="w-5/12 mt-4">
        <p class="bold-text">
          Authorization 2FA
        </p>
        <ZSwitch v-model="otp" size="medium" :loading="loading" :disabled="!otp" @change="UpdateUserOTP" />
      </div>
    </ZCard>
  </div>
</template>

<style lang="less">
.user-setting {
  &-invite-code {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 12px;
    width: 100%;
    height: 38px;
    background-color: rgba(132, 144, 170, 0.05);
    border: 1px solid rgba(33, 47, 79, 0.1);
    border-radius: 4px;
    transition: all 0.3s;

    span {
      color: @primary-color;
      cursor: pointer;
      user-select: none;
    }
  }

  &-button {
    cursor: pointer;
    color: @primary-color;

    &:hover {
      text-decoration: underline;
    }

    &-loading {
      cursor: not-allowed;
      color: @gray-color;

      &:hover {
        text-decoration: none;
      }
    }
  }
}
</style>
