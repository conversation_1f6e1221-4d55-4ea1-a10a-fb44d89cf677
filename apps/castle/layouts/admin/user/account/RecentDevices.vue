<script setup lang="ts">
import { UserAgentToBrowser, UserAgentToOS } from '~/mixins'
import countries from '~/library/countries'

const props = defineProps<{
  devices: UserDevice[]
  user: User
}>()

const emit = defineEmits(['deleteDevice'])

const adminStore = useAdminStore()

const columns: ZTableColumn[] = [
  {
    key: 'device',
    title: 'Device',
    scopedSlots: true,
  },
  {
    key: 'location',
    title: 'Location',
    scopedSlots: true,
  },
  {
    key: 'authenticated_at',
    title: 'Date',
    parse: ParseType.DateTime,
    formatBy: Format.DateTime,
  },
  {
    key: 'action',
    title: 'Action',
    scopedSlots: true,
    align: Align.Right,
  },
]

function getCountryName(code: string) {
  const country = countries.find(c => c.code === code)
  return country ? country.name : code
}

function RemoveDevice(item: UserDevice) {
  adminStore.DeleteUserDevice(props.user.uid, item.session_id)
  // devices.value.splice(devices.value.findIndex(device => device.session_id === item.session_id), 1)
  emit('deleteDevice', item)
}
</script>

<template>
  <ZCard title="Recent Devices" class="recent-device">
    <ZTablePro :columns="columns" :data-source="devices">
      <template #device="{ item }">
        {{ UserAgentToBrowser(item.user_agent) }} On {{ UserAgentToOS(item.user_agent) }}
      </template>
      <template #location="{ item }">
        {{ getCountryName(item.user_ip_country) }} ({{ item.user_ip }})
      </template>
      <template #action="{ item }">
        <ZIcon type="close" class="cursor-pointer" @click="RemoveDevice(item)" />
      </template>
    </ZTablePro>
  </ZCard>
</template>

<style lang="less">
.recent-device {
  min-height: 0 !important;

  .action {
    max-width: 80px;
  }

  .z-table-pro {
    box-shadow: none;

    &-head {
      padding: 0;
    }
  }
}
</style>
