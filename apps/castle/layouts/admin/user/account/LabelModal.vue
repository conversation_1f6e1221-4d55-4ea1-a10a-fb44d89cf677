<script setup lang="ts">
import type { ZAdminFormColumn } from '~/types'
import Validate from '~/validation/validate'

const emit = defineEmits(['addLabel', 'updateLabel'])

const adminStore = useAdminStore()
const visible = ref(false)
const uid = ref('')
const type = ref('')
const loading = ref(false)
const form = ref<Record<string, any>>({})

function openModal(typeModal: 'create' | 'update', userUID: string, label?: Label) {
  visible.value = true
  uid.value = userUID
  type.value = typeModal

  if (typeModal == 'create') form.value = {}
  else form.value = { ...label }
}

const title = computed(() => {
  if (type.value === 'create') return 'Create label'
  return 'Edit label'
})

const columns = computed(() => {
  const result: ZAdminFormColumn[] = [
    {
      fields: [
        {
          key: 'key',
          type: ZAdminFormFieldType.Input,
          label: 'Key(*)',
          value: form.value.key,
          disabled: type.value === 'update',
          validate: [Validate.required],
        },
        {
          key: 'value',
          type: ZAdminFormFieldType.Input,
          label: 'Value(*)',
          value: form.value.value,
          validate: [Validate.required],
        },
        {
          key: 'scope',
          type: ZAdminFormFieldType.Select,
          label: 'Scope(*)',
          value: form.value.scope,
          dataSource: [
            {
              value: LabelScope.Public,
            },
            {
              value: LabelScope.Private,
            },
          ],
          columns: [
            {
              key: 'value',
            },
          ],
          search: true,
          findBy: ['value'],
          valueKey: 'value',
          labelKey: 'value',
          validate: [Validate.required],
        },
      ],
    },
  ]

  return result
})

function onClick(label: Label) {
  if (type.value === 'create') CreateLabel(label)
  else UpdateLabel(label)

  visible.value = false
}

async function CreateLabel(label: Label) {
  loading.value = true
  const newLabel = await adminStore.AddLabelToUser({
    uid: uid.value,
    key: label.key,
    value: label.value,
    scope: label.scope,
  })
  if (newLabel) {
    emit('addLabel', newLabel)
  }
  loading.value = false
}

async function UpdateLabel(label: Label) {
  loading.value = true
  await adminStore.UpdateLabelFromUser(
    uid.value,
    label.key,
    label.scope,
    label.value,
    () => {
      emit('updateLabel', label)
    },
  )
  loading.value = false
}

defineExpose({
  openModal,
})
</script>

<template>
  <ZModal
    v-model="visible"
    class="page-dashboard-users-modal"
    :title="title"
  >
    <ZAdminForm :columns="columns" :loading="loading" @submit="(item: Label) => onClick(item)" />
  </ZModal>
</template>

<style lang="less">
.page-dashboard-users-modal {
  .z-overlay {
    width: 400px;
  }

  .z-input {
    height: 40px;
  }

  .z-dropdown-overlay {
    width: 250px;
  }

  .z-button {
    height: 40px;
    font-size: 18px;
    color: #fff;

    &:disabled {
      background-color: #e9eff3 !important;
      border: 1px solid #e9eff3 !important;
    }
  }

  .z-admin-form-button {
    display: block;

    .z-button {
      width: 100%;
      color: white;
      background-color: @primary-color;
    }
  }

  .z-form-col-item {
    padding: 0 !important;
  }
}
</style>
