<script setup lang="ts">
import LabelModal from './LabelModal.vue'

const props = defineProps<{
  user: User
  labels: Label[]
}>()

const emit = defineEmits(['addLabel', 'updateLabel', 'deleteLabel'])
const adminStore = useAdminStore()

const modelLabel = ref<InstanceType<typeof LabelModal>>()

async function DeleteLabel(label: Label) {
  await adminStore.RemoveLabelFromUser(props.user.uid, label.key, label.scope)
  emit('deleteLabel', label)
}
</script>

<template>
  <div class="user-label">
    <ZCard>
      <div class="flex justify-between">
        <div class="text-xl font-semibold">
          Label
        </div>
        <ZButton class="h-8" @click="modelLabel?.openModal('create', props.user.uid)">
          Add new
        </ZButton>
      </div>
      <div class="mt-4 flex rounded">
        <div
          v-for="l in labels"
          :key="l.id"
          class="user-label-item py-1 px-3 rounded-full mr-2"
          :class="[
            { 'user-label-item-blue': l.key === 'email' },
            { 'user-label-item-green': l.key === 'phone' },
            { 'user-label-item-yellow': l.key === 'profile' },
          ]"
          @click="modelLabel?.openModal('update', props.user.uid, l)"
        >
          {{ l.key }}:{{ l.value }}
          <ZIcon type="close" class="user-label-item-close" @click.stop="DeleteLabel(l)" />
        </div>
      </div>
    </ZCard>
  </div>
  <LabelModal
    ref="modelLabel"
    class="user-label"
    @add-label="(label: Label) => emit('addLabel', label)"
    @update-label="(label: Label) => emit('updateLabel', label)"
  />
</template>

<style lang="less">
.user-label {
  &-item {
    cursor: pointer;
    border: 1px solid @base-border-color;
    background-color: rgba(@gray-color, 0.1);

    &-blue {
      color: white;
      border: 1px solid @primary-color;
      background-color: @primary-color
    }

    &-green {
      color: white;
      border: 1px solid @up-color;
      background-color: @up-color;
    }

    &-yellow {
      color: white;
      border: 1px solid @warn-color;
      background-color: @warn-color
    }
  }

  .z-overlay {
    overflow: visible;
  }

  .z-admin-input {
    height: 38px;
    &-focused {
      label {
        transform: translateY(-20px);
      }
    }
  }

  &-dropdown {
    &-item {
      display: block;
      cursor: pointer;
      padding: 8px 12px;
      color: @text-color !important;

      &:hover {
        background-color: #f5f5f5;
      }
    }

    &-item + &-item {
      border-top: 1px solid @base-border-color;
    }
  }

  .z-dropdown {
    &-trigger {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 12px;
      width: 100%;
      height: 38px;
      border: 1px solid rgba(33, 47, 79, 0.1);
      border-radius: 4px;
      transition: all 0.3s;
      &:hover {
        border: 1px solid @primary-color;
      }
    }

    &-triggered {
      border: 1px solid @primary-color;
      box-shadow: 0 0 0 2px rgba(@primary-color, 0.2);
    }

    &-overlay {
      width: 100%;
      background-color: #fff;
      box-shadow: 0 3px 6px -4px #0000001f, 0 6px 16px #00000014, 0 9px 28px 8px #0000000d;
    }
  }
}
</style>
