<script setup lang="ts">
import { LabelScope } from '~/types'

const props = defineProps<{
  user: User
  labels: Label[]
}>()

const emit = defineEmits(['update:user', 'update:labels'])
const { user, labels } = useVModels(props, emit)
const loading = useState(() => false)

const adminStore = useAdminStore()

async function CreateLabel(uid: string, key: string, value: string, scope: string) {
  await adminStore.AddLabelToUser({
    uid,
    key,
    value,
    scope,
  })
  labels.value.push(
    {
      key,
      value,
      scope,
    } as Label,
  )
}

async function UpdateLabel(uid: string, key: string, scope: string, value: string) {
  await adminStore.UpdateLabelFromUser(uid, key, scope, value)
  const index = labels.value.findIndex(item => item.key === key)
  if (index !== -1) {
    labels.value[index].scope = scope
    labels.value[index].value = value
  }
}

async function DeleteLabel(uid: string, key: string, scope: string) {
  await adminStore.RemoveLabelFromUser(uid, key, scope)
  labels.value.splice(labels.value.findIndex(item => item.key === key), 1)
}

const emailVerified = computed(() => {
  return labels.value.findIndex(label => label.key === 'email' && label.value === 'verified') !== -1
})

const phoneVerified = computed(() => {
  return labels.value.findIndex(label => label.key === 'phone' && label.value === 'verified') !== -1
})

const profileVerified = computed(() => {
  return labels.value.findIndex(label => label.key === 'profile' && label.value === 'verified') !== -1
})

const countLevel = computed(() => {
  let count = 0
  if (emailVerified.value) count++
  if (phoneVerified.value) count++
  if (profileVerified.value) count++
  return count
})

async function changeLabelValue(key: string) {
  const label = labels.value.find(label => label.key === key)
  loading.value = true
  if (label) {
    if (label.value === 'pending') {
      await UpdateLabel(props.user.uid, label.key, label.scope, 'verified')
    } else {
      await DeleteLabel(props.user.uid, label.key, label.scope)
    }
  } else {
    await CreateLabel(props.user.uid, key, 'verified', LabelScope.Private)
  }
  user.value.level = countLevel.value
  loading.value = false
}
</script>

<template>
  <div class="pb-2">
    <ZCard>
      <div class="text-xl pb-4 font-semibold">
        Level: {{ user.level }}
      </div>
      <div class="flex justify-between items-center py-2">
        <div>
          <div>
            Email verification
          </div>
          <span class="text-gray-400">Confirm the email</span>
        </div>
        <div>
          <ZSwitch :model-value="emailVerified" :loading="loading" size="medium" @change="changeLabelValue('email')" />
        </div>
      </div>
      <div class="flex justify-between items-center py-2">
        <div>
          <div>
            Phone verification
          </div>
          <span class="text-gray-400">Confirm the phone</span>
        </div>
        <div>
          <ZSwitch :model-value="phoneVerified" :loading="loading" size="medium" @change="changeLabelValue('phone')" />
        </div>
      </div>
      <div class="flex justify-between items-center py-2">
        <div>
          <div>
            Profile verification
          </div>
          <span class="text-gray-400">Confirm the profile</span>
        </div>
        <div>
          <ZSwitch :model-value="profileVerified" :loading="loading" size="medium" @change="changeLabelValue('profile')" />
        </div>
      </div>
    </ZCard>
  </div>
</template>
