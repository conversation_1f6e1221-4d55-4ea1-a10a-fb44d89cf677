<script setup lang="ts">
import { format as formatDate, parseISO } from 'date-fns'

const props = defineProps<{
  activities: Activity[]
}>()

const steps = computed(() => {
  const result = Array<{
    id: number
    action: string
    date: string
    result: string
  }>()

  const removeRabish = (s: string) => {
    const replaceFunc = (match: string) => match.toUpperCase()
    let result = s
    result = result.replace('::', ' ')
    result = result.replace('_', ' ')
    result = result.replace(/ [a-zA-Z]/, replaceFunc)
    return result
  }

  props.activities.forEach((activity) => {
    result.push({
      id: activity.id,
      action: removeRabish(activity.action),
      date: formatDate(parseISO(activity.created_at), 'MMM dd, yyyy hh:mm a'),
      result: activity.result,
    })
  })

  return result
})
</script>

<template>
  <ZCard title="User Activity Timeline" class="user-activity">
    <ZSteps>
      <ZStep
        v-for="step in steps"
        :key="step.id"
        :title="step.action"
        :description="step.date"
        :color="step.result === 'succeed' ? 'green' : 'red'"
      />
    </ZSteps>
  </ZCard>
</template>

<style lang="less">
.user-activity {
  .timeline {
    &-point {
      &-red {
        border-color: @error-color;
      }

      &-green {
        border-color: @up-color;
      }
    }
  }
}
</style>
