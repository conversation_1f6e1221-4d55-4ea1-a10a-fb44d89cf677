<script setup lang="ts">
import { Line } from 'vue-chartjs'

const props = withDefaults(defineProps<{ activitiesMetricsData: UserActivityMetrics[] }>(), {
  activitiesMetricsData: () => [] as UserActivityMetrics[],
})

const chartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  elements: {
    line: {
      spanGaps: 40,
      tension: 0.5,
    },
  },
  scales: {
    x: {
      grid: {
        display: false,
        lineWidth: 1,
      },
    },
    y: {
      border: {
        dash: [5, 5],
      },
      grid: {
        display: true,
        lineWidth: 1,
      },
      ticks: {
        precision: 0,
      },
    },
  },
  interaction: {
    intersect: false,
    mode: 'index',
  },
  plugins: {
    tooltip: {
      position: 'nearest',
    },
    legend: {
      display: false,
    },
  },
  layout: {
    padding: {
      top: 24,
      left: 24,
      bottom: 0,
      right: 40,
    },
  },
} as any


const chartData = computed(() => {
  if (process.server) return

  const labels: string[] = []

  props.activitiesMetricsData.forEach((activity) => {
    labels.push(activity.date)
  })

  const loginData = props.activitiesMetricsData.map(activity => activity.login)
  const registerData = props.activitiesMetricsData.map(activity => activity.register)

  const data = {
    labels,
    datasets: [
      {
        fill: 'start',
        label: 'Login',
        backgroundColor : (ctx: any) => {
          const canvas = ctx.chart.ctx as CanvasRenderingContext2D
          const gradient = canvas.createLinearGradient(0, 0, 0, ctx.chart.height)

          gradient.addColorStop(0.5, 'rgba(42, 118, 244, 0.4)')
          gradient.addColorStop(1, 'rgba(255, 255, 255, 0.4)')

          return gradient
        },
        borderColor: 'rgba(49, 118, 244, 0.5)',
        data: loginData,
      },
      {
        fill: 'start',
        label: 'Register',
        borderColor: 'rgba(171, 198, 242, 1)',
        backgroundColor: (ctx: any) => {
          const canvas = ctx.chart.ctx
          const gradient = canvas.createLinearGradient(0, 0, 0, ctx.chart.height)

          gradient.addColorStop(0, 'rgba(135, 172, 233, 0.4)')
          gradient.addColorStop(1, 'rgba(255, 255, 255, 0.4)')

          return gradient
        },
        data: registerData,
        borderDash: [5, 5],
      },
    ],
  }

  return data
})
</script>

<template>
  <div class="flex-auto">
    <ClientOnly>
      <Line :options="chartOptions" :data="chartData!" />
    </ClientOnly>
  </div>
</template>
