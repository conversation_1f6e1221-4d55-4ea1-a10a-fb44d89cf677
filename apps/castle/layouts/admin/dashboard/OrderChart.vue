<script setup lang="ts">
import { Bar } from 'vue-chartjs'
import colors from '~/colors'

const props = withDefaults(defineProps<{ orderMetricsData: { action: string; count: number; date: string }[] }>(), {
  orderMetricsData: () => [] as { action: string; count: number; date: string }[],
})

const chartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  scales: {
    x: {
      grid: {
        display: false,
        lineWidth: 1,
      },
    },
    y: {
      border: {
        dash: [20, 5],
      },
      grid: {
        display: true,
        lineWidth: 1,
      },
      ticks: {
        precision: 0,
      },
    },
  },
  interaction: {
    intersect: false,
    mode: 'index',
  },
  plugins: {
    tooltip: {
      position: 'average',
    },
  },
  layout: {
    padding: {
      top: 12,
      left: 12,
      bottom: 0,
      right: 12,
    },
  },
} as any

const chartData = computed(() => {
  const labels: string[] = []

  props.orderMetricsData.forEach((order) => {
    if (!labels.includes(order.date)) labels.push(order.date)
  })

  return {
    labels,
    datasets: [
      {
        label: 'Filled',
        data: props.orderMetricsData.filter(order => order.action === 'Filled'),
        parsing: {
          xAxisKey: 'date',
          yAxisKey: 'count',
        },
        backgroundColor: colors['up-color'],
      },
      {
        label: 'Canceled',
        data: props.orderMetricsData.filter(order => order.action === 'Canceled'),
        parsing: {
          xAxisKey: 'date',
          yAxisKey: 'count',
        },
        backgroundColor: colors['primary-color'],
      },
    ],
  } as any
})
</script>

<template>
  <div class="flex-auto">
    <Bar :options="chartOptions" :data="chartData" />
  </div>
</template>
