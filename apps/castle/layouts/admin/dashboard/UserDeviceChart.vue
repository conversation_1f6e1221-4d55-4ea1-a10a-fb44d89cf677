<script setup lang="ts">
import { Doughnut } from 'vue-chartjs'

const props = defineProps<{
  userDeviceMetricsData?: UserDeviceMetrics
}>()

function capitalize(text: string) {
  text = text.replace(/_[a-z]/g, (match) => {
    return ` ${match.slice(1).toUpperCase()}`
  })
  return text.charAt(0).toUpperCase() + text.slice(1)
}

const convertDataUserDeviceChart = computed(() => {
  if (!props.userDeviceMetricsData) return []

  const result: { device: keyof UserDeviceActivity; count: number }[] = []
  const keys = Object.keys(props.userDeviceMetricsData.this_month) as (keyof UserDeviceActivity)[]
  for (const key of keys) {
    result.push({
      device: (key),
      count: props.userDeviceMetricsData.this_month[key],
    })
  }
  return result
})

const chartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  height: 250,
  cutout: '90%',
  plugins: {
    tooltip: {
      position: 'nearest',
    },
    legend: {
      display: false,
    },
  },
  layout: {
    padding: {
      top: 16,
      bottom: 28,
    },
  },
} as any

const chartData = computed(() => {
  const labels: string[] = []

  convertDataUserDeviceChart.value.forEach((item) => {
    labels.push(capitalize(item.device))
  })

  return {
    labels,
    datasets: [
      {
        data: convertDataUserDeviceChart.value.map(item => item.count),
        backgroundColor: ['#2a76f4', '#94baf9', '#d8e2f5', '#d8e2f5'],
      },
    ],
  }
})
</script>

<template>
  <div class="h-[260px]">
    <Doughnut :options="chartOptions" :data="chartData" />
  </div>
  <div class="z-device-user-statistical">
    <div class="z-device-user-statistical-row bold-text">
      <div class="z-device-user-statistical-col text-left">
        Device
      </div>
      <div class="z-device-user-statistical-col ">
        Sessions
      </div>
      <div class="z-device-user-statistical-col">
        Day
      </div>
      <div class="z-device-user-statistical-col">
        Week
      </div>
    </div>
    <div v-for="(item, key) in convertDataUserDeviceChart" :key="key" class="z-device-user-statistical-row">
      <div class="z-device-user-statistical-col text-left capitalize">
        {{ item.device }}
      </div>
      <div class="z-device-user-statistical-col">
        {{ item.count }}
      </div>
      <div class="z-device-user-statistical-col">
        {{ props.userDeviceMetricsData ? props.userDeviceMetricsData.today[item.device] - props.userDeviceMetricsData.yesterday[item.device] : String(0) }}
      </div>
      <div class="z-device-user-statistical-col">
        {{ props.userDeviceMetricsData ? props.userDeviceMetricsData.this_week[item.device] - props.userDeviceMetricsData.last_week[item.device] : String(0) }}
      </div>
    </div>
  </div>
</template>

<style lang="less">
.z {
  &-chart-device-user {
    height: 250px;
  }

  &-device-user-statistical {
    margin-top: 10px;

    &-row {
      padding: 4px 0;
      display: flex;
      justify-content: space-between;
      border-top: #eaf0f9 dashed 1px;
      border-bottom: #eaf0f9 dashed 1px;
    }

    &-col {
      flex: 1;
      text-align: right;
    }
  }
}
</style>
