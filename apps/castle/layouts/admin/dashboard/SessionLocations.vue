<script setup lang="ts">
import { Chart } from 'chart.js'
import { topojson } from 'chartjs-chart-geo'
import geo from '~/assets/json/geo.json'

const props = withDefaults(defineProps<{ sessionLocations: SessionLocation[] }>(), {
  sessionLocations: () => [] as SessionLocation[],
})

let chart: Chart

function getSessionLocationCount(country: string) {
  const sessionLocation = props.sessionLocations.filter((sessionLocation) => {
    if (country.includes('Hong Kong')) country = 'Hong Kong'

    return sessionLocation.country === country
  })
  if (!sessionLocation.length) return 0

  return {
    login: sessionLocation[0].login,
    register: sessionLocation[0].register,
  }
}

onMounted(() => {
  const chartRef = templateRef<HTMLCanvasElement>('chart')
  if (!chartRef.value) return

  const countries = (topojson.feature(geo as any, (geo as any).objects.countries) as any).features

  chart = new Chart(chartRef.value.getContext('2d')!, {
    type: 'choropleth',
    options: {
      plugins: {
        legend: {
          display: false,
        },
      },
      scales: {
        projection: {
          axis: 'x',
          projection: 'equalEarth',
          min: 0,
        },
        color: {
          axis: 'x',
          quantize: 5,
          ticks: {
            precision: 0,
          },
        },
      },
    },
    data: {
      labels: countries.map((d: { properties: { name: any } }) => d.properties.name),
      datasets: [{
        label: 'Countries',
        data: countries.map((d: any) => ({ feature: d, value: getSessionLocationCount(d.properties.name) })),
      }],
    },
  })
})
</script>

<template>
  <div class="flex items-center flex-auto">
    <canvas ref="chart" />
  </div>
</template>
