<script setup lang="ts">
import { Bar } from 'vue-chartjs'
import colors from '~/colors'

const adminStore  = useAdminStore()
const revenueStatisticsData = computed(() => adminStore.revenueStatistics)

const chartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  scales: {
    x: {
      grid: {
        display: false,
        lineWidth: 1,
      },
    },
    y: {
      border: {
        dash: [20, 5],
      },
      grid: {
        display: true,
        lineWidth: 1,
      },
      ticks: {
        precision: 0,
      },
    },
  },
  interaction: {
    intersect: false,
    mode: 'index',
  },
  plugins: {
    tooltip: {
      position: 'average',
    },
    legend: {
      display: false,
    },
  },
  layout: {
    padding: {
      top: 12,
      left: 12,
      bottom: 0,
      right: 12,
    },
  },
} as any

const chartData = computed(() => {
  const labels: string[] = []

  revenueStatisticsData.value.forEach((revenue) => {
    if (!labels.includes(revenue.recorded_at)) labels.push(revenue.recorded_at)
  })

  return {
    labels,
    datasets: [
      {
        label: 'Revenue',
        backgroundColor: colors['primary-color'],
        data: revenueStatisticsData.value.map(revenue => revenue.revenue),
      },
    ],
  } as any
})
</script>

<template>
  <div class="flex-auto">
    <Bar v-if="revenueStatisticsData.length > 0" :options="chartOptions" :data="chartData" />
  </div>
</template>
