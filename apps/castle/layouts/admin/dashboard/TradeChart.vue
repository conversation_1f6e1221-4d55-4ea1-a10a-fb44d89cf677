<script setup lang="ts">
import { Bar } from 'vue-chartjs'
import colors from '~/colors'

const props = withDefaults(defineProps<{ tradeMetricsData: TradeMetrics[] }>(), {
  tradeMetricsData: () => [] as TradeMetrics[],
})

const chartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  scales: {
    x: {
      grid: {
        display: false,
        lineWidth: 1,
      },
    },
    y: {
      border: {
        dash: [20, 5],
      },
      grid: {
        display: true,
        lineWidth: 1,
      },
      ticks: {
        precision: 0,
      },
    },
  },
  interaction: {
    intersect: false,
    mode: 'index',
  },
  plugins: {
    tooltip: {
      position: 'average',
    },
  },
  layout: {
    padding: {
      top: 12,
      left: 12,
      bottom: 0,
      right: 12,
    },
  },
} as any

const chartData = computed(() => {
  const labels: string[] = []

  props.tradeMetricsData.forEach((trade) => {
    if (!labels.includes(trade.date)) labels.push(trade.date)
  })

  return {
    labels,
    datasets: [
      {
        label: 'Trade',
        backgroundColor: colors['primary-color'],
        data: props.tradeMetricsData.map(trade => trade.count),
      },
    ],
  } as any
})
</script>

<template>
  <div class="flex-auto">
    <Bar :options="chartOptions" :data="chartData" />
  </div>
</template>
