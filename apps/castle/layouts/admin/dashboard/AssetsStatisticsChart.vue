<script setup lang="ts">
import { Line } from 'vue-chartjs'
import colors from '~/colors'

const adminStore  = useAdminStore()
const assetsStatisticsData = computed(() => adminStore.assetsStatistics)

const chartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  scales: {
    x: {
      grid: {
        display: false,
        lineWidth: 1,
      },
    },
    y: {
      border: {
        dash: [20, 5],
      },
      grid: {
        display: true,
        lineWidth: 1,
      },
      ticks: {
        precision: 0,
      },
    },
  },
  interaction: {
    intersect: false,
    mode: 'index',
  },
  plugins: {
    tooltip: {
      position: 'average',
    },
    legend: {
      display: false,
    },
  },
  layout: {
    padding: {
      top: 12,
      left: 12,
      bottom: 0,
      right: 12,
    },
  },
} as any

const chartData = computed(() => {
  const labels: string[] = []

  assetsStatisticsData.value.forEach((assets) => {
    if (!labels.includes(assets.recorded_at)) labels.push(assets.recorded_at)
  })

  return {
    labels,
    datasets: [
      {
        label: 'Total Assets',
        backgroundColor: colors['primary-color'],
        data: assetsStatisticsData.value.map(assets => assets.total_assets),
        borderColor: colors['primary-color'],
      },
    ],
  } as any
})
</script>

<template>
  <div class="flex-auto">
    <Line v-if="assetsStatisticsData.length > 0" :options="chartOptions" :data="chartData" />
  </div>
</template>
