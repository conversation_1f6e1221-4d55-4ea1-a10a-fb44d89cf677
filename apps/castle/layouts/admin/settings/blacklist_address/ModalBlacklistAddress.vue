<script setup lang="ts">
import type { ZAdminFormColumn } from '~/types'
import Validate from '~/validation/validate'

const adminStore = useAdminStore()
const visible = ref(false)
const type = ref('')
const form = ref<Record<string, any>>({})

const title = computed(() => {
  if (type.value === 'create') return 'Create Blacklist Address'
  if (type.value === 'update') return 'Update Blacklist Address'
})

function openModal(typeModal: 'create' | 'update', blacklistAddress?: BlacklistAddress) {
  visible.value = true
  type.value = typeModal

  if (typeModal === 'create') form.value = {}
  else form.value = { ...blacklistAddress }
}

const columns = computed(() => {
  const result: ZAdminFormColumn[] = [
    {
      fields: [
        {
          key: 'address',
          type: ZAdminFormFieldType.Input,
          label: 'Address(*)',
          value: form.value.address,
          validate: [Validate.required],
        },
      ],
    },
  ]

  return result
})

function onClick(blacklistAddress: BlacklistAddress) {
  if (type.value === 'create') CreateBlacklistAddress(blacklistAddress)
  else UpdateBlacklistAddress(blacklistAddress)

  visible.value = false
}

async function CreateBlacklistAddress(item: BlacklistAddress) {
  await adminStore.CreateBlacklistAddress({
    address: item.address,
  })
}

async function UpdateBlacklistAddress(item: BlacklistAddress) {
  await adminStore.UpdateBlacklistAddress({
    id: Number(form.value.id),
    address: item.address,
  })
}

defineExpose({
  openModal,
})
</script>

<template>
  <ZModal
    v-model="visible"
    class="layouts-admin-settings-blacklist-address-modal"
    :title="title"
  >
    <ZAdminForm :columns="columns" @submit="(item: BlacklistAddress) => onClick(item)" />
  </ZModal>
</template>

<style lang="less">
.layouts-admin-settings-blacklist-address-modal {
  .z-overlay {
    width: 400px;
  }

  .z-input {
    height: 40px;
  }

  .z-dropdown-overlay {
    width: 250px;
  }

  .z-dropdown {
    &-trigger {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 12px;
      width: 100%;
      height: 38px;
      border: 1px solid rgba(33, 47, 79, 0.1);
      border-radius: 4px;
      transition: all 0.3s;
      &:hover {
        border: 1px solid @primary-color;
      }
    }

    &-triggered {
      border: 1px solid @primary-color;
      box-shadow: 0 0 0 2px rgba(@primary-color, 0.2);
    }

    &-overlay {
      width: 100%;
      background-color: #fff;
      box-shadow: 0 3px 6px -4px #0000001f, 0 6px 16px #00000014, 0 9px 28px 8px #0000000d;
    }
  }

  .z-admin-form-button {
    display: block;

    .z-button {
      width: 100%;
      color: white;
      background-color: @primary-color;
    }
  }

  .z-form-col-item {
    padding: 0 !important;
  }
}
</style>
