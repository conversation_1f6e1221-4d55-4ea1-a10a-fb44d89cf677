<script setup lang="ts">
import type { ZAdminFormColumn } from '~/types'
import Validate from '~/validation/validate'

const props = defineProps<{
  blockchainId: number
}>()

const visible = ref(false)
const blockchainId = ref(props.blockchainId)

function openModal() {
  visible.value = true
}

defineExpose({
  openModal,
})

const { data: lastestBlock } = await useAsyncData(() => useAdminStore().GetBlockchainLatestBlock(blockchainId.value), {
  default: () => 0,
})

const columns = computed(() => {
  const result: ZAdminFormColumn[] = [
    {
      fields: [
        {
          key: 'lastest_block',
          type: ZAdminFormFieldType.Input,
          label: 'Lastest Block(*)',
          value: lastestBlock.value,
          disabled: true,
          validate: [Validate.required],
        },
        {
          key: 'block_number',
          type: ZAdminFormFieldType.Input,
          label: 'Block Number(*)',
          value: '',
          validate: [Validate.required],
          autoFocus: true,
        },
      ],
    },
  ]

  return result
})

async function onClick(data: { block_number: number }) {
  await useAdminStore().ProcessBlock({
    id: blockchainId.value,
    block_number: Number(data.block_number),
  }, () => {
    visible.value = false
  })
}
</script>

<template>
  <ZModal
    v-model="visible"
    class="layouts-admin-settings-blockchain"
    title="Process Block"
  >
    <ZAdminForm :columns="columns" @submit="onClick" />
  </ZModal>
</template>

<style lang="less">
.layouts-admin-settings-blockchain {
  .z-overlay {
    width: 400px;
  }

  .z-input {
    height: 40px;
  }

  .z-dropdown-overlay {
    width: 250px;
  }

  .z-button {
    height: 32px;
    font-size: 16px;
    color: #fff;

    &:disabled {
      background-color: #e9eff3 !important;
      border: 1px solid #e9eff3 !important;
    }
  }

  .z-admin-form-button {
    display: block;

    .z-button {
      width: 100%;
      color: white;
      background-color: @primary-color;
    }
  }

  .z-form-col-item {
    padding: 0 !important;
  }
}
</style>
