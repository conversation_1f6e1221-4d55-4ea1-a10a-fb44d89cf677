<script setup lang="ts">
import type { AsyncDataExecuteOptions } from 'nuxt/dist/app/composables/asyncData'
import { BlockchainClient } from '@zsmartex/types'
import { BlockchainStatus, type ZAdminFormColumn, ZAdminFormFieldType } from '~/types'
import Validate from '~/validation/validate'
import ModalProcess from '~/layouts/admin/settings/blockchains/ModalProcess.vue'

const props = defineProps<{
  blockchainId?: number
  modelValue: Blockchain
  pending?: boolean
  refresh?: (opts?: AsyncDataExecuteOptions) => Promise<void>
}>()

const emit = defineEmits(['update:modelValue'])

const modalProcess = ref<InstanceType<typeof ModalProcess>>()

const router = useRouter()
const adminStore = useAdminStore()
const menuActionStore = useMenuActionStore()

const isNewUpdate = ref(false)
const blockchain = useVModel(props, 'modelValue', emit)
const options = reactive<Record<string, string | boolean>>({ ...blockchain.value.options })
const oldOptions = reactive<Record<string, string | boolean>>({ ...blockchain.value.options })

const loading = useState(() => false)

const clientColumns: ZTableColumn[] = [
  {
    key: 'text',
    scopedSlots: true,
  },
]

function isUpdateAction() {
  if (props.blockchainId) return true
  return false
}

function changeOptionsValue(key: string, value: string | boolean | number) {
  options[key] = value
  checkNewUpdate()
}

function checkNewUpdate() {
  for (const key in options) {
    if (options[key] !== oldOptions[key]) {
      isNewUpdate.value = true
      return
    }
  }

  isNewUpdate.value = false
}

if (isUpdateAction()) {
  menuActionStore.setMenu([
    {
      key: 'refresh',
      title: 'Refresh',
      callback: async () => {
        loading.value = true
        props.refresh!()
        loading.value = false
      },
    },
    {
      key: 'process_block',
      title: 'Process Block',
      callback: async () => {
        modalProcess.value?.openModal()
      },
    },
  ])
}

const columns = computed<ZAdminFormColumn[]>(() => {
  return [
    {
      fields: [
        {
          key: 'name',
          type: ZAdminFormFieldType.Input,
          label: 'Name(*)',
          value: blockchain.value.name,
          class: '!w-6/12',
          validate: [Validate.required, Validate.maxLength(105)],
        },
        {
          key: 'status',
          type: ZAdminFormFieldType.Switch,
          label: 'Status',
          value: blockchain.value.status === BlockchainStatus.Active,
          class: '!w-6/12 flex justify-end items-center mt-0!',
        },
        {
          key: 'key',
          type: ZAdminFormFieldType.Input,
          label: 'Key(*)',
          value: blockchain.value.key,
          validate: [Validate.required, Validate.maxLength(32)],
        },
        {
          key: 'client',
          type: ZAdminFormFieldType.Select,
          label: 'Client(*)',
          value: blockchain.value.client,
          dataSource: adminStore.blockchainClients.map((item) => {
            return {
              key: item,
              text: item,
            }
          }),
          search: true,
          columns: clientColumns,
          replaceFunc: (text: string) => text.toUpperCase(),
          findBy: ['key'],
          valueKey: 'key',
          labelKey: 'text',
          validate: [Validate.required],
          class: blockchain.value.client === BlockchainClient.Qubic ? '!w-6/12' : '',
        },
        ...(blockchain.value.client === BlockchainClient.Qubic
          ? [
              {
                key: 'version',
                type: ZAdminFormFieldType.Switch,
                label: 'Version 2',
                value: options.version === 2,
                class: '!w-6/12 flex justify-end !mt-12 items-center',
                onChange: (value: boolean) => changeOptionsValue('version', value ? 2 : 1),
                validate: [Validate.required],
              },
            ]
          : []),
        {
          key: 'warning',
          type: ZAdminFormFieldType.Input,
          label: 'Warning',
          value: blockchain.value.warning,
          validate: [Validate.maxLength(255)],
        },
        {
          key: 'server',
          type: ZAdminFormFieldType.Input,
          label: 'Server',
          value: blockchain.value.server,
          // validate: blockchainClientsCustomURI.includes(blockchain.value.client) ? [] : [Validate.url],
        },
        {
          key: 'protocol',
          type: ZAdminFormFieldType.Input,
          label: 'Protocol(*)',
          value: blockchain.value.protocol,
          validate: [Validate.required, Validate.maxLength(32)],
        },
      ],
    },
    {
      fields: [
        {
          key: 'height',
          type: ZAdminFormFieldType.Input,
          label: 'Height(*)',
          value: blockchain.value.height,
          class: '!w-6/12',
          validate: [Validate.required, Validate.integer, Validate.min(0)],
        },
        {
          key: 'multiple_collection',
          type: ZAdminFormFieldType.Switch,
          label: 'Multiple Collection',
          value: blockchain.value.multiple_collection,
          class: '!w-6/12 flex justify-end items-center mt-0!',
        },
        {
          key: 'min_confirmations',
          type: ZAdminFormFieldType.Input,
          label: 'Min Confirmations(*)',
          value: blockchain.value.min_confirmations,
          class: '!w-6/12',
          validate: [Validate.required, Validate.integer, Validate.min(0)],
        },
        {
          key: 'withdraw_queue',
          type: ZAdminFormFieldType.Switch,
          label: 'Withdraw Queue',
          value: blockchain.value.withdraw_queue,
          class: '!w-6/12 flex justify-end items-center mt-0!',
        },
        {
          key: 'explorer_transaction',
          type: ZAdminFormFieldType.Input,
          label: 'Explorer Transaction',
          value: blockchain.value.explorer_transaction,
          validate: [Validate.url],
        },
        {
          key: 'explorer_address',
          type: ZAdminFormFieldType.Input,
          label: 'Explorer Address',
          value: blockchain.value.explorer_address,
          validate: [Validate.url],
        },
      ],
    },
  ]
})

const optionsColumns: ZTableColumn[] = computed(() => {
  const result: ZAdminFormColumn[] = [{
    fields: [],
  }, {
    fields: [],
  }]

  switch (blockchain.value.client) {
    case BlockchainClient.BitcoinFork:
      result[0].fields.push(
        {
          key: 'private_getblock_params',
          type: ZAdminFormFieldType.Select,
          label: 'GetBlock Params (optional)',
          dataSource: [
            {
              value: true,
            },
            {
              value: false,
            },
            {
              value: 0,
            },
            {
              value: 1,
            },
            {
              value: 2,
            },
          ],
          columns: [
            {
              key: 'value',
              scopedSlots: true,
            },
          ],
          search: true,
          findBy: ['value'],
          valueKey: 'value',
          value: options.private_getblock_params,
          replaceFunc: (text: string) => text.toString().toUpperCase(),
          labelKey: 'value',
          onChange: (value: string) => { changeOptionsValue('private_getblock_params', value) },
        },
        {
          key: 'private_getrawtransaction_params',
          type: ZAdminFormFieldType.Select,
          label: 'GetRawTransaction Params (optional)',
          dataSource: [
            {
              value: true,
            },
            {
              value: false,
            },
            {
              value: 0,
            },
            {
              value: 1,
            },
            {
              value: 2,
            },
          ],
          columns: [
            {
              key: 'value',
              scopedSlots: true,
            },
          ],
          search: true,
          findBy: ['value'],
          valueKey: 'value',
          value: options.private_getrawtransaction_params,
          replaceFunc: (text: string) => text.toString().toUpperCase(),
          labelKey: 'value',
          onChange: (value: string) => { changeOptionsValue('private_getrawtransaction_params', value) },
        },
      )
      break
  }

  return result
})

async function SubmitBlockchain(blockchain: Blockchain) {
  loading.value = true
  if (isUpdateAction() && props.blockchainId) {
    await adminStore.UpdateBlockchain(props.blockchainId, {
      key: blockchain.key,
      name: blockchain.name,
      client: blockchain.client,
      server: blockchain.server,
      protocol: blockchain.protocol,
      height: Number(blockchain.height),
      explorer_transaction: blockchain.explorer_transaction,
      explorer_address: blockchain.explorer_address,
      warning: blockchain.warning,
      status: blockchain.status ? BlockchainStatus.Active : BlockchainStatus.Disabled,
      min_confirmations: Number(blockchain.min_confirmations),
      multiple_collection: blockchain.multiple_collection,
      withdraw_queue: blockchain.withdraw_queue,
      options,
    }, () => {
      router.push('/settings/blockchains')
    })
  } else {
    await adminStore.CreateBlockchain({
      key: blockchain.key as string,
      name: blockchain.name as string,
      client: blockchain.client as BlockchainClient,
      server: blockchain.server as string,
      protocol: blockchain.protocol as string,
      height: Number(blockchain.height),
      explorer_transaction: blockchain.explorer_transaction as string,
      explorer_address: blockchain.explorer_address as string,
      warning: blockchain.warning as string,
      status: blockchain.status ? BlockchainStatus.Active : BlockchainStatus.Disabled,
      min_confirmations: Number(blockchain.min_confirmations),
      multiple_collection: blockchain.multiple_collection,
      withdraw_queue: blockchain.withdraw_queue,
      options,
    }, () => {
      router.push('/settings/blockchains')
    })
  }
  loading.value = false
}
</script>

<template>
  <div>
    <ZCard class="mb-4" :title="isUpdateAction() ? 'Update Blockchain' : 'Create Blockchain'">
      <ZAdminForm :is-new-update="isNewUpdate" :columns="columns" :loading="loading" :pending="pending" @submit="SubmitBlockchain" />
    </ZCard>
    <ModalProcess v-if="isUpdateAction()" ref="modalProcess" :blockchain-id="blockchainId!" />
    <ZCard v-if="blockchain.client === BlockchainClient.BitcoinFork" class="mb-4">
      <ZAdminForm :columns="optionsColumns" :loading="loading" hidden-submit :pending="pending" />
    </ZCard>
  </div>
</template>

<style lang="less">
.page-dashboard-settings-blockchains-action {
  .z-card {
    .z-admin-form {
      padding-top: 0;
    }
  }

  &-dropdown {
    &-item {
      display: block;
      cursor: pointer;
      padding: 8px 12px;
      color: @text-color !important;
      background-color: rgba(@gray-color, 0.1);

      &:hover {
        background-color: white;
      }
    }

    &-item + &-item {
      border-top: 1px solid @base-border-color;
    }
  }

  .z-dropdown {
    &-trigger {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 12px;
      width: 100%;
      height: 38px;
      border: 1px solid rgba(33, 47, 79, 0.1);
      border-radius: 4px;
      transition: all 0.3s;
      &:hover {
        border: 1px solid @primary-color;
      }
    }

    &-triggered {
      border: 1px solid @primary-color;
      box-shadow: 0 0 0 2px rgba(@primary-color, 0.2);
    }

    &-overlay {
      width: 100%;
      background-color: #fff;
      box-shadow: 0 3px 6px -4px #0000001f, 0 6px 16px #00000014, 0 9px 28px 8px #0000000d;
    }
  }

  .z-input {
    background-color: rgba(@gray-color, 0.05);
  }

  .z-button {
    width: 68px;
    height: 32px;
    background-color: @primary-color !important;
    color: white !important;
  }

  .z-form-row {
    &-label {
      margin: 0;
    }
  }
}
</style>
