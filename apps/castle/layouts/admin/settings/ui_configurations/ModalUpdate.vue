<script setup lang="ts">
import { VueDraggable } from '@zsmartex/draggable'

const adminStore = useAdminStore()
const visible = ref(false)
const loading = ref(false)
const search = ref('')
const form = ref<Record<string, any>>({})

const dataValues = computed(() => {
  switch (form.value.type) {
    case 'quote_list':
      return [{ id: 'all' }, ...adminStore.currencies.filter(c => (c.id.includes(search.value.toLowerCase()) || c.name.toLowerCase().includes(search.value.toLowerCase())) && !form.value.value.includes(c.id))]
    case 'market_feature_markets':
      return adminStore.markets.filter(m => (m.id.includes(search.value.toLowerCase()) || `${m.base_unit}/${m.quote_unit}`.toLowerCase().includes(search.value.toLowerCase())) && !form.value.value.includes(m.id))
    case 'home_feature_markets':
      return adminStore.markets.filter(m => (m.id.includes(search.value.toLowerCase()) || `${m.base_unit}/${m.quote_unit}`.toLowerCase().includes(search.value.toLowerCase())) && !form.value.value.includes(m.id))
    default:
      return []
  }
})

function openModal(configuration: UIConfiguration) {
  visible.value = true
  form.value = { ...configuration }
  if (!form.value.value) {
    form.value.value = []
  }
}

function GetMarketName(id: string) {
  const market = adminStore.markets.find(m => m.id === id)
  if (!market) return id

  return `${market.base_unit.toUpperCase()}/${market.quote_unit.toUpperCase()}`
}

function AddValue(item: any) {
  if (form.value.type === 'market_feature_markets') form.value.value.push(item.id)
  else if (form.value.type === 'home_feature_markets') form.value.value.push(item.id)
  else if (form.value.type === 'quote_list') form.value.value.push(item.id)
}

function RemoveValue(value: string) {
  form.value.value.splice(form.value.value.indexOf(value), 1)
}

function onClick() {
  UpdateUIConfiguration()

  visible.value = false
}

async function UpdateUIConfiguration() {
  await adminStore.UpdateUIConfiguration({
    type: form.value.type,
    value: form.value.value,
  })
}

defineExpose({
  openModal,
})
</script>

<template>
  <ZModal
    v-model="visible"
    class="layouts-admin-settings-ui-configurations-modal"
    title="Update Configuration"
  >
    <ZFormRow label="Type">
      <div class="capitalize">
        {{ form.type.replace("_", " ") }}
      </div>
    </ZFormRow>
    <ZFormRow :type="InputType.Text">
      <div class="flex items-center justify-between">
        <span>Values</span>
        <ZDropdown trigger="click" :placement="Placement.BottomRight">
          <span class="capitalize primary">Add</span>
          <template #overlay>
            <div class="layouts-admin-settings-ui-configurations-modal-dropdown-container">
              <ZAdminInput v-model="search" :type="InputType.Text" :auto-focus="true" placeholder="Search" />
              <div
                v-for="(item, i) in dataValues"
                :key="i"
                class="layouts-admin-settings-ui-configurations-modal-dropdown-item"
                @click="AddValue(item)"
              >
                <span v-if="form.type === 'quote_list'">{{ item.id.toUpperCase() }}</span>
                <span v-else-if="form.type === 'market_feature_markets'">{{ `${item.base_unit.toUpperCase()}/${item.quote_unit.toUpperCase()}` }}</span>
                <span v-else-if="form.type === 'home_feature_markets'">{{ `${item.base_unit.toUpperCase()}/${item.quote_unit.toUpperCase()}` }}</span>
              </div>
            </div>
          </template>
        </ZDropdown>
      </div>
      <VueDraggable
        v-model="form.value"
        :animation="150"
      >
        <div
          v-for="item, index in form.value"
          :key="index"
          class="drag-row"
        >
          <div class="drag-column">
            <span>{{ (form.type === 'market_feature_markets' || form.type === 'home_feature_markets') ? GetMarketName(item) : item.toUpperCase() }}</span>
          </div>
          <div class="w-[60px] flex items-center justify-end">
            <ZIconTimesRegular @click="RemoveValue(item)" />
          </div>
        </div>
      </VueDraggable>
    </ZFormRow>
    <div class="layouts-admin-settings-ui-configurations-modal-button flex justify-end">
      <ZButton :loading="loading" type="primary" class="leading-8!" @click="onClick">
        Submit
      </ZButton>
    </div>
  </ZModal>
</template>

<style lang="less">
.layouts-admin-settings-ui-configurations-modal {
  .z-overlay {
    width: 400px;
  }

  .z-admin-input {
    &-focused {
      box-shadow: none;
    }
  }

  &-dropdown {
    &-container {
      max-height: 200px;
      overflow-y: auto;
    }

    &-item {
      padding: 12px 16px;
      cursor: pointer;

      & + & {
        border-top: 1px solid @base-border-color;
      }
    }
  }

  .z-dropdown {
    .z-overlay {
      background-color: #fff;
      border-radius: 4px;
    }
  }

  &-button {
    margin-top: 36px;

    button {
      width: 100%;
      height: 40px;
    }
  }

  svg {
    cursor: pointer;
    width: 18px;
    height: 18px;
    fill: @gray-color;
  }

  span.primary {
    color: @primary-color;
  }

  span.gray {
    color: @gray-color;
  }

  .drag {
    &-row {
      display: flex;
      align-items: center;
      width: 100%;
      height: 48px;

      & + .drag-row {
        border-top: 1px solid rgba(33, 47, 79, 0.1);
      }
    }

    &-column {
      flex: 1;
      display: block;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .z-input {
    height: 40px;
  }

  .z-dropdown-overlay {
    width: 250px;
  }

  .z-form-row {
    &-content {
      height: auto;
    }
  }

  .z-admin-form-button {
    display: block;

    .z-button {
      width: 100%;
      color: white;
      background-color: @primary-color;
    }
  }

  .z-form-col-item {
    padding: 0 !important;
  }
}
</style>
