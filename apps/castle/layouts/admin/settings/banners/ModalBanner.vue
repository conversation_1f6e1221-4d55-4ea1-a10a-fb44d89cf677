<script setup lang="ts">
const props = defineProps<{
  modelValue: boolean
  image: string
  isCreate?: boolean
}>()

const emit = defineEmits(['update:modelValue', 'submit', 'change'])
const visible = useVModel(props, 'modelValue', emit)
</script>

<template>
  <ZModal
    v-model="visible"
    class="layouts-admin-settings-banners-modal"
    title="Image"
  >
    <div class="border-4 border-dashed">
      <div :class="{ loading: !isCreate }">
        <input id="image_preview" type="file" class="hidden" @change="(e) => emit('change', e)">

        <label for="image_preview" class="flex justify-center items-center block">
          <img v-if="image" :src="image" class="w-full">
          <ZIcon v-else type="upload" class="h-[200px] text-[80px] flex items-center" />
        </label>
      </div>
    </div>
    <ZButton
      v-if="!isCreate"
      class="!w-full mt-4 mb-2 text-base text-white"
      type="primary"
      html-type="submit"
      @click="emit('submit')"
    >
      Submit
    </ZButton>
  </ZModal>
</template>

<style lang="less">
.layouts-admin-settings-banners-modal {
  .z-overlay {
    width: 500px;
  }

  .z-input {
    height: 40px;
  }

  .z-dropdown-overlay {
    width: 250px;
  }

  .z-button {
    height: 32px;
    font-size: 16px;
    color: #fff;

    &:disabled {
      background-color: #e9eff3 !important;
      border: 1px solid #e9eff3 !important;
    }
  }

  .loading {
    min-height: 200px;
    background-color: #F1F2F3;
    background: transparent url(@/assets/img/loading_gif.gif) center center no-repeat;
  }
}
</style>
