<script setup lang="ts">
import { WhitelistedSmartContractState, type ZAdminFormColumn } from '~/types'
import Validate from '~/validation/validate'

const adminStore = useAdminStore()
const visible = ref(false)
const type = ref('')
const form = ref<Record<string, any>>({})

const title = computed(() => {
  if (type.value === 'create') return 'Create Whitelisted Smart Contract'
  if (type.value === 'update') return 'Update Whitelisted Smart Contract'
})

function openModal(typeModal: 'create' | 'update', whitelistedSmartContract?: WhitelistedSmartContract) {
  visible.value = true
  type.value = typeModal

  if (typeModal === 'create') form.value = {}
  else form.value = { ...whitelistedSmartContract }
}

const columns = computed(() => {
  const result: ZAdminFormColumn[] = [
    {
      fields: [
        {
          key: 'blockchain_key',
          type: ZAdminFormFieldType.Select,
          label: 'Blockchain(*)',
          value: form.value.blockchain_key,
          dataSource: adminStore.blockchains,
          columns: [
            {
              key: 'name',
            },
          ],
          findBy: ['name'],
          valueKey: 'key',
          labelKey: 'name',
          validate: [Validate.required],
        },
        {
          key: 'state',
          type: ZAdminFormFieldType.Select,
          label: 'State(*)',
          value: form.value.state,
          dataSource: [
            {
              value: WhitelistedSmartContractState.Active,
            },
            {
              value: WhitelistedSmartContractState.Disabled,
            },
          ],
          columns: [
            {
              key: 'value',
              scopedSlots: true,
            },
          ],
          replaceFunc: (text: string) => text[0].toUpperCase() + text.slice(1),
          findBy: ['value'],
          valueKey: 'value',
          labelKey: 'value',
          validate: [Validate.required],
        },
        {
          key: 'address',
          type: ZAdminFormFieldType.Input,
          label: 'Address(*)',
          value: form.value.address,
          validate: [Validate.required],
        },
        {
          key: 'description',
          type: ZAdminFormFieldType.Input,
          label: 'Description',
          value: form.value.description,
        },
      ],
    },
  ]

  return result
})

function onClick(whitelistedSmartContract: WhitelistedSmartContract) {
  if (type.value === 'create') CreateWhitelistedSmartContract(whitelistedSmartContract)
  else UpdateWhitelistedSmartContract(whitelistedSmartContract)

  visible.value = false
}

async function CreateWhitelistedSmartContract(item: WhitelistedSmartContract) {
  await adminStore.CreateWhitelistedSmartContract({
    blockchain_key: item.blockchain_key,
    state: item.state,
    address: item.address,
    description: item.description,
  })
}

async function UpdateWhitelistedSmartContract(item: WhitelistedSmartContract) {
  await adminStore.UpdateWhitelistedSmartContract({
    id: Number(form.value.id),
    blockchain_key: item.blockchain_key,
    state: item.state,
    address: item.address,
    description: item.description,
  })
}

defineExpose({
  openModal,
})
</script>

<template>
  <ZModal
    v-model="visible"
    class="layouts-admin-settings-whitelisted-smart-contract-modal"
    :title="title"
  >
    <ZAdminForm :columns="columns" @submit="(item: WhitelistedSmartContract) => onClick(item)" />
  </ZModal>
</template>

<style lang="less">
.layouts-admin-settings-whitelisted-smart-contract-modal {
  .z-overlay {
    width: 400px;
  }

  .z-input {
    height: 40px;
  }

  .z-dropdown-overlay {
    width: 250px;
  }

  .z-dropdown {
    &-trigger {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 12px;
      width: 100%;
      height: 38px;
      border: 1px solid rgba(33, 47, 79, 0.1);
      border-radius: 4px;
      transition: all 0.3s;
      &:hover {
        border: 1px solid @primary-color;
      }
    }

    &-triggered {
      border: 1px solid @primary-color;
      box-shadow: 0 0 0 2px rgba(@primary-color, 0.2);
    }

    &-overlay {
      width: 100%;
      background-color: #fff;
      box-shadow: 0 3px 6px -4px #0000001f, 0 6px 16px #00000014, 0 9px 28px 8px #0000000d;
    }
  }

  .z-admin-form-button {
    display: block;

    .z-button {
      width: 100%;
      color: white;
      background-color: @primary-color;
    }
  }

  .z-form-col-item {
    padding: 0 !important;
  }
}
</style>
