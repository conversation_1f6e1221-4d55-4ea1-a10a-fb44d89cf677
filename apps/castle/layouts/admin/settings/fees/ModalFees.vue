<script setup lang="ts">
import type { ZAdminFormColumn } from '~/types'
import Validate from '~/validation/validate'

const adminStore = useAdminStore()
const visible = ref(false)
const type = ref('')
const form = ref<Record<string, any>>({})

function openModal(typeModal: 'create' | 'update', tradingFee?: TradingFee) {
  visible.value = true
  type.value = typeModal

  if (typeModal == 'create') form.value = {}
  else form.value = { ...tradingFee }
}

const columns = computed(() => {
  const result: ZAdminFormColumn[] = [
    {
      fields: [
        {
          key: 'group',
          type: ZAdminFormFieldType.Input,
          label: 'Group(*)',
          value: form.value.group,
          validate: [Validate.required],
        },
        {
          key: 'market_id',
          type: ZAdminFormFieldType.Input,
          label: 'Market ID(*)',
          value: form.value.market_id,
          validate: [Validate.required],
        },
        {
          key: 'maker',
          type: ZAdminFormFieldType.Input,
          label: 'Maker Fee(*)',
          value: form.value.maker,
          validate: [Validate.required, Validate.double(8), Validate.min(0)],
        },
        {
          key: 'taker',
          type: ZAdminFormFieldType.Input,
          label: 'Taker(*)',
          value: form.value.taker,
          validate: [Validate.required, Validate.double(8), Validate.min(0)],
        },
      ],
    },
  ]

  return result
})

const title = computed(() => {
  if (type.value === 'create') return 'Create fees schedule'
  if (type.value === 'update') return 'Update fees schedule'
})

function onClick(tradingFee: TradingFee) {
  if (type.value === 'create') CreateTradingFee(tradingFee)
  else UpdateTradingFee(tradingFee)

  visible.value = false
}

async function CreateTradingFee(item: TradingFee) {
  const tradingFee = await adminStore.CreateTradingFee({
    group: item.group,
    market_id: item.market_id as string,
    maker: Number(item.maker),
    taker: Number(item.taker),
  })

  if (Object.keys(tradingFee).length > 0) {
    adminStore.trading_fees.push(tradingFee)
  }
}

async function UpdateTradingFee(item: TradingFee) {
  await adminStore.UpdateTradingFee({
    id: Number(form.value.id),
    group: item.group,
    market_id: item.market_id as string,
    maker: Number(item.maker),
    taker: Number(item.taker),
  }, () => {
    const index = adminStore.trading_fees.findIndex(t => t.id === Number(form.value.id))
    if (index !== -1) {
      item.id = Number(form.value.id)
      adminStore.trading_fees[index] = item
    }
  })
}

defineExpose({
  openModal,
})
</script>

<template>
  <ZModal
    v-model="visible"
    class="layouts-admin-settings-fees-modal"
    :title="title"
  >
    <ZAdminForm :columns="columns" @submit="(item: TradingFee) => onClick(item)" />
  </ZModal>
</template>

<style lang="less">
.layouts-admin-settings-fees-modal {
  .z-overlay {
    width: 400px;
  }

  .z-input {
    height: 40px;
  }

  .z-dropdown-overlay {
    width: 250px;
  }

  .z-button {
    height: 32px;
    font-size: 16px;
    color: #fff;

    &:disabled {
      background-color: #e9eff3 !important;
      border: 1px solid #e9eff3 !important;
    }
  }

  .z-admin-form-button {
    display: block;

    .z-button {
      width: 100%;
      color: white;
      background-color: @primary-color;
    }
  }

  .z-form-col-item {
    padding: 0 !important;
  }
}
</style>
