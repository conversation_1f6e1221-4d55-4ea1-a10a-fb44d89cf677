<script setup lang="ts">
import { RateLimitVerb, type ZAdminFormColumn } from '~/types'
import { RateLimitState } from '~/types'
import Validate from '~/validation/validate'

const adminStore = useAdminStore()
const visible = ref(false)
const type = ref('')
const form = ref<Record<string, any>>({})
const isNewUpdate = ref(false)
const uids = ref([''])
const emails = ref([''])
const ips = ref([''])

const oldValue = ref<RateLimit>({} as RateLimit)
const id = ref(0)
const max = ref('')
const duration = ref('');
const verb = ref<RateLimitVerb>(RateLimitVerb.ALL);
const path = ref('')
const state = ref(false)

const pathError = computed(() => {
  if (!/^api\/v2(?!.*\/$).*/.test(path.value)) return 'Path must start with "api/v2"'

  return ''
})

const maxError = computed(() => {
  if (max.value.length && (Number(max.value) <= 0 || !/^[-]*\d+$/.test(max.value) )) return 'Max must integer and > 0'

  return ''
})

const durationError = computed(() => {
  if (duration.value.length && (Number(duration.value) <= 0 || !/^[-]*\d+$/.test(duration.value) )) return 'Duration must integer and > 0'

  return ''
})

const title = computed(() => {
  if (type.value === 'create') return 'Create rate limit'
  if (type.value === 'update') return 'Update rate limit'
})

function openModal(typeModal: 'create' | 'update', rateLimit?: RateLimit) {
  visible.value = true
  type.value = typeModal

  uids.value = ['']
  emails.value = ['']
  ips.value = ['']

  if (typeModal === 'create') {
    id.value = 0
    max.value = ''
    duration.value = ''
    verb.value = RateLimitVerb.ALL
    path.value = ''
    state.value = false
  }
  else if (typeModal === 'update' && rateLimit) {
    id.value = rateLimit!.id
    max.value = String(rateLimit!.max)
    duration.value = String(rateLimit!.duration)
    verb.value = rateLimit!.verb
    path.value = rateLimit!.path
    state.value = rateLimit!.state === RateLimitState.Active
    oldValue.value = { ...rateLimit }

    if (rateLimit!.settings) {
      if (rateLimit!.settings.uids.length) {
        uids.value = [...rateLimit!.settings.uids, '']
      }
      if (rateLimit!.settings.emails.length) {
        emails.value = [...rateLimit!.settings.emails, '']
      }
      if (rateLimit!.settings.ips.length) {
        ips.value = [...rateLimit!.settings.ips, '']
      }
    }
  }
}

const disabledButton = computed(() => {
  if (!max.value.length) return true
  if (!duration.value.length) return true
  if (!path.value.length) return true
  if (!verb.value.length) return true
  if (pathError.value.length) return true
  if (durationError.value.length) return true
  if (maxError.value.length) return true

  return false
})

function onClick() {
  const settings = {
    uids: uids.value.filter(v => v !== ''),
    emails: emails.value.filter(v => v !== ''),
    ips: ips.value.filter(v => v !== ''),
  }

  const rateLimit: RateLimit = {
    max: Number(max.value),
    duration: Number(duration.value),
    path: path.value,
    state: state.value ? RateLimitState.Active : RateLimitState.Disabled,
    verb: verb.value as RateLimitVerb,
    settings: settings,
  }

  if (type.value === 'create') CreateRateLimit(rateLimit)
  else UpdateRateLimit(rateLimit)

  visible.value = false
}

async function CreateRateLimit(item: RateLimit) {
  await adminStore.CreateRateLimit({
    path: item.path,
    max: Number(item.max),
    duration: Number(item.duration),
    verb: item.verb as RateLimitVerb,
    state: item.state ? RateLimitState.Active : RateLimitState.Disabled,
    settings: item.settings,
  })
}

async function UpdateRateLimit(item: RateLimit) {
  await adminStore.UpdateRateLimit(id.value, {
    path: item.path as string,
    max: Number(item.max),
    duration: Number(item.duration),
    verb: item.verb as RateLimitVerb,
    state: item.state ? RateLimitState.Active : RateLimitState.Disabled,
    settings: item.settings,
  })
}

function ChangeUID(e: Event) {
  isNewUpdate.value = true

  let length = uids.value.length
  if (uids.value[length - 1].length) uids.value.push('')
  while (!uids.value[length - 1].length && !uids.value[length - 2].length && uids.value.length > 2) {
    uids.value.pop()
    length--
  }
}

function ChangeEmail(e: Event) {
  isNewUpdate.value = true

  let length = emails.value.length
  if (emails.value[length - 1].length) emails.value.push('')

  while (!emails.value[length - 1].length && !emails.value[length - 2].length && emails.value.length > 2) {
    emails.value.pop()
    length--
  }
}

function ChangeIP(e: Event) {
  isNewUpdate.value = true

  let length = ips.value.length
  if (ips.value[length - 1].length) ips.value.push('')

  while (!ips.value[length - 1].length && !ips.value[length - 2].length && ips.value.length > 2) {
    ips.value.pop()
    length--
  }
}

defineExpose({
  openModal,
})
</script>

<template>
  <ZModal
    v-model="visible"
    class="layouts-admin-settings-rate-limits-modal"
    :title="title"
  >
    <div>
      <div class="flex jusify-between">
        <ZAdminInput v-model="max" :error="maxError" :type="InputType.Text" label="Max" class="w-6/12" />
        <div class="w-6/12 flex items-center justify-end">
          <span class="mr-2">State</span>
          <ZSwitch v-model="state" size="medium" />
        </div>
      </div>
      <ZAdminInput class="mt-8" v-model="duration" :error="durationError" :type="InputType.Text" label="Duration (s)" />
      <ZAdminInput class="mt-8" v-model="path" :error="pathError" :type="InputType.Text" label="Path" />
      <ZSelect
        class="mt-8"
        v-model="verb"
        label="Verb"
        :data-source="[
          {
            name: 'ALL'
          },
          {
            name: 'GET'
          },
          {
            name: 'POST'
          },
          {
            name: 'PUT'
          },
          {
            name: 'PATCH'
          },
          {
            name: 'DELETE'
          },
          {
            name: 'OPTIONS'
          },
          {
            name: 'HEAD'
          },
        ]"
        :columns="[
          {
            key: 'name',
            scopedSlots: true,
          }
        ]"
        :findBy="['name']"
        valueKey="name"
        labelKey="name"
        :replace-func="(text: string) => text.toUpperCase()"
        search
      />
    </div>

    
    <div class="flex justify-between mt-4">
      <ZCard title="UIDs" class="z-card-box flex-1 mr-2">
        <div v-for="(item, index) in uids" :key="index" class="mt-2">
          <ZInput v-model="uids[index]" @input="ChangeUID" />
        </div>
      </ZCard>
      <ZCard title="Emails" class="z-card-box flex-1 mx-2">
        <div v-for="(item, index) in emails" :key="index" class="mt-2">
          <ZInput v-model="emails[index]" @input="ChangeEmail" />
        </div>
      </ZCard>
      <ZCard title="IPs" class="z-card-box flex-1 ml-2">
        <div v-for="(item, index) in ips" :key="index" class="mt-2">
          <ZInput v-model="ips[index]" @input="ChangeIP" />
        </div>
      </ZCard>
    </div>

    <div>
      <ZButton type="primary" :disabled="disabledButton" class="w-full! mt-8 h-8! leading-8!" @click="onClick">
        Submit
      </ZButton>
    </div>
  </ZModal>
</template>

<style lang="less">
.layouts-admin-settings-rate-limits-modal {
  .z-overlay {
    width: 00px;
  }

  .z-modal-layout-container {
    position: relative;
    width: 800px;
  }

  .z-modal-layout {
    width: 800px;
  }

  .z-input {
    height: 40px;
  }

  .z-dropdown-overlay {
    width: 250px;
  }

  .z-dropdown {
    &-trigger {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 12px;
      width: 100%;
      height: 38px;
      border: 1px solid rgba(33, 47, 79, 0.1);
      border-radius: 4px;
      transition: all 0.3s;
      &:hover {
        border: 1px solid @primary-color;
      }
    }

    &-triggered {
      border: 1px solid @primary-color;
      box-shadow: 0 0 0 2px rgba(@primary-color, 0.2);
    }

    &-overlay {
      width: 100%;
      background-color: #fff;
      box-shadow: 0 3px 6px -4px #0000001f, 0 6px 16px #00000014, 0 9px 28px 8px #0000000d;
    }
  }

  .z-admin-form-button {
    display: block;

    .z-button {
      width: 100%;
      color: white;
      background-color: @primary-color;
    }
  }

  .z-form-col-item {
    padding: 0 !important;
  }

  .z-card-box {
    padding: 0;
    box-shadow: none;

    .z-card-title {
      margin: 0;
      font-size: 14px;
    }
  }
}
</style>
