<script setup lang="ts">
import type { AsyncDataExecuteOptions } from 'nuxt/dist/app/composables/asyncData'
import type { RateLimitVerb, ZAdminFormColumn } from '~/types'
import { RateLimitState, ZAdminFormFieldType } from '~/types'
import Validate from '~/validation/validate'

const props = defineProps<{
  rateLimitId?: number
  modelValue: RateLimit
  pending?: boolean
  refresh?: (opts?: AsyncDataExecuteOptions) => Promise<void>
}>()

const emit = defineEmits(['update:modelValue'])

const router = useRouter()
const adminStore = useAdminStore()
const menuActionStore = useMenuActionStore()

const rateLimit = useVModel(props, 'modelValue', emit)
const isNewUpdate = useState(() => false)
const loading = useState(() => false)
const uids = useState(() => [''])
const emails = useState(() => [''])
const ips = useState(() => [''])

onMounted(() => {
  if (rateLimit.value.settings) {
    uids.value = rateLimit.value.settings.uids
    emails.value = rateLimit.value.settings.emails
    ips.value = rateLimit.value.settings.ips
  }
})

function isUpdateAction() {
  if (props.rateLimitId) return true
  return false
}

if (isUpdateAction()) {
  menuActionStore.setMenu([
    {
      key: 'refresh',
      title: 'Refresh',
      callback: async () => {
        loading.value = true
        props.refresh!()
        loading.value = false
      },
    },
  ])
}

const columns: ZAdminFormColumn[] = [
  {
    fields: [
      {
        key: 'state',
        type: ZAdminFormFieldType.Switch,
        label: 'State',
        value: rateLimit.value.state === RateLimitState.Active,
        class: 'flex justify-end items-center mt-0!',
      },
      {
        key: 'max',
        type: ZAdminFormFieldType.Input,
        label: 'Max(*)',
        value: rateLimit.value.max,
        validate: [Validate.required, Validate.integer, Validate.min(0)],
      },
      {
        key: 'duration',
        type: ZAdminFormFieldType.Input,
        label: 'Duration(*)',
        value: rateLimit.value.duration,
        validate: [Validate.required, Validate.integer, Validate.min(0)],
      },
    ],
  },
  {
    fields: [
      {
        key: 'path',
        type: ZAdminFormFieldType.Input,
        label: 'Path(*)',
        value: rateLimit.value.path,
        validate: [Validate.required],
      },
      {
        key: 'verb',
        type: ZAdminFormFieldType.Select,
        label: 'Verb(*)',
        value: rateLimit.value.verb,
        dataSource: [
          {
            name: 'ALL',
          },
          {
            name: 'GET',
          },
          {
            name: 'POST',
          },
          {
            name: 'PUT',
          },
          {
            name: 'PATCH',
          },
          {
            name: 'DELETE',
          },
          {
            name: 'OPTIONS',
          },
          {
            name: 'HEAD',
          },
        ],
        search: true,
        columns: [{
          key: 'name',
          scopedSlots: true,
        }],
        replaceFunc: (text: string) => text.toUpperCase(),
        findBy: ['name'],
        valueKey: 'name',
        labelKey: 'name',
        validate: [Validate.required],
      },
    ],
  },
]

async function SubmitRateLimit(rate_limit: RateLimit) {
  loading.value = true
  uids.value.pop()
  emails.value.pop()
  ips.value.pop()

  const settings = {
    uids: uids.value,
    emails: emails.value,
    ips: ips.value,
  }

  if (isUpdateAction() && props.rateLimitId) {
    await adminStore.UpdateRateLimit(props.rateLimitId, {
      path: rate_limit.path as string,
      max: Number(rate_limit.max),
      duration: Number(rate_limit.duration),
      verb: rate_limit.verb as RateLimitVerb,
      state: rate_limit.state ? RateLimitState.Active : RateLimitState.Disabled,
      settings,
    }, () => {
      router.push('/settings/rate_limits')
    })
  } else {
    await adminStore.CreateRateLimit({
      path: rate_limit.path as string,
      max: Number(rate_limit.max),
      duration: Number(rate_limit.duration),
      verb: rate_limit.verb as RateLimitVerb,
      state: rate_limit.state ? RateLimitState.Active : RateLimitState.Disabled,
      settings,
    }, () => {
      router.push('/settings/rate_limits')
    })
  }
  loading.value = false
}

function ChangeUID(e: Event) {
  isNewUpdate.value = true

  let length = uids.value.length
  if (uids.value[length - 1].length) uids.value.push('')
  while (!uids.value[length - 1].length && !uids.value[length - 2].length && uids.value.length > 2) {
    uids.value.pop()
    length--
  }
}

function ChangeEmail(e: Event) {
  isNewUpdate.value = true

  let length = emails.value.length
  if (emails.value[length - 1].length) emails.value.push('')

  while (!emails.value[length - 1].length && !emails.value[length - 2].length && emails.value.length > 2) {
    emails.value.pop()
    length--
  }
}

function ChangeIP(e: Event) {
  isNewUpdate.value = true

  let length = ips.value.length
  if (ips.value[length - 1].length) ips.value.push('')

  while (!ips.value[length - 1].length && !ips.value[length - 2].length && ips.value.length > 2) {
    ips.value.pop()
    length--
  }
}
</script>

<template>
  <div>
    <ZCard :title="isUpdateAction() ? 'Update Rate Limit' : 'Create Rate Limit'" class="mb-4">
      <ZAdminForm :columns="columns" :is-new-update="isNewUpdate" :loading="loading" :pending="pending" @submit="SubmitRateLimit" />
    </ZCard>
    <div class="flex justify-between">
      <ZCard title="UIDs" class="z-card-box flex-1 mr-1">
        <div v-for="(item, index) in uids" :key="index" class="mt-2">
          <ZInput v-model="uids[index]" @input="ChangeUID" />
        </div>
      </ZCard>
      <ZCard title="Emails" class="z-card-box flex-1 mx-1">
        <div v-for="(item, index) in emails" :key="index" class="mt-2">
          <ZInput v-model="emails[index]" @input="ChangeEmail" />
        </div>
      </ZCard>
      <ZCard title="IPs" class="z-card-box flex-1 ml-1">
        <div v-for="(item, index) in ips" :key="index" class="mt-2">
          <ZInput v-model="ips[index]" @input="ChangeIP" />
        </div>
      </ZCard>
    </div>
  </div>
</template>

<style lang="less">
.page-settings-rate_limits-update {
  .z-card-box {
    .z-card-title {
      margin: 0;
      font-size: 16px;
    }
  }
}
</style>
