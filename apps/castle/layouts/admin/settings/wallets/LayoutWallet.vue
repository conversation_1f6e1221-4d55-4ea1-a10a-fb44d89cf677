<script setup lang="ts">
import { WalletGateway, ZAdminFormFieldType } from '@zsmartex/types'
import type { AsyncDataExecuteOptions } from 'nuxt/dist/app/composables/asyncData'
import { InputType } from '@zsmartex/components/types'
import type { ZAdminFormColumn } from '~/types'
import { WalletStatus } from '~/types'
import Validate from '~/validation/validate'

const props = defineProps<{
  id?: string
  modelValue: Wallet
  pending?: boolean
  refresh?: (opts?: AsyncDataExecuteOptions) => Promise<any>
}>()

const emit = defineEmits(['update:modelValue'])

const router = useRouter()
const adminStore = useAdminStore()
const menuActionStore = useMenuActionStore()

const isNewUpdate = ref(false)
const wallet = useVModel(props, 'modelValue', emit)
const options = reactive<Record<string, string | boolean>>({ ...wallet.value.options })
const oldOptions = reactive<Record<string, string | boolean>>({ ...wallet.value.options })

const loading = useState(() => false)

function changeOptionsValue(key: string, value: string | boolean | number) {
  options[key] = value
  checkNewUpdate()
}

function checkNewUpdate() {
  for (const key in options) {
    if (options[key] !== oldOptions[key]) {
      isNewUpdate.value = true
      return
    }
  }

  isNewUpdate.value = false
}

const blockchainKeyColumns: ZTableColumn[] = [
  {
    key: 'key',
  },
]

const gatewayColumns: ZTableColumn[] = [
  {
    key: 'text',
    scopedSlots: true,
  },
]

const kindColumns: ZTableColumn[] = [
  {
    key: 'text',
    scopedSlots: true,
  },
]

const gateways = computed(() => {
  return adminStore.walletGateways.map((gateway) => {
    return {
      key: gateway,
      text: gateway,
    }
  })
})

const kinds = computed(() => {
  return adminStore.walletKinds.map((kind) => {
    return {
      key: kind,
      text: kind,
    }
  })
})

function isUpdateAction() {
  if (props.id) return true
  return false
}

const columns = computed(() => {
  const result: ZAdminFormColumn[] = [
    {
      fields: [
        {
          key: 'name',
          type: ZAdminFormFieldType.Input,
          label: 'Name(*)',
          value: wallet.value.name,
          class: '!w-6/12',
          validate: [Validate.required],
        },
        {
          key: 'status',
          type: ZAdminFormFieldType.Switch,
          label: 'Status',
          value: wallet.value.status === WalletStatus.Active,
          class: '!w-6/12 flex justify-end items-center mt-0!',
        },
        {
          key: 'blockchain_key',
          type: ZAdminFormFieldType.Select,
          label: 'Blockchain Key(*)',
          disabled: isUpdateAction(),
          value: wallet.value.blockchain_key,
          dataSource: adminStore.blockchains,
          search: true,
          scroll: true,
          columns: blockchainKeyColumns,
          findBy: ['key'],
          valueKey: 'key',
          labelKey: 'key',
          validate: [Validate.required],
        },
        {
          key: 'address',
          type: ZAdminFormFieldType.Input,
          label: 'Address(*)',
          value: wallet.value.address,
          validate: [Validate.required],
        },
        {
          key: 'kind',
          type: ZAdminFormFieldType.Select,
          label: 'Kind(*)',
          value: wallet.value.kind,
          dataSource: kinds.value,
          search: true,
          replaceFunc: (text: string) => (text[0].toUpperCase() + text.slice(1)),
          columns: kindColumns,
          findBy: ['key'],
          valueKey: 'key',
          labelKey: 'text',
          validate: [Validate.required],
        },
      ],
    },
    {
      fields: [
        {
          key: 'gateway',
          type: ZAdminFormFieldType.Select,
          label: 'Gateway Client(*)',
          value: wallet.value.gateway,
          dataSource: gateways.value,
          search: true,
          columns: gatewayColumns,
          replaceFunc: (text: string) => text.toUpperCase(),
          findBy: ['key'],
          valueKey: 'key',
          labelKey: 'key',
          validate: [Validate.required],
          class: wallet.value.gateway === WalletGateway.Qubic ? '!w-6/12' : '',
        },
        ...(wallet.value.gateway === WalletGateway.Qubic
          ? [
              {
                key: 'version',
                type: ZAdminFormFieldType.Switch,
                label: 'Version 2',
                value: options.version === 2,
                class: '!w-6/12 flex justify-end !mt-0 items-center',
                onChange: (value: boolean) => changeOptionsValue('version', value ? 2 : 1),
              },
            ]
          : []),
        {
          key: 'max_balance',
          type: ZAdminFormFieldType.Input,
          label: 'Maximum Balance(*)',
          value: wallet.value.max_balance,
          validate: [Validate.required, Validate.double(8), Validate.min(0)],
        },
        {
          key: 'uri',
          type: ZAdminFormFieldType.Input,
          label: 'Server URL',
          value: wallet.value.uri,
          // validate: walletGatewayCustomURI.includes(wallet.value.gateway) ? [] : [Validate.url],
        },
        {
          key: 'secret',
          type: ZAdminFormFieldType.Input,
          typeInput: InputType.Password,
          label: 'Wallet Secret',
          value: wallet.value.secret,
        },
      ],
    },
  ]
  return result
})

if (isUpdateAction()) {
  menuActionStore.setMenu([
    {
      key: 'refresh',
      title: 'Refresh',
      callback: async () => {
        loading.value = true
        props.refresh!()
        loading.value = false
      },
    },
  ])
}

async function SubmitWallet(wallet: Wallet) {
  loading.value = true
  if (isUpdateAction() && props.id) {
    await adminStore.UpdateWallet({
      id: Number(props.id),
      blockchain_key: wallet.blockchain_key,
      name: wallet.name,
      address: wallet.address,
      kind: wallet.kind,
      gateway: wallet.gateway,
      uri: wallet.uri,
      secret: wallet.secret,
      max_balance: Number(wallet.max_balance),
      status: wallet.status ? WalletStatus.Active : WalletStatus.Disabled,
      options: {
        version: options.version,
      },
    }, () => {
      router.push('/settings/wallets')
    })
  } else {
    await adminStore.CreateWallet({
      blockchain_key: wallet.blockchain_key as string,
      name: wallet.name as string,
      address: wallet.address,
      kind: wallet.kind as string,
      gateway: wallet.gateway as string,
      uri: wallet.uri,
      secret: wallet.secret,
      max_balance: Number(wallet.max_balance),
      status: wallet.status ? WalletStatus.Active : WalletStatus.Disabled,
      options: {
        version: options.version,
      },
    }, () => {
      router.push('/settings/wallets')
    })
  }
  loading.value = false
}
</script>

<template>
  <div class="page-dashboard-settings-wallets-action">
    <ZCard>
      <ZAdminForm :is-new-update="isNewUpdate" title="Wallet" :columns="columns" :loading="loading" :pending="pending" @submit="SubmitWallet" />
    </ZCard>
  </div>
</template>

<style lang="less">
.page-dashboard-settings-wallets-action {
  .z-input {
    background-color: rgba(@gray-color, 0.05);
  }

  .z-form-row {
    &-label {
      margin: 0;
    }
  }

  .z-table-pro {
    height: 200px;
    box-shadow: none;
  }
}
</style>
