<script setup lang="ts">
import type { ZAdminFormColumn } from '~/types'
import Validate from '~/validation/validate'

const adminStore = useAdminStore()
const visible = ref(false)
const type = ref('')
const form = ref<Record<string, any>>({})

function openModal(typeModal: 'create' | 'update', withdrawLimit?: WithdrawLimit) {
  visible.value = true
  type.value = typeModal

  if (typeModal === 'create') form.value = {}
  else form.value = { ...withdrawLimit }
}

const columns = computed(() => {
  const result: ZAdminFormColumn[] = [
    {
      fields: [
        {
          key: 'group',
          type: ZAdminFormFieldType.Input,
          label: 'Group(*)',
          value: form.value.group,
          validate: [Validate.required],
        },
        {
          key: 'kyc_level',
          type: ZAdminFormFieldType.Input,
          label: 'KYC Level(*)',
          value: form.value.kyc_level,
          validate: [Validate.required],
        },
        {
          key: 'limit',
          type: ZAdminFormFieldType.Input,
          label: 'Limit(*)',
          value: form.value.limit,
          validate: [Validate.required, Validate.double(8), Validate.min(0)],
        },
      ],
    },
  ]

  return result
})

const title = computed(() => {
  if (type.value === 'create') return 'Create withdraw limit'
  if (type.value === 'update') return 'Update withdraw limit'
})

function onClick(withdrawLimit: WithdrawLimit) {
  if (type.value === 'create') CreateWithdrawLimit(withdrawLimit)
  else UpdateWithdrawLimit(withdrawLimit)

  visible.value = false
}

async function CreateWithdrawLimit(item: WithdrawLimit) {
  const withdrawLimit = await adminStore.CreateWithdrawLimit({
    group: item.group,
    kyc_level: item.kyc_level,
    limit: Number(item.limit),
  })

  if (Object.keys(withdrawLimit).length > 0) {
    adminStore.withdraw_limits.push(withdrawLimit)
  }
}

async function UpdateWithdrawLimit(item: WithdrawLimit) {
  await adminStore.UpdateWithdrawLimit({
    id: Number(form.value.id),
    group: item.group,
    kyc_level: item.kyc_level,
    limit: Number(item.limit),
  }, () => {
    const index = adminStore.withdraw_limits.findIndex(t => t.id === Number(form.value.id))
    if (index !== -1) {
      item.id = Number(form.value.id)
      adminStore.withdraw_limits[index] = item
    }
  })
}

defineExpose({
  openModal,
})
</script>

<template>
  <ZModal
    v-model="visible"
    class="layouts-admin-settings-withdraw-limit-modal"
    :title="title"
  >
    <ZAdminForm :columns="columns" @submit="(item: WithdrawLimit) => onClick(item)" />
  </ZModal>
</template>

<style lang="less">
.layouts-admin-settings-withdraw-limit-modal {
  .z-overlay {
    width: 400px;
  }

  .z-input {
    height: 40px;
  }

  .z-dropdown-overlay {
    width: 250px;
  }

  .z-button {
    height: 32px;
    font-size: 16px;
    color: #fff;

    &:disabled {
      background-color: #e9eff3 !important;
      border: 1px solid #e9eff3 !important;
    }
  }

  .z-admin-form-button {
    display: block;

    .z-button {
      width: 100%;
      color: white;
      background-color: @primary-color;
    }
  }

  .z-form-col-item {
    padding: 0 !important;
  }
}
</style>
