<script setup lang="ts">
import type { AsyncDataExecuteOptions } from 'nuxt/dist/app/composables/asyncData'
import type { ZAdminFormColumn } from '~/types'
import { StrategyKind, StrategyState, ZAdminFormFieldType } from '~/types'
import Validate from '~/validation/validate'

const props = defineProps<{
  strategyId?: number
  modelValue: Strategy
  pending?: boolean
  refresh?: (opts?: AsyncDataExecuteOptions) => Promise<void>
}>()

const emit = defineEmits(['update:modelValue'])

const router = useRouter()
const adminStore = useAdminStore()
const menuActionStore = useMenuActionStore()

const strategy = useVModel(props, 'modelValue', emit)
const loading = useState(() => false)
const loadingRestart = useState(() => false)
const visible = useState(() => false)

function isUpdateAction() {
  if (props.strategyId) return true
  return false
}

function ReplaceFunc(text: string) {
  return text.toUpperCase().replace('_', ' ')
}

function GetBotMarketName(id: string) {
  const market = adminStore.botMarkets.find(m => m.id === Number(id))!
  let exchange = ''
  switch (market.exchange) {
    case 'zsmartex':
      exchange = 'ZSmartex'
      break
    default:
      exchange = market.exchange[0].toUpperCase() + market.exchange.slice(1)
  }
  return `${market.base_unit.toUpperCase()}/${market.quote_unit.toUpperCase()} - ${exchange} (#${market.id})`
}

const columns = computed(() => {
  const result: ZAdminFormColumn[] = [
    {
      fields: [
        {
          key: 'target_market_id',
          type: ZAdminFormFieldType.Select,
          label: 'Market(*)',
          class: '!w-9/12',
          value: strategy.value.target_market_id,
          dataSource: adminStore.botMarkets,
          columns: [
            {
              key: 'id',
              scopedSlots: true,
            },
          ],
          replaceFunc: GetBotMarketName,
          search: true,
          findBy: ['id'],
          valueKey: 'id',
          labelKey: 'id',
          validate: [Validate.required],
        },
        {
          key: 'state',
          type: ZAdminFormFieldType.Switch,
          label: 'State',
          value: strategy.value.state === StrategyState.Active,
          class: '!w-3/12 flex justify-end items-end',
        },
        {
          key: 'kind',
          type: ZAdminFormFieldType.Select,
          label: 'Type(*)',
          value: strategy.value.kind,
          dataSource: [
            {
              value: StrategyKind.MarketMaking,
            },
            {
              value: StrategyKind.CandleSampling,
            },
            {
              value: StrategyKind.PriceSampling,
            },
          ],
          columns: [
            {
              key: 'value',
              scopedSlots: true,
            },
          ],
          replaceFunc: ReplaceFunc,
          search: true,
          findBy: ['value'],
          valueKey: 'value',
          labelKey: 'value',
          validate: [Validate.required],
          onChange: (value: string) => {
            switch (value) {
              case 'market_making':
                strategy.value.kind = StrategyKind.MarketMaking
                break
              case 'price_sampling':
                strategy.value.kind = StrategyKind.PriceSampling
                break
              case 'candle_sampling':
                strategy.value.kind = StrategyKind.CandleSampling
                break
            }
          },
        },
      ],
    },
    {
      fields: [
        {
          key: 'flush',
          type: ZAdminFormFieldType.Input,
          label: 'Flush(seconds)(*)',
          value: strategy.value.flush,
          validate: [Validate.required, Validate.integer, Validate.min(0)],
        },
        {
          key: 'tick',
          type: ZAdminFormFieldType.Input,
          label: 'Tick(ms)(*)',
          value: strategy.value.tick,
          validate: [Validate.required, Validate.integer, Validate.min(0)],
        },
      ],
    },
  ]

  if (!strategy.value.options) {
    strategy.value.options = {
      level_count: 0,
      spread: 0,
      level_size: 0,
    }
  }

  if (strategy.value.kind) {
    if (strategy.value.kind === StrategyKind.MarketMaking) {
      result[0].fields.push(
        {
          key: 'level_count',
          type: ZAdminFormFieldType.Input,
          label: 'Level Count(*)',
          value: strategy.value.options.level_count,
          validate: [Validate.required, Validate.integer, Validate.min(0)],
        },
      )
      result[1].fields.push(
        {
          key: 'spread',
          type: ZAdminFormFieldType.Input,
          label: 'Spread(*)',
          value: strategy.value.options.spread,
          validate: [Validate.required, Validate.double(8), Validate.min(0)],
        },
      )
    } else if (strategy.value.kind === StrategyKind.CandleSampling) {
      result[0].fields.push(
        {
          key: 'level_size',
          type: ZAdminFormFieldType.Input,
          label: 'Level Size(*)',
          value: strategy.value.options.level_size,
          validate: [Validate.required, Validate.double(8), Validate.min(0)],
        },
      )
    }
  }

  return result
})

if (isUpdateAction()) {
  menuActionStore.setMenu([
    {
      key: 'refresh',
      title: 'Refresh',
      callback: async () => {
        loading.value = true
        props.refresh!()
        loading.value = false
      },
    },
  ])
}

async function SubmitStrategy(strategy: any) {
  loading.value = true
  const options: Record<string, number> = {}
  if (strategy.kind === StrategyKind.MarketMaking) {
    if (strategy.level_count) options.level_count = Number(strategy.level_count)
    if (strategy.spread) options.spread = Number(strategy.spread)
  } else {
    if (strategy.level_size) options.level_size = Number(strategy.level_size)
  }
  if (isUpdateAction() && props.strategyId) {
    await adminStore.UpdateStrategy({
      id: props.strategyId,
      kind: strategy.kind,
      target_market_id: Number(strategy.target_market_id),
      state: strategy.state ? StrategyState.Active : StrategyState.Disabled,
      tick: Number(strategy.tick),
      flush: Number(strategy.flush),
      options,
    }, () => {
      router.push('/bot/strategies')
    })
  } else {
    await adminStore.CreateStrategy({
      kind: strategy.kind,
      target_market_id: Number(strategy.target_market_id),
      state: strategy.state ? StrategyState.Active : StrategyState.Disabled,
      tick: Number(strategy.tick),
      flush: Number(strategy.flush),
      options,
    }, () => {
      router.push('/bot/strategies')
    })
  }
  loading.value = false
}

async function restart() {
  loadingRestart.value = true
  await adminStore.RestartStrategy(strategy.value.id)
  loadingRestart.value = false
  visible.value = false
}
</script>

<template>
  <ZCard>
    <ZAdminForm :columns="columns" :loading="loading" :pending="pending" @submit="SubmitStrategy">
      <template #header>
        <div class="flex justify-between mb-4">
          <div class="bold-text text-2xl">
            {{ isUpdateAction() ? 'Update Strategy' : 'Create Strategy' }}
          </div>
          <div v-if="isUpdateAction()" class="flex">
            <ZPopconfirm v-model="visible" :placement="Placement.TopRight" :loading="loadingRestart" trigger="click" @click="restart">
              <ZButton class="flex cursor-pointer items-center">
                <ZIcon type="spinner11" />Restart
              </ZButton>
            </ZPopconfirm>
          </div>
        </div>
      </template>
    </ZAdminForm>
  </ZCard>
</template>

<style lang="less">
.page-dashboard-settings-blockchains-action {
  &-dropdown {
    &-item {
      display: block;
      cursor: pointer;
      padding: 8px 12px;
      color: @text-color !important;
      background-color: rgba(@gray-color, 0.1);

      &:hover {
        background-color: white;
      }
    }

    &-item + &-item {
      border-top: 1px solid @base-border-color;
    }
  }

  .z-input {
    background-color: rgba(@gray-color, 0.05);
  }

  .z-button {
    width: 68px;
    height: 32px;
    background-color: @primary-color !important;
    color: white !important;
  }

  .z-form-row {
    &-label {
      margin: 0;
    }
  }
}
</style>
