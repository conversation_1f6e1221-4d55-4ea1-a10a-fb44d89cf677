<script setup lang="ts">
import type { ZAdminFormColumn } from '~/types'
import { ConfigKey } from '~/types'
import Validate from '~/validation/validate'

const adminStore = useAdminStore()
const visible = ref(false)
const type = ref('')
const form = ref<Record<string, any>>({})

const title = computed(() => {
  if (type.value === 'create') return 'Create config'
  if (type.value === 'update') return 'Update config'
})

function openModal(typeModal: 'create' | 'update', config?: Config) {
  visible.value = true
  type.value = typeModal

  if (typeModal === 'create') form.value = {}
  else form.value = { ...config }
}

const columns = computed(() => {
  const result: ZAdminFormColumn[] = [
    {
      fields: [
        {
          key: 'key',
          type: ZAdminFormFieldType.Select,
          label: 'Key(*)',
          value: form.value.key,
          dataSource: [
            {
              value: ConfigKey.AccessKey,
            },
            {
              value: ConfigKey.SecretKey,
            },
          ],
          columns: [
            {
              key: 'value',
            },
          ],
          disabled: type.value === 'update',
          search: true,
          findBy: ['value'],
          valueKey: 'value',
          labelKey: 'value',
          validate: [Validate.required],
        },
        {
          key: 'value',
          type: ZAdminFormFieldType.Input,
          label: 'Value(*)',
          value: form.value.value,
          validate: [Validate.required],
        },
      ],
    },
  ]

  return result
})

function onClick(config: Config) {
  if (type.value === 'create') CreateConfig(config)
  else UpdateConfig(config)

  visible.value = false
}

async function CreateConfig(item: Config) {
  await adminStore.CreateConfig({
    key: item.key,
    value: item.value,
  })
}

async function UpdateConfig(item: Config) {
  await adminStore.UpdateConfig({
    key: item.key,
    value: item.value,
  })
}

defineExpose({
  openModal,
})
</script>

<template>
  <ZModal
    v-model="visible"
    class="layouts-admin-bot-config-modal"
    :title="title"
  >
    <ZAdminForm :columns="columns" @submit="(item: Config) => onClick(item)" />
  </ZModal>
</template>

<style lang="less">
.layouts-admin-bot-config-modal {
  .z-overlay {
    width: 400px;
  }

  .z-input {
    height: 40px;
  }

  .z-dropdown-overlay {
    width: 250px;
  }

  .z-dropdown {
    &-trigger {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 12px;
      width: 100%;
      height: 38px;
      border: 1px solid rgba(33, 47, 79, 0.1);
      border-radius: 4px;
      transition: all 0.3s;
      &:hover {
        border: 1px solid @primary-color;
      }
    }

    &-triggered {
      border: 1px solid @primary-color;
      box-shadow: 0 0 0 2px rgba(@primary-color, 0.2);
    }

    &-overlay {
      width: 100%;
      background-color: #fff;
      box-shadow: 0 3px 6px -4px #0000001f, 0 6px 16px #00000014, 0 9px 28px 8px #0000000d;
    }
  }

  .z-admin-form-button {
    display: block;

    .z-button {
      width: 100%;
      color: white;
      background-color: @primary-color;
    }
  }

  .z-form-col-item {
    padding: 0 !important;
  }
}
</style>
