<script setup lang="ts">
import { BotMarketExchange } from '~/types'
import type { ZAdminFormColumn } from '~/types'
import Validate from '~/validation/validate'

const adminStore = useAdminStore()
const visible = ref(false)
const type = ref('')
const form = ref<Record<string, any>>({})

function openModal(typeModal: 'create' | 'update', botMarket?: BotMarket) {
  visible.value = true
  type.value = typeModal

  if (typeModal === 'create') form.value = {}
  else form.value = { ...botMarket }
}

const title = computed(() => {
  if (type.value === 'create') return 'Create market'
  return 'Update market'
})

function ReplaceFunc(text: string) {
  switch (text) {
    case 'zsmartex':
      return 'ZSmartex'
    default:
      return text[0].toUpperCase() + text.slice(1)
  }
}

const columns = computed(() => {
  const result: ZAdminFormColumn[] = [
    {
      fields: [
        {
          key: 'exchange',
          type: ZAdminFormFieldType.Select,
          label: 'Exchange(*)',
          value: form.value.exchange,
          dataSource: [
            {
              value: BotMarketExchange.Zsmartex,
            },
            {
              value: BotMarketExchange.Binance,
            },
            {
              value: BotMarketExchange.Coinmarketcap,
            },
          ],
          columns: [
            {
              key: 'value',
              scopedSlots: true,
            },
          ],
          disabled: type.value === 'update',
          search: true,
          replaceFunc: ReplaceFunc,
          findBy: ['value'],
          valueKey: 'value',
          labelKey: 'value',
          validate: [Validate.required],
        },
        {
          key: 'price_precision',
          type: ZAdminFormFieldType.Input,
          label: 'Price Precision(*)',
          value: form.value.price_precision,
          validate: [Validate.required, Validate.integer, Validate.min(0)],
        },
        {
          key: 'amount_precision',
          type: ZAdminFormFieldType.Input,
          label: 'Amount Precision(*)',
          value: form.value.amount_precision,
          validate: [Validate.required, Validate.integer, Validate.min(0)],
        },
        {
          key: 'min_price',
          type: ZAdminFormFieldType.Input,
          label: 'Min Price(*)',
          value: form.value.min_price,
          validate: [Validate.required, Validate.double(8), Validate.lte(0)],
        },
        {
          key: 'min_amount',
          type: ZAdminFormFieldType.Input,
          label: 'Min Amount(*)',
          value: form.value.min_amount,
          validate: [Validate.required, Validate.double(8), Validate.lte(0)],
        },
        {
          key: 'max_amount',
          type: ZAdminFormFieldType.Input,
          label: 'Max Amount(*)',
          value: form.value.max_amount,
          validate: [Validate.required, Validate.double(8), Validate.lte(0)],
        },
      ],
    },
  ]

  if (type.value === 'update') {
    result[0].fields.unshift({
      key: 'market',
      type: ZAdminFormFieldType.Text,
      label: 'Market',
      value: `${form.value.base_unit.toUpperCase()}/${form.value.quote_unit.toUpperCase()}`,
      class: '!w-6/12 flex justify-start items-center',
    })
    result[0].fields.splice(1, 1, {
      key: 'exchange',
      type: ZAdminFormFieldType.Text,
      label: 'Exchange',
      value: ReplaceFunc(form.value.exchange),
      class: '!w-6/12 flex justify- items-center',
    })
  } else if (type.value === 'create') {
    result[0].fields.splice(1, 0, {
      key: 'base_unit',
      type: ZAdminFormFieldType.Input,
      label: 'Base Unit(*)',
      value: form.value.base_unit,
      validate: [Validate.required],
      class: '!w-6/12 flex justify-start items-center',
    })
    result[0].fields.splice(2, 0, {
      key: 'quote_unit',
      type: ZAdminFormFieldType.Input,
      label: 'Quote Unit(*)',
      value: form.value.quote_unit,
      validate: [Validate.required],
      class: '!w-6/12 flex justify-end items-center',
    })
  }

  return result
})

function onClick(botMarket: BotMarket) {
  if (type.value === 'create') CreateMarket(botMarket)
  else UpdateMarket(botMarket)

  visible.value = false
}

async function CreateMarket(botMarket: BotMarket) {
  await adminStore.CreateBotMarket({
    exchange: botMarket.exchange,
    base_unit: botMarket.base_unit,
    quote_unit: botMarket.quote_unit,
    price_precision: Number(botMarket.price_precision),
    amount_precision: Number(botMarket.amount_precision),
    min_price: Number(botMarket.min_price),
    min_amount: Number(botMarket.min_amount),
    max_amount: Number(botMarket.max_amount),
  })
}

async function UpdateMarket(botMarket: BotMarket) {
  await adminStore.UpdateBotMarket({
    id: form.value.id,
    exchange: botMarket.exchange,
    base_unit: botMarket.base_unit,
    quote_unit: botMarket.quote_unit,
    price_precision: Number(botMarket.price_precision),
    amount_precision: Number(botMarket.amount_precision),
    min_price: Number(botMarket.min_price),
    min_amount: Number(botMarket.min_amount),
    max_amount: Number(botMarket.max_amount),
  })
}

defineExpose({
  openModal,
})
</script>

<template>
  <ZModal
    v-model="visible"
    class="layouts-admin-bot-market-modal"
    :title="title"
  >
    <ZAdminForm :columns="columns" @submit="(item: BotMarket) => onClick(item)" />
  </ZModal>
</template>

<style lang="less">
.layouts-admin-bot-market-modal {
  .z-overlay {
    width: 400px;
  }

  .z-input {
    height: 40px;
  }

  .z-dropdown-overlay {
    width: 250px;
  }

  .z-button {
    height: 32px;
    font-size: 16px;
    color: #fff;

    &:disabled {
      background-color: #e9eff3 !important;
      border: 1px solid #e9eff3 !important;
    }
  }

  .z-dropdown {
    &-trigger {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 12px;
      width: 100%;
      height: 38px;
      border: 1px solid rgba(33, 47, 79, 0.1);
      border-radius: 4px;
      transition: all 0.3s;
      &:hover {
        border: 1px solid @primary-color;
      }
    }

    &-triggered {
      border: 1px solid @primary-color;
      box-shadow: 0 0 0 2px rgba(@primary-color, 0.2);
    }

    &-overlay {
      width: 100%;
      background-color: #fff;
      box-shadow: 0 3px 6px -4px #0000001f, 0 6px 16px #00000014, 0 9px 28px 8px #0000000d;
    }
  }

  .z-admin-form-button {
    display: block;

    .z-button {
      width: 100%;
      color: white;
      background-color: @primary-color;
    }
  }

  .z-form-col-item {
    padding: 0 !important;
  }
}
</style>
