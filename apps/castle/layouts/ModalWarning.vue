<script setup lang="ts">
const props = defineProps<{
  modelValue: boolean
}>()

const emit = defineEmits(['update:modelValue'])
const visible = useVModel(props, 'modelValue', emit)
</script>

<template>
  <ZModal
    v-model="visible"
    class="layouts-admin-warn"
  >
    <div class="text-center mb-6 text-[#ffc107] layouts-admin-warn-icon">
      <ZIconWarnDuotone />
      <span class="block font-bold text-2xl mt-2">Warning Stats</span>
    </div>
    <div class="text-center text-[16px] mb-[24px]">
      Failed to fetch stats from the server
    </div>
  </ZModal>
</template>

<style lang="less">
.layouts-admin-warn {
  &-icon {
    svg {
      width: 120px;
      height: 120px;

      .cls-1 {
        fill: @warn-color;
      }

      .cls-2 {
        fill: white;
      }
    }
  }

  .z-overlay {
    width: 400px;
  }

  .z-input {
    height: 40px;
  }

  .z-dropdown-overlay {
    width: 250px;
  }

  .z-button {
    height: 32px;
    font-size: 16px;
    color: #fff;

    &:disabled {
      background-color: #e9eff3 !important;
      border: 1px solid #e9eff3 !important;
    }
  }

  .z-admin-form-button {
    display: block;

    .z-button {
      width: 100%;
      color: white;
      background-color: @primary-color;
    }
  }

  .z-form-col-item {
    padding: 0 !important;
  }
}
</style>
