worker_processes auto;
# pid /run/nginx.pid;

events {  
  multi_accept       on;
  worker_connections 65535;
}


http {
  map $http_upgrade $connection_upgrade {  
    default upgrade;
    ''      close;
  }

  server {
    listen 8080;
    listen [::]:8080;
    index index.html;

    client_max_body_size 500m;
    proxy_read_timeout 360s;

    location /api/v2/websocket {
      proxy_http_version 1.1;
      proxy_set_header Upgrade $http_upgrade;
      proxy_set_header Connection $connection_upgrade;
      proxy_pass https://www.zsmartex.com/api/v2/websocket;
    }

    location /api {
      proxy_set_header Host www.zsmartex.com;
      proxy_pass https://www.zsmartex.com/api;
      proxy_redirect off;
    }

    location / {
      proxy_pass            http://host.docker.internal:3000;
      proxy_http_version    1.1;
      proxy_redirect        off;
    }
  }
}
