import type { <PERSON>Query } from '@zsmartex/core/composables/useQuery'
import { getUnixTime } from 'date-fns'
import { acceptHMRUpdate, defineStore } from 'pinia'
import type { Filter } from '~/types'
import { FilterType } from '~/types'

type Payload = Record<string, any>
type Callback<T = Payload> = (payload: T) => void

export const useFilterStore = defineStore('filters', {
  state: () => ({
    callback: ref<Callback<any>>(),
    filters: ref<Filter[]>([]),
    loading: ref(false),
    visible: ref(false),
  }),
  actions: {
    submitFiler() {
      const params = useUrlSearchParams('history')
      const payload: Payload = {}

      this.filters.forEach((filter) => {
        if (!filter.value) {
          delete params[filter.key]
          return
        }

        if (filter.type === FilterType.DateRange) {
          if (filter.value[0] && filter.value[1]) {
            const timeFrom = getUnixTime(filter.value[0])
            const timeTo = getUnixTime(filter.value[1])

            if (timeFrom < timeTo) {
              payload.time_from = timeFrom
              payload.time_to = timeTo
              params.time_from = timeFrom.toString()
              params.time_to = timeTo.toString()
            } else {
              filter.value[0] = ''
              filter.value[1] = ''
            }
          }
        } else if (filter.value) {
          payload[filter.key] = filter.value
          params[filter.key] = String(filter.value)
        } else if (!filter.value) {
          delete params[filter.key]
        }
      })

      if (this.callback) {
        this.callback(payload)
      }
      this.visible = false
    },
    setFilter(filters: Filter[]) {
      if (process.server) return

      this.filters = filters
    },
    clearFilter() {
      this.filters = []
      this.callback = undefined
    },
    onSubmit<T extends DataQuery>(callback: Callback<T>) {
      if (process.server) return
      this.callback = callback
    },
  },
})

if (import.meta.hot)
  import.meta.hot.accept(acceptHMRUpdate(useFilterStore, import.meta.hot))
