import { acceptHMRUpdate, defineStore } from 'pinia'

export const useIndicator = defineStore({
  id: 'indicator',
  state() {
    return {
      loading: ref(false),
      timer: ref<NodeJS.Timer>(),
      throttle: ref<NodeJS.Timeout>(),
      progress: ref(0),
      step: 5,
    }
  },
  actions: {
    async start(_throttle?: number) {
      this.clear()
      this.progress = 0
      this.loading = true
      if (_throttle) {
        if (process.client) {
          this.throttle = setTimeout(this.startTimer, _throttle)
        }
      } else {
        this.startTimer()
      }
    },
    async stop() {
      this.progress = 100
      this.hide()
    },
    hide() {
      this.clear()
      if (process.client) {
        setTimeout(() => {
          this.loading = false
          setTimeout(() => {
            this.progress = 0
          }, 400)
        }, 500)
      }
    },
    clear() {
      clearInterval(this.timer)
      clearTimeout(this.throttle)
      this.timer = undefined
      this.throttle = undefined
    },
    increase(num: number) {
      this.progress = Math.min(100, this.progress + num)
    },
    startTimer() {
      if (process.client) {
        this.timer = setInterval(() => {
          this.increase(this.step)
        }, 100)
      }
    },
  },
})

if (import.meta.hot)
  import.meta.hot.accept(acceptHMRUpdate(useIndicator, import.meta.hot))
