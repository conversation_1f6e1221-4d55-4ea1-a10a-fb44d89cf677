import { acceptHMRUpdate, defineStore } from 'pinia'
import type { MenuActionItem } from '~/types'

export const useMenuActionStore = defineStore('menu-action', {
  state: () => ({
    items: ref<MenuActionItem[]>([]),
  }),
  actions: {
    setMenu(items: MenuActionItem[]) {
      this.items = items.map((item) => {
        if (process.server) {
          return {
            key: item.key,
            icon: item.icon,
            title: item.title,
          } as MenuActionItem
        } else {
          return item
        }
      })
    },
    clear() {
      this.items = []
    },
  },
})

if (import.meta.hot)
  import.meta.hot.accept(acceptHMRUpdate(useMenuActionStore, import.meta.hot))
