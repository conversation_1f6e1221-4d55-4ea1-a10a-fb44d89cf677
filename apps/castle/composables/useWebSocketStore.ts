// import { acceptHMRUpdate, defineStore } from 'pinia'
// import config from '~/config'

// export const useWebSocketStore = defineStore({
//   id: 'websocket',
//   state() {
//     return {
//       channels: ref<string[]>([]),
//       socket: ref<WebSocket>(),
//     }
//   },
//   actions: {
//     connect() {
//       const socket = this.socket = new WebSocket(config.api().ws)
//       socket.onopen = () => {
//         this.subscribe(...this.channels)
//       }

//       socket.onerror = () => {
//         this.connect()
//       }

//       socket.onmessage = (event) => {
//         this.messageHandler(JSON.parse(event.data))
//       }
//     },
//     subscribe(...channels: string[]) {
//       for (const channel of channels) {
//         if (!this.channels.includes(channel)) this.channels.push(channel)
//       }

//       const socket = this.socket

//       if (!socket) return
//       if (socket.readyState !== WebSocket.OPEN) return

//       socket.send(JSON.stringify({ event: 'subscribe', streams: channels }))
//     },
//     unsubscribe(...channels: string[]) {
//       for (const channel of this.channels) {
//         const index = this.channels.indexOf(channel)

//         if (index >= 0) this.channels.splice(index, 0)
//       }

//       const socket = this.socket

//       if (!socket) return
//       if (socket.readyState !== WebSocket.OPEN) return

//       socket.send(JSON.stringify({ event: 'unsubscribe', streams: channels }))
//     },
//     messageHandler(message: any) {
//       const adminStore = useAdminStore()

//       Object.keys(message).forEach((key) => {
//         const data = message[key]

//         switch (key) {
//           case 'users.activities':
//             adminStore.userActivityMetrics = adminStore.userActivityMetrics.map((item) => {
//               if (item.date === data.date) {
//                 item[data.action as 'login' | 'register'] = data.count
//                 return item
//               }
//               return item
//             })
//             break
//           case 'orders.processed':
//             adminStore.processedOrderMetrics = adminStore.processedOrderMetrics.map((item) => {
//               if (item.date === data.date) {
//                 item.count = data.count
//                 return item
//               }
//               return item
//             })
//             break
//           case 'orders.canceled':
//             adminStore.canceledOrderMetrics = adminStore.canceledOrderMetrics.map((item) => {
//               if (item.date === data.date) {
//                 item.count = data.count
//                 return item
//               }
//               return item
//             })
//             break
//           case 'trades.executed':
//             adminStore.tradeMetrics = adminStore.tradeMetrics.map((item) => {
//               if (item.date === data.date) {
//                 item.count = data.count
//                 return item
//               }
//               return item
//             })
//             break
//           case 'sessions.devices':
//             adminStore.userDevice.this_month[data.device as keyof UserDeviceActivity] += data.count - adminStore.userDevice.today[data.device as keyof UserDeviceActivity]
//             adminStore.userDevice.this_week[data.device as keyof UserDeviceActivity] += data.count - adminStore.userDevice.today[data.device as keyof UserDeviceActivity]
//             adminStore.userDevice.today[data.device as keyof UserDeviceActivity] = data.count
//             break
//           case 'sessions.locations':
//             const index = adminStore.sessionLocations.findIndex(item => item.country === data.country)
//             if (index !== -1) {
//               adminStore.sessionLocations[index].login = data.login
//               adminStore.sessionLocations[index].register = data.register
//             }
//             break
//           default:
//             break
//         }
//       })
//     },
//   },
// })

// if (import.meta.hot)
//   import.meta.hot.accept(acceptHMRUpdate(useWebSocketStore, import.meta.hot))
