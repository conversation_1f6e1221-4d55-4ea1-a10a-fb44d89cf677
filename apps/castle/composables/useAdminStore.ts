import { acceptHMRUpdate, defineStore } from 'pinia'
import { addDays, endOfDay, startOfDay } from 'date-fns'
import type { BlockchainClient, MarketState, WalletGateway } from '@zsmartex/types'
import type { BlockchainCurrencyStatus, BlockchainStatus, CurrencyStatus, StrategyKind, StrategyState, WhitelistedSmartContractState } from '~/types'

export const useAdminStore = defineStore({
  id: 'admin',
  state() {
    return {
      visibleStatsWarning: false,
      statsDatabase: ref<StatDatabase[]>([]),
      statsVault: ref<StatVault[]>([]),
      markets: ref<Market[]>([]),
      public_currencies: Array<Currency>(),
      currencies: Array<Currency>(),
      blockchains: ref<Blockchain[]>([]),
      rate_limits: ref<RateLimit[]>([]),
      ui_configurations: ref<UIConfiguration[]>([]),
      wallets: ref<Wallet[]>([]),
      trading_fees: ref<TradingFee[]>([]),
      withdraw_limits: ref<WithdrawLimit[]>([]),
      banners: ref<Banner[]>([]),
      userActivityMetrics: ref<UserActivityMetrics[]>([]),
      orderMetrics: ref<OrderMetrics[]>([]),
      revenueStatistics: ref<RevenueStatistics[]>([]),
      assetsStatistics: ref<AssetsStatistics[]>([]),
      todayRevenue: ref(0),
      sessionLocations: ref<SessionLocation[]>([]),
      tradeMetrics: ref<TradeMetrics[]>([]),
      userDevice: ref<UserDeviceMetrics>(),
      configs: ref<Config[]>([]),
      blacklist_addresses: ref<BlacklistAddress[]>([]),
      whitelisted_smart_contracts: ref<WhitelistedSmartContract[]>([]),
      botMarkets: ref<BotMarket[]>([]),
      strategies: ref<Strategy[]>([]),
      recentTrades: ref<Trade[]>([]),
      recentSessions: ref<Activity[]>([]),
      blockchainClients: ref<BlockchainClient[]>([]),
      walletKinds: ref<WalletKind[]>([]),
      walletGateways: ref<WalletGateway[]>([]),
      systemAlerts: ref<SystemAlert[]>([]),
    }
  },
  getters: {
    groups() {
      const tradingFeeGroups: string[] = this.trading_fees.map(item => item.group)
      const withdrawLimitGroups: string[] = this.withdraw_limits.map(item => item.group)

      const groups = [...new Set([...tradingFeeGroups, ...withdrawLimitGroups])]

      return groups
    },
  },
  actions: {
    async CallbackLogin() {
      const userStore = useUserStore()

      if (userStore.role === UserRole.Member) return

      await this.FetchData()

      if (process.client) {
        navigateTo('/dashboard')
      }
    },
    async FetchData() {
      const userStore = useUserStore()
      await this.FetchBlacklistAddress()
      await this.FetchPublicCurrencies()
      await this.FetchSystemAlerts()
      if ([UserRole.SuperAdmin, UserRole.Admin].includes(userStore.role)) {
        await Promise.all([
          this.FetchMarkets(),
          this.FetchAdvertisements(),
          this.FetchTradingFees(),
          this.FetchWithdrawLimits(),
          this.FetchRecentTrades(),
          this.FetchRecentActivities(),
          this.FetchCurrencies(),
          this.FetchBlockchains(),
          this.FetchRateLimits(),
          this.FetchUIConfigurations(),
          this.FetchWallets(),
          this.FetchBlockchainClients(),
          this.FetchWalletKinds(),
          this.FetchWalletGateWays(),
          this.FetchWhitelistedSmartContracts(),
          this.FetchConfigs({}),
          this.FetchBotMarkets({}),
          this.FetchStrategies({}),
          this.FetchTodayRevenue(),
          this.FetchRevenueStatistics({}),
          this.FetchAssetsStatistics({}),
          this.FetchActivitiesMetrics({}),
          this.FetchOrderMetrics({}),
          this.FetchTradeMetrics({}),
          this.FetchUserDeviceMetrics({}),
          this.FetchSessionLocations({}),
          this.FetchStatsDatabase(),
          this.FetchStatsVault(),
        ])
      }
    },
    async FetchSystemAlerts() {
      try {
        const { headers, data } = await useBetterFetch<SystemAlert[]>('trade/admin/system_alerts', {
          method: 'GET',
        })
        this.systemAlerts = data
        return { headers, data }
      } catch (error) {
        return error
      }
    },
    async MarkViewSystemAlert(id: number) {
      try {
        await useBetterFetch(`trade/admin/system_alerts/${id}/mark_view`, {
          method: 'PUT',
        })

        this.systemAlerts.forEach((item) => {
          if (item.alert_id === id) {
            item.viewed = true
          }
        })
      } catch (error) {
        return error
      }
    },
    async FetchMarkets(callback?: () => void) {
      try {
        const { headers } = await useBetterFetch<Market[]>('trade/admin/markets', {
          method: 'GET',
        })

        const total = Number(headers.total)

        const { data } = await useBetterFetch<Market[]>('trade/admin/markets', {
          method: 'GET',
          params: {
            page: 1,
            limit: total,
          },
        })

        this.markets = data
        if (callback) callback()
      } catch (error) {
        return error
      }
    },
    async CreateMarket(params: {
      base_unit: string
      quote_unit: string
      min_price: number
      min_amount: number
      position?: number
      amount_precision?: number
      price_precision?: number
      total_precision?: number
      max_price?: number
      state?: MarketState
    }, callback?: () => void) {
      try {
        const { data } = await useBetterFetch<Market>('trade/admin/markets', {
          method: 'POST',
          body: {
            ...params,
          },
        })
        Message.success({
          message: 'Create market successfully',
        })
        if (callback) callback()
        this.markets.push(data)
      }
      catch (error) {
        return error
      }
    },
    async UpdateMarketState(id: string, state: string, callback?: () => void) {
      try {
        await useBetterFetch<Market>('trade/admin/markets', {
          method: 'PUT',
          body: {
            id,
            state,
          },
        })
        Message.success({
          message: 'Update market successfully',
        })
        if (callback) callback()
      }
      catch (error) {
        return error
      }
    },
    async UpdateMarket(id: string, params: {
      position?: number
      min_price?: number
      min_amount?: number
      amount_precision?: number
      price_precision?: number
      total_precision?: number
      max_price?: number
      state?: MarketState
    }, callback?: () => void) {
      try {
        const { data } = await useBetterFetch<Market>('trade/admin/markets', {
          method: 'PUT',
          body: {
            id,
            ...params,
          },
        })
        Message.success({
          message: 'Update market successfully',
        })
        if (callback) callback()
        const index = this.markets.findIndex(item => item.id === id)
        if (index !== -1) {
          this.markets.splice(index, 1, data)
        }
      }
      catch (error) {
        return error
      }
    },
    async FetchPublicCurrencies(callback?: () => void) {
      try {
        const { data } = await useBetterFetch<Currency[]>('trade/public/currencies', {
          method: 'GET',
        })

        this.public_currencies = data
        if (callback) callback()
      } catch (error) {
        return error
      }
    },
    async FetchCurrencies(callback?: () => void) {
      try {
        const { headers } = await useBetterFetch<Currency[]>('trade/admin/currencies', {
          method: 'GET',
        })

        const total = Number(headers.total)

        const { data } = await useBetterFetch<Currency[]>('trade/admin/currencies', {
          method: 'GET',
          params: {
            page: 1,
            limit: total,
          },
        })

        this.currencies = data
        if (callback) callback()
      } catch (error) {
        return error
      }
    },
    async FetchDepositAddresses(params: Record<string, any> = {}, callback?: () => void) {
      params.page ||= 1
      params.limit ||= 15

      const { headers, data } = await useBetterFetch<DepositAddress[]>('trade/admin/deposit_addresses', {
        method: 'GET',
        params,
      })

      if (callback) callback()
      return { headers, data }
    },
    FetchCurrency(id: string) {
      return useBetterFetch<Currency>(`trade/admin/currencies/${id}`)
    },
    async CreateCurrency(params: {
      id: string
      name: string
      position?: number
      precision?: number
      status?: CurrencyStatus
      home_page?: string
      icon_url?: string
      description?: string
    }, callback?: () => void) {
      try {
        const { data } = await useBetterFetch<Currency>('trade/admin/currencies', {
          method: 'POST',
          body: {
            ...params,
          },
        })
        Message.success({
          message: 'Create currency successfully',
        })
        if (callback) callback()
        data.name = params.name
        this.currencies.push(data)
      }
      catch (error) {
        return error
      }
    },
    async UpdateCurrencyStatus(params: { id: string; status: CurrencyStatus }) {
      try {
        await useBetterFetch<Currency>('trade/admin/currencies', {
          method: 'PUT',
          body: {
            ...params,
          },
        })
        Message.success({
          message: 'Update currency successfully',
        })
      }
      catch (error) {
        return error
      }
    },
    async UpdateCurrency(params: {
      id: string
      position?: number
      precision?: number
      name?: string
      status?: CurrencyStatus
      home_page?: string
      icon_url?: string
      description?: string
    }, callback?: () => void) {
      try {
        const { data } = await useBetterFetch<Currency>('trade/admin/currencies', {
          method: 'PUT',
          body: {
            ...params,
          },
        })
        Message.success({
          message: 'Update currency successfully',
        })
        if (callback) callback()
        const index = this.currencies.findIndex(item => item.id === params.id)
        if (index !== -1) {
          if (params.name) data.name = params.name
          this.currencies.splice(index, 1, data)
        }
      }
      catch (error) {
        return error
      }
    },
    async FetchBlockchains(callback?: () => void) {
      try {
        const { headers } = await useBetterFetch<Blockchain[]>('trade/admin/blockchains', {
          method: 'GET',
        })

        const total = Number(headers.total)

        const { data } = await useBetterFetch<Blockchain[]>('trade/admin/blockchains', {
          method: 'GET',
          params: {
            page: 1,
            limit: total,
          },
        })

        this.blockchains = data
        if (callback) callback()
      } catch (error) {
        return error
      }
    },
    async FetchRateLimits(callback?: () => void) {
      try {
        const { headers } = await useBetterFetch<RateLimit[]>('auth/admin/rate_limits', {
          method: 'GET',
        })

        const total = Number(headers.total)

        const { data } = await useBetterFetch<RateLimit[]>('auth/admin/rate_limits', {
          method: 'GET',
          params: {
            page: 1,
            limit: total,
          },
        })

        this.rate_limits = data
        if (callback) callback()
      } catch (error) {
        return error
      }
    },
    async FetchUIConfigurations(callback?: () => void) {
      try {
        const { headers } = await useBetterFetch<UIConfiguration[]>('trade/admin/ui_configurations', {
          method: 'GET',
        })

        const total = Number(headers.total)

        const { data } = await useBetterFetch<UIConfiguration[]>('trade/admin/ui_configurations', {
          method: 'GET',
          params: {
            page: 1,
            limit: total,
          },
        })

        this.ui_configurations = data
        if (callback) callback()
      } catch (error) {
        return error
      }
    },
    FetchBlockchain(id: string) {
      return useBetterFetch<Blockchain>(`trade/admin/blockchains/${id}`)
    },
    FetchRateLimit(id: string) {
      return useBetterFetch<RateLimit>(`auth/admin/rate_limits/${id}`)
    },
    async CreateBlockchain(params: {
      key: string
      name: string
      client: BlockchainClient
      height: number
      protocol: string
      explorer_transaction: string
      explorer_address: string
      warning: string
      server: string
      status: BlockchainStatus
      min_confirmations: number
    }, callback?: () => void) {
      try {
        const { data } = await useBetterFetch<Blockchain>('trade/admin/blockchains', {
          method: 'POST',
          body: {
            ...params,
          },
        })
        Message.success({
          message: 'Create blockchain successfully',
        })
        if (callback) callback()
        this.blockchains.push(data)
      }
      catch (error) {
        return error
      }
    },
    async CreateRateLimit(params: {
      max: number
      duration: number
      verb: string
      path: string
      state: string
      settings: RateLimitSettings
    }, callback?: () => void) {
      try {
        const { data } = await useBetterFetch<RateLimit>('auth/admin/rate_limits', {
          method: 'POST',
          body: {
            ...params,
          },
        })
        Message.success({
          message: 'Create rate limit successfully',
        })
        if (callback) callback()
        this.rate_limits.push(data)
      }
      catch (error) {
        return error
      }
    },
    async UpdateUIConfiguration(params: {
      type: string
      value: string[]
    }, callback?: () => void) {
      try {
        const { data } = await useBetterFetch<UIConfiguration>('trade/admin/ui_configurations', {
          method: 'PUT',
          body: {
            ...params,
          },
        })
        Message.success({
          message: 'Update configuration successfully',
        })
        if (callback) callback()

        const index = this.ui_configurations.findIndex(item => item.type === params.type)
        if (index !== -1) {
          this.ui_configurations.splice(index, 1, data)
        }
      }
      catch (error) {
        return error
      }
    },
    async UpdateBlockchain(id: number, params: {
      key?: string
      name?: string
      client?: BlockchainClient
      server?: string
      protocol?: string
      height?: number
      explorer_transaction?: string
      explorer_address?: string
      warning?: string
      status?: BlockchainStatus
      min_confirmations?: number
    }, callback?: () => void) {
      try {
        const { data } = await useBetterFetch<Blockchain>('trade/admin/blockchains', {
          method: 'PUT',
          body: {
            id,
            ...params,
          },
        })
        Message.success({
          message: 'Update blockchain successfully',
        })
        if (callback) callback()
        const index = this.blockchains.findIndex(item => item.id === id)
        if (index !== -1) {
          this.blockchains.splice(index, 1, data)
        }
      }
      catch (error) {
        return error
      }
    },
    async UpdateRateLimit(id: number, params: {
      max?: number
      duration?: number
      verb?: string
      path?: string
      state?: string
      settings?: RateLimitSettings
    }, callback?: () => void) {
      try {
        const { data } = await useBetterFetch<RateLimit>('auth/admin/rate_limits', {
          method: 'PUT',
          body: {
            id,
            ...params,
          },
        })
        Message.success({
          message: 'Update rate limit successfully',
        })
        if (callback) callback()
        const index = this.rate_limits.findIndex(item => item.id === id)
        if (index !== -1) {
          this.rate_limits.splice(index, 1, data)
        }
      }
      catch (error) {
        return error
      }
    },
    async FetchProfilesPending(params: Record<string, any> = {}, callback?: () => void) {
      const { headers, data } = await useBetterFetch<Profile[]>('auth/admin/profiles', {
        method: 'GET',
        params,
      })
      if (callback) callback()
      return { headers, data }
    },
    FetchUserProfiles(uid: string) {
      return useBetterFetch<Profile[]>(`auth/admin/profiles?uid=${uid}`)
    },
    async UpdateProfileState(id: number, state: string, callback?: () => void) {
      try {
        await useBetterFetch<Profile[]>('auth/admin/profiles', {
          method: 'PUT',
          body: {
            id,
            state,
          },
        })
        Message.success({
          message: 'Update Profile successfully',
        })
        if (callback) callback()
      }
      catch (error) {
        return error
      }
    },
    async FetchUsers(params: Record<string, any> = {}, callback?: () => void) {
      params.page ||= 1
      params.limit ||= 15

      const { headers, data } = await useBetterFetch<User[]>('auth/admin/users', {
        method: 'GET',
        params,
      })
      if (callback) callback()
      return { headers, data }
    },
    FetchMember(uid: string) {
      return useBetterFetch<Member>(`trade/admin/members/${uid}`)
    },
    FetchUser(uid: string) {
      return useBetterFetch<User>(`auth/admin/users/${uid}`)
    },
    async UpdateUser(params: { uid: string; email?: string; state?: string; role?: string; note?: string }) {
      try {
        await useBetterFetch<User>('auth/admin/users', {
          method: 'PUT',
          body: {
            ...params,
          },
        })
        Message.success({
          message: 'Update User successfully',
        })
      }
      catch (error) {
        return error
      }
    },
    async UpdateUserRole(params: { uid: string; role: string }) {
      try {
        await useBetterFetch<User>('auth/admin/users/role', {
          method: 'PUT',
          body: {
            ...params,
          },
        })
        Message.success({
          message: 'Update user role successfully',
        })
      }
      catch (error) {
        return error
      }
    },
    async UpdateUserOTP(uid: string, otp: boolean, callback?: () => void) {
      try {
        await useBetterFetch<User>('auth/admin/users', {
          method: 'PUT',
          body: {
            uid,
            otp,
          },
        })
        Message.success({
          message: 'Update User successfully',
        })
        if (callback) callback()
      }
      catch (error) {
        return error
      }
    },
    GetUserLabels(uid: string) {
      return useBetterFetch<Label[]>(`auth/admin/users/${uid}/labels`)
    },
    async AddLabelToUser(params: {
      uid: string
      key: string
      value: string
      scope?: string
      description?: string
    }, callback?: () => void) {
      try {
        const { data } = await useBetterFetch<Label>('auth/admin/users/label', {
          method: 'POST',
          body: {
            ...params,
          },
        })
        Message.success({
          message: 'Add label successfully',
        })
        if (callback) callback()
        return data
      }
      catch (error) {
        return {} as Label
      }
    },
    async UpdateLabelFromUser(uid: string, key: string, scope: string, value: string, callback?: () => void) {
      try {
        await useBetterFetch<Label>('auth/admin/users/label', {
          method: 'PUT',
          body: {
            uid,
            key,
            scope,
            value,
          },
        })
        Message.success({
          message: 'Update label successfully',
        })
        if (callback) callback()
      }
      catch (error) {
        return error
      }
    },
    async RemoveLabelFromUser(uid: string, key: string, scope: string, callback?: () => void) {
      try {
        await useBetterFetch('auth/admin/users/label', {
          method: 'DELETE',
          body: {
            uid,
            key,
            scope,
          },
        })
        Message.success({
          message: 'Remove label successfully',
        })
        if (callback) callback()
      }
      catch (error) {
        return error
      }
    },
    FetchUserDevices(uid: string) {
      return useBetterFetch<UserDevice[]>(`auth/admin/users/${uid}/devices`)
    },
    async DeleteUserDevice(uid: string, sessionId: string) {
      try {
        await useBetterFetch<UserDevice>(`auth/admin/users/${uid}/devices/${sessionId}`, {
          method: 'DELETE',
        })
        Message.success({
          message: 'Delete device successfully',
        })
      }
      catch (error) {
        return error
      }
    },
    async UpdateUserInviteCode(uid: string, inviteCode: string | null, callback?: () => void) {
      try {
        await useBetterFetch<UserDevice>(`auth/admin/users/${uid}/invite_code`, {
          method: 'PUT',
          body: {
            invite_code: inviteCode,
          },
        })
        Message.success({
          message: 'Update user successfully',
        })
        if (callback) callback()
      }
      catch (error) {
        return error
      }
    },
    async UpdateUserGroup(uid: string, group: string, callback?: () => void) {
      try {
        await useBetterFetch<UserDevice>(`trade/admin/members/${uid}`, {
          method: 'PUT',
          body: {
            group,
          },
        })
        Message.success({
          message: 'Update user successfully',
        })
        if (callback) callback()
      }
      catch (error) {
        return error
      }
    },
    FetchUserActivities(params: Record<string, any> = {}) {
      return useBetterFetch<Activity[]>('auth/admin/activities', {
        params,
      })
    },
    async FetchTrades(params: Record<string, any> = {}, callback?: () => void) {
      const { headers, data } = await useBetterFetch<Trade[]>('trade/admin/trades', {
        method: 'GET',
        params,
      })
      if (callback) callback()
      return { headers, data }
    },
    async FetchAssets(uid: string, callback?: () => void) {
      const { headers, data } = await useBetterFetch<Asset[]>(`trade/admin/members/${uid}/assets`, {
        method: 'GET',
      })
      if (callback) callback()
      return { headers, data }
    },
    async FetchOrders(params: Record<string, any> = {}, callback?: () => void) {
      const { headers, data } = await useBetterFetch<Order[]>('trade/admin/orders', {
        method: 'GET',
        params,
      })
      if (callback) callback()
      return { headers, data }
    },
    async FetchAdvertisements(params: Record<string, any> = {}, callback?: () => void) {
      try {
        const { headers, data } = await useBetterFetch<Advertisement[]>('trade/admin/advertisements', {
          method: 'GET',
          params,
        })
        if (callback) callback()
        return { headers, data }
      } catch (error) {
        return error
      }
    },
    async BanAdvertisement(id: string, callback?: () => void) {
      try {
        await useBetterFetch(`trade/admin/advertisements/${id}/ban`, {
          method: 'POST',
        })
        if (callback) callback()
        Message.success({
          message: 'Ban Advertisement successfully',
        })
      }
      catch (error) {
        return error
      }
    },
    async UnbanAdvertisement(id: string, callback?: () => void) {
      try {
        await useBetterFetch(`trade/admin/advertisements/${id}/unban`, {
          method: 'POST',
        })
        if (callback) callback()
        Message.success({
          message: 'Unban Advertisement successfully',
        })
      }
      catch (error) {
        return error
      }
    },
    async FailP2PTrade(id: string, unpaidId: number, callback?: () => void) {
      try {
        await useBetterFetch('trade/admin/p2p_trades/fail', {
          method: 'POST',
          body: {
            id,
            unpaid_id: Number(unpaidId),
          },
        })
        if (callback) callback()
        Message.success({
          message: 'Fail P2P Trade successfully',
        })
      }
      catch (error) {
        return error
      }
    },
    async ProcessP2PTrade(id: string, unpaidId: number, callback?: () => void) {
      try {
        await useBetterFetch('trade/admin/p2p_trades/process', {
          method: 'POST',
          body: {
            id,
            unpaid_id: Number(unpaidId),
          },
        })
        if (callback) callback()
        Message.success({
          message: 'Process P2P Trade successfully',
        })
      }
      catch (error) {
        return error
      }
    },
    async ProcessingComplain(id: number, callback?: () => void) {
      try {
        await useBetterFetch(`trade/admin/p2p_complains/${id}/processing`, {
          method: 'POST',
        })
        if (callback) callback()
        Message.success({
          message: 'Processing Complain successfully',
        })
      }
      catch (error) {
        return error
      }
    },
    async ProcessedComplain(id: number, callback?: () => void) {
      try {
        await useBetterFetch(`trade/admin/p2p_complains/${id}/processed`, {
          method: 'POST',
        })
        if (callback) callback()
        Message.success({
          message: 'Processed Complain successfully',
        })
      }
      catch (error) {
        return error
      }
    },
    async DenyComplain(id: number, response: string, callback?: () => void) {
      try {
        await useBetterFetch('trade/admin/p2p_complains/deny', {
          method: 'POST',
          body: {
            id: Number(id),
            response,
          },
        })
        if (callback) callback()
        Message.success({
          message: 'Deny Complain successfully',
        })
      }
      catch (error) {
        return error
      }
    },
    FetchP2PProfile(uid: string) {
      return useBetterFetch<P2PProfile>(`trade/admin/members/${uid}/profile`)
    },
    async VerifyProfile(uid: string, callback?: () => void) {
      try {
        await useBetterFetch<Order[]>(`trade/admin/members/${uid}/profile/verify`, {
          method: 'POST',
        })
        if (callback) callback()
        Message.success({
          message: 'Verify profile successfully',
        })
      }
      catch (error) {
        return error
      }
    },
    FetchAdvertisement(id: string) {
      return useBetterFetch<Advertisement>(`trade/admin/advertisements/${id}`)
    },
    FetchP2PTrade(id: string) {
      return useBetterFetch<P2PTrade>(`trade/admin/p2p_trades/${id}`)
    },
    FetchComplainsByStaticID(id: string) {
      return useBetterFetch<P2PComplain[]>(`trade/admin/p2p_complains/${id}`)
    },
    FetchP2PTradeConversations(id: string) {
      return useBetterFetch<ConversationMessage[]>(`trade/admin/p2p_trades/${id}/conversations`)
    },
    FetchComplain(id: number) {
      return useBetterFetch<P2PComplain>(`trade/admin/p2p_complains/${id}`)
    },
    async FetchP2PTrades(params: Record<string, any> = {}, callback?: () => void) {
      const { headers, data } = await useBetterFetch<P2PTrade[]>('trade/admin/p2p_trades', {
        method: 'GET',
        params,
      })
      if (callback) callback()
      return { headers, data }
    },
    async FetchP2PTradesInAdvertisement(id: string, params: Record<string, any> = {}, callback?: () => void) {
      const { headers, data } = await useBetterFetch<P2PTrade[]>(`trade/admin/advertisements/${id}/trades`, {
        method: 'GET',
        params,
      })
      if (callback) callback()
      return { headers, data }
    },
    async FetchP2PTradesComplains(params: Record<string, any> = {}, callback?: () => void) {
      const { headers, data } = await useBetterFetch<P2PTrade[]>('trade/admin/p2p_trades/complains', {
        method: 'GET',
        params,
      })
      if (callback) callback()
      return { headers, data }
    },
    async FetchConversationsP2PTrade(id: number, callback?: () => void) {
      const { data } = await useBetterFetch<ConversationMessage[]>(`trade/admin/p2p_trades/${id}/conversations`, {
        method: 'GET',
      })
      if (callback) callback()
      return data
    },
    async FetchP2PComplains(params: Record<string, any> = {}, callback?: () => void) {
      const { headers, data } = await useBetterFetch<P2PComplain[]>('trade/admin/p2p_complains', {
        method: 'GET',
        params,
      })
      if (callback) callback()
      return { headers, data }
    },
    async FetchPayments(params: Record<string, any> = {}, callback?: () => void) {
      const { headers, data } = await useBetterFetch<Payment[]>('trade/admin/payments', {
        method: 'GET',
        params,
      })
      if (callback) callback()
      return { headers, data }
    },
    async FetchPaymentsInAdvertisement(id: string, callback?: () => void) {
      const { headers, data } = await useBetterFetch<Payment[]>(`trade/admin/advertisements/${id}/payments`, {
        method: 'GET',
      })
      if (callback) callback()
      return { headers, data }
    },
    async UpdatePayment(id: number, state: string, callback?: () => void) {
      try {
        await useBetterFetch<Payment>('trade/admin/payments', {
          method: 'POST',
          body: {
            id,
            state,
          },
        })
        if (callback) callback()
        Message.success({
          message: 'Update payment successfully',
        })
      }
      catch (error) {
        return error
      }
    },
    async CancelOrder(id: number, callback?: () => void) {
      try {
        await useBetterFetch<Order[]>(`trade/admin/orders/${id}/cancel`, {
          method: 'POST',
        })
        if (callback) callback()
        Message.success({
          message: 'Cancel order successfully',
        })
      }
      catch (error) {
        return error
      }
    },
    async CancelOrders(params: { market: string; side?: string }, callback?: () => void) {
      try {
        await useBetterFetch<Order[]>('trade/admin/orders/cancel', {
          method: 'POST',
          body: params,
        })
        if (callback) callback()
        Message.success({
          message: 'Cancel orders successfully',
        })
      }
      catch (error) {
        return error
      }
    },
    async FetchDeposits(params: Record<string, any> = {}, callback?: () => void) {
      const { headers, data } = await useBetterFetch<Deposit[]>('trade/admin/deposits', {
        method: 'GET',
        params,
      })
      if (callback) callback()
      return { headers, data }
    },
    async FetchDeposit(id: number, callback?: () => void) {
      const { data } = await useBetterFetch<Deposit>(`trade/admin/deposits/${id}`, {
        method: 'GET',
      })
      if (callback) callback()
      return data
    },
    async FetchCollects(params: Record<string, any> = {}, callback?: () => void) {
      const { headers, data } = await useBetterFetch<Collect[]>('trade/admin/collects', {
        method: 'GET',
        params,
      })
      if (callback) callback()
      return { headers, data }
    },
    async FetchCollect(id: number, callback?: () => void) {
      const { data } = await useBetterFetch<Collect>(`trade/admin/collects/${id}`, {
        method: 'GET',
      })
      if (callback) callback()
      return data
    },
    async SendCollectAction(params: { id: number; action: string }, callback?: () => void) {
      try {
        await useBetterFetch('trade/admin/collects/actions', {
          method: 'POST',
          body: params,
        })
        if (callback) callback()
      }
      catch (error) {
        return error
      }
    },
    async FetchStatsDatabase(callback?: () => void) {
      try {
        const { data } = await useBetterFetch<StatDatabase[]>('kouda/admin/monitor/database', {
          method: 'GET',
        })

        this.statsDatabase = data
        if (callback) callback()
      } catch (error) {
        this.visibleStatsWarning = true
        return error
      }
    },
    async FetchStatsVault(callback?: () => void) {
      try {
        const { data } = await useBetterFetch<StatVault[]>('kouda/admin/monitor/vault', {
          method: 'GET',
        })

        this.statsVault = data
        if (callback) callback()
      } catch (error) {
        this.visibleStatsWarning = true
        return error
      }
    },
    async ExportDepositAddress(params: { id: number; address: string; otp_code: string }, callback?: () => void) {
      try {
        const { data } = await useBetterFetch<{
          private_key: string
          address: string
          exported_at: string
          last_exported_by: string
        }>(`trade/admin/deposit_addresses/${params.id}/export`, {
          method: 'POST',
          body: {
            address: params.address,
            otp_code: params.otp_code,
          },
        })
        if (callback) callback()
        return data
      }
      catch (error) {
        return {} as {
          private_key: string
          address: string
          exported_at: string
          last_exported_by: string
        }
      }
    },
    async FetchWithdraws(params: Record<string, any> = {}, callback?: () => void) {
      const { headers, data } = await useBetterFetch<Withdraw[]>('trade/admin/withdraws', {
        method: 'GET',
        params,
      })
      if (callback) callback()
      return { headers, data }
    },
    async FetchWithdraw(id: number, callback?: () => void) {
      const { data } = await useBetterFetch<Withdraw>(`trade/admin/withdraws/${id}`, {
        method: 'GET',
      })
      if (callback) callback()
      return data
    },
    async SendDepositAction(params: { id: number; action: string }, callback?: () => void) {
      try {
        await useBetterFetch(`trade/admin/deposits/${params.id}/actions`, {
          method: 'POST',
          body: params,
        })
        if (callback) callback()
      }
      catch (error) {
        return error
      }
    },
    async SendWithdrawAction(params: { id: number; action: string }, callback?: () => void) {
      try {
        await useBetterFetch('trade/admin/withdraws/actions', {
          method: 'POST',
          body: params,
        })
        if (callback) callback()
      }
      catch (error) {
        return error
      }
    },
    async FetchWallets(params: Record<string, any> = {}, callback?: () => void) {
      try {
        const { headers } = await useBetterFetch<Wallet[]>('trade/admin/wallets', {
          method: 'GET',
          params,
        })

        const total = Number(headers.total)

        const { data } = await useBetterFetch<Wallet[]>('trade/admin/wallets', {
          method: 'GET',
          params: {
            page: 1,
            limit: total,
          },
        })

        this.wallets = data
        if (callback) callback()
      } catch (error) {
        return error
      }
    },
    FetchWallet(id: string) {
      return useBetterFetch<Wallet>(`trade/admin/wallets/${id}`)
    },
    async FetchBlockchainClients() {
      try {
        const { data: clients } = await useBetterFetch<BlockchainClient[]>('trade/admin/blockchains/clients')

        this.blockchainClients = clients
      } catch (error) {
        return error
      }
    },
    async FetchWalletKinds() {
      try {
        const { data: kinds } = await useBetterFetch<string[]>('trade/admin/wallets/kinds')

        this.walletKinds = kinds
      } catch (error) {
        return error
      }
    },
    async FetchWalletGateWays() {
      try {
        const { data: gateways } = await useBetterFetch<WalletGateway[]>('trade/admin/wallets/gateways')

        this.walletGateways = gateways
      } catch (error) {
        return error
      }
    },
    async CreateWallet(params: {
      blockchain_key: string
      name: string
      address?: string
      kind: string
      gateway: string
      uri?: string
      secret?: string
      max_balance?: number
      status?: string
    }, callback?: () => void) {
      try {
        const { data } = await useBetterFetch<Wallet>('trade/admin/wallets', {
          method: 'POST',
          body: {
            ...params,
          },
        })
        Message.success({
          message: 'Create wallet successfully',
        })
        if (callback) callback()
        this.wallets.push(data)
      }
      catch (error) {
        return error
      }
    },
    async UpdateWallet(params: {
      id: number
      blockchain_key?: string
      name?: string
      address?: string
      kind?: string
      gateway?: string
      uri?: string
      secret?: string
      max_balance?: number
      status?: string
    }, callback?: () => void) {
      try {
        const { data } = await useBetterFetch<Wallet>('trade/admin/wallets', {
          method: 'PUT',
          body: {
            ...params,
          },
        })
        Message.success({
          message: 'Update wallet successfully',
        })
        if (callback) callback()
        const index = this.wallets.findIndex(item => item.id === params.id)
        if (index !== -1) {
          this.wallets.splice(index, 1, data)
        }
      }
      catch (error) {
        return error
      }
    },
    async AddCurrencyToWallet(id: number, currencies: string[], callback?: () => void) {
      try {
        await useBetterFetch<Wallet>('trade/admin/wallets/currencies', {
          method: 'POST',
          body: {
            id,
            currencies,
          },
        })
        Message.success({
          message: 'Add currency successfully',
        })
        if (callback) callback()
      }
      catch (error) {
        return error
      }
    },
    async RemoveCurrencyFromWallet(id: number, currencies: string[], callback?: () => void) {
      try {
        await useBetterFetch<Wallet>('trade/admin/wallets/currencies', {
          method: 'DELETE',
          body: {
            id,
            currencies,
          },
        })
        Message.success({
          message: 'Remove currency successfully',
        })
        if (callback) callback()
      }
      catch (error) {
        return error
      }
    },
    async FetchBanners(callback?: () => void) {
      const { headers, data } = await useBetterFetch<Banner[]>('kouda/admin/banners', {
        method: 'GET',
      })
      if (callback) callback()
      return { headers, data }
    },
    async CreateBanner(form: FormData, callback?: (banner: Banner) => void) {
      try {
        const { data: banner } = await useBetterFetch<Banner>('kouda/admin/banners', {
          headers: { 'Content-Type': 'multipart/form-data' },
          method: 'POST',
          body: form,
        })
        Message.success({
          message: 'Create banner successfully',
        })
        if (callback) callback(banner)
        return banner
      }
      catch (error) {
        return {} as Banner
      }
    },
    async UpdateBanner(form: FormData, callback?: () => void) {
      try {
        await useBetterFetch<Banner>('kouda/admin/banners', {
          headers: { 'Content-Type': 'multipart/form-data' },
          method: 'PUT',
          body: form,
        })
        Message.success({
          message: 'Update banner successfully',
        })
        if (callback) callback()
      }
      catch (error) {
        return error
      }
    },
    async UpdateBannerPosition(uuid: string, position: number, callback?: () => void) {
      try {
        await useBetterFetch<Banner>('kouda/admin/banners/position', {
          method: 'PUT',
          body: {
            uuid,
            position,
          },
        })
        Message.success({
          message: 'Update position banner successfully',
        })
        if (callback) callback()
      }
      catch (error) {
        return error
      }
    },
    async DeleteBlacklistAddress(id: number, callback?: () => void) {
      try {
        await useBetterFetch<BlacklistAddress>('trade/admin/blacklist_addresses', {
          method: 'DELETE',
          body: {
            id,
          },
        })
        Message.success({
          message: 'Delete blacklist address successfully',
        })
        if (callback) callback()
        this.blacklist_addresses = this.blacklist_addresses.filter(item => item.id !== id)
      }
      catch (error) {
        return error
      }
    },
    async DeleteRateLimit(id: number, callback?: () => void) {
      try {
        await useBetterFetch<RateLimit>(`auth/admin/rate_limits/${id}`, {
          method: 'DELETE',
        })
        Message.success({
          message: 'Delete rate limit successfully',
        })
        if (callback) callback()
        this.rate_limits = this.rate_limits.filter(item => item.id !== id)
      }
      catch (error) {
        return error
      }
    },
    async DeleteWhitelistedSmartContract(id: number, callback?: () => void) {
      try {
        await useBetterFetch<WhitelistedSmartContract>('trade/admin/whitelisted_smart_contracts', {
          method: 'DELETE',
          body: {
            id,
          },
        })
        Message.success({
          message: 'Delete whitelisted smart contract successfully',
        })
        if (callback) callback()
        this.whitelisted_smart_contracts = this.whitelisted_smart_contracts.filter(item => item.id !== id)
      }
      catch (error) {
        return error
      }
    },
    async DeleteBanner(uuid: string, callback?: () => void) {
      try {
        await useBetterFetch<Banner>(`kouda/admin/banners/${uuid}`, {
          method: 'DELETE',
        })
        Message.success({
          message: 'Delete banner successfully',
        })
        if (callback) callback()
        this.banners = this.banners.filter(item => item.uuid !== uuid)
      }
      catch (error) {
        return error
      }
    },
    FetchBlockchainCurrency(id: string) {
      return useBetterFetch<BlockchainCurrency>(`trade/admin/blockchain_currencies/${id}`)
    },
    FetchBlockchainCurrencies(params: Record<string, any> = {}) {
      return useBetterFetch<BlockchainCurrency[]>('trade/admin/blockchain_currencies', {
        method: 'GET',
        params,
      })
    },
    async CreateBlockchainCurrency(params: {
      currency_id: string
      blockchain_key: string
      subunits?: number
      parent_id?: string | null
      min_deposit_amount?: number
      min_record_deposit_amount?: number
      min_collection_amount?: number
      deposit_fee?: number
      withdraw_fee?: number
      withdraw_fee_percentage?: boolean
      options?: object
      visible_deposit_enabled?: boolean
      deposit_enabled?: boolean
      withdraw_enabled?: boolean
      untrusted?: boolean
      status?: BlockchainCurrencyStatus
      use_parent_fee?: boolean
    }) {
      try {
        const { data: blockchainCurrency } = await useBetterFetch<BlockchainCurrency>('trade/admin/blockchain_currencies', {
          method: 'POST',
          body: params,
        })
        navigateTo(`/exchange/currencies/${blockchainCurrency.currency_id}`)
        Message.success({
          message: 'Create blockchain currency successfully',
        })
      }
      catch (error) {
        return error
      }
    },

    async UpdateBlockchainCurrency(params: {
      id: number
      currency_id?: string
      blockchain_key?: string
      subunits?: number
      parent_id?: string | null
      min_deposit_amount?: number
      min_record_deposit_amount?: number
      min_collection_amount?: number
      min_withdraw_amount?: number
      deposit_fee?: number
      withdraw_fee?: number
      withdraw_fee_percentage?: boolean
      options?: object
      visible_deposit_enabled?: boolean
      deposit_enabled?: boolean
      withdraw_enabled?: boolean
      untrusted?: boolean
      status?: BlockchainCurrencyStatus
      use_parent_fee?: boolean
    }) {
      try {
        const { data: blockchainCurrency } = await useBetterFetch<BlockchainCurrency>('trade/admin/blockchain_currencies', {
          method: 'PUT',
          body: {
            ...params,
          },
        })
        navigateTo(`/exchange/currencies/${blockchainCurrency.currency_id}`)
        Message.success({
          message: 'Update blockchain currency successfully',
        })
      }
      catch (error) {
        return error
      }
    },

    async FetchOperationAssets(params: Record<string, any> = {}, callback?: () => void) {
      const { headers, data } = await useBetterFetch<OperationAsset[]>('trade/admin/operation_assets', {
        method: 'GET',
        params,
      })
      if (callback) callback()
      return { headers, data }
    },
    async FetchOperationRevenues(params: Record<string, any> = {}, callback?: () => void) {
      const { headers, data } = await useBetterFetch<OperationRevenue[]>('trade/admin/operation_revenues', {
        method: 'GET',
        params,
      })
      if (callback) callback()
      return { headers, data }
    },
    async FetchOperationExpenses(params: Record<string, any> = {}, callback?: () => void) {
      const { headers, data } = await useBetterFetch<OperationExpense[]>('trade/admin/operation_expenses', {
        method: 'GET',
        params,
      })
      if (callback) callback()
      return { headers, data }
    },
    async FetchOperationLiabilities(params: Record<string, any> = {}, callback?: () => void) {
      const { headers, data } = await useBetterFetch<OperationLiability[]>('trade/admin/operation_liabilities', {
        method: 'GET',
        params,
      })
      if (callback) callback()
      return { headers, data }
    },
    async FetchTradingFees(params: Record<string, any> = {}, callback?: () => void) {
      try {
        const { headers } = await useBetterFetch<TradingFee[]>('trade/admin/trading_fees', {
          method: 'GET',
          params,
        })

        const total = Number(headers.total)

        const { data } = await useBetterFetch<TradingFee[]>('trade/admin/trading_fees', {
          method: 'GET',
          params: {
            page: 1,
            limit: total,
          },
        })

        this.trading_fees = data
        if (callback) callback()
      } catch (error) {
        return error
      }
    },
    async FetchWithdrawLimits(params: Record<string, any> = {}, callback?: () => void) {
      try {
        const { headers } = await useBetterFetch<WithdrawLimit[]>('trade/admin/withdraw_limits', {
          method: 'GET',
          params,
        })

        const total = Number(headers.total)

        const { data } = await useBetterFetch<WithdrawLimit[]>('trade/admin/withdraw_limits', {
          method: 'GET',
          params: {
            page: 1,
            limit: total,
          },
        })

        this.withdraw_limits = data
        if (callback) callback()
      } catch (error) {
        return error
      }
    },
    async CreateTradingFee(params: {
      maker: number
      taker: number
      group?: string
      market_id: string
    }, callback?: () => void) {
      try {
        const { data } = await useBetterFetch<TradingFee>('trade/admin/trading_fees', {
          method: 'POST',
          body: {
            ...params,
          },
        })
        Message.success({
          message: 'Create trading fee successfully',
        })
        if (callback) callback()
        return data
      }
      catch (error) {
        return {} as TradingFee
      }
    },
    async CreateWithdrawLimit(params: {
      limit: number
      group?: string
      kyc_level?: string
    }, callback?: () => void) {
      try {
        const { data } = await useBetterFetch<WithdrawLimit>('trade/admin/withdraw_limits', {
          method: 'POST',
          body: {
            ...params,
          },
        })
        Message.success({
          message: 'Create withdraw limit successfully',
        })
        if (callback) callback()
        return data
      }
      catch (error) {
        return {} as WithdrawLimit
      }
    },
    async UpdateTradingFee(params: {
      id: number
      maker?: number
      taker?: number
      group?: string
      market_id?: string
    }, callback?: () => void) {
      try {
        await useBetterFetch<TradingFee>('trade/admin/trading_fees', {
          method: 'PUT',
          body: {
            ...params,
          },
        })
        Message.success({
          message: 'Update trading fee successfully',
        })
        if (callback) callback()
      }
      catch (error) {
        return error
      }
    },
    async UpdateWithdrawLimit(params: {
      id: number
      limit?: number
      group?: string
      kyc_level?: string
    }, callback?: () => void) {
      try {
        await useBetterFetch<WithdrawLimit>('trade/admin/withdraw_limits', {
          method: 'PUT',
          body: {
            ...params,
          },
        })
        Message.success({
          message: 'Update withdraw limit successfully',
        })
        if (callback) callback()
      }
      catch (error) {
        return error
      }
    },
    async DeleteTradingFee(id: number, callback?: () => void) {
      try {
        await useBetterFetch<TradingFee>(`trade/admin/trading_fees/${id}`, {
          method: 'DELETE',
        })
        Message.success({
          message: 'Delete trading fee successfully',
        })
        if (callback) callback()
      }
      catch (error) {
        return error
      }
    },
    async DeleteWithdrawLimit(id: number, callback?: () => void) {
      try {
        await useBetterFetch<WithdrawLimit>(`trade/admin/withdraw_limits/${id}`, {
          method: 'DELETE',
        })
        Message.success({
          message: 'Delete withdraw limit successfully',
        })
        if (callback) callback()
      }
      catch (error) {
        return error
      }
    },
    async FetchActivitiesAdmin(params: Record<string, any> = {}, callback?: () => void) {
      params.page ||= 1
      params.limit ||= 15

      const { headers, data } = await useBetterFetch<Activity[]>('auth/admin/activities/admin', {
        method: 'GET',
        params,
      })
      if (callback) callback()
      return { headers, data }
    },
    FetchActivityAdmin(query?: string) {
      return useBetterFetch<Activity[]>(`auth/admin/activities/admin${query}`)
    },

    async FetchActivitiesMetrics(params: Record<string, any> = {}, callback?: () => void) {
      try {
        const date = new Date()
        const timeTo = params.time_to ? params.time_to : Math.floor(endOfDay(date).getTime() / 1000)
        const timeFrom = params.time_to ? params.time_from : Math.floor(startOfDay(addDays(date, -6)).getTime() / 1000)

        const { data } = await useBetterFetch<UserActivityMetrics[]>(`auth/admin/activities/metrics/users?time_to=${timeTo}&time_from=${timeFrom}`, {
          method: 'GET',
        })
        this.userActivityMetrics = data
        if (callback) callback()
      } catch (error) {
        return error
      }
    },
    async FetchOrderMetrics(params: Record<string, any> = {}, callback?: () => void) {
      try {
        const date = new Date()
        const timeTo = params.time_to ? params.time_to : Math.floor(endOfDay(date).getTime() / 1000)
        const timeFrom = params.time_to ? params.time_from : Math.floor(startOfDay(addDays(date, -6)).getTime() / 1000)

        const { headers, data } = await useBetterFetch<OrderMetrics[]>(`trade/admin/orders/metrics?time_to=${timeTo}&time_from=${timeFrom}`, {
          method: 'GET',
        })
        this.orderMetrics = data
        if (callback) callback()
        return { headers }
      }
      catch (error) {
        return { } as { headers: Headers }
      }
    },
    async FetchTradeMetrics(params: Record<string, any> = {}, callback?: () => void) {
      try {
        const date = new Date()
        const timeTo = params.time_to ? params.time_to : Math.floor(endOfDay(date).getTime() / 1000)
        const timeFrom = params.time_to ? params.time_from : Math.floor(startOfDay(addDays(date, -6)).getTime() / 1000)

        const { headers, data } = await useBetterFetch<TradeMetrics[]>(`trade/admin/trades/metrics?time_to=${timeTo}&time_from=${timeFrom}`, {
          method: 'GET',
        })
        this.tradeMetrics = data
        if (callback) callback()
        return { headers }
      }
      catch (error) {
        return { } as { headers: Headers }
      }
    },
    async FetchUserDeviceMetrics(params: Record<string, any> = {}, callback?: () => void) {
      try {
        const { headers, data } = await useBetterFetch<UserDeviceMetrics>('auth/admin/activities/metrics/devices', {
          method: 'GET',
          params,
        })
        this.userDevice = data
        if (callback) callback()
        return { headers }
      }
      catch (error) {
        return error
      }
    },
    async FetchSessionLocations(params: Record<string, any> = {}, callback?: () => void) {
      try {
        const date = new Date()
        const timeTo = params.time_to ? params.time_to : Math.floor(endOfDay(date).getTime() / 1000)
        const timeFrom = params.time_to ? params.time_from : Math.floor(startOfDay(addDays(date, -6)).getTime() / 1000)

        const { headers, data } = await useBetterFetch<SessionLocation[]>(`auth/admin/activities/metrics/location?time_to=${timeTo}&time_from=${timeFrom}`, {
          method: 'GET',
        })
        this.sessionLocations = data
        if (callback) callback()
        return { headers }
      } catch (error) {
        this.sessionLocations = []
      }
    },
    async FetchBlacklistAddress(params: Record<string, any> = {}, callback?: () => void) {
      try {
        const { headers } = await useBetterFetch<BlacklistAddress[]>('trade/admin/blacklist_addresses', {
          method: 'GET',
          params,
        })

        const total = Number(headers.total)

        const { data } = await useBetterFetch<BlacklistAddress[]>('trade/admin/blacklist_addresses', {
          method: 'GET',
          params: {
            page: 1,
            limit: total,
          },
        })

        this.blacklist_addresses = data

        if (callback) callback()
      } catch (error) {
        return error
      }
    },
    async FetchWhitelistedSmartContracts(params: Record<string, any> = {}, callback?: () => void) {
      try {
        const { headers } = await useBetterFetch<WhitelistedSmartContract[]>('trade/admin/whitelisted_smart_contracts', {
          method: 'GET',
          params,
        })

        const total = Number(headers.total)

        const { data } = await useBetterFetch<WhitelistedSmartContract[]>('trade/admin/whitelisted_smart_contracts', {
          method: 'GET',
          params: {
            page: 1,
            limit: total,
          },
        })

        this.whitelisted_smart_contracts = data

        if (callback) callback()
      } catch (error) {
        return error
      }
    },
    async FetchConfigs(params: Record<string, any> = {}, callback?: () => void) {
      try {
        const { headers } = await useBetterFetch<Config[]>('quantex/admin/configs', {
          method: 'GET',
          params,
        })

        const total = Number(headers.total)

        const { data } = await useBetterFetch<Config[]>('quantex/admin/configs', {
          method: 'GET',
          params: {
            page: 1,
            limit: total,
          },
        })

        this.configs = data

        if (callback) callback()
      } catch (error) {
        return error
      }
    },
    async CreateBlacklistAddress(params: {
      address: string
    }, callback?: () => void) {
      try {
        const { data } = await useBetterFetch<BlacklistAddress>('trade/admin/blacklist_addresses', {
          method: 'POST',
          body: {
            ...params,
          },
        })

        Message.success({
          message: 'Create blacklist address successfully',
        })

        this.blacklist_addresses.push(data)
        if (callback) callback()
      }
      catch (error) {
        return {} as BlacklistAddress
      }
    },
    async CreateWhitelistedSmartContract(params: {
      blockchain_key: string
      state: WhitelistedSmartContractState
      address: string
      description?: string
    }, callback?: () => void) {
      try {
        const { data } = await useBetterFetch<WhitelistedSmartContract>('trade/admin/whitelisted_smart_contracts', {
          method: 'POST',
          body: {
            ...params,
          },
        })

        Message.success({
          message: 'Create whitelisted smart contract successfully',
        })

        this.whitelisted_smart_contracts.push(data)
        if (callback) callback()
      }
      catch (error) {
        return {} as WhitelistedSmartContract
      }
    },
    async UpdateBlacklistAddress(params: {
      id: number
      address: string
    }, callback?: () => void) {
      try {
        const { data: blacklistAddress } = await useBetterFetch<BlacklistAddress>('trade/admin/blacklist_addresses', {
          method: 'PUT',
          body: {
            ...params,
          },
        })

        Message.success({
          message: 'Update nlacklist address successfully',
        })

        const index = this.blacklist_addresses.findIndex(c => c.id === params.id)
        if (index !== -1) {
          this.blacklist_addresses[index] = blacklistAddress
        }

        if (callback) callback()
      }
      catch (error) {
        return error
      }
    },
    async UpdateWhitelistedSmartContract(params: {
      id: number
      blockchain_key?: string
      state?: WhitelistedSmartContractState
      address?: string
      description?: string
    }, callback?: () => void) {
      try {
        const { data: whitelistedSmartContract } = await useBetterFetch<WhitelistedSmartContract>('trade/admin/whitelisted_smart_contracts', {
          method: 'PUT',
          body: {
            ...params,
          },
        })

        Message.success({
          message: 'Update whitelisted smart contract successfully',
        })

        const index = this.whitelisted_smart_contracts.findIndex(c => c.id === params.id)
        if (index !== -1) {
          this.whitelisted_smart_contracts[index] = whitelistedSmartContract
        }

        if (callback) callback()
      }
      catch (error) {
        return error
      }
    },
    async CreateConfig(params: {
      key: string
      value: string
    }, callback?: () => void) {
      try {
        const { data } = await useBetterFetch<Config>('quantex/admin/configs', {
          method: 'POST',
          body: {
            ...params,
          },
        })

        Message.success({
          message: 'Create config successfully',
        })

        this.configs.push(data)
        if (callback) callback()
      }
      catch (error) {
        return {} as Config
      }
    },
    async UpdateConfig(params: {
      key: string
      value: string
    }, callback?: () => void) {
      try {
        const { data: config } = await useBetterFetch<Config>('quantex/admin/configs', {
          method: 'PUT',
          body: {
            ...params,
          },
        })

        Message.success({
          message: 'Update config successfully',
        })

        const index = this.configs.findIndex(c => c.key === params.key)
        if (index !== -1) {
          this.configs[index] = config
        }

        if (callback) callback()
      }
      catch (error) {
        return error
      }
    },
    async FetchBotMarkets(params: Record<string, any> = {}, callback?: () => void) {
      try {
        const { headers } = await useBetterFetch<BotMarket[]>('quantex/admin/markets', {
          method: 'GET',
          params,
        })

        const total = Number(headers.total)

        const { data } = await useBetterFetch<BotMarket[]>('quantex/admin/markets', {
          method: 'GET',
          params: {
            page: 1,
            limit: total,
          },
        })

        this.botMarkets = data
        if (callback) callback()
      } catch (error) {
        return error
      }
    },
    async CreateBotMarket(params: {
      exchange: string
      base_unit: string
      quote_unit: string
      price_precision: number
      amount_precision: number
      min_price: number
      min_amount: number
      max_amount: number
    }, callback?: () => void) {
      try {
        const { data } = await useBetterFetch<BotMarket>('quantex/admin/markets', {
          method: 'POST',
          body: {
            ...params,
          },
        })

        Message.success({
          message: 'Create market successfully',
        })

        this.botMarkets.push(data)
        if (callback) callback()
      }
      catch (error) {
        return {} as BotMarket
      }
    },
    async UpdateBotMarket(params: {
      id: number
      exchange?: string
      base_unit?: string
      quote_unit?: string
      price_precision?: number
      amount_precision?: number
      min_price?: number
      min_amount?: number
      max_amount?: number
    }, callback?: () => void) {
      try {
        const { data: botMarket } = await useBetterFetch<BotMarket>('quantex/admin/markets', {
          method: 'PUT',
          body: {
            ...params,
          },
        })

        Message.success({
          message: 'Update market successfully',
        })

        const index = this.botMarkets.findIndex(m => m.id === Number(botMarket.id))
        if (index !== -1) {
          this.botMarkets[index] = botMarket
        }

        if (callback) callback()
      }
      catch (error) {
        return error
      }
    },
    async FetchStrategies(params: Record<string, any> = {}, callback?: () => void) {
      try {
        const { headers } = await useBetterFetch<Strategy[]>('quantex/admin/strategies', {
          method: 'GET',
          params,
        })

        const total = Number(headers.total)

        const { data } = await useBetterFetch<Strategy[]>('quantex/admin/strategies', {
          method: 'GET',
          params: {
            page: 1,
            limit: total,
          },
        })

        this.strategies = data
        if (callback) callback()
      } catch (error) {
        return error
      }
    },
    async FetchStrategy(id: number, callback?: () => void) {
      const { headers, data } = await useBetterFetch<Strategy>(`quantex/admin/strategies/${id}`, {
        method: 'GET',
      })
      if (callback) callback()
      return { headers, data }
    },
    async CreateStrategy(params: {
      kind: StrategyKind
      target_market_id: number
      state: StrategyState
      tick: number
      flush: number
      options?: {
        level_count?: number
        spread?: number
        level_size?: number
      }
    }, callback?: () => void) {
      try {
        const { data } = await useBetterFetch<Strategy>('quantex/admin/strategies', {
          method: 'POST',
          body: {
            ...params,
          },
        })
        Message.success({
          message: 'Create strategy successfully',
        })
        if (callback) callback()
        this.strategies.push(data)
      }
      catch (error) {
        return error
      }
    },
    async UpdateStrategy(params: {
      id: number
      kind?: StrategyKind
      target_market_id?: number
      state?: StrategyState
      tick?: number
      flush?: number
      options?: Record<string, number>
    }, callback?: () => void) {
      try {
        const { data } = await useBetterFetch<Strategy>('quantex/admin/strategies', {
          method: 'PUT',
          body: {
            ...params,
          },
        })
        Message.success({
          message: 'Update strategy successfully',
        })
        if (callback) callback()
        const index = this.strategies.findIndex(s => s.id === data.id)
        if (index !== -1) {
          this.strategies[index] = data
        }
      }
      catch (error) {
        return error
      }
    },
    async RestartStrategy(id: number, callback?: () => void) {
      try {
        await useBetterFetch('quantex/admin/strategies/restart', {
          method: 'POST',
          body: {
            id,
          },
        })
        Message.success({
          message: 'Restart strategy successfully',
        })
        if (callback) callback()
      }
      catch (error) {
        return error
      }
    },
    async CreateMarketStrategy(params: { strategy_id: number; markets: number[] }, callback?: () => void) {
      try {
        const { data } = await useBetterFetch<Strategy>('quantex/admin/strategy_source_markets/', {
          method: 'POST',
          body: {
            ...params,
          },
        })
        Message.success({
          message: 'Create market strategy successfully',
        })
        if (callback) callback()
        this.strategies.push(data)
      }
      catch (error) {
        return error
      }
    },
    async DeleteMarketStrategy(params: { strategy_id: number; markets: number[] }, callback?: () => void) {
      try {
        await useBetterFetch('quantex/admin/strategy_source_markets/', {
          method: 'DELETE',
          body: {
            ...params,
          },
        })
        Message.success({
          message: 'Delete market strategy successfully',
        })
        if (callback) callback()
      }
      catch (error) {
        return error
      }
    },
    async GetBlockchainLatestBlock(id: number, callback?: () => void) {
      try {
        const { data } = await useBetterFetch<number>(`trade/admin/blockchains/${id}/latest_block`)
        if (callback) callback()
        return data
      }
      catch (error) {
        return 0
      }
    },
    async ProcessBlock(params: { id: number; block_number: number }, callback?: () => void) {
      try {
        await useBetterFetch<number>('trade/admin/blockchains/process_block', {
          method: 'POST',
          body: {
            ...params,
          },
        })
        Message.success({
          message: `Process block ${params.block_number} successfully`,
        })
        if (callback) callback()
      }
      catch (error) {
        return 0
      }
    },
    async FetchTodayRevenue() {
      try {
        const { data } = await useBetterFetch<{ total_revenues: number }>('trade/admin/statistics/revenues/today')
        this.todayRevenue = Number(data.total_revenues)
      }
      catch (error) {
        return error
      }
    },
    async FetchRevenueStatistics(params: Record<string, any> = {}, callback?: () => void) {
      try {
        const date = new Date()
        const timeTo = params.time_to ? params.time_to : Math.floor(endOfDay(date).getTime() / 1000)
        const timeFrom = params.time_to ? params.time_from : Math.floor(startOfDay(addDays(date, -6)).getTime() / 1000)

        const { data } = await useBetterFetch<RevenueStatistics[]>(`trade/admin/statistics/revenues?time_to=${timeTo}&time_from=${timeFrom}`, {
          method: 'GET',
        })
        this.revenueStatistics = data
        if (callback) callback()
      }
      catch (error) {
        return error
      }
    },
    async FetchAssetsStatistics(params: Record<string, any> = {}, callback?: () => void) {
      try {
        const date = new Date()
        const timeTo = params.time_to ? params.time_to : Math.floor(endOfDay(date).getTime() / 1000)
        const timeFrom = params.time_to ? params.time_from : Math.floor(startOfDay(addDays(date, -6)).getTime() / 1000)

        const { data } = await useBetterFetch<AssetsStatistics[]>(`trade/admin/statistics/assets?time_to=${timeTo}&time_from=${timeFrom}`, {
          method: 'GET',
        })
        this.assetsStatistics = data
        if (callback) callback()
      }
      catch (error) {
        return error
      }
    },
    async FetchRecentTrades() {
      try {
        const { data } = await useBetterFetch<Trade[]>('trade/admin/trades')
        this.recentTrades = data
      }
      catch (error) {
        return error
      }
    },
    async FetchRecentActivities() {
      try {
        const { data } = await useBetterFetch<Activity[]>('auth/admin/activities', {
          params: {
            action: 'login',
          },
        })
        this.recentSessions = data
      }
      catch (error) {
        return error
      }
    },
  },
})

if (import.meta.hot)
  import.meta.hot.accept(acceptHMRUpdate(useAdminStore, import.meta.hot))
