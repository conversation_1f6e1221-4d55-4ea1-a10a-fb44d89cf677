import { BlockchainClient, WalletGateway } from '@zsmartex/types'

export const blockchainClientsCustomURI: BlockchainClient[] = [
  BlockchainClient.Tron,
  BlockchainClient.Spacemesh,
]

export const walletGatewayCustomURI: WalletGateway[] = [
  WalletGateway.Tron,
  WalletGateway.Spacemesh,
]

export const allowedRoles = [UserRole.SuperAdmin, UserRole.Admin, UserRole.Support1, UserRole.Support2] as const
