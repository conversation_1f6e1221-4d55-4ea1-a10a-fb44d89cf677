import type { InputType } from '@zsmartex/components/types'
import type { Placement } from '@zsmartex/types'

export enum ProfileState {
  Temporary = 'temporary',
  Pending = 'pending',
  Verified = 'verified',
  Reject = 'reject',
}

export enum LabelScope {
  Private = 'private',
  Public = 'public',
}

export enum P2PComplainState {
  Pending = 'pending',
  Processing = 'processing',
  Processed = 'processed',
  Denied = 'denied',
}

export enum GasPriceRate {
  Standard = 'standard',
  Fast = 'fast',
  Turbo = 'turbo',
  DoubleSpeed = 'double_speed',
}

export enum BlockchainStatus {
  Active = 'active',
  Disabled = 'disabled',
  Idle = 'idle',
}

export enum RateLimitState {
  Active = 'active',
  Disabled = 'disabled',
}

export enum  RateLimitVerb {
  ALL     = "ALL",
	GET     = "GET",
	POST    = "POST",
	PUT     = "PUT",
	PATCH   = "PATCH",
	DELETE  = "DELETE",
	OPTIONS = "OPTIONS",
	HEAD    = "HEAD",
}

export enum CurrencyStatus {
  Enabled = 'enabled',
  Disabled = 'disabled',
}

export enum BlockchainCurrencyStatus {
  Active = 'active',
  Disabled = 'disabled',
}

export enum WalletStatus {
  Active = 'active',
  Disabled = 'disabled',
}

export enum BannerState {
  Enabled = 'enabled',
  Disabled = 'disabled',
}

export enum PaymentState {
  Enabled = 'enabled',
  Disabled = 'disabled',
  Denied = 'denied',
}

export enum WalletKind {
  Deposit = 'deposit',
  Fee = 'fee',
  Hot = 'hot',
  Warm = 'warm',
  Cold = 'cold',
}

export enum ZAdminFormFieldType {
  Input = 'input',
  InputCopy = 'input-copy',
  Select = 'select',
  Text = 'text',
  Switch = 'switch',
  Textarea = 'textarea',
  Checkbox = 'checkbox',
}

export interface ZAdminFormField {
  key: string
  label?: string
  labelStyle?: string
  type: ZAdminFormFieldType
  typeInput?: InputType
  value?: string | number | boolean
  validate?: ((value: any) => any)[]
  scopedSlots?: boolean
  dataSource?: any[]
  search?: boolean
  columns?: ZTableColumn[]
  findBy?: string[]
  valueKey?: string
  labelKey?: string
  class?: string
  scroll?: boolean
  disabled?: boolean
  autoFocus?: boolean
  replaceFunc?: (text: string) => string
  onChange?: (...data: any) => void
  onInput?: (...data: any) => void
}

export interface ZAdminFormColumn {
  class?: string
  fields: ZAdminFormField[]
}

export enum ConfigKey {
  AccessKey = 'access_key',
  SecretKey = 'secret_key',
}

export enum BotMarketExchange {
  Zsmartex = 'zsmartex',
  Binance = 'binance',
  Coinmarketcap = 'coinmarketcap',
}

export enum StrategyState {
  Active = 'active',
  Disabled = 'disabled',
}

export enum StrategyKind {
  MarketMaking = 'market_making',
  CandleSampling = 'candle_sampling',
  PriceSampling = 'price_sampling',
}

export enum WhitelistedSmartContractState {
  Active = 'active',
  Disabled = 'disabled',
}

export interface DisabledRule {
  key: string
  required?: boolean
}

export type ZSelectList = {
  text: string
  key: string
}[]

export interface Filter {
  key: string
  label: string
  value?: any
  type: FilterType
  data?: any
  columns?: ZTableColumn[]
  valueKey?: string
  labelKey?: string
  findBy?: string[]
  datePlacement?: Placement
  scroll?: boolean
  replaceFunc?: (text: string) => string
}

export interface MenuActionItem {
  key: string
  title: string
  icon?: string
  callback: () => void
}

export const enum FilterType {
  Input = 'input',
  Select = 'select',
  DateRange = 'date-range',
  InputNumber = 'input-number',
}

export type StorageLike = Pick<Storage, 'getItem' | 'removeItem' | 'setItem'>

export interface PersistedStateOptions {
  /**
   * Storage key to use.
   * @default $store.id
   */
  key?: string

  /**
   * Where to store persisted state.
   * @default localStorage
   */
  storage?: StorageLike

  /**
   * Dot-notation paths to partially save state.
   * @default undefined
   */
  paths?: Array<string>

  /**
   * Overwrite initial state (patch otherwise).
   * @default false
   */
  overwrite?: boolean
}
