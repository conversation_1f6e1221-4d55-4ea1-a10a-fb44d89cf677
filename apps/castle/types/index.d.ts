import type {
  BlockchainClient,
  DepositType,
  Activity as IActivity,
  ConversationMessage as IConversationMessage,
  Trade as ITrade,
  Withdraw as IWithdraw,
  SystemAlertType,
  WalletGateway,
} from '@zsmartex/types'
import type { BlockchainStatus, RateLimitState, RateLimitVerb, StrategyKind, StrategyState, WhitelistedSmartContractState } from './index'

declare global {
  interface Activity extends IActivity {
    uid: string
    email: string
    role: string
    target_email: string
  }

  interface UserActivityMetrics {
    login: number
    register: number
    date: string
  }

  interface UserDeviceActivity {
    desktop: number
    tablet: number
    mobile: number
    unknown: number
  }

  interface UserDeviceMetrics {
    this_month: UserDeviceActivity
    this_week: UserDeviceActivity
    last_week: UserDeviceActivity
    today: UserDeviceActivity
    yesterday: UserDeviceActivity
  }

  interface SessionLocation {
    country: string
    login: number
    register: number
  }

  interface Banner {
    uuid: string
    url: string
    state: BannerState
    type: string
    position: number
    created_at: string
    updated_at: string
  }

  interface BlockchainCurrency {
    id: number
    currency_id: string
    blockchain_key: string
    parent_id?: string
    visible_deposit_enabled: boolean
    deposit_enabled: boolean
    withdraw_enabled: boolean
    use_parent_fee: boolean
    subunits: number
    min_deposit_amount: number
    min_record_deposit_amount: number
    min_collection_amount: number
    min_withdraw_amount: number
    deposit_fee?: number
    withdraw_fee: number
    withdraw_fee_percentage: boolean
    untrusted?: boolean
    options?: Record<string, string>
    status: BlockchainCurrencyStatus
    created_at: string
    updated_at: string
  }

  interface Blockchain {
    id: number
    key: string
    name: string
    client: BlockchainClient
    height: number
    protocol: string
    server?: string
    explorer_address?: string
    explorer_transaction?: string
    min_confirmations: number
    untrusted: bool
    status: BlockchainStatus
    warning: string
    multiple_collection: bool
    withdraw_queue: bool
    created_at: string
    updated_at: string
    options: Record<string, any>
  }

  interface RateLimitSettings {
    uids: string[]
    emails: string[]
    ips: string[]
  }
  interface UIConfiguration {
    id: number
    type: string
    value: string[]
    created_at: string
    updated_at: string
  }

  interface RateLimit {
    id?: number
    max: number
    duration: number
    verb: RateLimitVerb
    path: string
    settings: RateLimitSettings
    state: RateLimitState
    created_at?: string
    updated_at?: string
  }

  interface Currency {
    id: string
    name: string
    icon_url: string
    default_network_id?: number
    status: CurrencyStatus
    position: number
    precision: number
    price: string
    created_at: string
    updated_at: string
  }

  interface Advertisement {
    id: number
    member_id: number
    uid: string
    coin_currency: string
    fiat_currency: string
    filled_amount: number
    origin_amount: number
    self_unlock_amount: number
    origin_locked: number
    locked: number
    side: AdvertisementSide
    min: number
    max: number
    payments: Payment[]
    price: number
    state: AdvertisementState
    created_at: string
    updated_at: string
  }

  interface P2PTrade {
    id: number
    static_id: string
    maker_id: number
    taker_id: number
    maker_email: string
    taker_email: string
    supporter_email: string
    unpaid_email: string
    maker_uid: string
    taker_uid: string
    supporter_uid: string
    unpaid_uid: string
    coin_currency: string
    advertisement_id: number
    conversation_id: number
    payment_id: number
    state: P2PTradeState
    amount: number
    maker_locked: number
    taker_locked: number
    maker_accept_at: boolean
    taker_accept_at: boolean
    created_at: string
    updated_at: string
    completed_at: string
    expired_at: string
  }

  interface P2PProfile {
    id: number
    member_id: number
    total_trade: number
    success_rate: number
    time_average: number
    verified: boolean
    created_at: string
    updated_at: string
  }

  interface P2PProfile {
    id: number
    member_id: number
    total_trade: number
    success_rate: number
    time_average: number
    verified: boolean
    created_at: string
    updated_at: string
  }

  interface ConversationMessage extends IConversationMessage {
    email: string
    uid: string
    conversation_id: number
    content?: string
    filename?: string
    type?: string
  }

  interface Payment {
    id: number
    uid: string
    type: string
    state: PaymentState
    name_account: string
    data: Record<string, string>
    created_at: string
    updated_at: string
  }

  interface P2PComplain {
    id: number
    member_id: number
    member_uid: string
    member_email: string
    static_id: string
    supporter_uid: string
    supporter_email: string
    content: string
    state: P2PComplainState
    response: string
    created_at: string
    updated_at: string
  }

  interface DepositAddress {
    id: number
    member_id: number
    email: string
    blockchain_key: string
    address: string
    exported_at: string
    last_exported_by: string
    able_to_export: boolean
    created_at: string
    updated_at: string
  }

  interface SystemAlert {
    alert_id: number
    type: SystemAlertType
    message: string
    viewed: boolean
    metadata: object
    created_at: string
  }

  interface StatDatabase {
    metric: {
      __name__: string
      instance: string
      job: string
      server: string
      upstream_host: string
    }
    values: (string | number)[][]
  }

  interface StatVault {
    metric: {
      __name__: string
      instance: string
      job: string
      peer_id: string
    }
    values: (string | number)[][]
  }

  interface Deposit {
    id: number
    type: DepositType
    member_id: number
    currency_id: string
    uid: string
    email: string
    blockchain_key: string
    amount: number
    address: string
    from_address: string
    txid: string
    status: DepositStatus
    block_number: number
    tid: string
    credited: bool
    Spreads: Record<string, any>[]
    errors?: string[]
    collects: Collect[]
    created_at: string
    updated_at: string
    completed_at?: string
    internal_transfer?: InternalTransfer
  }

  interface Collect {
    id: number
    currency_id: string
    blockchain_key: string
    amount: number
    from_address: string
    to_address: string
    txid: string
    txout: number
    status: CollectStatus
    errors?: string[]
    created_at: string
    updated_at: string
    completed_at?: string
    deposits: Deposit[]
  }

  interface Device {
    session_id: string
    user_ip: string
    user_agent: string
    current_session: boolean
    authenticated_at: string
  }

  interface Label {
    id: number
    user_id: number
    key: string
    value: string
    scope: LabelScope
    description?: string
    created_at: string
    updated_at: string
  }

  interface Market {
    id: string
    base_unit: string
    quote_unit: string
    amount_precision: number
    price_precision: number
    total_precision: number
    min_price: number
    max_price: number
    min_amount: number
    position: number
    state: MarketState
    created_at: string
    updated_at: string
  }

  interface OperationAsset {
    id: number
    code: number
    currency_id: string
    debit: number
    credit: number
    reference_type?: string
    reference_id?: number
    created_at: string
    updated_at: string
  }

  interface OperationExpense {
    id: number
    code: number
    currency_id: string
    debit: number
    credit: number
    reference_type?: string
    reference_id?: number
    created_at: string
    updated_at: string
  }

  interface OperationLiability {
    id: number
    code: number
    member_id?: number
    currency_id: string
    debit: number
    credit: number
    reference_type?: string
    reference_id?: number
    created_at: string
    updated_at: string
  }

  interface OperationRevenue {
    id: number
    code: number
    member_id?: number
    currency_id: string
    debit: number
    credit: number
    reference_type?: string
    reference_id?: number
    created_at: string
    updated_at: string
  }

  interface Order {
    id: number
    uid: string
    email: string
    ask: string
    bid: string
    market_id: string
    member_id: number
    stop_price?: number
    filled_amount: number
    origin_amount: number
    locked: number
    origin_locked: number
    funds_received: number
    side: OrderSide
    type: OrderType
    state: OrderState
    maker_fee: number
    taker_fee: number
    trades_count: number
    created_at: string
    updated_at: string
  }

  interface OrderMetrics {
    count: number
    date: string
    action: string
  }

  interface Profile {
    id: number
    uid: number
    full_name: string
    country: string
    document_type: string
    document_number: string
    state: ProfileState
    created_at: string
    updated_at: string
  }

  export interface Asset {
    currency: string
    balance: string
    locked: string
    type: string
  }

  interface Trade extends ITrade {
    id: number
    ask: string
    bid: string
    market_id: string
    maker_uid: string
    taker_uid: string
    maker_email: string
    taker_email: string
    maker_order_id: number
    taker_order_id: number
    maker_id: number
    taker_id: number
  }

  interface TradeMetrics {
    count: number
    date: string
  }

  interface TradingFee {
    id: number
    market_id: string
    group: string
    maker: number
    taker: number
    created_at: string
    updated_at: string
  }

  interface UserDevice {
    session_id: string
    user_ip: string
    user_ip_country: string
    user_agent: string
    authenticated_at: string
  }

  interface User {
    id: number
    uid: string
    username?: string
    email: string
    level: number
    otp: boolean
    role: UserRole
    state: UserState
    note?: string
    data?: Record<string, any>
    created_at: string
    updated_at: string
    profile: Profile
    label: Label
  }

  interface Member {
    id: number
    uid: string
    username?: string
    email: string
    level: number
    otp: boolean
    role: UserRole
    state: UserState
    data?: Record<string, any>
    group: string
    created_at: string
    updated_at: string
    profile: Profile
    label: Label
  }

  interface Wallet {
    id: number
    blockchain_key: string
    name: string
    address: string
    kind: WalletKind
    gateway: WalletGateway
    balance: Record<string, string>
    uri: string
    secret: string
    max_balance: number
    status: WalletStatus
    currencies?: string[]
    created_at: string
    updated_at: string
  }

  interface WithdrawLimit {
    id: number
    group: string
    kyc_level: string
    limit: string
    filled: string
    created_at: string
    updated_at: string
  }

  interface Withdraw extends IWithdraw {
    id: number
    uid: string
    email: string
    member_id: number
    currency_id: string
    amount: number
    fee: number
    status: WithdrawStatus
    sum: number
    tid: string
    rid: string
    error?: string[]
    created_at: string
    updated_at: string
    completed_at?: string
    internal_transfer?: InternalTransfer
  }

  interface InternalTransfer {
    id: number
    tid: string
    from_member: {
      uid: string
      email?: string
      username?: string
    }
    to_member: {
      uid: string
      email?: string
      username?: string
    }
    currency: string
    amount: number
    created_at: string
    updated_at: string
  }

  interface Config {
    key: string
    value: string
    created_at?: string
    updated_at?: string
  }

  interface BlacklistAddress {
    id: number
    address: string
    created_at: string
    updated_at: string
  }

  interface WhitelistedSmartContract {
    id: number
    blockchain_key: string
    state: WhitelistedSmartContractState
    address: string
    description: string
    created_at: string
    updated_at: string
  }

  interface BotMarket {
    id: number
    exchange: BotMarketExchange
    base_unit: string
    quote_unit: string
    price_precision: number
    amount_precision: number
    spread: number
    min_price: number
    min_amount: number
    max_amount: number
    level_count: number
    readonly created_at?: string
    readonly updated_at?: string
  }

  interface Strategy {
    id: number
    kind: StrategyKind
    target_market_id: number
    state: StrategyState
    tick: number
    flush: number
    markets: number[]
    options: Record<string, any>
    created_at: string
    updated_at: string
  }

  interface StrategySourceMarket {
    strategy_id: number
    market_id: number
  }
  interface RevenueStatistics {
    revenue: string
    data: Record<string, number>[]
    recorded_at: string
  }

  interface AssetsStatistics {
    total_assets: string
    data: Record<string, number>[]
    recorded_at: string
  }
}

export {}
