<script setup lang="ts">
import '@/assets/styles/index.less'

useHead({
  title: 'ZSmartex v3 Control Panel',
})
const route = useRoute()
useI18n()

const pathName = computed(() => {
  if (route.matched.length === 0) return ''

  const path = route.matched[route.matched.length - 1].path
  const pathSplited = path.substring(1).split('/')
  if (pathSplited[pathSplited.length - 1][0] === ':') {
    pathSplited.splice(pathSplited.length - 1, 1)
  }
  return pathSplited.join('-') || 'home'
})

const layoutCls = computed(() => {
  return ['page', pathName.value].join('-')
})
</script>

<template>
  <ZLayout :class="layoutCls">
    <ZIndicator color="primary-color" :height="2" :throttle="500" />
    <NuxtLayout>
      <div class="z-layout-content">
        <NuxtPage />
      </div>
    </NuxtLayout>
    <NotificationGroup />
    <MessageGroup />
  </ZLayout>
</template>
