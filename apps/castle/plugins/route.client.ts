export default defineNuxtPlugin((/* nuxtApp */) => {
  const router = useRouter()
  const filterStore = useFilterStore()
  const menuActionStore = useMenuActionStore()

  router.beforeEach((to, from, next) => {
    if (to.path !== from.path) {
      filterStore.clearFilter()
      menuActionStore.clear()
    }

    if (to.path === '/') {
      next('/dashboard')
      return
    }

    next()
  })
})
