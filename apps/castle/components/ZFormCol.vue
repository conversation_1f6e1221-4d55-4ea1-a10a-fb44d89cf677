<script setup lang="ts">
withDefaults(defineProps<{ col: number; title?: string }>(), {
  col: 1,
})
</script>

<template>
  <div class="z-form-col" :class="[`z-form-col-${col}`]">
    <div v-if="title" class="z-form-col-title bold-text">
      {{ title }}
    </div>
    <div class="z-form-col-item">
      <slot />
    </div>
  </div>
</template>

<style lang="less">
.z-form-col {
  border-right: 1px solid @base-border-color;

  &-title {
    margin-bottom: 12px;
    padding: 0 48px;
    font-size: 20px;
  }

  &-item {
    display: flex;
    flex-wrap: wrap;
    padding: 0 48px;
  }

  &-1 {
    width: 100%;
  }

  &-2 {
    width: 50%;
  }

  &-3 {
    width: 33.33%;
  }

  &-4 {
    width: 25%;
  }

  &-5 {
    width: 20%;
  }

  &-6 {
    width: 16.66%;
  }
}
</style>
