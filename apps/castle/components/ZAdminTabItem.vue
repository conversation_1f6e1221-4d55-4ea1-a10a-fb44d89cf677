<script setup lang="ts">
defineProps<{
  title: string
}>()
</script>

<template>
  <div class="z-admin-tab-item">
    <div class="z-admin-tab-item-content">
      {{ title }}
    </div>
  </div>
</template>

<style lang="less">
.z-admin-tab-item {
  display: flex;
  margin-right: 8px;
  align-items: center;
  border-radius: 4px;
  color: rgba(@text-color, 0.8) !important;
  display: flex;
  font-weight: 400;
  justify-content: center;
  line-height: normal;
  padding: 2px 16px;
  text-align: center;
  color: @white-color;
  height: 38px;
  cursor: pointer;

  &-active {
    border: 1px solid rgba(@primary-color, 0.8);
    background-color: @primary-color;
    color: rgba(@white-color, 1) !important;
  }

  &-content {
    font-size: 16px;
    line-height: 24px;
  }
}
</style>
