<script setup lang="ts">
defineProps<{
  title?: string
  description?: string
  color?: string
}>()
</script>

<template>
  <div class="timeline-item">
    <span
      class="timeline-point"
      :class="[
        { 'timeline-point-red': color === 'red' },
        { 'timeline-point-green': color === 'green' },
      ]"
    />
    <div class="mt-[-18px]">
      <div class="timeline-item-title capitalize bold-text">
        {{ title }}
      </div>
      <p class="timeline-item-desc">
        {{ description }}
      </p>
    </div>
  </div>
</template>

<style lang="less">
.timeline {
  &-item {
    position: relative;
    padding-left: 48px;
    padding-bottom: 16px;
    // border-left: 1px solid #d4d8dd;
    &:before {
      content: '';
      position: absolute;
      top: 10px;
      bottom: 0;
      left: 0;
      width: 1px;
      background-color: #d4d8dd;
    }
    &-title {
      margin-bottom: 4px;
      font-size: 17px;
      font-weight: 600;
    }
    &-desc {
      color: rgba(@text-color, 0.8);
      font-size: 16px;
    }
  }
  &-point {
    position: relative;
    left: -54px;
    top: 0;
    z-index: 2;
    display: block;
    height: 12px;
    width: 12px;
    border-radius: 50%;
    border: 2px solid #5a8dee;
  }
}
</style>
