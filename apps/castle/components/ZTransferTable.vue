<script setup lang="ts">
import type { ZTableColumn } from '@zsmartex/components/types'
import { FindBy } from '~/mixins'

const props = defineProps<{
  loading?: boolean
  disabledLeft?: boolean
  disabledRight?: boolean
  leftTitle: string
  leftColumns: ZTableColumn[]
  leftData: any[]
  rightTitle: string
  rightColumns: ZTableColumn[]
  rightData: any[]
  searchKeys: string[]
}>()

const emit = defineEmits(['transferLeft', 'transferRight', 'clickLeft', 'clickRight'])

const leftSearch = ref('')
const rightSearch = ref('')

const leftDataFilter = computed(() => {
  return FindBy(props.leftData, props.searchKeys, leftSearch.value)
})

const rightDataFilter = computed(() => {
  return FindBy(props.rightData, props.searchKeys, rightSearch.value)
})

function TransferLeft() {
  emit('transferLeft')
}

function TransferRight() {
  emit('transferRight')
}
</script>

<template>
  <div class="flex items-center z-transfer-table mt-6">
    <div class="flex-1">
      <div class="bold-text text-2xl mb-4">
        {{ leftTitle }}
      </div>
      <div class="z-transfer-table-box shadow-md">
        <div class="p-3 bg-white">
          <ZInput v-model="leftSearch" placeholder="Search" class="mb-1 w-full">
            <template #prefix>
              <ZIcon type="search" class="mr-2" />
            </template>
          </ZInput>
        </div>
        <ZTablePro
          :data-source="leftDataFilter"
          :columns="leftColumns"
          :hover="true"
          :scroll="true"
          @click="(item: any) => emit('clickLeft', item)"
        >
          <template
            v-for="column of leftColumns"
            #[column.key]="{ item }"
          >
            <slot
              v-if="$slots[column.key] && column.scopedSlots"
              :name="column.key"
              :item="item"
              :column="column"
            />
            <slot
              v-else-if="column.scopedSlots"
              :name="`left-${column.key}`"
              :item="item"
              :column="column"
            />
          </template>
        </ZTablePro>
      </div>
    </div>
    <div class="m-3">
      <ZButton :disabled="disabledLeft" class="mb-2" @click="TransferLeft">
        <ZIcon class="block" type="arrow" />
      </ZButton>
      <div class="transform rotate-180">
        <ZButton :disabled="disabledRight" @click="TransferRight">
          <ZIcon class="block" type="arrow" />
        </ZButton>
      </div>
    </div>
    <div class="flex-1">
      <div class="bold-text text-2xl mb-4">
        {{ rightTitle }}
      </div>
      <div class="z-transfer-table-box shadow-md">
        <div class="p-3 bg-white">
          <ZInput v-model="rightSearch" placeholder="Search" class="w-full mb-1">
            <template #prefix>
              <ZIcon type="search" class="mr-2" />
            </template>
          </ZInput>
        </div>
        <ZTablePro
          :data-source="rightDataFilter"
          :columns="rightColumns"
          :hover="true"
          :scroll="true"
          @click="(item: any) => emit('clickRight', item)"
        >
          <template
            v-for="column of rightColumns"
            #[column.key]="{ item }"
          >
            <slot
              v-if="$slots[column.key] && column.scopedSlots"
              :name="column.key"
              :item="item"
              :column="column"
            />
            <slot
              v-else-if="column.scopedSlots"
              :name="`right-${column.key}`"
              :item="item"
              :column="column"
            />
          </template>
        </ZTablePro>
      </div>
    </div>
  </div>
</template>

<style lang="less">
.z-transfer-table {
  &-box {
    border-radius: 4px;
    overflow: hidden;
  }

  .z-button {
    width: 24px;
    height: 24px;
    padding: 0;
  }

  .z-table-pro {
    height: 300px !important;

    &-head {
      padding: 0;
    }
  }
}
</style>
