<script setup lang="ts">
const props = defineProps<{
  title?: string
  to: string
}>()

const route = useRoute()

const isActive = computed(() => {
  return route.path.startsWith(props.to)
})

const fixTo = () => {
  if (props.to === route.path) return route.fullPath
  return props.to
}
</script>

<template>
  <NuxtLink :to="fixTo()" class="z-admin-menu-item" :class="[{ 'z-admin-menu-item-active': isActive }]">
    <div class="z-admin-menu-item-link text-base">
      <div class="">
        {{ title }}
      </div>
    </div>
  </NuxtLink>
</template>

<style lang="less">
.z-admin-menu-item {
  &-link {
    display: flex;
    align-items: center;
  }

  &.z-admin-menu-item-active {
    background-color: rgba(@primary-color, 0.7) !important;
    color: @white-color !important;
    border-radius: 4px;
  }
}
</style>
