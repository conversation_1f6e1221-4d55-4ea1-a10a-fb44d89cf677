<script setup lang="ts">
const props = defineProps<{
  modelValue: Record<string, any>
}>()
const emit = defineEmits(['update:modelValue', 'click'])

const json = useVModel(props, 'modelValue', emit)
const value = ref('')

function AddProperty() {
  if (json.value[value.value] !== undefined || value.value === '') {
    value.value = ''
    return
  }
  json.value[value.value] = ''
  value.value = ''
}

function DeleteProperty(key: string) {
  delete json.value[key]
}
</script>

<template>
  <ZCard>
    <div class="z-properties">
      <ZFormCol :col="2">
        <div class="z-properties-title">
          <span class="bold-text text-lg">Properties</span>
          <ZPopover trigger="click" :placement="Placement.BottomRight">
            <div class="flex items-center cursor-pointer">
              <ZIcon type="plus" class="text-sm h-5 rounded-full block" />
              <span class="ml-2 text-sm">Add property</span>
            </div>
            <template #content>
              <ZFormRow label="New property" :overflow="false">
                <div class="flex items-center">
                  <ZInput v-model="value" class="w-48" />
                  <ZIcon type="plus" class="cursor-pointer ml-4 text-sm h-5 rounded-full block" @click="AddProperty" />
                </div>
              </ZFormRow>
            </template>
          </ZPopover>
        </div>
        <div class="mt-8">
          <ZFormRow v-for="(_, key) in json" :key="key" :overflow="false">
            <div class="flex items-center">
              <ZAdminInput v-model="json[key]" class="flex-1" :label="key" />
              <ZIcon type="minus" class="cursor-pointer ml-4 text-sm h-5 rounded-full block" @click="DeleteProperty(key)" />
            </div>
          </ZFormRow>
        </div>
      </ZFormCol>
      <ZFormCol :col="2" class="pt-12">
        <div class="z-properties-json rounded px-4 py-8 mb-4">
          <label>JSON</label>
          <div class="text-sm">
            {
            <div v-for="(item, key, index) in json" :key="key" class="pl-4">
              {{ index + 1 === Object.keys(json).length ? `"${key}": ${item ? `"${item}"` : 'null'}` : `"${key}": ${item ? `"${item}"` : 'null'},` }}
            </div>
            }
          </div>
        </div>
      </ZFormCol>
    </div>
  </ZCard>
</template>

<style lang="less">
.z-properties {
  display: flex;
  margin: 0 -48px;

  &-title {
    display: flex;
    justify-content: space-between;
  }

  &-json {
    position: relative;
    border: 1px solid @base-border-color;
    border-radius: 4px;

    label {
      position: absolute;
      top: -10px;
      font-size: 14px;
      font-weight: bold;
      background-color: white;
    }
  }

  .z-popover {
    .z-form-row {
      margin: 0;
    }
  }

  .z-button {
    height: 36px;
  }

  .z-icon {
    padding: 1px;
    height: 16px;
    border: 1px solid @gray-color;
    color: @gray-color;
    font-size: 12px;
  }

  .z-form-col-item {
    display: block;
  }
}
</style>
