<script setup lang="ts">
const route = useRoute()
const userStore = useUserStore()

interface MenuItem {
  title: string
  link: string
  subItems?: MenuItem[]
}

const config = useRuntimeConfig()

const exchangeMenuItem = computed(() => {
  const item: MenuItem = {
    title: 'Exchange',
    link: '/exchange',
    subItems: [
      {
        title: 'Currencies',
        link: '/currencies',
      },
      {
        title: 'Markets',
        link: '/markets',
      },
      {
        title: 'Orders',
        link: '/orders',
      },
      {
        title: 'Trades',
        link: '/trades',
      },
    ],
  }

  if (userStore.role === UserRole.SuperAdmin) {
    item.subItems!.push({
      title: 'Tools',
      link: '/tools',
      subItems: [
        {
          title: 'Deposit Addresses',
          link: '/deposit_addresses',
        },
      ],
    })
  }

  if (config.public.p2p) {
    item.subItems!.push({
      title: 'P2P',
      link: '/p2p',
      subItems: [
        {
          title: 'Advertisements',
          link: '/advertisements',
        },
        {
          title: 'P2P Trades',
          link: '/trades',
        },
        {
          title: 'P2P Complains',
          link: '/complains',
        },
        {
          title: 'Payments',
          link: '/payments',
        },
      ],
    })
  }

  return item
})

const settingsMenuItem = computed(() => {
  const item: MenuItem = {
    title: 'Settings',
    link: '/settings',
    subItems: [
      {
        title: 'Blockchains',
        link: '/blockchains',
      },
      {
        title: 'Wallets',
        link: '/wallets',
      },
      {
        title: 'Fees Schedule',
        link: '/fees_schedule',
      },
      {
        title: 'Withdraw Limits',
        link: '/withdraw_limits',
      },
      {
        title: 'Blacklist Address',
        link: '/blacklist_address',
      },
      {
        title: 'Whitelisted Smart Contracts',
        link: '/whitelisted_smart_contracts',
      },
      {
        title: 'Banners',
        link: '/banners',
      },
      {
        title: 'Rate Limit',
        link: '/rate_limits',
      },
      {
        title: 'UI Configurations',
        link: '/ui_configurations',
      },
    ],
  }

  if (userStore.role === UserRole.SuperAdmin || userStore.role === UserRole.Admin) {
    item.subItems!.push({
      title: 'Backup Monitoring',
      link: '/backup_monitoring',
    })
  }

  return item
})

const menuItems: MenuItem[] = [
  {
    title: 'Users',
    link: '/users',
    subItems: [
      {
        title: 'User Directory',
        link: '/user_directory',
      },
      {
        title: 'Operators',
        link: '/operators',
      },
      {
        title: 'Pending Applications',
        link: '/pending_applications',
      },
    ],
  },
  exchangeMenuItem.value,
  {
    title: 'Accountings',
    link: '/accountings',
    subItems: [
      {
        title: 'Deposits',
        link: '/deposits',
      },
      {
        title: 'Withdraws',
        link: '/withdraws',
      },
      {
        title: 'Collects',
        link: '/collects',
      },
      {
        title: 'Operations',
        link: '/operations',
      },
    ],
  },
  settingsMenuItem.value,
  // {
  //   title: 'Bot',
  //   link: '/bot',
  //   subItems: [
  //     {
  //       title: 'Config',
  //       link: '/config',
  //     },
  //     {
  //       title: 'Markets',
  //       link: '/markets',
  //     },
  //     {
  //       title: 'Strategies',
  //       link: '/strategies',
  //     },
  //   ],
  // },
]

function isDefaultOpenItem(item: MenuItem, parentLink?: string) {
  const { link } = item
  const { path } = route

  let url = link
  if (parentLink) {
    url = parentLink + url
  }

  return path.startsWith(url)
}

function CountItem(item: MenuItem) {
  let result = 0
  if (item.subItems) {
    for (const sub of item.subItems) {
      result++

      if (sub.subItems) result += sub.subItems.length
    }
  }

  return result
}
</script>

<template>
  <div class="z-admin-menu flex flex-col">
    <div class="z-admin-menu-header flex">
      <NuxtLink to="/dashboard" class="z-admin-menu-header-logo">
        <img src="@/assets/img/rectangular_logo.png">
      </Nuxtlink>
    </div>
    <div class="z-admin-menu-divider mt-0" />
    <div class="z-admin-menu-body flex-1">
      <ZAdminMenuItem v-for="(item, index) in menuItems" :key="index" :title="item.title" :height="50 * CountItem(item)" :default-visible="isDefaultOpenItem(item)" class="pt-[2px] cursor-pointer">
        <template v-for="(sub, key) in item.subItems" :key="key">
          <div v-if="sub.subItems">
            <ZAdminMenuItem :title="sub.title" :height="50 * CountItem(sub)" :default-visible="isDefaultOpenItem(sub, item.link)" class="pt-[2px] cursor-pointer">
              <template v-for="(s, k) in sub.subItems" :key="k">
                <ZAdminMenuSubItem :title="s.title" :to="`${item.link}${sub.link}${s.link}`" />
              </template>
            </ZAdminMenuItem>
          </div>
          <ZAdminMenuSubItem v-else :title="sub.title" :to="`${item.link}${sub.link}`" />
        </template>
      </ZAdminMenuItem>
    </div>
    <div class="z-admin-menu-footer bold-text" @click="userStore.Logout()">
      LOGOUT
    </div>
  </div>
</template>

<style lang="less">
.z-admin-menu {
  height: 100vh;
  font-size: 14px;
  width: 280px;
  background-color: #182535;
  color: @white-color;
  overflow-x: auto;
  user-select: none;

  &-footer {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 12px;
    width: 100%;
    height: 64px;
    cursor: pointer;
    transition: all 0.3s;
    font-size: 16px;

    &:hover {
      background-color: @primary-color
    }
  }

  &-header {
    padding: 0px 24px;
    height: 64px;

    &-logo {
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;

      img {
        height: 45px;
      }
    }
  }

  &-divider {
    margin-bottom: 10px;
  }

  &-body {
    overflow: overlay;
    padding: 2px 10px;
  }

  &-wrap {
    background-color: #212e3d;
  }
}
</style>
