<script setup lang="ts">
import type { Ref } from 'vue'
import { copyToClipboard } from '@zsmartex/utils'
import type { ZAdminFormColumn, ZAdminFormField } from '~/types'

const props = defineProps<{
  loading?: boolean
  pending?: boolean
  columns: ZAdminFormColumn[]
  hiddenSubmit?: boolean
  isNewUpdate?: boolean
}>()

const emits = defineEmits(['submit'])

const values: Ref<Record<string, any>> = ref({})
const oldValue: Record<string, any> = ref({})

onMounted(() => {
  const columns = props.columns
  columns.forEach((column) => {
    column.fields.forEach((field) => {
      if (field.value || Number.isInteger(field.value) || typeof field.value == 'boolean') {
        if (field.type === 'switch') {
          values.value[field.key] = field.value
          oldValue.value[field.key] = field.value
        } else {
          values.value[field.key] = String(field.value)
          oldValue.value[field.key] = String(field.value)
        }
      } else {
        if (field.type === 'switch') {
          values.value[field.key] = false
          oldValue.value[field.key] = false
        } else {
          values.value[field.key] = ''
          oldValue.value[field.key] = ''
        }
      }
    })
  })
})

watch(() => props.columns, () => {
  const columns = props.columns
  columns.forEach((column) => {
    column.fields.forEach((field) => {
      if (field.value !== values.value[field.key]) {
        if (field.value || Number.isInteger(field.value) || typeof field.value == 'boolean') {
          if (field.type === 'switch') {
            values.value[field.key] = field.value
            oldValue.value[field.key] = field.value
          } else {
            values.value[field.key] = String(field.value)
            oldValue.value[field.key] = String(field.value)
          }
        } else {
          if (field.type === 'switch') {
            if (oldValue.value[field.key] !== false) {
              oldValue.value[field.key] = false
              values.value[field.key] = false
            }
          } else {
            if (oldValue.value[field.key] !== '') {
              oldValue.value[field.key] = ''
              values.value[field.key] = ''
            }
          }
        }
      }
    })
  })

  checkValidate()
})

const isError = ref(false)

watch(values.value, () => {
  checkValidate()
})

function checkValidate() {
  const columns = props.columns
  for (const column of columns) {
    for (const field of column.fields) {
      if (field.validate) {
        const value = values.value[field.key]
        if (field.validate.some((v) => {
          return v(value)
        })) {
          isError.value = true
          return
        } else {
          isError.value = false
        }
      }
    }
  }
}

function onSubmit() {
  emits('submit', unref(values))
}

function onChange(item: ZAdminFormField, value: any) {
  if (!item.onChange) return

  item.onChange(value)
}

function onInput(item: ZAdminFormField, e: Event) {
  if (!item.onInput) return

  item.onInput((e.target as HTMLInputElement).value)
}

const disabledButton = computed(() => {
  if (isError.value) return isError.value
  for (const key in values.value) {
    if (values.value[key] !== oldValue.value[key]) return false
  }

  if (props.isNewUpdate) return false

  return true
})

function copy(value: string) {
  Message.success({
    message: $t('success'),
  })

  copyToClipboard(value)
}
</script>

<template>
  <div class="z-admin-form pt-4">
    <slot name="header" />
    <ZFormContainer>
      <div v-if="pending" class="min-h-[200px]">
        <ZLoading />
      </div>
      <ZFormCol v-for="(column, index) in columns" v-else :key="index" :col="columns.length">
        <ZFormRow v-for="(item, i) in column.fields" :key="i" :overflow="false" :class="item.class" :content-class="item.type === 'switch' ? 'flex items-center' : ''">
          <slot
            v-if="item.scopedSlots"
            :name="item.key"
            :item="item"
            :columns="columns"
            :values="values"
          />
          <ZAdminInput v-else-if="item.type === 'input'" v-model="values[item.key]" :type="item.typeInput" :auto-focus="item.autoFocus" :validate="item.validate" :disabled="item.disabled" :label="item.label" @input="onInput(item, $event)" />
          <ZAdminInput v-else-if="item.type === 'input-copy'" v-model="values[item.key]" :type="item.typeInput" :auto-focus="item.autoFocus" :validate="item.validate" :disabled="item.disabled" :label="item.label" @input="onInput(item, $event)">
            <template #suffix>
              <div class="flex justify-center items-center mr-1">
                <ZIconCopyAltFilled class="cursor-pointer ml-1" @click="copy(values[item.key])" />
              </div>
            </template>
          </ZAdminInput>
          <div v-else-if="item.type === 'switch'" class="flex items-center">
            <span :class="`mr-2 ${item.labelStyle}`">{{ item.label }}</span>
            <ZSwitch v-model="values[item.key]" size="medium" @change="(v: boolean) => onChange(item, v)" />
          </div>
          <div v-else-if="item.type === 'text'">
            <span>{{ item.label }}:</span>
            <div>{{ item.replaceFunc ? item.replaceFunc(item.value as string) : item.value }}</div>
          </div>
          <div v-else-if="item.type === 'textarea'" class="flex flex-col">
            <span class="mb-2">{{ item.label }}:</span>
            <textarea v-model="values[item.key]" rows="6" :disabled="item.disabled" />
          </div>
          <ZSelect
            v-else-if="item.type === 'select' && item.dataSource && item.columns && item.findBy && item.valueKey && item.labelKey"
            v-model="values[item.key]"
            :data-source="item.dataSource"
            :search="item.search"
            :columns="item.columns"
            :find-by="item.findBy"
            :label="item.label"
            :value-key="item.valueKey"
            :label-key="item.labelKey"
            :scroll="item.scroll"
            :disabled="item.disabled"
            :replace-func="item.replaceFunc"
            @change="(v: string) => onChange(item, v)"
          />
          <ZCheckbox v-else-if="item.type === ZAdminFormFieldType.Checkbox" v-model="values[item.key]" />
        </ZFormRow>
      </ZFormCol>
    </ZFormContainer>
    <div v-if="!hiddenSubmit" class="z-admin-form-button flex justify-end">
      <ZButton :loading="loading" type="primary" class="h-8! leading-8!" :disabled="disabledButton" @click="onSubmit">
        Submit
      </ZButton>
    </div>
  </div>
</template>

<style lang="less">
.z-admin-form {
  &-button {
    margin-top: 36px;
  }

  .z-dropdown {
    &-trigger {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 12px;
      width: 100%;
      height: 40px;
      border: 1px solid rgba(33, 47, 79, 0.1);
      border-radius: 4px;
      transition: all 0.3s;
      &:hover {
        border: 1px solid @primary-color;
      }
    }

    &-triggered {
      border: 1px solid @primary-color;
      box-shadow: 0 0 0 2px rgba(@primary-color, 0.2);
    }

    &-overlay {
      width: 100%;
      background-color: #fff;
      box-shadow: 0 3px 6px -4px #0000001f, 0 6px 16px #00000014, 0 9px 28px 8px #0000000d;
    }
  }

  textarea {
    padding: 12px;
    font-size: 14px;
    font-family: 'URWDIN-Regular';
    outline: none;
    border-radius: 4px;
    border: 1px solid @base-border-color;
    transition: 0.3s all;

    &:hover {
      border: 1px solid @primary-color;
    }

    &:focus {
      border: 1px solid @primary-color;
      box-shadow: 0 0 0 2px rgba(@primary-color, 0.2);
    }
  }

  .z-form-container {
    .z-form-col:first-child {
      .z-form-col-item {
        padding: 0 48px 0 0;
      }
    }

    .z-form-col:last-child {
      border: none;

      .z-form-col-item {
        padding: 0 0 0 48px;
      }
    }
  }
}
</style>
