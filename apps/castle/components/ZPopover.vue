<script setup lang="ts">
import { Placement } from '@zsmartex/types'

const props = withDefaults(defineProps<{
  placement?: Placement
  trigger?: 'hover' | 'click'
  modelValue: boolean
  isOperator?: boolean
}>(), {
  placement: Placement.TopLeft,
  trigger: 'hover',
  modelValue: false,
  isOperator: true,
})

const visible = ref(false)

const instance = getCurrentInstance()
const overlay = templateRef<HTMLElement>('overlay')

const overlayRect = computed(() => {
  return useElementBounding(overlay)
})

const placement = ref(props.placement)

onMounted(() => {
  onClickOutside(instance?.proxy?.$el, () => {
    visible.value = false
  })
})

function onTriggerClick() {
  if (props.trigger === 'click') visible.value = true
}

function onTriggerHoverOver() {
  if (props.trigger === 'hover') visible.value = true
}

function onTriggerHoverLeave() {
  if (props.trigger === 'hover') visible.value = false
}

watch(visible, async (visible) => {
  if (!visible) return

  await nextTick()

  if (overlayRect.value.y.value <= 12 && props.isOperator) {
    placement.value = Placement.BottomRight
  }
})
</script>

<template>
  <div class="z-popover">
    <div @mouseover="onTriggerHoverOver" @mouseleave="onTriggerHoverLeave" @click="onTriggerClick">
      <slot />
    </div>
    <transition name="z-popover-show">
      <div
        v-if="visible"
        ref="overlay"
        class="z-popover-overlay"
        :class="[`z-popover-overlay-${placement}`]"
      >
        <div v-if="$slots.title" class="z-popover-overlay-title bold-text">
          <slot name="title" />
        </div>
        <div class="z-popover-overlay-content">
          <slot name="content" />
        </div>
      </div>
    </transition>
  </div>
</template>

<style lang="less">
.z-popover {
  position: relative;
  display: inline-block;

  &-overlay {
    position: absolute;
    background-color: white;
    box-shadow: 0 3px 6px -4px #0000001f, 0 6px 16px #00000014, 0 9px 28px 8px #0000000d;
    z-index: 1;

    &:after {
      content: '';
      position: absolute;
      width: 8px;
      height: 8px;
      background-color: #fff;
      transform: rotate(45deg);
      z-index: 2;
    }

    &-title {
      padding: 4px 16px;
      min-width: 260px;
      min-height: 32px;
      font-size: 14px;
      color: @text-color;
      background-color: #fff;
      border-bottom: 1px solid @base-border-color;
    }

    &-content {
      padding: 12px 16px;
      min-width: 260px;
      font-size: 14px;
    }

    &-topLeft {
      bottom: 100% + 36px;
      left: 0;

      &:after {
        content: '';
        left: 12px;
        bottom: -4px;
      }
    }

    &-topCenter {
      bottom: 100% + 36px;
      left: 0;
      transform: translate(-50%, 0) !important;
      left: 50%;

      &:after {
        content: '';
        transform: translateX(-50%) rotate(45deg);
        left: calc(50% + 3px);
        bottom: -4px;
      }
    }

    &-topRight {
      bottom: 100% + 36px;
      right: 0;

      &:after {
        content: '';
        right: 12px;
        bottom: -4px;
      }
    }

    &-leftTop {
      top: 0;
      right: 100% + 36px;

      &:after {
        content: '';
        right: -4px;
        top: 8px;
      }
    }

    &-left {
      top: 50%;
      transform: translateY(-50%) !important;
      right: 100% + 36px;

      &:after {
        content: '';
        right: -4px;
        top: 50%;
        transform: translateY(-50%) rotate(45deg);
      }
    }

    &-leftBottom {
      bottom: 0;
      right: 100% + 36px;

      &:after {
        content: '';
        right: -4px;
        bottom: 8px;
      }
    }

    &-bottomLeft {
      top: 100% + 36px;
      left: 0;

      &:after {
        content: '';
        left: 12px;
        top: -4px;
      }
    }

    &-bottom {
      top: 100% + 36px;
      left: 50%;
      transform: translate(-50%, 0) !important;

      &:after {
        content: '';
        left: 50%;
        transform: translateX(-50%) rotate(45deg);
        top: -4px;
      }
    }

    &-bottomRight {
      top: 100% + 36px;
      right: 0;

      &:after {
        content: '';
        right: 12px;
        top: -4px;
      }
    }

    &-rightTop {
      top: 0;
      left: 100% + 36px;

      &:after {
        content: '';
        left: -4px;
        top: 8px;
      }
    }

    &-right {
      top: 50%;
      transform: translateY(-50%) !important;
      left: 100% + 36px;

      &:after {
        content: '';
        left: -4px;
        top: 50%;
        transform: translateY(-50%) rotate(45deg);
      }
    }

    &-rightBottom {
      bottom: 0;
      left: 100% + 36px;

      &:after {
        content: '';
        left: -4px;
        bottom: 8px;
      }
    }
  }

  &-show {
    &-enter-active, &-leave-active {
      transition: all 0.2s;
    }

    &-enter-from, &-leave-to {
      opacity: 0;
      transform: scale(0.7)
    }
  }
}
</style>
