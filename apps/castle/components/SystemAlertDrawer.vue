<script setup lang="ts">
const props = defineProps<{
  modelValue: boolean
}>()

const emit = defineEmits(['update:modelValue'])
const visible = useVModel(props, 'modelValue', emit)
const adminStore = useAdminStore()
</script>

<template>
  <ZDrawer v-model="visible" title="System Alerts" class="system-alert-drawer">
    <SystemAlertItem v-for="(alert, index) in adminStore.systemAlerts" :key="index" :alert="alert" />
  </ZDrawer>
</template>

<style lang="less">
.system-alert {
  &-drawer {
    .z-drawer-content {
      padding: 0;
    }
  }

  &-item {
    padding: 10px 0;
    border-bottom: 1px solid #e5e7eb;
    background-color: rgba(0, 0, 0, 0.1);
    font-size: 14px;
    color: #4b5563;
    line-height: 1.5;
    cursor: pointer;
    padding: 12px;

    &-viewed {
      background-color: white;
    }

    &-type {
    }

    &-time {
      font-size: 12px;
      margin-right: 24px;
    }

    .caret {
      position: absolute;
      top: 50%;
      right: 0;
      height: 18px;
      color: #9ca3af;
      transition: transform 0.2s ease-in-out;
      transform: rotate(0deg) translateY(-60%);
    }
  }
}
</style>
