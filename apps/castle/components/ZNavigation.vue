<script setup lang='ts'>
const route = useRoute()

const breadcrumb = computed(() => {
  const result = Array<{ name: string; to?: string; path?: string }>()

  route.path.split('/').slice(1, 4).forEach((item, index) => {
    if (index === 2 && item !== 'create' && item !== 'update' && result[1].name === 'Currencies') {
      result.push({
        name: item.toUpperCase(),
        to: `/${item}`,
      })
    } else {
      item = item.replace(/_[a-z]/g, (match) => {
        return ` ${match.slice(1).toUpperCase()}`
      })
      item = item[0].toUpperCase() + item.slice(1)

      result.push({
        name: item,
        path: `${route.path.split('/').slice(0, index + 2).join('/')}`,
      })
    }
  })

  return result
})
</script>

<template>
  <div v-for="(item, index) in breadcrumb" :key="index">
    <template v-if="index === (breadcrumb.length - 1)">
      <div class="bread-item bold-text">
        {{ item.name }}
      </div>
    </template>
    <template v-else-if="index === 0">
      <div class="bread-item bold-text mr-1">
        {{ `${item.name} /` }}
      </div>
    </template>
    <template v-else>
      <div class="mr-2">
        <NuxtLink class="bread-item bold-text cursor-pointer hover:text-blue-400" :to="item.path">
          {{ item.name }}
        </NuxtLink>
        /
      </div>
    </template>
  </div>
</template>

<style lang="less">
  .bread-item {
    color: rgba(@text-color, 0.4);
    &-active {
      color: rgba(@text-color, 0.8);
      font-size: 22px;
    }
  }
</style>
