<script setup lang="ts">
import colors from '~/colors'

const props = withDefaults(defineProps<{
  color?: keyof typeof colors
  height?: number
  throttle?: number
}>(), {
  color: 'primary-color',
  height: 2,
  throttle: 500,
})

const indicatorStore = useIndicator()

function StartLoading() {
  indicatorStore.start(props.throttle)
}

function StopLoading() {
  indicatorStore.stop()
}

const nuxtApp = useNuxtApp()
nuxtApp.hook('page:start', StartLoading)
nuxtApp.hook('page:finish', StopLoading)
onBeforeUnmount(() => indicatorStore.clear())
</script>

<template>
  <div
    class="z-indicator"
    :style="indicatorStore.progress ? `
    width: ${indicatorStore.progress}%;
    height: ${height}px;
    background-color: ${colors[color]};
    opacity: ${indicatorStore.loading ? 1 : 0};
    backgroundSize: ${(100 / indicatorStore.progress) * 100}% auto;
  ` : ''"
  />
</template>

<style lang="less">
.z-indicator {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 999;
  transition: width 0.2s, height 0.4s, opacity 0.4s;
}
</style>
