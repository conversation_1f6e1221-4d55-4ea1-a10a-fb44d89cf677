<script setup lang="ts">
withDefaults(defineProps<{
  title: string
  value: string | number
  percentage: number
}>(), {
  title: '',
  value: 0,
  percentage: 0,
})
</script>

<template>
  <div class="z-admin-card">
    <div class="z-admin-card-head">
      <span class="text-sm">{{ title }}</span>
      <ZIconTrendUpFilled v-if="percentage >= 0" />
      <ZIconTrendDownFilled v-if="percentage < 0" />
    </div>
    <div class="z-admin-card-count">
      <div class="mr-1 font-bold text-xl">
        {{ value }}
      </div>
      <div v-if="percentage >= 0" class="text-green-400">
        + {{ percentage }} %
      </div>
      <div v-else class="text-red-400">
        {{ percentage }} %
      </div>
    </div>
  </div>
</template>

<style lang="less">
.z-admin-card {
  color: #677788;
  border-radius: 4px;
  background-color: white;
  box-shadow: 0 2px 14px rgb(38 60 85 / 16%);
  padding: 22px;
  border-color: @base-border-color;
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  &-count {
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
  }

  &-head {
    display: flex;
    justify-content: space-between;

    svg {
      width: 24px;
      height: 24px;
    }

    .trend-up-filled {
      .cls-1 {
        fill: @up-color;
      }
    }

    .trend-down-filled {
      .cls-1 {
        fill: @down-color;
      }
    }
  }
}
</style>
