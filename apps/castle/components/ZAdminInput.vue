<script setup lang="ts">
import { InputType } from '@zsmartex/components/types'
import { RandomString } from '~/mixins'

const props = withDefaults(defineProps<{
  type?: InputType
  placeholder?: string
  label?: string
  name?: string
  maxLength?: number
  disabled?: boolean
  error?: string
  class?: string
  validate?: ((value: string) => any)[]
  modelValue: string
  autoFocus?: boolean
}>(), {
  type: InputType.Text,
  validate: () => ([]),
  modelValue: '',
  autoFocus: false,
})

const emit = defineEmits(['update:modelValue', 'focus', 'blur', 'input'])

const instance = getCurrentInstance()
const value = useVModel(props, 'modelValue', emit)
const input = templateRef<HTMLInputElement>('input')

const focused = ref(false)
const randomName = RandomString()

const inputType = computed(() => {
  if (props.type === 'password') {
    return 'password'
  } else {
    return 'text'
  }
})

const errorText = computed(() => {
  if (props.error) {
    return props.error
  } else {
    for (const v of props.validate) {
      if (v(value.value) && value.value.length > 0) {
        return v(value.value)
      }
    }
  }
})

watch(value, () => {
  if (!value.value.length) return
  if (props.type === 'number') {
    if (/\D/.test(value.value)) {
      value.value = value.value.replace(/\D/g, '')
    }
  }
  if (props.maxLength && value.value.length > props.maxLength) {
    value.value = value.value.slice(0, props.maxLength)
  }
})

onMounted(() => {
  if (props.autoFocus) {
    input.value?.focus()
  }
})

function focus() {
  input.value.focus()
}

function blur() {
  input.value.blur()
}

function onInput(e: Event) {
  const target = (e.target as HTMLInputElement)
  let inputValue = target.value

  if (props.type === InputType.Number) {
    if (/\D/.test(inputValue)) {
      inputValue = inputValue.replace(/\D/g, '')
    }
  } else if (props.type === InputType.Decimal) {
    if (/[^0123456789.]/.test(inputValue)) {
      inputValue = inputValue.replace(/[^0123456789.]/g, '')
    }

    const numberDot = inputValue.match(/\./g)
    if (numberDot && numberDot.length > 1) {
      if (inputValue[inputValue.length - 1] === '.') {
        inputValue = inputValue.slice(0, inputValue.length - 1)
      }
    }
  }
  if (props.maxLength && inputValue.length > props.maxLength) {
    inputValue = inputValue.slice(0, props.maxLength)
  }

  value.value = inputValue
  target.value = inputValue

  if (instance?.proxy) {
    instance.proxy.$forceUpdate()
  }

  emit('input', e as InputEvent)
}

function onFocus(e: FocusEvent) {
  emit('focus', e)
  focused.value = true
}

function onBlur(e: FocusEvent) {
  emit('blur', e)
  focused.value = false
}

defineExpose({
  focus,
  blur,
})
</script>

<template>
  <div
    class="relative"
    :class="[$props.class]"
  >
    <div
      class="z-admin-input" :class="[
        {
          'z-admin-input-focused': focused,
          'z-admin-input-disabled': disabled,
          'z-admin-input-error': errorText,
        },
      ]"
      :disabled="disabled"
    >
      <div v-if="$slots.prefix" class="z-admin-input-prefix">
        <slot name="prefix" />
      </div>
      <input
        :id="randomName"
        ref="input"
        v-model="value"
        :type="inputType"
        :autocomplete="randomName"
        :name="randomName"
        :max-length="maxLength"
        placeholder=" "
        @focus="onFocus"
        @blur="onBlur"
        @input="onInput"
      >
      <label
        :for="randomName"
        @focus="onFocus"
      >{{ label }}</label>
      <div v-if="$slots.suffix" class="z-admin-input-suffix">
        <slot name="suffix" />
      </div>
    </div>
    <div v-if="errorText" class="z-admin-input-error-container">
      <transition name="z-admin-input-error">
        <div v-if="errorText" class="z-admin-input-error-content">
          {{ errorText }}
        </div>
      </transition>
    </div>
  </div>
</template>

<style lang="less">
.z-admin-input {
  position: relative;
  display: flex;
  align-items: center;
  height: 40px;
  line-height: 35px;
  transition: all 0.3s ease-in-out;
  border: 1px solid @base-border-color;
  border-radius: 4px;

  svg {
    width: 24px;
    height: 24px;
    fill: @gray-color;
  }

  label {
    cursor: text;
    position: absolute;
    left: 12px;
    transform: translateY(0%);
    transition: 0.3s;
    padding: 0 2px;
    color: rgba(@gray-color, 0.8);
  }

  input:not(:placeholder-shown) ~ label {
    font-size: 14px;
    transform: translateY(-21px);
    transition: 0.3s;
    background-color: #fff;
    line-height: 10px;
    color: @text-color;
  }

  &-focused {
    box-shadow: 0 0 0 1px rgba(@primary-color, 0.65);
    label {
      font-size: 14px;
      transform: translateY(-21px);
      transition: 0.3s;
      background-color: #fff;
      line-height: 10px;
      color: @text-color;
    }
  }

  &-disabled {
    cursor: not-allowed;

    input {
      cursor: not-allowed;
      pointer-events: none;
    }
  }

  input {
    background-color: transparent;
    padding: 0 12px;
    width: 100%;
    height: 100%;
    color: inherit;

    &::placeholder {
      color: @placeholder-color;
    }
  }

  &-error {
    box-shadow: 0 0 0 1px rgba(@down-color, 0.65);

    &-container {
      position: absolute;
      height: 28px;
      width: 100%;
      overflow: hidden;
    }

    &-content {
      position: absolute;
      width: 100%;
      height: 20px;
      margin-top: 4px;
      padding-left: 4px;
      line-height: 20px;
      color: @down-color;
      top: 0;
      left: 0;
      opacity: 1;
      transition: all 0.3s;
    }

    &-enter,
    &-leave-active {
      opacity: 0;
      left: 0;
      top: -20px;
    }

    .z-auth-input-container {
      border-color: @down-color !important;
    }
  }
}
</style>
