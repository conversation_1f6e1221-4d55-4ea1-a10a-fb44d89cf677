<script setup lang="ts">
const props = withDefaults(defineProps<{
  height: number
  title: string
  defaultVisible: boolean
}>(), {
  defaultVisible: false,
})

const visible = ref(false)

visible.value = props.defaultVisible
</script>

<template>
  <div>
    <div class="z-admin-menu-item mt-4" :class="[{ 'z-admin-menu-item-selected': visible }]" @click="visible = !visible">
      <div class="z-admin-menu-item-link text-base">
        <div>{{ title }}</div>
      </div>
      <ZIcon v-if="!visible" type="arrow" />
      <ZIcon v-else type="arrow-down" />
    </div>
    <div
      class="z-admin-menu-sub"
      :style="{ maxHeight: visible ? `${height}px` : '0px' }"
    >
      <slot />
    </div>
  </div>
</template>

<style lang="less">
@height_menu_sub_item: 80px;
.z-admin-menu {

  &-sub {
    opacity: 1;
    transition: all 0.3s ease-in-out;
    overflow: hidden;
    position: relative;
    background-color: #212e3d;
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;

    .z-admin-menu-sub {
      .z-admin-menu-item {
        padding-left: 80px;
        background-color:  #2a3645;

        &::before {
          left: 50px;
        }
      }
    }

    &:first-child {
      padding-top: 2px;
    }

    .z-admin-menu-item {
      margin: 0;
      padding-left: 50px;
      border-radius: 4px;
      transition: all .4s;

      &-selected {
        background-color:  #2a3645;
      }

      &::before {
        content: "";
        position: absolute;
        left: 22px;
        width: 0.375rem;
        height: 0.375rem;
        border-radius: 50%;
        background-color: #bec5cc;
      }
    }

    &-1 {
      max-height: 50px
    }
    &-2 {
      max-height: 100px
    }
    &-3 {
      max-height: 150px
    }
    &-4 {
      max-height: 200px
    }
    &-5 {
      max-height: 250px
    }
    &-6 {
      max-height: 300px
    }
    &-7 {
      max-height: 350px
    }
    &-8 {
      max-height: 400px
    }
  }

  &-item {
    display: flex;
    padding: 2px 20px;
    height: 50px;
    background-color: #212e3d;
    align-items: center;
    justify-content: space-between;
    color: #bec5cc;
    color: rgba(@white-color, .8);
    transition: all .4s;

    &:first-child {
      padding-bottom: 2px;
      border-top-left-radius: 4px;
      border-top-right-radius: 4px;
    }

    &:last-child {
      border-bottom-left-radius: 4px;
      border-bottom-right-radius: 4px;
    }

    &:hover {
      background-color: #2a3645;
    }
  }
}
</style>
