<script lang="ts" setup>
import type { ZAdminFormColumn } from '~/types'

const props = defineProps<{
  alert: SystemAlert
}>()

const adminStore = useAdminStore()

const visible = ref(false)

const alertType = computed(() => {
  switch (props.alert.type) {
    case SystemAlertType.ReorgProtection:
      return 'Reorg Protection'
    case SystemAlertType.ServerDiskSpaceLow:
      return 'Service Disk Space Low'
    case SystemAlertType.WithdrawLimitHit:
      return 'Withdraw Limit Hit'
    default:
      return 'Unknown Alert'
  }
})

const formColumns: ZAdminFormColumn[] = computed(() => {
  const fields = [
    {
      fields: [
        {
          key: 'type',
          type: ZAdminFormFieldType.Text,
          label: 'Type',
          value: alertType.value,
        },
        {
          key: 'message',
          type: ZAdminFormFieldType.Text,
          label: 'Message',
          value: props.alert.message,
          class: 'mb-2 message',
        },
        {
          key: 'metadata',
          type: ZAdminFormFieldType.Text,
          label: 'Metadata',
          value: props.alert.metadata,
          scopedSlots: true,
          class: 'metadata',
        },
      ],
    },
  ]

  return fields
})

async function openModal() {
  visible.value = true

  if (!props.alert.viewed) {
    await adminStore.MarkViewSystemAlert(props.alert.alert_id)
  }
}

defineExpose({
  openModal,
})
</script>

<template>
  <ZModal
    v-model="visible"
    class="layouts-admin-system-alerts-modal"
    title="Alert Details"
  >
    <ZAdminForm :columns="formColumns">
      <template #metadata>
        <div class="mb-2">
          Metadata
        </div>
        <div class="p-2 bg-gray-1 border border-gray-7 rounded-md">
          <template v-if="alert.type === SystemAlertType.ReorgProtection">
            <p>
              <span>
                UID: <NuxtLink :to="`users/user_directory/${alert.metadata.uid}`" target="member">{{ alert.metadata.uid }}</NuxtLink>
              </span>
              <br>
              <span>
                Email: <NuxtLink :to="`users/user_directory/${alert.metadata.uid}`" target="member">{{ alert.metadata.email }}</NuxtLink>
              </span>
            </p>
            <p>
              <span class="mr-4">
                Deposit: <NuxtLink :to="`/accountings/deposits/${alert.metadata.deposit_id}`" target="deposit">{{ alert.metadata.deposit_id }}</NuxtLink>
              </span>
              <span>
                Withdraw: <NuxtLink :to="`/accountings/withdraws/${alert.metadata.withdraw_id}`" target="withdraw">{{ alert.metadata.withdraw_id }}</NuxtLink>
              </span>
              <br>
              <span>Deposit TxID: {{ alert.metadata.deposit_txid }}</span>
            </p>
          </template>
          <template v-else-if="alert.type === SystemAlertType.ServerDiskSpaceLow">
            <p>
              <span>Total: {{ alert.metadata.total }}</span>
              <br>
              <span>Available: {{ alert.metadata.available }}</span>
            </p>
          </template>
          <template v-else-if="alert.type === SystemAlertType.WithdrawLimitHit">
            <p>
              <span>
                UID: <NuxtLink :to="`users/user_directory/${alert.metadata.uid}`" target="member">{{ alert.metadata.uid }}</NuxtLink>
              </span>
              <br>
              <span>
                Email: <NuxtLink :to="`users/user_directory/${alert.metadata.uid}`" target="member">{{ alert.metadata.email }}</NuxtLink>
              </span>
            </p>
            <p>
              <span>
                Type: {{ alert.metadata.type.toUpperCase() }}
              </span>
              <br>
              <span>
                Address: {{ alert.metadata.address }}
              </span>
              <br>
              <span>
                Blockchain: {{ alert.metadata.blockchain_key }}
              </span>
              <br>
              <span>
                Amount: {{ alert.metadata.amount }} {{ alert.metadata.currency_id.toUpperCase() }}
              </span>
            </p>
          </template>
        </div>
      </template>
    </ZAdminForm>
  </ZModal>
</template>

<style lang="less">
.layouts-admin-system-alerts-modal {
  .z-admin-form-button {
    display: none;

    .z-button {
      width: 100%;
      color: white;
      background-color: @primary-color;
    }
  }

  .z-form-row + .z-form-row {
    margin-top: 20px;
  }

  .z-form-col-item {
    padding: 0 !important;
  }

  .message {
    .z-form-row-content > div > div {
      color: @down-color;
    }
  }

  .z-form-row-content {
    height: auto;
  }
}
</style>
