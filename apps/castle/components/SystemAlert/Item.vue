<script setup lang="ts">
import { formatDistanceToNow } from 'date-fns'
import { SystemAlertModal } from '#components'

const props = defineProps<{
  alert: SystemAlert
}>()

const modal = ref<InstanceType<typeof SystemAlertModal>>()

const alertType = computed(() => {
  switch (props.alert.type) {
    case SystemAlertType.ReorgProtection:
      return 'Reorg Protection'
    case SystemAlertType.ServerDiskSpaceLow:
      return 'Service Disk Space Low'
    case SystemAlertType.WithdrawLimitHit:
      return 'Withdraw Limit Hit'
    default:
      return 'Unknown Alert'
  }
})

function openModal() {
  modal.value?.openModal()
}
</script>

<template>
  <div class="system-alert-item" :class="{ 'system-alert-item-viewed': alert.viewed }">
    <div class="flex justify-between relative" @click="openModal">
      <span class="system-alert-item-type">Type: {{ alertType }}</span>
      <span class="system-alert-item-time">{{ formatDistanceToNow(new Date(alert.created_at), { addSuffix: true }) }}</span>
      <ZIconCaretDownFilled class="caret" />
    </div>
    <SystemAlertModal ref="modal" :alert="alert" />
  </div>
</template>
