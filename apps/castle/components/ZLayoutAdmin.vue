<script setup lang="ts">
import { fromUnixTime } from 'date-fns/esm'
import type { Filter } from '~/types'
import { FilterType } from '~/types'

const adminStore = useAdminStore()
const filterStore = useFilterStore()
const alertVisible = ref(false)
const menuActionStore = useMenuActionStore()
const currentYear = new Date().getFullYear()

const timeFrom = computed({
  get() {
    const filter = filterStore.filters.find((filter: Filter) => filter.type === FilterType.DateRange)

    if (filter?.value) {
      return filter.value[0] || null
    }

    return null
  },
  set(val: Date | null) {
    const filter = filterStore.filters.find((filter: Filter) => filter.type === FilterType.DateRange)

    if (!filter) {
      return
    }

    if (!filter.value) {
      filter.value = []
    }

    filter.value[0] = val
  },
})

const timeTo = computed({
  get() {
    const filter = filterStore.filters.find((filter: Filter) => filter.type === FilterType.DateRange)

    if (filter?.value) {
      return filter.value[1] || null
    }

    return null
  },
  set(val: Date | null) {
    const filter = filterStore.filters.find((filter: Filter) => filter.type === FilterType.DateRange)

    if (!filter) {
      return
    }

    if (!filter.value) {
      filter.value = []
    }

    filter.value[1] = val
  },
})

const filterVisble = computed(() => {
  return filterStore.visible
})

watch([filterStore.filters, filterVisble], () => {
  const params = useUrlSearchParams('history')

  for (let index = 0; index < filterStore.filters.length; index++) {
    const filter = filterStore.filters[index]

    switch (filter.type) {
      case FilterType.DateRange:
        if (params.time_from && params.time_to) {
          if (Number(params.time_from) && Number(params.time_to)) {
            timeFrom.value = fromUnixTime(Number(params.time_from))
            timeTo.value = fromUnixTime(Number(params.time_to))
          } else {
            delete params.time_from
            delete params.time_to
          }
        }
        break
      default:
        filter.value = params[filter.key] || ''
    }
  }
}, { deep: true })

function openFilter() {
  filterStore.visible = !filterStore.visible
}

function openAlert() {
  alertVisible.value = !alertVisible.value
}

function onFilterSubmit() {
  filterStore.submitFiler()
}
</script>

<template>
  <div class="w-full dashboard flex-auto min-w-0">
    <div class="relative w-[280px]">
      <ZAdminMenu class="fixed top-0 left-0 bot-0" />
    </div>
    <div class="dashboard-content flex flex-auto flex-col px-6">
      <div class="dashboard-content-header ">
        <div class="flex pt-4 pl-2 text-xl text-gray-400 pb-4 mb-1">
          <ZNavigation />
        </div>
        <ClientOnly>
          <div class="flex">
            <template v-for="item in menuActionStore.items" :key="item.key">
              <div class="flex items-center cursor-pointer mr-8" @click="item.callback">
                <ZIcon v-if="item.icon" :type="item.icon" class="text-gray-400" />
                <span class="ml-2">{{ item.title }}</span>
              </div>
            </template>
            <div v-if="filterStore.filters.length > 0" class="dashboard-content-header-filter flex items-center cursor-pointer mr-4" @click="openFilter">
              <ZIcon type="filter" class="text-gray-400" />
            </div>
            <div class="dashboard-content-header-filter flex items-center cursor-pointer relative" @click="openAlert">
              <ZIconBellDuotone />
              <span class="absolute text-white text-[12px] text-center w-15px h-15px rounded-2xl right-[4px] bottom-[18px]" :class="{ 'bg-red-600': adminStore.systemAlerts.filter(alert => !alert.viewed).length > 0, 'bg-gray-500': adminStore.systemAlerts.filter(alert => !alert.viewed).length === 0 }">
                {{ adminStore.systemAlerts.filter(alert => !alert.viewed).length > 99 ? '99+' : '' }}
                {{ adminStore.systemAlerts.filter(alert => !alert.viewed).length < 100 && adminStore.systemAlerts.filter(alert => !alert.viewed).length > 0 ? adminStore.systemAlerts.filter(alert => !alert.viewed).length : '' }}
                {{ adminStore.systemAlerts.filter(alert => !alert.viewed).length }}
              </span>
            </div>
          </div>
        </ClientOnly>
      </div>
      <div class="flex flex-auto min-w-0 flex-col">
        <slot />
        <div class="flex w-full justify-end flex-auto min-w-0 items-end text-gray-500 text-lg mt-4 bold-text">
          © 2018-{{ currentYear }} <a target="_blank" href="https://www.zsmartex.com" class="ml-2">ZSmartex</a>. All rights reserved.
        </div>
      </div>
    </div>
    <SystemAlertDrawer v-model="alertVisible" />
    <ZDrawer v-model="filterStore.visible" title="Filter" :loading="filterStore.loading">
      <template #footer>
        <ZButton type="primary" class="mr-2 h-[36px]" :loading="filterStore.loading" @click="onFilterSubmit">
          Submit
        </ZButton>
        <ZButton type="error" class="h-[36px]" @click="filterStore.visible = false">
          Cancel
        </ZButton>
      </template>
      <template v-for="filter in filterStore.filters" :key="filter.key">
        <div class="mb-4 fitler">
          <ZAdminInput v-if="filter.type === FilterType.Input" v-model="filter.value" :label="filter.label" />
          <ZSelect
            v-else-if="filter.type === FilterType.Select && filter.columns && filter.findBy && filter.valueKey && filter.labelKey"
            v-model="filter.value"
            :data-source="filter.data"
            :search="true"
            :scroll="filter.scroll"
            :columns="filter.columns"
            :find-by="filter.findBy"
            :value-key="filter.valueKey"
            :label-key="filter.labelKey"
            :label="filter.label"
            :replace-func="filter.replaceFunc"
          />
          <div v-else-if="filter.type === FilterType.DateRange" class="flex items-center">
            <ZDatePicker v-model="timeFrom" placeholder="Start date" :placement="Placement.BottomLeft" />
            <ZIcon type="ic_back" class="transform rotate-180 px-2" />
            <ZDatePicker v-model="timeTo" placeholder="End date" :placement="Placement.BottomRight" />
          </div>
        </div>
      </template>
    </ZDrawer>
  </div>
</template>

<style lang="less">
.dashboard {
  display: flex;

  &-content {
    width: calc(100% - 280px);

    &-header {
      display: flex;
      justify-content: space-between;
      position: relative;
      background-color: @layout-background-color;

      &-filter {
        height: 100%;
        padding: 8px;

        img {
          width: 100%;
          height: 100%;
          border-radius: 50%;
        }
      }
    }
  }

  .z-drawer {
    &-content {
      i {
        color: rgba(@text-color, 0.5)
      }

      .z-date-picker {
        .z-dropdown {
          &-overlay {
            width: fit-content;
          }

          &-trigger {
            input {
              padding: 0;
            }

            div {
              margin: 0;
              display: flex;
              align-items: center;
            }
          }
        }
      }

      .z-dropdown {
        &-trigger {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 0 12px;
          width: 100%;
          height: 38px;
          border: 1px solid rgba(33, 47, 79, 0.1);
          border-radius: 4px;
          transition: all 0.3s;
          &:hover {
            border: 1px solid @primary-color;
          }
        }

        &-triggered {
          border: 1px solid @primary-color;
          box-shadow: 0 0 0 2px rgba(@primary-color, 0.2);
        }

        &-overlay {
          // width: 100%;
          background-color: #fff;
          box-shadow: 0 3px 6px -4px #0000001f, 0 6px 16px #00000014, 0 9px 28px 8px #0000000d;
        }
      }
    }
  }
}
</style>
