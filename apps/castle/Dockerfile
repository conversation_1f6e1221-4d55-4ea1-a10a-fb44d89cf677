# Base image
FROM node:20 AS base

ENV PNPM_HOME="/root/.local/share/pnpm"
ENV PATH="${PATH}:${PNPM_HOME}"

RUN corepack enable && \
      corepack prepare pnpm@9.12.0 --activate

WORKDIR /app

RUN pnpm install -g turbo

# Prune all packages
FROM base AS pruner

COPY . .

RUN turbo prune --scope=@zsmartex/castle --docker

# Install package using pnpm
FROM base AS installer

COPY --from=pruner /app/out/json/ .
RUN true
COPY --from=pruner /app/out/pnpm-lock.yaml ./pnpm-lock.yaml
RUN true
COPY --from=pruner /app/out/pnpm-workspace.yaml ./pnpm-workspace.yaml
RUN true
COPY --from=pruner /app/turbo.json ./turbo.json

RUN pnpm install --frozen-lockfile

# Build castle source code
FROM base AS builder

COPY --from=installer /app/ .
COPY --from=pruner /app/out/full/ .

RUN turbo run build -- --scope=@zsmartex/castle --include-dependencies

# Start castle server
FROM base AS runner

ENV NODE_ENV production

RUN addgroup --system --gid 1001 app
RUN adduser --system --no-create-home --shell /sbin/nologin --gid 1001 --uid 1001 app
RUN chown -R app:app /app

USER app

COPY --from=builder --chown=app:app /app/apps/castle/.output/ ./

EXPOSE 3000

CMD ["node", "server/index.mjs"]
