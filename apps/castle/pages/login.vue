<script setup lang="ts">
import type { Country } from '@zsmartex/types'
import { UserState } from '@zsmartex/types'
import { type ZAuthFormField, ZAuthFormFieldType, type ZTabItem, type ZTableColumn } from '@zsmartex/components/types'
import Validate from '~/validation/validate'
import countries from '~/library/countries'
import type Button from '#components/Button.vue'

definePageMeta({
  middleware: ['guest-only'],
  layout: 'login',
})

const delayButtonEmail = ref<InstanceType<typeof Button>>()
const delayButtonPhone = ref<InstanceType<typeof Button>>()

const userStore = useUserStore()
const adminStore = useAdminStore()

const activeTab = ref('email')
const searchBoxVisible = ref(false)
const number = ref('')
const region = ref('VN')

const codeSelected = computed(() => {
  return countries.find(c => c.code === unref(region))?.mobile_code as string
})

const phoneModalSelectColumns: ZTableColumn[] = [
  {
    key: 'mobile_code',
    scopedSlots: true,
  },
]

const tabs: ZTabItem[] = [
  {
    key: 'email',
    text: 'Email',
  },
  {
    key: 'phone',
    text: 'Phone',
  },
]

const needCode = computed(() => {
  return userStore.need_otp || userStore.need_email || userStore.need_phone
})

const fields = computed(() => {
  const fields: ZAuthFormField[] = [
    {
      key: 'password',
      hidden: needCode.value,
      label: 'Password',
      name: 'password',
      type: ZAuthFormFieldType.Input,
      inputType: InputType.Password,
      placeholder: 'Enter your password',
      required: true,
      validate: [Validate.pattern(/^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])(?=.*?[#?!@$%^&*-]).{8,}$/)],
      transformErrors: {
        'input.error.pattern': 'At least 8 characters, must include UPPER CASE letters and numbers',
      },
    },
    {
      key: 'email_code',
      name: 'email_code',
      type: ZAuthFormFieldType.Input,
      inputType: InputType.Number,
      hidden: !userStore.need_email,
      label: 'Email Code',
      placeholder: 'Enter the code sent to your email',
      required: userStore.need_email,
      maxLength: 6,
      validate: [Validate.minLength(6)],
      transformErrors: {
        'input.error.min_length': 'At least 6 characters',
      },
    },
    {
      key: 'phone_code',
      name: 'phone_code',
      type: ZAuthFormFieldType.Input,
      inputType: InputType.Number,
      hidden: !userStore.need_phone,
      label: 'Phone Code',
      placeholder: 'Enter the code sent to your phone',
      required: userStore.need_phone,
      maxLength: 6,
      validate: [Validate.minLength(6)],
      transformErrors: {
        'input.error.min_length': 'At least 6 characters',
      },
    },
    {
      key: 'otp_code',
      name: 'otp_code',
      type: ZAuthFormFieldType.Input,
      inputType: InputType.Number,
      hidden: !userStore.need_otp,
      label: 'OTP Code',
      placeholder: 'OTP Code',
      required: userStore.need_otp,
      maxLength: 6,
      validate: [Validate.minLength(6)],
      transformErrors: {
        'input.error.min_length': 'At least 6 characters',
      },
    },
  ]

  if (activeTab.value === 'email') {
    fields.unshift(
      {
        key: 'email',
        name: 'email',
        hidden: needCode.value,
        label: 'Email',
        type: ZAuthFormFieldType.Input,
        inputType: InputType.Text,
        placeholder: 'Enter your e-mail address',
        required: true,
        validate: [Validate.email],
        transformErrors: {
          'input.error.email': 'Invalid email/sub-account format',
        },
      },
    )
  }

  return fields
})

function onSearchBoxClicked(item: Country) {
  searchBoxVisible.value = false
  region.value = item.code
}

async function GenerateLoginCode(type: 'email' | 'phone') {
  const button = type === 'email' ? delayButtonEmail : delayButtonPhone

  await userStore.GenerateCodeLogin(
    userStore._cache.email,
    userStore._cache.phone_number,
    userStore._cache.region,
    type,
  )

  button.value?.StartDelay()
}

onBeforeUnmount(() => {
  userStore.need_otp = false
  userStore.need_email = false
  userStore.need_phone = false
})

async function login({ email, password, email_code: emailCode, otp_code: OTPCode, phone_code: phoneCode }: { email: string; password: string; email_code: string; phone_code: string; otp_code: string }) {
  const params: Record<string, string> = {
    password,
  }

  if (email) {
    params.email = email
  }
  else {
    params.phone_number = number.value
    params.region = region.value
  }

  await userStore.Login(params, {
    email_code: emailCode,
    phone_code: phoneCode,
    otp_code: OTPCode,
  }, adminStore.CallbackLogin)
}
</script>

<template>
  <ZAuthForm
    title="Login to ZSmartex"
    :loading="userStore.state === UserState.Pending"
    :fields="fields"
    @submit="login"
  >
    <template v-if="!needCode" #head>
      <ZTab v-model="activeTab" :tabs="tabs" />
    </template>
    <template v-if="activeTab === 'phone' && !needCode" #phone>
      <div class="page-login-phone relative flex">
        <ZSearchBox
          v-model="searchBoxVisible"
          :data-source="countries"
          :columns="phoneModalSelectColumns"
          :find-by="['mobile_code', 'name']"
          @click="onSearchBoxClicked"
        >
          +{{ codeSelected }}
          <ZIcon class="ml-2" type="arrow-down" />
          <template #mobile_code="{ item }">
            +{{ item.mobile_code }} {{ item.name }}
          </template>
        </ZSearchBox>
        <ZCol>
          <ZAuthInput
            v-model="number"
            :type="InputType.Number"
            placeholder="Phone number"
            name="phone_number"
          />
        </ZCol>
      </div>
    </template>
    <template #email_code-suffix>
      <ZButton
        ref="delayButtonEmail"
        :delay="{
          time: 60,
          content: 'Get [#{time}] again',
        }"
        @click="GenerateLoginCode('email')"
      >
        Get Code
      </ZButton>
    </template>
    <template #phone_code-suffix>
      <ZButton
        ref="delayButtonPhone"
        :delay="{
          time: 60,
          content: 'Get [#{time}] again',
        }"
        @click="GenerateLoginCode('phone')"
      >
        Get Code
      </ZButton>
    </template>
  </ZAuthForm>
</template>

<style lang="less">
@import '~/assets/styles/layouts/auth.less';

.page-login {
  &-links {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  &-phone {
    margin-top: 12px;
    padding-bottom: 18px;
    height: 68px;

    .z-input {
      height: 50px;
      // background-color: white;
    }

    .z-dropdown {
      margin-right: 12px;
      border: 1px solid @base-border-color;

      &-trigger {
        display: flex;
        border-radius: 4px;
        padding: 8px 12px;
        align-items: center;
        cursor: pointer;
      }

      &-overlay {
        width: 250px;
      }
    }
  }
}
</style>
