<script setup lang="ts">
import { copyToClipboard, parseTemplate } from '@zsmartex/utils'
import { CollectStatus, DepositStatus, DepositType } from '@zsmartex/types'
import { format as formatDate, parseISO } from 'date-fns'

const adminStore = useAdminStore()

const id = useRoute().params.id as string
const loadingAction = useState(() => false)
const menuActionStore = useMenuActionStore()

const { data: deposit, pending: pendingDeposit, refresh: refreshDeposit } = await useAsyncData(() => adminStore.FetchDeposit(Number(id)))

menuActionStore.setMenu([
  {
    key: 'refresh',
    title: 'Refresh',
    callback: async () => {
      refreshDeposit()
    },
  },
])

const columns: ZTableColumn[] = [
  {
    title: 'ID',
    key: 'id',
  },
  {
    title: 'TxID',
    key: 'txid',
    overflow: true,
    scopedSlots: true,
  },
  {
    title: 'Amount',
    key: 'amount',
    align: Align.Right,
  },
  {
    title: 'Currency',
    key: 'currency_id',
    toUpper: true,
    align: Align.Right,
    sortKeyword: true,
  },
  {
    title: 'Status',
    key: 'status',
    scopedSlots: true,
    align: Align.Right,
    sortKeyword: true,
  },
  {
    title: 'Date',
    key: 'created_at',
    formatBy: Format.DateTime,
    parse: ParseType.DateTime,
    align: Align.Right,
  },
]

function copy(value: string) {
  Message.success({
    message: $t('success'),
  })

  copyToClipboard(value)
}

function ExplorerAddress(deposit: Deposit, address?: string) {
  const blockchain = adminStore.blockchains.find(b => b.key === deposit.blockchain_key)
  if (blockchain && blockchain.explorer_address) {
    return blockchain.explorer_address.replace('#{address}', address || deposit.address)
  }

  return ''
}

function ExplorerTransaction(item: Deposit | Collect) {
  const blockchain = adminStore.blockchains.find(b => b.key === item.blockchain_key)
  if (blockchain && blockchain.explorer_transaction) {
    return parseTemplate(blockchain.explorer_transaction, item)
  }

  return ''
}

async function SendDepositAction(action: string) {
  loadingAction.value = true
  await adminStore.SendDepositAction({
    id: deposit.value!.id,
    action,
  }, () => {
    switch (action) {
      case 'accept':
        deposit.value!.status = DepositStatus.Accepted
        break
      case 'process':
        deposit.value!.status = DepositStatus.Processing
        break
      case 'reject':
        deposit.value!.status = DepositStatus.Rejected
        break
      default:
        break
    }
  })
  loadingAction.value = false
}
</script>

<template>
  <div>
    <ZCard class="page-accountings-deposits-deposit">
      <div v-if="pendingDeposit" class="min-h-[200px]">
        <ZLoading />
      </div>
      <div v-else>
        <div class="flex justify-between mb-4">
          <div class="bold-text text-2xl">
            Deposit ID: {{ deposit!.id }} ({{ deposit!.type.toUpperCase() }})
          </div>
          <div v-if="deposit!.type === DepositType.OnChain" class="flex">
            <ZButton
              v-if="deposit!.status === DepositStatus.Submitted"
              class="h-[32px] ml-4" :loading="loadingAction"
              @click="SendDepositAction('accept')"
            >
              Accept
            </ZButton>
            <ZButton
              v-if="deposit!.status === DepositStatus.Accepted"
              class="h-[32px] ml-4" :loading="loadingAction"
              @click="SendDepositAction('process')"
            >
              Process
            </ZButton>
            <ZButton
              class="h-[32px] ml-4" :loading="loadingAction"
              @click="SendDepositAction('reject')"
            >
              Reject
            </ZButton>
          </div>
        </div>
        <div class="flex mx-[-48px]">
          <ZFormCol :col="3">
            <ZFormRow label="Email">
              {{ deposit!.email }}
            </ZFormRow>
            <ZFormRow label="Tid">
              {{ deposit!.tid }}
            </ZFormRow>
            <ZFormRow label="Created At">
              {{ formatDate(parseISO(deposit!.created_at), 'MMM dd, yyyy hh:mm a') }}
            </ZFormRow>
            <ZFormRow label="Completed At">
              {{ deposit!.completed_at ? formatDate(parseISO(deposit!.completed_at), 'MMM dd, yyyy hh:mm a') : '---' }}
            </ZFormRow>
          </ZFormCol>
          <ZFormCol :col="3">
            <ZFormRow label="Currency">
              {{ deposit!.currency_id.toUpperCase() }}
            </ZFormRow>
            <template v-if="deposit!.type === DepositType.OnChain">
              <ZFormRow label="Blockchain Key">
                {{ deposit!.blockchain_key }}
              </ZFormRow>
              <ZFormRow label="Address">
                <template #label>
                  <ZIconCopyAltFilled class="cursor-pointer" @click="copy(deposit!.address)" />
                </template>
                <NuxtLink :to="ExplorerAddress(deposit!)" target="_blank">
                  {{ deposit!.address && deposit!.address.length > 153 ? `${deposit!.address.slice(0, 75)}...${deposit!.address.slice(-78)}` : deposit!.address }}
                </NuxtLink>
              </ZFormRow>
              <ZFormRow label="From address">
                <template #label>
                  <ZIconCopyAltFilled class="cursor-pointer" @click="copy(deposit!.from_address)" />
                </template>
                <NuxtLink :to="ExplorerAddress(deposit!, deposit!.from_address)" target="_blank">
                  {{ deposit!.from_address && deposit!.from_address.length > 153 ? `${deposit!.from_address.slice(0, 75)}...${deposit!.from_address.slice(-78)}` : deposit!.from_address }}
                </NuxtLink>
              </ZFormRow>
            </template>
            <template v-else>
              <ZFormRow label="From Member">
                <NuxtLink :to="`/users/user_directory/${deposit!.internal_transfer.from_member.uid}`">
                  {{ deposit!.internal_transfer.from_member.uid }}
                </NuxtLink>
              </ZFormRow>
              <ZFormRow label="To Member">
                <NuxtLink :to="`/users/user_directory/${deposit!.internal_transfer.to_member.uid}`">
                  {{ deposit!.internal_transfer.to_member.uid }}
                </NuxtLink>
              </ZFormRow>
            </template>
          </ZFormCol>
          <div class="w-4/12 px-12">
            <ZFormRow label="Status" class="capitalize">
              <span
                class="capitalize"
                :class="[
                  { 'text-green-500': deposit!.status === DepositStatus.Collected || deposit!.status === DepositStatus.Collecting },
                  { 'text-red-400': deposit!.status === DepositStatus.Canceled || deposit!.status === DepositStatus.Rejected || deposit!.status === DepositStatus.Errored },
                  { 'text-gray-400': deposit!.status === DepositStatus.Accepted || deposit!.status === DepositStatus.Submitted || deposit!.status === DepositStatus.Skipped },
                  { 'text-blue-500': deposit!.status === DepositStatus.Processing },
                  { 'text-blue-400': deposit!.status === DepositStatus.PrepareProcessing },
                  { 'text-yellow-400': deposit!.status === DepositStatus.FeeCollected || deposit!.status === DepositStatus.FeeCollecting || deposit!.status === DepositStatus.FeeProcessing },
                ]"
              >
                {{ deposit!.status }}
              </span>
              <ZPopover v-if="deposit!.status === DepositStatus.Errored" :is-operator="false" :placement="Placement.TopCenter">
                <ZIcon type="guandian" class="text-[14px] cursor-pointer ml-[6px] text-gray-400" />
                <template #content>
                  {{ deposit!.errors.length > 0 ? deposit!.errors[0] : 'Unknown error' }}
                </template>
              </ZPopover>
            </ZFormRow>
            <ZFormRow label="Amount">
              {{ deposit!.amount }}
            </ZFormRow>
            <ZFormRow v-if="deposit.type === DepositType.OnChain" label="Block number">
              {{ deposit!.block_number }}
            </ZFormRow>
            <ZFormRow label="TxID">
              <NuxtLink :to="ExplorerTransaction(deposit!)" target="_blank">
                {{ deposit!.txid }}
              </NuxtLink>
            </ZFormRow>
          </div>
        </div>
      </div>
    </ZCard>

    <div v-if="deposit && deposit.type === DepositType.OnChain" class="page-accountings-deposits-collects py-2 my-2">
      <ZTablePro
        :columns="columns"
        :data-source="deposit.collects"
        :loading="pendingDeposit"
        :hover="true"
        :allow-sort-data="false"
        @click="(item: Collect) => Number(item.id) !== -1 && navigateTo(`/accountings/collects/${item.id}`)"
      >
        <template #head>
          <div class="bold-text text-xl">
            Collects
          </div>
        </template>
        <template #status="{ item }">
          <span
            class="capitalize"
            :class="[
              { 'text-green-500': item.status === CollectStatus.Succeed || item.status === CollectStatus.Collecting },
              { 'text-red-400': item.status === CollectStatus.Reject || item.status === CollectStatus.Errored },
              { 'text-gray-400': item.status === CollectStatus.Submitted },
              { 'text-blue-500': item.status === CollectStatus.Processing },
              { 'text-yellow-400': item.status === CollectStatus.FeeCollected || item.status === CollectStatus.FeeProcessing },
            ]"
          >
            {{ item.status }}
          </span>
        </template>
        <template #txid="{ item }">
          <NuxtLink v-if="item.txid !== '---'" :to="ExplorerTransaction(item)" target="_blank">
            {{ item.txid }}
          </NuxtLink>
          <span v-else>{{ item.txid }}</span>
        </template>
      </ZTablePro>
    </div>
  </div>
</template>

<style lang="less">
.page-accountings-deposits-deposit {
  .z-form-row {
    margin-bottom: 0;
  }

  a {
    color: @text-color;
    transition: all 0.3s;

    &:hover {
      color: @text-color;
      text-decoration: underline;
    }
  }

  &-collects {
    .id {
      max-width: 100px;
    }

    .email {
      max-width: 300px;
    }

    .amount {
      max-width: 140px;
    }

    .currency_id {
      max-width: 140px;
    }

    .status {
      max-width: 160px;
    }

    .created_at {
      max-width: 180px;
    }
  }
}
</style>
