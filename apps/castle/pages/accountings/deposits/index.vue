<script setup lang="ts">
import { DepositStatus } from '@zsmartex/types'
import { parseTemplate } from '@zsmartex/utils'
import { FilterType } from '~/types'

const adminStore = useAdminStore()

const total = useState(() => 0)
const filterStore = useFilterStore()
const menuActionStore = useMenuActionStore()

const { query, callbacks } = useQuery()

const { data: deposits, pending, refresh } = await useAsyncData(async () => {
  const { data, headers } = await adminStore.FetchDeposits(query.value)

  total.value = Number(headers.total)

  return data
}, { default: () => ([]) })

callbacks.push(refresh)

menuActionStore.setMenu([
  {
    key: 'refresh',
    title: 'Refresh',
    callback: refresh,
  },
])

const columns: ZTableColumn[] = [
  {
    title: 'ID',
    key: 'id',
    sort: true,
  },
  {
    title: 'Type',
    key: 'type',
    sort: true,
    toUpper: true,
  },
  {
    title: 'Email',
    key: 'email',
    overflow: true,
    scopedSlots: true,
  },
  {
    title: 'TxID',
    key: 'txid',
    overflow: true,
    scopedSlots: true,
  },
  {
    title: 'Amount',
    key: 'amount',
    align: Align.Right,
    sort: true,
  },
  {
    title: 'Currency',
    key: 'currency_id',
    toUpper: true,
    align: Align.Right,
    sort: true,
    sortKeyword: true,
  },
  {
    title: 'Credited',
    key: 'credited',
    align: Align.Right,
    scopedSlots: true,
  },
  {
    title: 'Status',
    key: 'status',
    scopedSlots: true,
    align: Align.Right,
    sort: true,
    sortKeyword: true,
  },
  {
    title: 'Date',
    key: 'created_at',
    formatBy: Format.DateTime,
    parse: ParseType.DateTime,
    align: Align.Right,
    sort: true,
  },
]

const status = [
  {
    value: DepositStatus.Accepted,
  },
  {
    value: DepositStatus.Canceled,
  },
  {
    value: DepositStatus.Collected,
  },
  {
    value: DepositStatus.Collecting,
  },
  {
    value: DepositStatus.Errored,
  },
  {
    value: DepositStatus.FeeCollected,
  },
  {
    value: DepositStatus.FeeCollecting,
  },
  {
    value: DepositStatus.PrepareProcessing,
  },
  {
    value: DepositStatus.Processing,
  },
  {
    value: DepositStatus.Rejected,
  },
  {
    value: DepositStatus.Skipped,
  },
  {
    value: DepositStatus.Submitted,
  },
]

function capitalize(text: string) {
  text = text.replace(/_[a-z]/g, (match) => {
    return ` ${match.slice(1).toUpperCase()}`
  })
  return text.charAt(0).toUpperCase() + text.slice(1)
}

filterStore.setFilter([
  {
    key: 'date',
    label: 'Date',
    type: FilterType.DateRange,
  },
  {
    key: 'id',
    label: 'ID',
    type: FilterType.Input,
  },
  {
    key: 'currency',
    type: FilterType.Select,
    label: 'Currency',
    data: adminStore.currencies,
    scroll: true,
    replaceFunc: (text: string) => text.toUpperCase(),
    columns: [
      {
        key: 'id',
        scopedSlots: true,
      },
    ],
    findBy: ['id'],
    valueKey: 'id',
    labelKey: 'id',
  },
  {
    key: 'blockchain_key',
    type: FilterType.Select,
    label: 'Blockchain Key',
    data: adminStore.blockchains,
    scroll: true,
    columns: [
      {
        key: 'key',
      },
    ],
    findBy: ['key'],
    valueKey: 'key',
    labelKey: 'key',
  },
  {
    key: 'status',
    label: 'Status',
    type: FilterType.Select,
    columns: [
      {
        key: 'value',
        scopedSlots: true,
      },
    ],
    replaceFunc: capitalize,
    data: status,
    findBy: ['value'],
    valueKey: 'value',
    labelKey: 'value',
    scroll: true,
  },
  {
    key: 'txid',
    label: 'TxID',
    type: FilterType.Input,
  },
  {
    key: 'address',
    label: 'Address',
    type: FilterType.Input,
  },
  {
    key: 'tid',
    label: 'Tid',
    type: FilterType.Input,
  },
  {
    key: 'email',
    label: 'Email',
    type: FilterType.Input,
  },
  {
    key: 'uid',
    label: 'UID',
    type: FilterType.Input,
  },
])

filterStore.onSubmit(async (payload) => {
  query.value = {
    page: 1,
    limit: query.value.limit,
    ...payload,
  }
})

function ExplorerTransaction(deposit: Deposit) {
  const blockchain = adminStore.blockchains.find(b => b.key === deposit.blockchain_key)
  if (blockchain && blockchain.explorer_transaction) {
    return parseTemplate(blockchain.explorer_transaction, deposit)
  }

  return ''
}
</script>

<template>
  <div class="page-dashboard-accountings-deposits py-2">
    <ZTablePro
      v-model:query="query"
      :columns="columns"
      :data-source="deposits"
      :loading="pending"
      :hover="true"
      :allow-sort-data="false"
      @click="(item: Deposit) => Number(item.id) !== -1 && navigateTo(`/accountings/deposits/${item.id}`)"
    >
      <template #head>
        <div class="bold-text text-xl">
          Deposits
        </div>
      </template>
      <template #credited="{ item }">
        <div
          :class="[
            { 'text-green-500': item.credited },
            { 'text-orange-500': !item.credited },
          ]"
        >
          {{ String(item.credited).toUpperCase() }}
        </div>
      </template>
      <template #status="{ item }">
        <span
          class="capitalize"
          :class="[
            { 'text-green-500': item.status === DepositStatus.Collected || item.status === DepositStatus.Collecting },
            { 'text-red-400': item.status === DepositStatus.Canceled || item.status === DepositStatus.Rejected || item.status === DepositStatus.Errored },
            { 'text-gray-400': item.status === DepositStatus.Accepted || item.status === DepositStatus.Submitted || item.status === DepositStatus.Skipped },
            { 'text-blue-400': item.status === DepositStatus.PrepareProcessing },
            { 'text-blue-500': item.status === DepositStatus.Processing },
            { 'text-yellow-400': item.status === DepositStatus.FeeCollected || item.status === DepositStatus.FeeCollecting || item.status === DepositStatus.FeeProcessing },
          ]"
        >
          {{ item.status }}
        </span>
      </template>
      <template #email="{ item }">
        <NuxtLink v-if="item.uid !== '---'" :to="`/users/user_directory/${item.uid}`">
          {{ item.email }}
        </NuxtLink>
        <span v-else>{{ item.email }}</span>
      </template>
      <template #txid="{ item }">
        <NuxtLink v-if="item.txid !== '---'" :to="ExplorerTransaction(item)" target="_blank">
          {{ item.txid }}
        </NuxtLink>
        <span v-else>{{ item.txid }}</span>
      </template>
      <template #foot>
        <ZPagination v-model="query.page" v-model:page-size="query.limit" :loading="pending" :total="total" />
      </template>
    </ZTablePro>
  </div>
</template>

<style lang="less">
.page-dashboard-accountings-deposits {
  .id {
    max-width: 100px;
  }

  .email {
    max-width: 300px;
  }

  .amount {
    max-width: 140px;
  }

  .currency_id {
    max-width: 140px;
  }

  .status {
    max-width: 160px;
  }

  .created_at {
    max-width: 180px;
  }

  .z-table {
    a {
      color: @text-color;
      transition: all 0.3s;

      &:hover {
        color: @text-color;
        text-decoration: underline;
      }
    }
  }
}
</style>
