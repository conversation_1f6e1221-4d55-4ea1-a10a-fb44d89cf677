<script setup lang="ts">
const activeTab = ref('assets')
const route = useRoute()

const tabs: ZTabItem[] = [
  {
    key: 'assets',
    text: 'Assets',
    slotName: true,
  },
  {
    key: 'liabilities',
    text: 'Liabilities',
    slotName: true,
  },
  {
    key: 'revenues',
    text: 'Revenues',
    slotName: true,
  },
  {
    key: 'expenses',
    text: 'Expenses',
    slotName: true,
  },
]

onMounted(async () => {
  const path = route.path.split('/accountings/operations/')[1]
  if (!path) {
    await navigateTo(`/accountings/operations/${activeTab.value}`)
    return
  }

  activeTab.value = path
})

function onClick(tabKey: string) {
  navigateTo(`/accountings/operations/${tabKey}`)
}
</script>

<template>
  <div>
    <div class="p-2 operations">
      <ZCard class="no-padding-bottom">
        <ZTab v-model="activeTab" :tabs="tabs" @click="onClick">
          <template v-for="tab in tabs" #[tab.key] :key="tab.key">
            {{ tab.text }}
          </template>
        </ZTab>
      </ZCard>
      <NuxtPage />
    </div>
  </div>
</template>

<style lang="less">
.no-padding-bottom {
  padding-bottom: 0;
}
</style>
