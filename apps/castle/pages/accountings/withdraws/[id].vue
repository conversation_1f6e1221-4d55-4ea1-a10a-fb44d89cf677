<script setup lang="ts">
import { format as formatDate, parseISO } from 'date-fns'
import { copyToClipboard, parseTemplate } from '@zsmartex/utils'
import { WithdrawType } from '@zsmartex/types'

const adminStore = useAdminStore()

const id = useRoute().params.id as string
const loadingAction = useState(() => false)
const menuActionStore = useMenuActionStore()

const { data: withdraw, pending: pendingWithdraw, refresh: refreshWithdraw } = await useAsyncData(() => adminStore.FetchWithdraw(Number(id)))

menuActionStore.setMenu([
  {
    key: 'refresh',
    title: 'Refresh',
    callback: async () => {
      refreshWithdraw()
    },
  },
])

const columns: ZTableColumn[] = [
  {
    title: 'ID',
    key: 'id',
    sort: true,
  },
  {
    title: 'Currency',
    key: 'currency',
    scopedSlots: true,
  },
  {
    title: 'TxID',
    key: 'txid',
    overflow: true,
    scopedSlots: true,
  },
  {
    title: 'Recipient Address',
    key: 'rid',
    overflow: true,
    scopedSlots: true,
  },
  {
    title: 'Amount',
    key: 'amount',
    sort: true,
  },
  {
    title: 'Status',
    key: 'status',
    scopedSlots: true,
    sort: true,
    sortKeyword: true,
  },
  {
    title: 'Date',
    key: 'created_at',
    align: Align.Right,
    formatBy: Format.DateTime,
    parse: ParseType.DateTime,
    sort: true,
  },
]

function copy(value: string) {
  Message.success({
    message: $t('success'),
  })

  copyToClipboard(value)
}

async function SendWithdrawAction(action: string) {
  loadingAction.value = true
  await adminStore.SendWithdrawAction({
    id: withdraw.value!.id,
    action,
  }, () => {
    switch (action) {
      case 'process':
      case 'reprocess':
        withdraw.value!.status = WithdrawStatus.Processing
        break
      case 'error':
        withdraw.value!.status = WithdrawStatus.Errored
        break
      case 'cancel':
        withdraw.value!.status = WithdrawStatus.Canceled
        break
      case 'reject':
        withdraw.value!.status = WithdrawStatus.Rejected
        break
      case 'skip':
        withdraw.value!.status = WithdrawStatus.Skipped
        break
      case 'fail':
        withdraw.value!.status = WithdrawStatus.Failed
        break
      default:
        break
    }
  })
  loadingAction.value = false
}

function ExplorerAddress(withdraw: Withdraw) {
  const blockchain = adminStore.blockchains.find(b => b.key === withdraw.blockchain_key)
  if (blockchain && blockchain.explorer_address) {
    return blockchain.explorer_address.replace('#{address}', withdraw.rid)
  }

  return ''
}

function ExplorerTransaction(withdraw: Withdraw) {
  const blockchain = adminStore.blockchains.find(b => b.key === withdraw.blockchain_key)
  if (blockchain && blockchain.explorer_transaction) {
    return parseTemplate(blockchain.explorer_transaction, withdraw)
  }

  return ''
}
</script>

<template>
  <div>
    <ZCard class="page-accountings-deposits-deposit">
      <div v-if="pendingWithdraw" class="min-h-[200px]">
        <ZLoading />
      </div>
      <div v-else>
        <div class="flex justify-between mb-4">
          <div class="bold-text text-2xl">
            Withdraw ID: {{ withdraw!.id }} ({{ withdraw!.type.toUpperCase() }})
          </div>
          <div v-if="withdraw!.type === WithdrawType.OnChain" class="flex">
            <ZButton
              v-if="[WithdrawStatus.Accepted, WithdrawStatus.Skipped, WithdrawStatus.Errored].includes(withdraw!.status)"
              class="h-[32px] ml-4" :loading="loadingAction"
              @click="SendWithdrawAction('process')"
            >
              Process
            </ZButton>
            <ZButton
              v-if="[WithdrawStatus.Processing].includes(withdraw!.status)"
              class="h-[32px] ml-4" :loading="loadingAction"
              @click="SendWithdrawAction('reprocess')"
            >
              Re-Process
            </ZButton>
            <ZButton
              v-if="[WithdrawStatus.Processing, WithdrawStatus.Confirming].includes(withdraw!.status)"
              class="h-[32px] ml-4" :loading="loadingAction"
              @click="SendWithdrawAction('error')"
            >
              Error
            </ZButton>
            <ZButton
              v-if="[WithdrawStatus.Prepared, WithdrawStatus.Accepted].includes(withdraw!.status)"
              class="h-[32px] ml-4" :loading="loadingAction"
              @click="SendWithdrawAction('cancel')"
            >
              Cancel
            </ZButton>
            <ZButton
              v-if="[WithdrawStatus.ToReject, WithdrawStatus.Accepted, WithdrawStatus.Confirming, WithdrawStatus.UnderReview].includes(withdraw!.status)"
              class="h-[32px] ml-4" :loading="loadingAction"
              @click="SendWithdrawAction('reject')"
            >
              Reject
            </ZButton>
            <ZButton
              v-if="[WithdrawStatus.Processing].includes(withdraw!.status)"
              class="h-[32px] ml-4" :loading="loadingAction"
              @click="SendWithdrawAction('skip')"
            >
              Skip
            </ZButton>
            <ZButton
              v-if="[WithdrawStatus.Processing, WithdrawStatus.Confirming, WithdrawStatus.Skipped, WithdrawStatus.Errored, WithdrawStatus.UnderReview, WithdrawStatus.Banned].includes(withdraw!.status)"
              class="h-[32px] ml-4" :loading="loadingAction"
              @click="SendWithdrawAction('fail')"
            >
              Fail
            </ZButton>
          </div>
        </div>
        <div class="flex mx-[-48px]">
          <ZFormCol :col="3">
            <ZFormRow label="Email">
              {{ withdraw!.email }}
            </ZFormRow>
            <ZFormRow label="Tid">
              {{ withdraw!.tid }}
            </ZFormRow>
            <ZFormRow label="Created At">
              {{ formatDate(parseISO(withdraw!.created_at), 'MMM dd, yyyy hh:mm a') }}
            </ZFormRow>
            <ZFormRow label="Completed At">
              {{ withdraw!.completed_at ? formatDate(parseISO(withdraw!.completed_at), 'MMM dd, yyyy hh:mm a') : '---' }}
            </ZFormRow>
          </ZFormCol>
          <ZFormCol :col="3">
            <ZFormRow label="Currency">
              {{ withdraw!.currency_id.toUpperCase() }}
            </ZFormRow>
            <ZFormRow v-if="withdraw.type === WithdrawType.OnChain" label="Blockchain Key">
              {{ withdraw!.blockchain_key }}
            </ZFormRow>
            <ZFormRow label="Sum">
              {{ withdraw!.sum }}
            </ZFormRow>
            <ZFormRow v-if="withdraw.type === WithdrawType.Internal" label="From Member">
              <NuxtLink :to="`/users/user_directory/${withdraw!.internal_transfer.from_member.uid}`">
                {{ withdraw!.internal_transfer.from_member.uid }}
              </NuxtLink>
            </ZFormRow>
            <ZFormRow v-if="withdraw.type === WithdrawType.OnChain" label="Rid">
              <template #label>
                <ZIconCopyAltFilled class="cursor-pointer" @click="copy(withdraw!.rid)" />
              </template>
              <NuxtLink :to="ExplorerAddress(withdraw!)" target="_blank">
                {{ withdraw!.rid && withdraw!.rid.length > 153 ? `${withdraw!.rid.slice(0, 75)}...${withdraw!.rid.slice(-78)}` : withdraw!.rid }}
              </NuxtLink>
            </ZFormRow>
            <ZFormRow v-else label="To Member">
              <NuxtLink :to="`/users/user_directory/${withdraw!.internal_transfer.to_member.uid}`">
                {{ withdraw!.internal_transfer.to_member.uid }}
              </NuxtLink>
            </ZFormRow>
          </ZFormCol>
          <div class="w-4/12 px-12">
            <ZFormRow label="Status" class="capitalize">
              <span
                class="capitalize"
                :class="[
                  { 'text-green-500': withdraw!.status === WithdrawStatus.Succeed },
                  { 'text-red-400': withdraw!.status === WithdrawStatus.Rejected || withdraw!.status === WithdrawStatus.ToReject || withdraw!.status === WithdrawStatus.Errored || withdraw!.status === WithdrawStatus.Failed || withdraw!.status === WithdrawStatus.Banned },
                  { 'text-gray-400': withdraw!.status === WithdrawStatus.Skipped || withdraw!.status === WithdrawStatus.UnderReview || withdraw!.status === WithdrawStatus.Canceled || withdraw!.status === WithdrawStatus.Prepared },
                  { 'text-blue-500': withdraw!.status === WithdrawStatus.Processing || withdraw!.status === WithdrawStatus.Confirming },
                  { 'text-yellow-400': withdraw!.status === WithdrawStatus.Accepted },
                ]"
              >
                {{ withdraw!.status }}
              </span>
              <ZPopover v-if="withdraw!.status === WithdrawStatus.Errored || withdraw?.status === WithdrawStatus.Banned || withdraw?.status === WithdrawStatus.Failed" :is-operator="false" :placement="Placement.TopCenter">
                <ZIcon type="guandian" class="text-[14px] cursor-pointer ml-[6px] text-gray-400" />
                <template #content>
                  {{ withdraw!.error.length > 0 ? withdraw!.error?.join("||") : 'Unknown error' }}
                </template>
              </ZPopover>
            </ZFormRow>
            <ZFormRow label="Amount">
              {{ withdraw!.amount }}
            </ZFormRow>
            <ZFormRow label="Fee">
              {{ withdraw!.fee }}
            </ZFormRow>
            <ZFormRow v-if="withdraw.type === WithdrawType.OnChain" label="Block number">
              {{ withdraw!.block_number }}
            </ZFormRow>
            <ZFormRow label="TxID">
              <div class="page-accountings-deposits-deposit-overflow">
                <NuxtLink :to="ExplorerTransaction(withdraw!)" target="_blank">
                  {{ withdraw!.txid }}
                </NuxtLink>
              </div>
            </ZFormRow>
          </div>
        </div>
      </div>
    </ZCard>
  </div>
</template>

<style lang="less">
.page-accountings-deposits-deposit {
  .z-form-row {
    margin-bottom: 0;
  }

  a {
    color: @text-color;
    transition: all 0.3s;

    &:hover {
      color: @text-color;
      text-decoration: underline;
    }
  }
}
</style>
