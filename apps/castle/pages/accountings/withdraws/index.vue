<script setup lang="ts">
import { WithdrawType } from '@zsmartex/types'
import { parseTemplate } from '@zsmartex/utils'
import { FilterType } from '~/types'

const adminStore = useAdminStore()

const total = useState(() => 0)
const filterStore = useFilterStore()
const { query, callbacks } = useQuery()
const menuActionStore = useMenuActionStore()

const { data: withdraws, pending, refresh } = await useAsyncData(async () => {
  const { data, headers } = await adminStore.FetchWithdraws(query.value)
  total.value = Number(headers.total)

  return data
}, { default: () => ([]) })

callbacks.push(refresh)

const columns: ZTableColumn[] = [
  {
    title: 'ID',
    key: 'id',
    sort: true,
  },
  {
    title: 'Type',
    key: 'type',
    sort: true,
    toUpper: true,
  },
  {
    title: 'Email',
    key: 'email',
    scopedSlots: true,
    overflow: true,
  },
  {
    title: 'Currency',
    key: 'currency',
    scopedSlots: true,
  },
  {
    title: 'TxID',
    key: 'txid',
    overflow: true,
    scopedSlots: true,
  },
  {
    title: 'Recipient Address',
    key: 'rid',
    overflow: true,
    scopedSlots: true,
  },
  {
    title: 'Amount',
    key: 'amount',
    sort: true,
  },
  {
    title: 'Status',
    key: 'status',
    scopedSlots: true,
    sort: true,
    sortKeyword: true,
  },
  {
    title: 'Date',
    key: 'created_at',
    align: Align.Right,
    formatBy: Format.DateTime,
    parse: ParseType.DateTime,
    sort: true,
  },
]

menuActionStore.setMenu([
  {
    key: 'refresh',
    title: 'Refresh',
    callback: refresh,
  },
])

const status = [
  {
    id: 1,
    value: WithdrawStatus.Accepted,
  },
  {
    id: 2,
    value: WithdrawStatus.Canceled,
  },
  {
    id: 3,
    value: WithdrawStatus.Confirming,
  },
  {
    id: 4,
    value: WithdrawStatus.Errored,
  },
  {
    id: 5,
    value: WithdrawStatus.Failed,
  },
  {
    id: 6,
    value: WithdrawStatus.Prepared,
  },
  {
    id: 7,
    value: WithdrawStatus.Processing,
  },
  {
    id: 8,
    value: WithdrawStatus.Rejected,
  },
  {
    id: 9,
    value: WithdrawStatus.Skipped,
  },
  {
    id: 10,
    value: WithdrawStatus.Succeed,
  },
  {
    id: 11,
    value: WithdrawStatus.ToReject,
  },
  {
    id: 12,
    value: WithdrawStatus.UnderReview,
  },
  {
    id: 13,
    value: WithdrawStatus.Banned,
  },
]

function capitalize(text: string) {
  text = text.replace(/_[a-z]/g, (match) => {
    return ` ${match.slice(1).toUpperCase()}`
  })
  return text.charAt(0).toUpperCase() + text.slice(1)
}

filterStore.setFilter([
  {
    key: 'date',
    label: 'Date',
    type: FilterType.DateRange,
  },
  {
    key: 'id',
    label: 'ID',
    type: FilterType.Input,
  },
  {
    key: 'currency',
    type: FilterType.Select,
    label: 'Currency',
    data: adminStore.currencies,
    scroll: true,
    replaceFunc: (text: string) => text.toUpperCase(),
    columns: [
      {
        key: 'id',
        scopedSlots: true,
      },
    ],
    findBy: ['id'],
    valueKey: 'id',
    labelKey: 'id',
  },
  {
    key: 'blockchain_key',
    type: FilterType.Select,
    label: 'Blockchain Key',
    data: adminStore.blockchains,
    scroll: true,
    columns: [
      {
        key: 'key',
      },
    ],
    findBy: ['key'],
    valueKey: 'key',
    labelKey: 'key',
  },
  {
    key: 'status',
    label: 'Status',
    type: FilterType.Select,
    columns: [
      {
        key: 'value',
        scopedSlots: true,
      },
    ],
    replaceFunc: capitalize,
    data: status,
    findBy: ['value'],
    valueKey: 'value',
    labelKey: 'value',
    scroll: true,
  },
  {
    key: 'txid',
    label: 'TxID',
    type: FilterType.Input,
  },
  {
    key: 'tid',
    label: 'Tid',
    type: FilterType.Input,
  },
  {
    key: 'rid',
    label: 'Rid',
    type: FilterType.Input,
  },
  {
    key: 'email',
    label: 'Email',
    type: FilterType.Input,
  },
  {
    key: 'uid',
    label: 'UID',
    type: FilterType.Input,
  },
])

filterStore.onSubmit(async (payload) => {
  query.value = {
    page: 1,
    limit: query.value.limit,
    ...payload,
  }
})

function ExplorerAddress(withdraw: Withdraw) {
  const blockchain = adminStore.blockchains.find(b => b.key === withdraw.blockchain_key)
  if (blockchain && blockchain.explorer_address) {
    return blockchain.explorer_address.replace('#{address}', withdraw.rid)
  }

  return ''
}

function ExplorerTransaction(withdraw: Withdraw) {
  const blockchain = adminStore.blockchains.find(b => b.key === withdraw.blockchain_key)
  if (blockchain && blockchain.explorer_transaction && withdraw.txid) {
    return parseTemplate(blockchain.explorer_transaction, withdraw)
  }

  return ''
}
</script>

<template>
  <div class="page-dashboard-accountings-withdraws py-2">
    <ZTablePro
      v-model:query="query"
      :columns="columns"
      :data-source="withdraws"
      :loading="pending"
      :hover="true"
      :allow-sort-data="false"
      @click="(item: Withdraw) => Number(item.id) !== -1 && navigateTo(`/accountings/withdraws/${item.id}`)"
    >
      <template #head>
        <div class="bold-text text-xl">
          Withdraws
        </div>
      </template>
      <template #email="{ item }">
        <NuxtLink v-if="item.uid !== '---'" :to="`/users/user_directory/${item.uid}`">
          {{ item.email }}
        </NuxtLink>
        <span v-else>{{ item.email }}</span>
      </template>
      <template #status="{ item }">
        <span
          class="capitalize"
          :class="[
            { 'text-green-500': item.status === WithdrawStatus.Succeed },
            { 'text-red-400': item.status === WithdrawStatus.Rejected || item.status === WithdrawStatus.ToReject || item.status === WithdrawStatus.Errored || item.status === WithdrawStatus.Failed || item.status === WithdrawStatus.Banned },
            { 'text-gray-400': item.status === WithdrawStatus.Skipped || item.status === WithdrawStatus.UnderReview || item.status === WithdrawStatus.Canceled || item.status === WithdrawStatus.Prepared },
            { 'text-blue-500': item.status === WithdrawStatus.Processing || item.status === WithdrawStatus.Confirming },
            { 'text-yellow-400': item.status === WithdrawStatus.Accepted },
          ]"
        >
          {{ item.status }}
        </span>
      </template>
      <template #txid="{ item }">
        <NuxtLink v-if="item.txid !== '---'" :to="ExplorerTransaction(item)" target="_blank">
          {{ item.txid }}
        </NuxtLink>
        <span v-else>{{ item.txid }}</span>
      </template>
      <template #rid="{ item }">
        <NuxtLink v-if="item.rid !== '---'" :to="item.type === WithdrawType.Internal ? `/users/user_directory/${item.internal_transfer.to_member.uid}` : ExplorerAddress(item)" :target="item.type === WithdrawType.Internal ? '_self' : '_blank'">
          {{ item.type === WithdrawType.Internal ? item.internal_transfer.to_member.uid : item.rid }}
        </NuxtLink>
        <span v-else>{{ item.rid }}</span>
      </template>
      <template #currency="{ item }">
        <NuxtLink to="/exchange/currencies">
          {{ item.currency_id.toUpperCase() }}
        </NuxtLink>
      </template>
      <template #foot>
        <ZPagination v-model="query.page" v-model:page-size="query.limit" :loading="pending" :total="total" />
      </template>
    </ZTablePro>
  </div>
</template>

<style lang="less">
.page-dashboard-accountings-withdraws {
  .id {
    max-width: 60px;
  }

  .email {
    max-width: 180px;
  }

  .currency {
    max-width: 100px;
  }

  .amount {
    max-width: 120px;
  }

  .status {
    max-width: 120px;
  }

  .created_at {
    max-width: 140px;
  }

  .z-table {
    a {
      color: @text-color;
      transition: all 0.3s;

      &:hover {
        color: @text-color;
        text-decoration: underline;
      }
    }
  }
}
</style>
