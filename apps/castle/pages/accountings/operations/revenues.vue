<script setup lang="ts">
import { FilterType } from '~/types'

const adminStore = useAdminStore()

const total = useState(() => 0)
const filterStore = useFilterStore()
const { query, callbacks } = useQuery()
const menuActionStore = useMenuActionStore()

const { data: revenues, pending, refresh } = await useAsyncData(async () => {
  const { data, headers } = await adminStore.FetchOperationRevenues(query.value)
  total.value = Number(headers.total)

  return data
}, { lazy: true, default: () => ([]) })

callbacks.push(refresh)

menuActionStore.setMenu([
  {
    key: 'refresh',
    title: 'Refresh',
    callback: refresh,
  },
])

const columns: ZTableColumn[] = [
  {
    title: 'ID',
    key: 'id',
    sort: true,
  },
  {
    title: 'Code',
    key: 'code',
    sort: true,
  },
  {
    title: 'Currency',
    key: 'currency_id',
    toUpper: true,
    sort: true,
    sortKeyword: true,
  },
  {
    title: 'Ref ID',
    key: 'reference_id',
    sort: true,
  },
  {
    title: 'Ref Type',
    key: 'reference_type',
    sort: true,
    sortKeyword: true,
  },
  {
    title: 'Credit',
    key: 'credit',
    sort: true,
  },
  {
    title: 'Debit',
    key: 'debit',
    sort: true,
  },
  {
    title: 'Created At',
    key: 'created_at',
    formatBy: Format.DateTime,
    parse: ParseType.DateTime,
    sort: true,
  },
]

filterStore.setFilter([
  {
    key: 'reference_type',
    label: 'Reference Type',
    type: FilterType.Input,
  },
  {
    key: 'reference_id',
    label: 'Reference ID',
    type: FilterType.Input,
  },
  {
    key: 'code',
    label: 'Code',
    type: FilterType.Input,
  },
  {
    key: 'currency',
    type: FilterType.Select,
    label: 'Currency',
    data: adminStore.currencies,
    scroll: true,
    replaceFunc: (text: string) => text.toUpperCase(),
    columns: [
      {
        key: 'id',
        scopedSlots: true,
      },
    ],
    findBy: ['id'],
    valueKey: 'id',
    labelKey: 'id',
  },
])

filterStore.onSubmit(async (payload) => {
  query.value = {
    page: 1,
    limit: query.value.limit,
    ...payload,
  }
})
</script>

<template>
  <div class="page-dashboard-accountings-deposits py-2">
    <ZTablePro v-model:query="query" :columns="columns" :data-source="revenues" :loading="pending" :allow-sort-data="false">
      <template #head>
        <div class="bold-text text-xl">
          Operation Revenues
        </div>
      </template>
      <template #foot>
        <ZPagination v-model="query.page" v-model:page-size="query.limit" :loading="pending" :total="total" />
      </template>
    </ZTablePro>
  </div>
</template>
