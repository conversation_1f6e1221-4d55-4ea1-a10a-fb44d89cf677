<script setup lang="ts">
import { CollectStatus, DepositStatus } from '@zsmartex/types'
import { format as formatDate, parseISO } from 'date-fns'
import { copyToClipboard, parseTemplate } from '@zsmartex/utils'

const adminStore = useAdminStore()

const id = useRoute().params.id as string
const loadingAction = useState(() => false)
const menuActionStore = useMenuActionStore()

const { data: collect, pending: pendingCollect, refresh: refreshCollect } = await useAsyncData(() => adminStore.FetchCollect(Number(id)))
menuActionStore.setMenu([
  {
    key: 'refresh',
    title: 'Refresh',
    callback: async () => {
      refreshCollect()
    },
  },
])

const columns: ZTableColumn[] = [
  {
    title: 'ID',
    key: 'id',
  },
  {
    title: 'TxID',
    key: 'txid',
    overflow: true,
    scopedSlots: true,
  },
  {
    title: 'Amount',
    key: 'amount',
    align: Align.Right,
  },
  {
    title: 'Currency',
    key: 'currency_id',
    toUpper: true,
    align: Align.Right,
    sortKeyword: true,
  },
  {
    title: 'Credited',
    key: 'credited',
    align: Align.Right,
    scopedSlots: true,
  },
  {
    title: 'Status',
    key: 'status',
    scopedSlots: true,
    align: Align.Right,
    sortKeyword: true,
  },
  {
    title: 'Date',
    key: 'created_at',
    formatBy: Format.DateTime,
    parse: ParseType.DateTime,
    align: Align.Right,
  },
]

function copy(value: string) {
  Message.success({
    message: $t('success'),
  })

  copyToClipboard(value)
}

async function SendCollectAction(action: string) {
  loadingAction.value = true
  await adminStore.SendCollectAction({
    id: collect.value!.id,
    action,
  }, () => {
    switch (action) {
      case 'process':
        collect.value!.status = CollectStatus.Processing
        break
      case 'error':
        collect.value!.status = CollectStatus.Errored
        break
      default:
        break
    }
  })
  loadingAction.value = false
}

function ExplorerAddress(collect: Collect, address: string) {
  const blockchain = adminStore.blockchains.find(b => b.key === collect.blockchain_key)
  if (blockchain && blockchain.explorer_address) {
    return blockchain.explorer_address.replace('#{address}', address)
  }

  return ''
}

function ExplorerTransaction(item: Deposit | Collect) {
  const blockchain = adminStore.blockchains.find(b => b.key === item.blockchain_key)
  if (blockchain && blockchain.explorer_transaction) {
    return parseTemplate(blockchain.explorer_transaction, item)
  }

  return ''
}
</script>

<template>
  <div>
    <ZCard class="page-accountings-collects-collect">
      <div v-if="pendingCollect" class="min-h-[200px]">
        <ZLoading />
      </div>
      <div v-else>
        <div class="flex justify-between mb-4">
          <div class="bold-text text-2xl">
            Collect ID: {{ collect!.id }}
          </div>
          <div class="flex">
            <ZButton
              v-if="[CollectStatus.Submitted, CollectStatus.Errored].includes(collect!.status)"
              class="h-[32px] ml-4" :loading="loadingAction"
              @click="SendCollectAction('process')"
            >
              Process
            </ZButton>
            <ZButton
              v-if="[CollectStatus.Collecting, CollectStatus.Processing, CollectStatus.FeeProcessing].includes(collect!.status)"
              class="h-[32px] ml-4" :loading="loadingAction"
              @click="SendCollectAction('error')"
            >
              Error
            </ZButton>
          </div>
        </div>
        <div v-if="collect" class="flex mx-[-48px]">
          <ZFormCol :col="3">
            <ZFormRow label="Currency">
              {{ collect.currency_id.toUpperCase() }}
            </ZFormRow>
            <ZFormRow label="Blockchain Key">
              {{ collect.blockchain_key }}
            </ZFormRow>
            <ZFormRow label="Created At">
              {{ formatDate(parseISO(collect.created_at), 'MMM dd, yyyy hh:mm a') }}
            </ZFormRow>
            <ZFormRow label="Completed At">
              {{ collect.completed_at ? formatDate(parseISO(collect.completed_at), 'MMM dd, yyyy hh:mm a') : '---' }}
            </ZFormRow>
          </ZFormCol>
          <ZFormCol :col="3">
            <ZFormRow label="Deposit Count">
              {{ collect.deposits.length }}
            </ZFormRow>
            <ZFormRow label="From address">
              <template #label>
                <ZIconCopyAltFilled class="cursor-pointer" @click="copy(collect.from_address)" />
              </template>
              <NuxtLink :to="ExplorerAddress(collect, collect.from_address)" target="_blank">
                {{ collect.from_address && collect.from_address.length > 153 ? `${collect.from_address.slice(0, 75)}...${collect.from_address.slice(-78)}` : collect.from_address }}
              </NuxtLink>
            </ZFormRow>
            <ZFormRow label="To address">
              <template #label>
                <ZIconCopyAltFilled class="cursor-pointer" @click="copy(collect.to_address)" />
              </template>
              <NuxtLink :to="ExplorerAddress(collect, collect!.to_address)" target="_blank">
                {{ collect.to_address && collect.to_address.length > 153 ? `${collect.to_address.slice(0, 75)}...${collect.to_address.slice(-78)}` : collect.to_address }}
              </NuxtLink>
            </ZFormRow>
          </ZFormCol>
          <div class="w-4/12 px-12">
            <ZFormRow label="Status" class="capitalize">
              <span
                class="capitalize"
                :class="[
                  { 'text-green-500': collect!.status === CollectStatus.Succeed || collect!.status === CollectStatus.Collecting },
                  { 'text-red-400': collect!.status === CollectStatus.Reject || collect!.status === CollectStatus.Errored },
                  { 'text-gray-400': collect!.status === CollectStatus.Submitted },
                  { 'text-blue-500': collect!.status === CollectStatus.Processing },
                  { 'text-yellow-400': collect!.status === CollectStatus.FeeCollected || collect!.status === CollectStatus.FeeProcessing },
                ]"
              >
                {{ collect!.status }}
              </span>
              <ZPopover v-if="collect!.status === DepositStatus.Errored" :is-operator="false" :placement="Placement.TopCenter">
                <ZIcon type="guandian" class="text-[14px] cursor-pointer ml-[6px] text-gray-400" />
                <template #content>
                  {{ collect!.errors.length > 0 ? collect!.errors[0] : 'Unknown error' }}
                </template>
              </ZPopover>
            </ZFormRow>
            <ZFormRow label="Amount">
              {{ collect!.amount }}
            </ZFormRow>
            <ZFormRow label="TxID">
              <NuxtLink :to="ExplorerTransaction(collect)" target="_blank">
                {{ collect.txid }}
              </NuxtLink>
            </ZFormRow>
          </div>
        </div>
      </div>
    </ZCard>

    <div v-if="collect" class="page-accountings-collects-deposits py-2 my-2">
      <ZTablePro
        :columns="columns"
        :data-source="collect.deposits"
        :loading="pendingCollect"
        :hover="true"
        :allow-sort-data="false"
        @click="(item: Deposit) => Number(item.id) !== -1 && navigateTo(`/accountings/deposits/${item.id}`)"
      >
        <template #head>
          <div class="bold-text text-xl">
            Deposits
          </div>
        </template>
        <template #credited="{ item }">
          <div
            :class="[
              { 'text-green-500': item.credited },
              { 'text-orange-500': !item.credited },
            ]"
          >
            {{ String(item.credited).toUpperCase() }}
          </div>
        </template>
        <template #status="{ item }">
          <span
            class="capitalize"
            :class="[
              { 'text-green-500': item.status === DepositStatus.Collected || item.status === DepositStatus.Collecting },
              { 'text-red-400': item.status === DepositStatus.Canceled || item.status === DepositStatus.Rejected || item.status === DepositStatus.Errored },
              { 'text-gray-400': item.status === DepositStatus.Accepted || item.status === DepositStatus.Submitted || item.status === DepositStatus.Skipped },
              { 'text-blue-500': item.status === DepositStatus.Processing },
              { 'text-blue-400': item.status === DepositStatus.PrepareProcessing },
              { 'text-yellow-400': item.status === DepositStatus.FeeCollected || item.status === DepositStatus.FeeCollecting || item.status === DepositStatus.FeeProcessing },
            ]"
          >
            {{ item.status }}
          </span>
        </template>
        <template #txid="{ item }">
          <NuxtLink v-if="item.txid !== '---'" :to="ExplorerTransaction(item)" target="_blank">
            {{ item.txid }}
          </NuxtLink>
          <span v-else>{{ item.txid }}</span>
        </template>
      </ZTablePro>
    </div>
  </div>
</template>

<style lang="less">
.page-accountings-collects-collect {
  .z-form-row {
    margin-bottom: 0;
  }

  a {
    color: @text-color;
    transition: all 0.3s;

    &:hover {
      color: @text-color;
      text-decoration: underline;
    }
  }

  &-deposits {
    .id {
      max-width: 100px;
    }

    .amount {
      max-width: 140px;
    }

    .currency_id {
      max-width: 140px;
    }

    .status {
      max-width: 160px;
    }

    .created_at {
      max-width: 180px;
    }
  }
}
</style>
