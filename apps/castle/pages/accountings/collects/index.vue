<script setup lang="ts">
import { CollectStatus } from '@zsmartex/types'
import { parseTemplate } from '@zsmartex/utils'
import { FilterType } from '~/types'

const adminStore = useAdminStore()

const total = useState(() => 0)
const filterStore = useFilterStore()
const menuActionStore = useMenuActionStore()

const { query, callbacks } = useQuery()

const { data: collects, pending, refresh } = await useAsyncData(async () => {
  const { data, headers } = await adminStore.FetchCollects(query.value)

  total.value = Number(headers.total)

  return data
}, { default: () => ([]) })

callbacks.push(refresh)

menuActionStore.setMenu([
  {
    key: 'refresh',
    title: 'Refresh',
    callback: refresh,
  },
])

const columns: ZTableColumn[] = [
  {
    title: 'ID',
    key: 'id',
    sort: true,
  },
  {
    title: 'TxID',
    key: 'txid',
    overflow: true,
    scopedSlots: true,
  },
  {
    title: 'Amount',
    key: 'amount',
    align: Align.Right,
    sort: true,
  },
  {
    title: 'Currency',
    key: 'currency_id',
    toUpper: true,
    align: Align.Right,
    sort: true,
    sortKeyword: true,
  },
  {
    title: 'Status',
    key: 'status',
    scopedSlots: true,
    align: Align.Right,
    sort: true,
    sortKeyword: true,
  },
  {
    title: 'Date',
    key: 'created_at',
    formatBy: Format.DateTime,
    parse: ParseType.DateTime,
    align: Align.Right,
    sort: true,
  },
]

const status = [
  {
    value: CollectStatus.Collecting,
  },
  {
    value: CollectStatus.Errored,
  },
  {
    value: CollectStatus.Succeed,
  },
  {
    value: CollectStatus.Errored,
  },
  {
    value: CollectStatus.Reject,
  },
  {
    value: CollectStatus.FeeCollected,
  },
  {
    value: CollectStatus.FeeProcessing,
  },
  {
    value: CollectStatus.Processing,
  },
  {
    value: CollectStatus.Submitted,
  },
]

function capitalize(text: string) {
  text = text.replace(/_[a-z]/g, (match) => {
    return ` ${match.slice(1).toUpperCase()}`
  })
  return text.charAt(0).toUpperCase() + text.slice(1)
}

filterStore.setFilter([
  {
    key: 'date',
    label: 'Date',
    type: FilterType.DateRange,
  },
  {
    key: 'id',
    label: 'ID',
    type: FilterType.Input,
  },
  {
    key: 'currency',
    type: FilterType.Select,
    label: 'Currency',
    data: adminStore.currencies,
    scroll: true,
    replaceFunc: (text: string) => text.toUpperCase(),
    columns: [
      {
        key: 'id',
        scopedSlots: true,
      },
    ],
    findBy: ['id'],
    valueKey: 'id',
    labelKey: 'id',
  },
  {
    key: 'blockchain_key',
    type: FilterType.Select,
    label: 'Blockchain Key',
    data: adminStore.blockchains,
    scroll: true,
    columns: [
      {
        key: 'key',
      },
    ],
    findBy: ['key'],
    valueKey: 'key',
    labelKey: 'key',
  },
  {
    key: 'status',
    label: 'Status',
    type: FilterType.Select,
    columns: [
      {
        key: 'value',
        scopedSlots: true,
      },
    ],
    replaceFunc: capitalize,
    data: status,
    findBy: ['value'],
    valueKey: 'value',
    labelKey: 'value',
    scroll: true,
  },
  {
    key: 'txid',
    label: 'TxID',
    type: FilterType.Input,
  },
  {
    key: 'from_address',
    label: 'From Address',
    type: FilterType.Input,
  },
])

filterStore.onSubmit(async (payload) => {
  query.value = {
    page: 1,
    limit: query.value.limit,
    ...payload,
  }
})

function ExplorerTransaction(collect: Collect) {
  const blockchain = adminStore.blockchains.find(b => b.key === collect.blockchain_key)
  if (blockchain && blockchain.explorer_transaction) {
    return parseTemplate(blockchain.explorer_transaction, collect)
  }

  return ''
}
</script>

<template>
  <div class="page-dashboard-accountings-collects py-2">
    <ZTablePro
      v-model:query="query"
      :columns="columns"
      :data-source="collects"
      :loading="pending"
      :hover="true"
      :allow-sort-data="false"
      @click="(item: Collect) => Number(item.id) !== -1 && navigateTo(`/accountings/collects/${item.id}`)"
    >
      <template #head>
        <div class="bold-text text-xl">
          Collects
        </div>
      </template>
      <template #status="{ item }">
        <span
          class="capitalize"
          :class="[
            { 'text-green-500': item.status === CollectStatus.Succeed || item.status === CollectStatus.Collecting },
            { 'text-red-400': item.status === CollectStatus.Reject || item.status === CollectStatus.Errored },
            { 'text-gray-400': item.status === CollectStatus.Submitted },
            { 'text-blue-500': item.status === CollectStatus.Processing },
            { 'text-yellow-400': item.status === CollectStatus.FeeCollected || item.status === CollectStatus.FeeProcessing },
          ]"
        >
          {{ item.status }}
        </span>
      </template>
      <template #txid="{ item }">
        <NuxtLink v-if="item.txid !== '---'" :to="ExplorerTransaction(item)" target="_blank">
          {{ item.txid }}
        </NuxtLink>
        <span v-else>{{ item.txid }}</span>
      </template>
      <template #foot>
        <ZPagination v-model="query.page" v-model:page-size="query.limit" :loading="pending" :total="total" />
      </template>
    </ZTablePro>
  </div>
</template>

<style lang="less">
.page-dashboard-accountings-collects {
  .id {
    max-width: 100px;
  }

  .email {
    max-width: 300px;
  }

  .amount {
    max-width: 140px;
  }

  .currency_id {
    max-width: 140px;
  }

  .status {
    max-width: 160px;
  }

  .created_at {
    max-width: 180px;
  }

  .z-table {
    a {
      color: @text-color;
      transition: all 0.3s;

      &:hover {
        color: @text-color;
        text-decoration: underline;
      }
    }
  }
}
</style>
