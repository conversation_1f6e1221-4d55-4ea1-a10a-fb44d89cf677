<script setup lang="ts">
import { useOfflineData } from '@zsmartex/core/composables'
import { FilterType, StrategyKind, StrategyState } from '~/types'

const adminStore = useAdminStore()

const total = useState(() => 0)
const loading = useState(() => false)
const { query } = useQuery()
const filterStore = useFilterStore()
const menuActionStore = useMenuActionStore()
const tempRestart = useState<Record<string, boolean>>(() => ({}))
const tempRestartLoading = useState<Record<string, boolean>>(() => ({}))
const tempState = useState<Record<string, boolean>>(() => ({}))
const tempLoading = useState<Record<string, boolean>>(() => ({}))

const strategies = computed(() => {
  const data = adminStore.strategies.map(item => ({
    ...item,
  }))

  total.value = data.length

  return useOfflineData(query, data)
})

menuActionStore.setMenu([
  {
    key: 'refresh',
    title: 'Refresh',
    callback: async () => {
      loading.value = true
      await adminStore.FetchStrategies()
      loading.value = false
    },
  },
  {
    key: 'create strategy',
    icon: 'plus',
    title: 'Create Strategy',
    callback: () => {
      navigateTo({
        path: '/bot/strategies/create',
      })
    },
  },
])

const columns: ZTableColumn[] = [
  {
    title: 'Target Market',
    key: 'market',
    scopedSlots: true,
  },
  {
    title: 'Kind',
    key: 'kind',
    scopedSlots: true,
  },
  {
    title: 'State',
    key: 'state',
    scopedSlots: true,
  },
  {
    title: 'Action',
    key: 'action',
    align: Align.Right,
    scopedSlots: true,
  },
]

async function UpdateStrategyStatus(item: Strategy) {
  tempLoading.value[item.id] = true
  await adminStore.UpdateStrategy(
    {
      id: item.id,
      state: item.state === StrategyState.Active ? StrategyState.Disabled : StrategyState.Active,
    },
  )
  tempLoading.value[item.id] = false
  item.state = item.state === StrategyState.Active ? StrategyState.Disabled : StrategyState.Active
}

filterStore.setFilter([
  {
    key: 'kind',
    label: 'Kind',
    type: FilterType.Select,
    columns: [
      {
        key: 'value',
        scopedSlots: true,
      },
    ],
    data: [
      {
        value: StrategyKind.MarketMaking,
      },
      {
        value: StrategyKind.CandleSampling,
      },
      {
        value: StrategyKind.PriceSampling,
      },
    ],
    findBy: ['value'],
    valueKey: 'value',
    labelKey: 'value',
    replaceFunc: (text: string) => text.toUpperCase().replace('_', ' '),
  },
  {
    key: 'state',
    label: 'State',
    type: FilterType.Select,
    columns: [
      {
        key: 'value',
        scopedSlots: true,
      },
    ],
    data: [
      {
        value: 'active',
      },
      {
        value: 'disabled',
      },
    ],
    findBy: ['value'],
    valueKey: 'value',
    labelKey: 'value',
    replaceFunc: (text: string) => text[0].toUpperCase() + text.slice(1),
  },
])

filterStore.onSubmit(async (payload) => {
  query.value = {
    page: 1,
    limit: query.value.limit,
    ...payload,
  }
})


function GetBotMarketName(id: number) {
  const market = adminStore.botMarkets.find(m => m.id === id)!
  let exchange = ''
  switch (market.exchange) {
    case 'zsmartex':
      exchange = 'ZSmartex'
      break
    default:
      exchange = market.exchange[0].toUpperCase() + market.exchange.slice(1)
  }
  return `${market.base_unit.toUpperCase()}/${market.quote_unit.toUpperCase()} - ${exchange}`
}

async function restart(strategy: Strategy) {
  tempRestartLoading.value[strategy.id] = true
  await adminStore.RestartStrategy(strategy.id)
  tempRestartLoading.value[strategy.id] = false
  tempRestart.value[strategy.id] = false
}
</script>

<template>
  <div class="page-bot-strategies py-2">
    <ZTablePro
      v-model:query="query"
      :columns="columns"
      :data-source="strategies"
      :loading="loading"
    >
      <template #head>
        <div class="bold-text text-xl">
          Strategies
        </div>
      </template>
      <template #market="{ item }">
        {{ GetBotMarketName(item.target_market_id) }}
        (<NuxtLink to="/bot/markets" class="hover:underline hover:decoration-solid">
          {{ ` #${item.target_market_id} ` }}
        </NuxtLink>)
      </template>
      <template #kind="{ item }">
        {{ item.kind.toUpperCase().replace('_', ' ') }}
      </template>
      <template #state="{ item }">
        <ZPopconfirm v-model="tempState[item.id]" :placement="Placement.TopRight" trigger="click" @click="UpdateStrategyStatus(item)">
          <ZSwitch :model-value="item.state === StrategyState.Active ? true : false" size="medium" :loading="tempLoading[item.id]" />
        </ZPopconfirm>
      </template>
      <template #action="{ item }">
        <div class="flex mr-2">
          <ZPopconfirm v-model="tempRestart[item.id]" :placement="Placement.TopRight" :loading="tempRestartLoading[item.id]" trigger="click" @click="restart(item)">
            <ZButton class="flex cursor-pointer items-center">
              <ZIcon type="spinner11" />Restart
            </ZButton>
          </ZPopconfirm>
        </div>
        <NuxtLink :to="`/bot/strategies/update/${item.id}`">
          <ZIcon type="edit" class="text-[20px]" />
        </Nuxtlink>
      </template>
      <template #foot>
        <ZPagination v-model="query.page" v-model:page-size="query.limit" :loading="loading" :total="total" />
      </template>
    </ZTablePro>
  </div>
</template>

<style lang="less">
.page-bot-strategies {
  .state {
    max-width: 120px;
  }

  .action {
    max-width: 120px;
  }
}
</style>
