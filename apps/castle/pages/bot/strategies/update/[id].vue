<script setup lang="ts">
import LayoutStrategy from '~/layouts/admin/bot/strategy/LayoutStrategy.vue'

const adminStore = useAdminStore()

const id = useRoute().params.id

const { data: strategy, pending, refresh } = await useAsyncData(() => adminStore.FetchStrategy(Number(id)).then(res => res.data))

onMounted(() => {
  if (!strategy.value!.markets) {
    strategy.value!.markets = []
  }
})

const tempChecked = useState<Record<string, boolean>>(() => ({}))

const leftColumns: ZTableColumn[] = [
  {
    key: 'select',
    scopedSlots: true,
  },
  {
    title: 'Exchange',
    key: 'exchange',
    scopedSlots: true,
  },
  {
    title: 'Market',
    key: 'market',
    scopedSlots: true,
  },
]

const rightColumns: ZTableColumn[] = [
  {
    key: 'select',
    scopedSlots: true,
  },
  {
    title: 'Exchange',
    key: 'exchange',
    scopedSlots: true,
  },
  {
    title: 'Market',
    key: 'market',
    scopedSlots: true,
  },
]

const internalMarkets = computed(() => {
  if (!strategy.value!.markets) return []
  return adminStore.botMarkets.filter(m => strategy.value!.markets.includes(m.id))
})

const externalMarkets = computed(() => {
  const ids = internalMarkets.value.map(m => m.id)
  return adminStore.botMarkets.filter(m => m.id !== strategy.value!.target_market_id && !ids.includes(m.id))
})

async function DeleteMarkets() {
  const markets: number[] = []
  for (const market of internalMarkets.value) {
    if (tempChecked.value[market.id]) {
      markets.push(Number((adminStore.botMarkets.find(m => m.id === market.id) as BotMarket).id))
    }
  }

  await adminStore.DeleteMarketStrategy({
    strategy_id: Number(id),
    markets,
  }, () => {
    markets.forEach((id) => {
      const index = strategy.value!.markets.findIndex(m => m === id)
      if (index !== -1) {
        strategy.value!.markets.splice(index, 1)
      }
    })
    tempChecked.value = {}
  })
}

async function CreateMarkets() {
  const markets: number[] = []
  for (const market of externalMarkets.value) {
    if (tempChecked.value[market.id]) {
      markets.push(Number((adminStore.botMarkets.find(m => m.id === market.id) as BotMarket).id))
    }
  }

  await adminStore.CreateMarketStrategy({
    strategy_id: Number(id),
    markets,
  }, () => {
    for (const market of markets) {
      strategy.value!.markets.push(adminStore.botMarkets.find(m => m.id === market)?.id as number)
    }
    tempChecked.value = {}
  })
}

const disableTransferLeft = computed(() => {
  for (const market of internalMarkets.value) {
    if (tempChecked.value[market.id]) {
      return false
    }
  }
  return true
})

const disableTransferRight = computed(() => {
  for (const market of externalMarkets.value) {
    if (tempChecked.value[market.id]) {
      return false
    }
  }
  return true
})

function SelectMarket(market: Market) {
  tempChecked.value[market.id] = !tempChecked.value[market.id]
}

function capitalize(text: string) {
  switch (text) {
    case 'zsmartex':
      return 'ZSmartex'
    default:
      return text[0].toUpperCase() + text.slice(1)
  }
}
</script>

<template>
  <div>
    <LayoutStrategy :strategy-id="strategy!.id" :model-value="strategy!" :refresh="refresh" :pending="pending" />

    <ZTransferTable
      left-title="Linked Markets"
      right-title="Existing Markets"
      :left-columns="leftColumns"
      :right-columns="rightColumns"
      :left-data="internalMarkets"
      :right-data="externalMarkets"
      :disabled-left="disableTransferLeft"
      :disabled-right="disableTransferRight"
      :search-keys="['exchange', 'id', 'base_unit', 'quote_unit']"
      @transfer-left="DeleteMarkets"
      @transfer-right="CreateMarkets"
      @click-left="SelectMarket"
      @click-right="SelectMarket"
    >
      <template #left-select="{ item }">
        <ZCheckbox v-model="tempChecked[item.id]" />
      </template>
      <template #left-exchange="{ item }">
        {{ capitalize(item.exchange) }}
      </template>
      <template #left-market="{ item }">
        {{ `${item.base_unit.toUpperCase()}/${item.quote_unit.toUpperCase()}` }}
        (<NuxtLink to="/bot/markets" class="hover:underline hover:decoration-solid">
          {{ ` #${item.id} ` }}
        </NuxtLink>)
      </template>
      <template #right-select="{ item }">
        <ZCheckbox v-model="tempChecked[item.id]" />
      </template>
      <template #right-exchange="{ item }">
        {{ capitalize(item.exchange) }}
      </template>
      <template #right-market="{ item }">
        {{ `${item.base_unit.toUpperCase()}/${item.quote_unit.toUpperCase()}` }}
        (<NuxtLink to="/bot/markets" class="hover:underline hover:decoration-solid">
          {{ ` #${item.id} ` }}
        </NuxtLink>)
      </template>
    </ZTransferTable>
  </div>
</template>
