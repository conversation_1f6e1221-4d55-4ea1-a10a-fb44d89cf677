<script setup lang="ts">
import { useOfflineData } from '@zsmartex/core/composables'
import { BotMarketExchange, FilterType } from '~/types'
import ModalMarket from '~/layouts/admin/bot/market/ModalMarket.vue'

const adminStore = useAdminStore()
const total = useState(() => 0)
const loading = useState(() => false)
const { query } = useQuery()
const filterStore = useFilterStore()
const menuActionStore = useMenuActionStore()

const modalMarket = ref<InstanceType<typeof ModalMarket>>()

const markets = computed(() => {
  const data = adminStore.botMarkets.map(item => ({
    ...item,
  }))

  total.value = data.length

  return useOfflineData(query, data)
})

menuActionStore.setMenu([
  {
    key: 'refresh',
    title: 'Refresh',
    callback: async () => {
      loading.value = true
      await adminStore.FetchBotMarkets()
      loading.value = false
    },
  },
  {
    key: 'create market',
    icon: 'plus',
    title: 'Create Market',
    callback: () => {
      // showCreate()
      modalMarket.value?.openModal('create')
    },
  },
])

const columns: ZTableColumn[] = [
  {
    title: 'Exchange',
    key: 'exchange',
    sort: true,
    scopedSlots: true,
  },
  {
    title: 'Market',
    key: 'market',
    scopedSlots: true,
  },
  {
    title: 'Min price',
    key: 'min_price',
    sort: true,
    parse: ParseType.Decimal,
    align: Align.Center,
  },
  {
    title: 'Min amount',
    key: 'min_amount',
    sort: true,
    parse: ParseType.Decimal,
    align: Align.Center,
  },
  {
    title: 'Max amount',
    key: 'max_amount',
    sort: true,
    parse: ParseType.Decimal,
    align: Align.Center,
  },
  {
    title: 'Action',
    key: 'action',
    align: Align.Right,
    scopedSlots: true,
  },
]

function capitalize(text: string) {
  switch (text) {
    case 'zsmartex':
      return 'ZSmartex'
    default:
      return text[0].toUpperCase() + text.slice(1)
  }
}

filterStore.setFilter([
  {
    key: 'exchange',
    label: 'Exchange',
    type: FilterType.Select,
    columns: [
      {
        key: 'value',
        scopedSlots: true,
      },
    ],
    replaceFunc: capitalize,
    data: [
      {
        value: BotMarketExchange.Zsmartex,
      },
      {
        value: BotMarketExchange.Binance,
      },
      {
        value: BotMarketExchange.Coinmarketcap,
      },
    ],
    findBy: ['value'],
    valueKey: 'value',
    labelKey: 'value',
  },
  {
    key: 'base_unit',
    label: 'Base Unit',
    type: FilterType.Input,
  },
  {
    key: 'quote_unit',
    label: 'Quote Unit',
    type: FilterType.Input,
  },
])

filterStore.onSubmit(async (payload) => {
  query.value = {
    page: 1,
    limit: query.value.limit,
    ...payload,
  }
})
</script>

<template>
  <div class="page-bot-markets py-2">
    <ZTablePro
      v-model:query="query"
      :columns="columns"
      :data-source="markets"
      :loading="loading"
    >
      <template #head>
        <div class="bold-text text-xl">
          Markets
        </div>
      </template>
      <template #exchange="{ item }">
        {{ capitalize(item.exchange) }}
      </template>
      <template #market="{ item }">
        {{ `${item.base_unit.toUpperCase()}/${item.quote_unit.toUpperCase()}` }}
      </template>
      <template #action="{ item }">
        <span class="mr-4 cursor-pointer" @click="modalMarket?.openModal('update', item)">
          <ZIcon type="edit" class="text-[20px]" />
        </span>
      </template>
      <template #foot>
        <ZPagination v-model="query.page" v-model:page-size="query.limit" :loading="loading" :total="total" />
      </template>
    </ZTablePro>
    <ModalMarket ref="modalMarket" />
  </div>
</template>
