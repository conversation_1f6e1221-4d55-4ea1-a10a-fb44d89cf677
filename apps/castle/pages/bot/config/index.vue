<script setup lang="ts">
import { useOfflineData } from '@zsmartex/core/composables'
import ModalConfig from '~/layouts/admin/bot/config/ModalConfig.vue'

const adminStore = useAdminStore()

const total = useState(() => 0)
const loading = useState(() => false)
const { query } = useQuery()
const menuActionStore = useMenuActionStore()

const modalConfig = ref<InstanceType<typeof ModalConfig>>()

const configs = computed(() => {
  const data = adminStore.configs.map(item => ({
    ...item,
  }))

  total.value = data.length

  return useOfflineData(query, data)
})

menuActionStore.setMenu([
  {
    key: 'refresh',
    title: 'Refresh',
    callback: async () => {
      loading.value = true
      await adminStore.FetchConfigs()
      loading.value = false
    },
  },
  {
    key: 'create config',
    icon: 'plus',
    title: 'Create Config',
    callback: () => {
      modalConfig.value?.openModal("create")
    },
  },
])

const columns: ZTableColumn[] = [
  {
    title: 'Key',
    key: 'key',
  },
  {
    title: 'Action',
    key: 'action',
    align: Align.Right,
    scopedSlots: true,
  },
]
</script>

<template>
  <div class="page-bot-configs py-2">
    <ZTablePro
      v-model:query="query"
      :columns="columns"
      :data-source="configs"
      :loading="loading"
    >
      <template #head>
        <div class="bold-text text-xl">
          Configs
        </div>
      </template>
      <template #action="{ item }">
        <span class="mr-4 cursor-pointer" @click="modalConfig?.openModal('update', item)">
          <ZIcon type="edit" class="text-[20px]" />
        </span>
      </template>
      <template #foot>
        <ZPagination v-model="query.page" v-model:page-size="query.limit" :loading="loading" :total="total" />
      </template>
    </ZTablePro>
    <ModalConfig ref="modalConfig" />
  </div>
</template>

<style lang="less">
.page-bot-configs {
  .action {
    max-width: 200px;
  }
}
</style>
