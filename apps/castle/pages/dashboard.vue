<script setup lang="ts">
import { addDays, endOfDay, format as formatDate, getUnixTime, parseISO, startOfDay } from 'date-fns'
import {
  ArcElement,
  BarElement,
  CategoryScale,
  Chart,
  Filler,
  Legend,
  LineElement,
  LinearScale,
  PointElement,
  Title,
  Tooltip,
} from 'chart.js'
import { ChoroplethController, ColorScale, GeoFeature, ProjectionScale } from 'chartjs-chart-geo'
import ActivityChart from '~/layouts/admin/dashboard/UserActivityChart.vue'
import OrderChart from '~/layouts/admin/dashboard/OrderChart.vue'
import TradeChart from '~/layouts/admin/dashboard/TradeChart.vue'
import RevenueStatisticsChart from '~/layouts/admin/dashboard/RevenueStatisticsChart.vue'
import AssetsStatisticsChart from '~/layouts/admin/dashboard/AssetsStatisticsChart.vue'
import UserDeviceChart from '~/layouts/admin/dashboard/UserDeviceChart.vue'
import SessionLocations from '~/layouts/admin/dashboard/SessionLocations.vue'
import { UserAgentToBrowser, UserAgentToOS } from '~/mixins'
import countries from '~/library/countries'

const adminStore = useAdminStore()

const activeTabStatistics = useState(() => 'orders')
const date = useState(() => 'week')
const timeFrom = useState(() => getUnixTime(
  new Date(new Date().setDate(new Date().getDate() - 8)),
).toString())
const timeTo = useState(() => getUnixTime(new Date()).toString())
const dateRange = ref<(Date | null)[]>([null, null])
const showOption = useState(() => false)
const loading = ref(false)

const tabsStatistics: ZTabItem[] = [
  {
    key: 'orders',
    text: 'Orders',
  },
  {
    key: 'trades',
    text: 'Trades',
  },
  {
    key: 'revenues',
    text: 'Revenues',
  },
  {
    key: 'assets',
    text: 'Assets',
  },
]

const recentTradesColumns: ZTableColumn[] = [
  {
    title: 'Maker order email',
    key: 'maker_email',
    scopedSlots: true,
    overflow: true,
  },
  {
    title: 'Taker order email',
    key: 'taker_email',
    scopedSlots: true,
    overflow: true,
  },
  {
    title: 'Market',
    key: 'market',
    scopedSlots: true,
  },
  {
    title: 'Price',
    key: 'price',
  },
  {
    title: 'Amount',
    key: 'amount',
  },
  {
    title: 'Total',
    key: 'total',
    sort: true,
  },
  {
    title: 'Side',
    key: 'side',
    scopedSlots: true,
  },
  {
    title: 'Date',
    key: 'created_at',
    align: Align.Right,
    formatBy: Format.DateTime,
    parse: ParseType.DateTime,
  },
]

const recentSessionsColumns: ZTableColumn[] = [
  {
    key: 'device',
    title: 'Device',
    scopedSlots: true,
  },
  {
    key: 'location',
    title: 'Location',
    scopedSlots: true,
    align: Align.Right,
    class: 'text-right',
  },
]

function getMarket(id: string) {
  const market = adminStore.markets.find(m => m.id === id)
  return market
}

const orderMetrics = useState(() => adminStore.orderMetrics)

const userLoginDataToday = computed(() => adminStore.userActivityMetrics.length ? adminStore.userActivityMetrics[adminStore.userActivityMetrics.length - 1].login : 0)
const orderProcessedDataToday = computed(() => orderMetrics.value.length ? orderMetrics.value[orderMetrics.value.length - 1].count : 0)
const tradeDataToday = computed(() => adminStore.tradeMetrics.length ? adminStore.tradeMetrics[adminStore.tradeMetrics.length - 1].count : 0)

const percentageUserChanged = computed(() => {
  return countPercentageChanged(adminStore.userActivityMetrics.length >= 2 ? adminStore.userActivityMetrics[adminStore.userActivityMetrics.length - 2].login : 0, userLoginDataToday.value)
})

const percentageOrderChanged = computed(() => {
  return countPercentageChanged(orderMetrics.value.length >= 2 ? orderMetrics.value[orderMetrics.value.length - 2].count : 0, orderProcessedDataToday.value)
})

const percentageTradeChanged = computed(() => {
  return countPercentageChanged(adminStore.tradeMetrics.length >= 2 ? adminStore.tradeMetrics[adminStore.tradeMetrics.length - 2].count : 0, tradeDataToday.value)
})

const todayRevenue = computed(() => {
  return adminStore.todayRevenue.toLocaleString('en-US')
})

const percentageRevenueChanged = computed(() => {
  return countPercentageChanged(
    adminStore.revenueStatistics.length >= 2 ? Number(adminStore.revenueStatistics[adminStore.revenueStatistics.length - 2].revenue) : 0,
    adminStore.revenueStatistics.length >= 1 ? Number(adminStore.revenueStatistics[adminStore.revenueStatistics.length - 1].revenue) : 0,
  )
})

function countPercentageChanged(dataYesterday: number, dataToday: number) {
  if (dataToday === 0 || dataYesterday === 0) return 0
  return Number(((dataToday / dataYesterday - 1) * 100).toFixed(2))
}

function getCountryName(code: string) {
  const country = countries.find(c => c.code === code)
  return country ? country.name : code
}

async function fetchChart() {
  loading.value = true

  const params = {
    time_to: timeTo.value,
    time_from: timeFrom.value,
  }

  await Promise.all([
    adminStore.FetchActivitiesMetrics(params),
    adminStore.FetchSessionLocations(params),
    adminStore.FetchOrderMetrics(params),
    adminStore.FetchTradeMetrics(params),
    adminStore.FetchRevenueStatistics(params),
    adminStore.FetchAssetsStatistics(params),
  ])

  loading.value = false
}

async function click7days() {
  date.value = 'week'
  timeFrom.value = getUnixTime(startOfDay(addDays(new Date(), -6))).toString()
  timeTo.value = getUnixTime(endOfDay(new Date())).toString()
  showOption.value = false
  await fetchChart()
}

async function click30days() {
  date.value = 'month'
  timeFrom.value = getUnixTime(startOfDay(addDays(new Date(), -29))).toString()
  timeTo.value = getUnixTime(endOfDay(new Date())).toString()
  showOption.value = false
  await fetchChart()
}

watch(dateRange.value, async () => {
  if (dateRange.value[0] && dateRange.value[1] && dateRange.value[0] <= dateRange.value[1]) {
    date.value = 'customize'
    timeFrom.value = getUnixTime(startOfDay(dateRange.value[0])).toString()
    timeTo.value = getUnixTime(endOfDay(dateRange.value[1])).toString()
    await fetchChart()
    showOption.value = false
  }
})

if (process.client) {
  Chart.register(
    ChoroplethController,
    GeoFeature,
    ColorScale,
    ProjectionScale,
    CategoryScale,
    LinearScale,
    PointElement,
    LineElement,
    ArcElement,
    BarElement,
    Title,
    Tooltip,
    Legend,
    Filler,
  )
}
</script>

<template>
  <div class="page-dashboard-top grid grid-cols-4 gap-4">
    <ZAdminCard title="Session" :value="userLoginDataToday" :percentage="percentageUserChanged" />
    <ZAdminCard title="Orders" :value="orderProcessedDataToday" :percentage="percentageOrderChanged" />
    <ZAdminCard title="Trades" :value="tradeDataToday" :percentage="percentageTradeChanged" />
    <ZAdminCard title="Revenue" :value="`$${todayRevenue}`" :percentage="percentageRevenueChanged" />
  </div>
  <div class="page-dashboard-date flex">
    <div
      class="page-dashboard-date-day"
      :class="{ 'button-selected': date === 'week' }"
      @click="click7days"
    >
      7 days
    </div>
    <div
      class="page-dashboard-date-day ml-3"
      :class="{ 'button-selected': date === 'month' }"
      @click="click30days"
    >
      30 days
    </div>
    <div
      v-if="!showOption"
      class="page-dashboard-date-day ml-3"
      :class="{ 'button-selected': date === 'customize' }"
      @click="showOption = true"
    >
      Customize
    </div>
    <ZRangePicker
      v-else
      v-model="dateRange"
      class="ml-3"
      :placement="Placement.BottomLeft"
    />
  </div>
  <ZCard class="page-dashboard-locations" title="Session Locations">
    <ZLoading v-if="loading" />
    <!-- <SessionLocations v-else :session-locations="adminStore.sessionLocations" /> -->
  </ZCard>
  <ZCard class="page-dashboard-user-activities-chart" title="User activities">
    <ZLoading v-if="loading" />
    <ActivityChart v-else :activities-metrics-data="adminStore.userActivityMetrics" />
  </ZCard>
  <ZCard class="page-dashboard-users-devices-chart" title="User devices">
    <UserDeviceChart :user-device-metrics-data="adminStore.userDevice" />
  </ZCard>
  <ZCard class="page-dashboard-statistics">
    <ZTab v-model="activeTabStatistics" class="bold-text" :tabs="tabsStatistics" />
    <ZLoading v-if="loading" />
    <OrderChart v-else-if="activeTabStatistics === 'orders'" :order-metrics-data="adminStore.orderMetrics" />
    <TradeChart v-else-if="activeTabStatistics === 'trades'" :trade-metrics-data="adminStore.tradeMetrics" />
    <RevenueStatisticsChart v-else-if="activeTabStatistics === 'revenues'" />
    <AssetsStatisticsChart v-else />
  </ZCard>
  <ZTablePro class="page-dashboard-activities" :columns="recentSessionsColumns" :data-source="adminStore.recentSessions" scroll title="Recent Sessions">
    <template #device="{ item }">
      {{ UserAgentToBrowser(item.user_agent) }} On {{ UserAgentToOS(item.user_agent) }}
    </template>
    <template #location="{ item }">
      <ZTooltip :title="formatDate(parseISO(item.created_at), 'yyyy-MM-dd HH:mm:ss')" :placement="TooltipPlacement.BottomCenter">
        <span class="cursor-pointer">
          {{ getCountryName(item.user_ip_country) }} ({{ item.user_ip }})
        </span>
      </ZTooltip>
    </template>
  </ZTablePro>
  <ZTablePro class="page-dashboard-trades" :columns="recentTradesColumns" :data-source="adminStore.recentTrades" scroll title="Recent Trades">
    <template #maker_email="{ item }">
      <NuxtLink :to="`/users/user_directory/${item.maker_uid}`">
        {{ item.maker_email }}
      </NuxtLink>
    </template>
    <template #taker_email="{ item }">
      <NuxtLink :to="`/users/user_directory/${item.taker_uid}`">
        {{ item.taker_email }}
      </NuxtLink>
    </template>
    <template #market="{ item }">
      <NuxtLink :to="`/exchange/markets/update/${item.market_id}`">
        {{ getMarket(item.market_id)!.base_unit.toUpperCase() }}/{{ getMarket(item.market_id)!.quote_unit.toUpperCase() }}
      </NuxtLink>
    </template>
    <template #price="{ item }">
      <span
        :class="[
          { 'text-up': item.side === OrderSide.Buy },
          { 'text-down': item.side === OrderSide.Sell },
        ]"
      >
        {{ item.price }}
      </span>
    </template>
    <template #side="{ item }">
      <span
        class="page-dashboard-trades-side capitalize"
        :class="[{ 'bg-down text-down': item.side === OrderSide.Sell }, { 'bg-up text-up': item.side === OrderSide.Buy }]"
      >
        {{ item.side }}
      </span>
    </template>
  </ZTablePro>
</template>

<style lang="less">
.page-dashboard {
  // background-color: white;

  // .dashboard-content-header {
  //   background-color: transparent;
  //   font-size: 32px;
  //   padding: 24px 0;

  //   .bread-item {
  //     color: @text-color;
  //   }
  // }

  .page-dashboard-activities {
    .z-table-content {
      padding-bottom: 20px;
    }
  }

  .z-layout-content {
    display: grid;
    height: 100%;
    grid-template-columns: 500px 1fr 1fr 1fr 1fr 400px;
    grid-template-rows: 110px 32px 310px 87px 198px 292px 500px;
    //  65px 245px 45px 224px 250px 500px;
    gap: 16px 16px;
    grid-template-areas:
      "top top top top top users-devices"
      "date date date date date users-devices"
      "user-activities user-activities user-activities user-activities user-activities users-devices"
      "user-activities user-activities user-activities user-activities user-activities locations"
      "statistics statistics statistics statistics statistics locations"
      "statistics statistics statistics statistics statistics activities"
      "trades trades trades trades trades activities";
  }

  &-date {
    &-day {
      padding: 6px 12px;
      background-color: white;
      border: 1px solid @base-border-color;
      border-radius: 4px;
      cursor: pointer;

      &.button-selected {
        border: 1px solid @primary-color;
        color: @primary-color;
      }
    }
  }

  .z-card {
    // box-shadow: none;
    // border-color: @base-border-color;

    &-head {
      margin-bottom: 0;
    }
  }

  &-top {
    grid-area: top;
  }

  &-user-activities-chart {
    grid-area: user-activities;
    padding: 0;
    padding-top: 16px;

    .z-card-head {
      padding: 0 24px;
    }
  }

  &-users-devices-chart {
    grid-area: users-devices;

    .z-card-head {
      font-size: 20px;
    }
  }

  &-statistics {
    grid-area: statistics;
    padding: 0;
    padding-top: 16px;

    .z-tab {
      padding: 0 24px;
    }
  }

  &-locations {
    grid-area: locations;
    padding: 0;
    padding-top: 16px;

    .z-card-head {
      padding: 0 24px;
      font-size: 20px;
    }
  }

  &-activities {
    grid-area: activities;
  }

  &-trades {
    grid-area: trades;

    .market {
      max-width: 160px;
    }

    .side {
      max-width: 80px;
    }

    &-side {
      padding: 4px 12px;
      line-height: 1;
      border-radius: 4px;
    }
  }

  &-activities, &-trades {
    padding: 0;

    .z-table {
      height: calc(100% - 60px);
    }
  }

  .z-tab-item {
    font-size: 18px;
  }

  .z-table {
    a {
      color: @text-color;
      transition: all 0.3s;

      &:hover {
        color: @text-color;
        text-decoration: underline;
      }
    }
  }

  .z-card-overall {
    display: flex;
    flex-direction: column;
  }

  .z-card-content {
    display: flex;
    flex-direction: column;
    height: 100%;
  }
}
</style>
