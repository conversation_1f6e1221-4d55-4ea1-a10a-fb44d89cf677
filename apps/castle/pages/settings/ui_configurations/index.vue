<script setup lang="ts">
import ModalUpdate from '~/layouts/admin/settings/ui_configurations/ModalUpdate.vue'

const adminStore = useAdminStore()

const { query } = useQuery()
const total = useState(() => 0)
const loading = useState(() => false)
const menuActionStore = useMenuActionStore()

const modalUpdate = ref<InstanceType<typeof ModalUpdate>>()

const uiConfigurations = computed(() => {
  return adminStore.ui_configurations
})

menuActionStore.setMenu([
  {
    key: 'refresh',
    title: 'Refresh',
    callback: async () => {
      loading.value = true
      await adminStore.FetchUIConfigurations()
      loading.value = false
    },
  },
])

const columns: ZTableColumn[] = [
  {
    title: 'ID',
    key: 'id',
    sort: true,
    sortBy: SortBy.Number,
  },
  {
    title: 'Type',
    key: 'type',
    sort: true,
    sortBy: SortBy.String,
    scopedSlots: true,
  },
  {
    title: 'Action',
    key: 'action',
    scopedSlots: true,
    align: Align.Right,
  },
]
</script>

<template>
  <div class="page-settings-ui-configurations py-2">
    <ZTablePro v-model:query="query" :columns="columns" :data-source="uiConfigurations" :loading="loading">
      <template #head>
        <div class="bold-text text-xl">
          UI Configurations
        </div>
      </template>
      <template #type="{ item }">
        <span class="capitalize">{{ item.type.replace('_', ' ') }}</span>
      </template>
      <template #action="{ item }">
        <span class="mr-4 cursor-pointer" @click="modalUpdate?.openModal(item)">
          <ZIcon type="edit" class="text-[20px]" />
        </span>
      </template>
      <template #foot>
        <ZPagination v-model="query.page" v-model:page-size="query.limit" :loading="loading" :total="total" />
      </template>
    </ZTablePro>
    <ModalUpdate ref="modalUpdate" />
  </div>
</template>

<style lang="less">
.page-settings-ui-configurations {
}
</style>
