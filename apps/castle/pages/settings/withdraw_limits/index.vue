<script setup lang="ts">
import { capitalize } from '@zsmartex/utils'
import { useOfflineData } from '@zsmartex/core/composables'
import { FilterType } from '~/types'
import ModalWithdrawLimit from '~/layouts/admin/settings/withdraw_limit/ModalWithdrawLimit.vue'

const adminStore = useAdminStore()
const { query } = useQuery()
const total = useState(() => 0)
const loading = ref(false)
const tempDelete = ref<Record<string, boolean>>({})
const filterStore = useFilterStore()
const menuActionStore = useMenuActionStore()

const modalWithdrawLimit = ref<InstanceType<typeof ModalWithdrawLimit>>()

const withdraw_limits = computed(() => {
  const data = adminStore.withdraw_limits.map(item => ({
    ...item,
  }))

  total.value = data.length

  return useOfflineData(query, data)
})

const columns: ZTableColumn[] = [
  {
    title: 'ID',
    key: 'id',
    sort: true,
    sortBy: SortBy.Number,
  },
  {
    title: 'Group',
    key: 'group',
    overflow: true,
    sort: true,
    sortBy: SortBy.String,
    scopedSlots: true,
  },
  {
    title: 'KYC Level',
    key: 'kyc_level',
  },
  {
    title: 'Limit',
    key: 'limit',
    overflow: true,
    sort: true,
    sortBy: SortBy.Number,
  },
  {
    title: 'Action',
    key: 'action',
    align: Align.Right,
    scopedSlots: true,
  },
]

filterStore.setFilter([
  {
    key: 'group',
    label: 'Group',
    type: FilterType.Input,
  },
  {
    key: 'kyc_level',
    label: 'KYC Level',
    type: FilterType.Input,
  },
])

filterStore.onSubmit(async (payload) => {
  query.value = {
    page: 1,
    limit: query.value.limit,
    ...payload,
  }
})

menuActionStore.setMenu([
  {
    key: 'refresh',
    title: 'Refresh',
    callback: async () => {
      loading.value = true
      await adminStore.FetchWithdrawLimits()
      loading.value = false
    },
  },
  {
    key: 'create withdraw limit',
    icon: 'plus',
    title: 'Create Withdraw Limit',
    callback: () => {
      modalWithdrawLimit.value?.openModal('create')
    },
  },
])

async function DeleteWithdrawLimit(item: WithdrawLimit) {
  if (item.id) {
    await adminStore.DeleteWithdrawLimit(item.id, () => {
      const index = adminStore.withdraw_limits.findIndex(t => t.id === Number(item.id))
      if (index !== -1) {
        adminStore.withdraw_limits.splice(index, 1)
      }
    })
  }
}
</script>

<template>
  <div class="page-dashboard-settings-withdraw-limit py-2">
    <ZTablePro v-model:query="query" :columns="columns" :data-source="withdraw_limits" :loading="loading">
      <template #head>
        <div class="bold-text text-xl">
          Withdraw Limits
        </div>
      </template>
      <template #group="{ item }">
        {{ capitalize(item.group) }}
      </template>
      <template #action="{ item }">
        <span class="mr-4 cursor-pointer" @click="modalWithdrawLimit?.openModal('update', item)">
          <ZIcon type="edit" class="text-[20px]" />
        </span>
        <ZPopconfirm v-model="tempDelete[item.id]" :placement="Placement.TopRight" trigger="click" @click="DeleteWithdrawLimit(item)">
          <ZIcon type="trash" class="text-[20px] cursor-pointer" />
        </ZPopconfirm>
      </template>
      <template #foot>
        <ZPagination v-model="query.page" v-model:page-size="query.limit" :loading="loading" :total="total" />
      </template>
    </ZTablePro>
    <ModalWithdrawLimit ref="modalWithdrawLimit" />
  </div>
</template>

<style lang="less">
.page-dashboard-settings-withdraw-limit {
  .id {
    max-width: 80px;
  }

  .kind {
    max-width: 120px;
  }

  .max_balance {
    max-width: 180px;
  }

  .status {
    max-width: 80px;
  }

  .action {
    max-width: 80px;
  }

  .z-table {
    a {
      color: @text-color;
      transition: all 0.3s;

      &:hover {
        color: @text-color;
        text-decoration: underline;
      }
    }
  }
}
</style>
