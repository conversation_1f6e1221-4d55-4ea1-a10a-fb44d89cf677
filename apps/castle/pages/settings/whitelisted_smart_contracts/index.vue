<script setup lang="ts">
import { useOfflineData } from '@zsmartex/core/composables'
import { WhitelistedSmartContractState, FilterType } from '~/types'
import ModalWhitelistedSmartContract from '~/layouts/admin/settings/whitelisted_smart_contract/ModalWhitelistedSmartContract.vue'

const adminStore = useAdminStore()
const menuActionStore = useMenuActionStore()
const filterStore = useFilterStore()
const total = useState(() => 0)
const loading = useState(() => false)
const { query } = useQuery()
const tempDelete = useState<Record<string, boolean>>(() => ({}))
const tempDeleteLoading = useState<Record<string, boolean>>(() => ({}))

const modalWhitelistedSmartContract = ref<InstanceType<typeof ModalWhitelistedSmartContract>>()

const whitelisted_smart_contracts = computed(() => {
  const data = adminStore.whitelisted_smart_contracts.map(item => ({
    ...item,
  }))

  total.value = data.length

  return useOfflineData(query, data)
})

menuActionStore.setMenu([
  {
    key: 'refresh',
    title: 'Refresh',
    callback: async () => {
      loading.value = true
      await adminStore.FetchWhitelistedSmartContracts()
      loading.value = false
    },
  },
  {
    key: 'create whitelisted smart contract',
    icon: 'plus',
    title: 'Create Whitelisted Smart Contract',
    callback: () => {
      modalWhitelistedSmartContract.value?.openModal('create')
    },
  },
])

const columns: ZTableColumn[] = [
  {
    title: 'ID',
    key: 'id',
  },
  {
    title: 'Blockchain Key',
    key: 'blockchain_key',
  },
  {
    title: 'State',
    key: 'state',
  },
  {
    title: 'Address',
    key: 'address',
  },
  {
    title: 'Description',
    key: 'description',
  },
  {
    title: 'Date',
    key: 'updated_at',
    formatBy: Format.DateTime,
    parse: ParseType.DateTime,
  },
  {
    title: 'Action',
    key: 'action',
    align: Align.Right,
    scopedSlots: true,
  },
]

async function DeleteWhitelistedSmartContract(item: WhitelistedSmartContract) {
  const index = adminStore.whitelisted_smart_contracts.findIndex(address => address.id === item.id)
  if (index !== -1 && item.id) {
    tempDeleteLoading.value[item.id] = true

    await adminStore.DeleteWhitelistedSmartContract(item.id)

    tempDeleteLoading.value[item.id] = false
  }
}

filterStore.setFilter([
  {
    key: 'blockchain_key',
    label: 'Blockchain',
    type: FilterType.Select,
    data: adminStore.blockchains,
    columns: [
      {
        key: 'name',
      },
    ],
    findBy: ['name'],
    valueKey: 'key',
    labelKey: 'name',
  },
  {
    key: 'state',
    label: 'State',
    type: FilterType.Select,
    data: [
      {
        value: WhitelistedSmartContractState.Active,
      },
      {
        value: WhitelistedSmartContractState.Disabled,
      },
    ],
    columns: [
      {
        key: 'value',
        scopedSlots: true,
      },
    ],
    replaceFunc: (text: string) => text[0].toUpperCase() + text.slice(1),
    findBy: ['value'],
    valueKey: 'value',
    labelKey: 'value',
  },
])

filterStore.onSubmit<Record<string, string>>(async (payload) => {
  query.value = {
    page: 1,
    ...payload,
  }

  if (!Number(payload.time_from) || !Number(payload.time_to)) {
    delete query.value.date
  }
})
</script>

<template>
  <div class="page-settings-whitelisted-smart-contract py-2">
    <ZTablePro
      v-model:query="query"
      :columns="columns"
      :data-source="whitelisted_smart_contracts"
      :loading="loading"
    >
      <template #head>
        <div class="bold-text text-xl">
          Whitelisted Smart Contract
        </div>
      </template>
      <template #action="{ item }">
        <span class="mr-4 cursor-pointer" @click="modalWhitelistedSmartContract?.openModal('update', item)">
          <ZIcon type="edit" class="text-[20px]" />
        </span>
        <ZPopconfirm v-model="tempDelete[item.id]" :placement="Placement.TopRight" trigger="click" @click="DeleteWhitelistedSmartContract(item)">
          <ZButton class="delete" :loading="tempDeleteLoading[item.id]">
            Delete
          </ZButton>
        </ZPopconfirm>
      </template>
      <template #foot>
        <ZPagination v-model="query.page" v-model:page-size="query.limit" :loading="loading" :total="total" />
      </template>
    </ZTablePro>
    <ModalWhitelistedSmartContract ref="modalWhitelistedSmartContract" />
  </div>
</template>

<style lang="less">
.page-settings-whitelisted-smart-contract {
  .action {
    max-width: 200px;
  }
}
</style>
