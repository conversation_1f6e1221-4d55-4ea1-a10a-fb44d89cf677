<script setup lang="ts">
import { useOfflineData } from '@zsmartex/core/composables'
import { capitalize, uppercase } from '@zsmartex/utils'
import { FilterType, WalletKind, WalletStatus } from '~/types'

const adminStore = useAdminStore()

const { query } = useQuery()
const total = useState(() => 0)
const loading = useState(() => false)
const tempLoading = useState<Record<string, boolean>>(() => ({}))
const filterStore = useFilterStore()
const menuActionStore = useMenuActionStore()

const wallets = computed(() => {
  const data = adminStore.wallets.map(item => ({
    ...item,
  }))

  total.value = data.length

  return useOfflineData(query, data)
})

menuActionStore.setMenu([
  {
    key: 'refresh',
    title: 'Refresh',
    callback: async () => {
      loading.value = true
      await adminStore.FetchWallets()
      loading.value = false
    },
  },
  {
    key: 'create wallet',
    icon: 'plus',
    title: 'Create Wallet',
    callback: () => {
      navigateTo({
        path: '/settings/wallets/create',
      })
    },
  },
])

const columns: ZTableColumn[] = [
  {
    title: 'ID',
    key: 'id',
    sort: true,
    sortBy: SortBy.Number,
  },
  {
    title: 'Name',
    key: 'name',
    overflow: true,
    sort: true,
    sortBy: SortBy.String,
  },
  {
    title: 'Kind',
    key: 'kind',
    scopedSlots: true,
    sort: true,
    sortBy: SortBy.String,
  },
  {
    title: 'Address',
    key: 'address',
    overflow: true,
    scopedSlots: true,
  },
  {
    title: 'Max Balance',
    key: 'max_balance',
    sort: true,
    sortBy: SortBy.Number,
  },
  {
    title: 'Status',
    key: 'status',
    scopedSlots: true,
  },
  {
    title: 'Action',
    key: 'action',
    align: Align.Right,
    scopedSlots: true,
  },
]

const kinds = [
  {
    value: WalletKind.Cold,
  },
  {
    value: WalletKind.Deposit,
  },
  {
    value: WalletKind.Fee,
  },
  {
    value: WalletKind.Hot,
  },
  {
    value: WalletKind.Warm,
  },
]

async function UpdateWalletStatus(item: any) {
  tempLoading.value[item.id] = true
  await adminStore.UpdateWallet(
    {
      id: item.id,
      status: item.status === WalletStatus.Active ? WalletStatus.Disabled : WalletStatus.Active,
    },
  )
  tempLoading.value[item.id] = false
  item.status = item.status === WalletStatus.Active ? WalletStatus.Disabled : WalletStatus.Active
}

filterStore.setFilter([
  {
    key: 'blockchain_key',
    label: 'Blockchain Key',
    type: FilterType.Select,
    data: adminStore.blockchains,
    scroll: true,
    columns: [
      {
        key: 'key',
      },
    ],
    findBy: ['key'],
    valueKey: 'key',
    labelKey: 'key',
  },
  {
    key: 'kind',
    label: 'Kind',
    type: FilterType.Select,
    columns: [
      {
        key: 'value',
        scopedSlots: true,
      },
    ],
    replaceFunc: capitalize,
    data: kinds,
    findBy: ['value'],
    valueKey: 'value',
    labelKey: 'value',
    scroll: true,
  },
  {
    key: 'gateway',
    label: 'Gateway',
    type: FilterType.Select,
    columns: [
      {
        key: 'value',
      },
    ],
    replaceFunc: uppercase,
    data: adminStore.blockchainClients.map((item) => {
      return {
        value: item,
      }
    }),
    findBy: ['value'],
    valueKey: 'value',
    labelKey: 'value',
  },
])

filterStore.onSubmit(async (payload) => {
  query.value = {
    page: 1,
    limit: query.value.limit,
    ...payload,
  }
})

function ExplorerAddress(wallet: Wallet) {
  const blockchain = adminStore.blockchains.find(b => b.key === wallet.blockchain_key)
  if (blockchain && blockchain.explorer_address) {
    return blockchain.explorer_address.replace('#{address}', wallet.address)
  }

  return ''
}
</script>

<template>
  <div class="page-dashboard-accountings-wallets py-2">
    <ZTablePro v-model:query="query" :columns="columns" :data-source="wallets" :loading="loading">
      <template #head>
        <div class="bold-text text-xl">
          Wallets
        </div>
      </template>
      <template #kind="{ item }">
        <span class="capitalize">{{ item.kind }}</span>
      </template>
      <template #status="{ item }">
        <ZSwitch :model-value="item.status === WalletStatus.Active ? true : false" :loading="tempLoading[item.id]" size="medium" @change="UpdateWalletStatus(item)" />
      </template>
      <template #action="{ item }">
        <NuxtLink :to="`/settings/wallets/update/${item.id}`">
          <ZIcon type="edit" class="text-[20px]" />
        </Nuxtlink>
      </template>
      <template #address="{ item }">
        <NuxtLink :to="ExplorerAddress(item)" target="_blank">
          {{ item.address }}
        </NuxtLink>
      </template>
      <template #foot>
        <ZPagination v-model="query.page" v-model:page-size="query.limit" :loading="loading" :total="total" />
      </template>
    </ZTablePro>
  </div>
</template>

<style lang="less">
.page-dashboard-accountings-wallets {
  .id {
    max-width: 80px;
  }

  .kind {
    max-width: 120px;
  }

  .max_balance {
    max-width: 180px;
  }

  .status {
    max-width: 80px;
  }

  .action {
    max-width: 80px;
  }

  .z-table {
    a {
      color: @text-color;
      transition: all 0.3s;

      &:hover {
        color: @text-color;
        text-decoration: underline;
      }
    }
  }
}
</style>
