<script setup lang="ts">
import LayoutWallet from '~/layouts/admin/settings/wallets/LayoutWallet.vue'

const adminStore = useAdminStore()

const id = useRoute().params.id as string

const { data: wallet, pending, refresh } = await useAsyncData(() => adminStore.FetchWallet(id as string).then(res => res.data))

const linkedCurrencies = ref(adminStore.currencies.filter(currency => wallet.value!.currencies?.includes(currency.id as string)))
const tempChecked = ref<Record<string, boolean>>({})

const existingCurrencies = computed(() => {
  return adminStore.currencies.filter(currency => !linkedCurrencies.value.includes(currency))
})

const disableTransferLeft = computed(() => {
  for (const currency of linkedCurrencies.value) {
    if (tempChecked.value[currency.id]) {
      return false
    }
  }
  return true
})

const disableTransferRight = computed(() => {
  for (const currency of existingCurrencies.value) {
    if (tempChecked.value[currency.id]) {
      return false
    }
  }
  return true
})

const leftColumns: ZTableColumn[] = [
  {
    key: 'select',
    scopedSlots: true,
  },
  {
    title: 'Code',
    key: 'id',
    toUpper: true,
  },
  {
    title: 'Name',
    key: 'name',
    align: Align.Right,
  },
]

const rightColumns: ZTableColumn[] = [
  {
    key: 'select',
    scopedSlots: true,
  },
  {
    title: 'Code',
    key: 'id',
    toUpper: true,
  },
  {
    title: 'Name',
    key: 'name',
    align: Align.Right,
  },
]

async function TransferData() {
  const currencies: string[] = []
  for (const currencyId in tempChecked.value) {
    if (tempChecked.value[currencyId]) {
      currencies.push((adminStore.currencies.find(c => c.id === currencyId) as Currency).id as string)
    }
  }

  await adminStore.AddCurrencyToWallet(Number(id), currencies, () => {
    for (const currency of existingCurrencies.value) {
      if (tempChecked.value[currency.id]) {
        linkedCurrencies.value.push(adminStore.currencies.find(c => c.id === currency.id) as Currency)
      }
    }
    tempChecked.value = {}
  })
}

async function DeleteCurrency() {
  const currencies: string[] = []
  for (const currency of linkedCurrencies.value) {
    if (tempChecked.value[currency.id]) {
      currencies.push((adminStore.currencies.find(c => c.id === currency.id) as Currency).id as string)
    }
  }

  await adminStore.RemoveCurrencyFromWallet(Number(id), currencies, () => {
    currencies.forEach((id) => {
      const index = linkedCurrencies.value.findIndex(c => c.id === id)
      if (index !== -1) {
        linkedCurrencies.value.splice(index, 1)
      }
    })
    tempChecked.value = {}
  })
}

function SelectCurrency(currency: Currency) {
  tempChecked.value[currency.id] = !tempChecked.value[currency.id]
}
</script>

<template>
  <div>
    <LayoutWallet :id="id" :model-value="wallet!" :pending="pending" :refresh="refresh" />

    <ZTransferTable
      left-title="Linked Currencies"
      right-title="Existing Currencies"
      :left-columns="leftColumns"
      :right-columns="rightColumns"
      :left-data="linkedCurrencies"
      :right-data="existingCurrencies"
      :disabled-left="disableTransferLeft"
      :disabled-right="disableTransferRight"
      :search-keys="['id', 'name']"
      @transfer-left="DeleteCurrency"
      @transfer-right="TransferData"
      @click-left="SelectCurrency"
      @click-right="SelectCurrency"
    >
      <template #left-select="{ item }">
        <ZCheckbox :model-value="tempChecked[item.id]" @change="SelectCurrency(item)" />
      </template>
      <template #right-select="{ item }">
        <ZCheckbox :model-value="tempChecked[item.id]" @change="SelectCurrency(item)" />
      </template>
    </ZTransferTable>
  </div>
</template>
