<script setup lang="ts">
import { capitalize } from '@zsmartex/utils'
import { useOfflineData } from '@zsmartex/core/composables'
import { FilterType } from '~/types'
import ModalFees from '~/layouts/admin/settings/fees/ModalFees.vue'

const adminStore = useAdminStore()
const { query } = useQuery()
const total = useState(() => 0)
const loading = ref(false)
const tempDelete = ref<Record<string, boolean>>({})
const filterStore = useFilterStore()
const menuActionStore = useMenuActionStore()

const modalFees = ref<InstanceType<typeof ModalFees>>()

const tradingFees = computed(() => {
  const data = adminStore.trading_fees.map(item => ({
    ...item,
  }))

  total.value = data.length

  return useOfflineData(query, data)
})

const columns: ZTableColumn[] = [
  {
    title: 'ID',
    key: 'id',
    sort: true,
    sortBy: SortBy.Number,
  },
  {
    title: 'Group',
    key: 'group',
    overflow: true,
    sort: true,
    sortBy: SortBy.String,
    scopedSlots: true,
  },
  {
    title: 'Market',
    key: 'market',
    scopedSlots: true,
  },
  {
    title: 'Maker fee',
    key: 'maker',
    overflow: true,
    sort: true,
    sortBy: SortBy.Number,
  },
  {
    title: 'Taker fee',
    key: 'taker',
    sort: true,
    sortBy: SortBy.Number,
  },
  {
    title: 'Action',
    key: 'action',
    align: Align.Right,
    scopedSlots: true,
  },
]

function marketBaseUnit(id: string) {
  const market = adminStore.markets.find(m => m.id === id)
  return market ? market.base_unit : ''
}

function marketQuoteUnit(id: string) {
  const market = adminStore.markets.find(m => m.id === id)
  return market ? market.quote_unit : ''
}

filterStore.setFilter([
  {
    key: 'group',
    label: 'Group',
    type: FilterType.Input,
  },
  {
    key: 'market_id',
    label: 'Market ID',
    type: FilterType.Input,
  },
])

filterStore.onSubmit(async (payload) => {
  query.value = {
    page: 1,
    limit: query.value.limit,
    ...payload,
  }
})

menuActionStore.setMenu([
  {
    key: 'refresh',
    title: 'Refresh',
    callback: async () => {
      loading.value = true
      await adminStore.FetchTradingFees()
      loading.value = false
    },
  },
  {
    key: 'create fee',
    icon: 'plus',
    title: 'Create Fee',
    callback: () => {
      modalFees.value?.openModal('create')
    },
  },
])

async function DeleteTradingFee(item: TradingFee) {
  if (item.id) {
    await adminStore.DeleteTradingFee(item.id, () => {
      const index = adminStore.trading_fees.findIndex(t => t.id === Number(item.id))
      if (index !== -1) {
        adminStore.trading_fees.splice(index, 1)
      }
    })
  }
}
</script>

<template>
  <div class="page-dashboard-accountings-fees py-2">
    <ZTablePro v-model:query="query" :columns="columns" :data-source="tradingFees" :loading="loading">
      <template #head>
        <div class="bold-text text-xl">
          Fees Schedule
        </div>
      </template>
      <template #group="{ item }">
        {{ capitalize(item.group) }}
      </template>
      <template #market="{ item }">
        <NuxtLink :to="`/exchange/markets/update/${marketBaseUnit(item.market_id)}${marketQuoteUnit(item.market_id)}`">
          {{ (marketBaseUnit(item.market_id) as string).toUpperCase() }}/{{ (marketQuoteUnit(item.market_id) as string).toUpperCase() }}
        </NuxtLink>
      </template>
      <template #action="{ item }">
        <span class="mr-4 cursor-pointer" @click="modalFees?.openModal('update', item)">
          <ZIcon type="edit" class="text-[20px]" />
        </span>
        <ZPopconfirm v-model="tempDelete[item.id]" :placement="Placement.TopRight" trigger="click" @click="DeleteTradingFee(item)">
          <ZIcon type="trash" class="text-[20px] cursor-pointer" />
        </ZPopconfirm>
      </template>
      <template #foot>
        <ZPagination v-model="query.page" v-model:page-size="query.limit" :loading="loading" :total="total" />
      </template>
    </ZTablePro>
    <ModalFees ref="modalFees" />
  </div>
</template>

<style lang="less">
.page-dashboard-accountings-fees {
  .id {
    max-width: 80px;
  }

  .kind {
    max-width: 120px;
  }

  .max_balance {
    max-width: 180px;
  }

  .status {
    max-width: 80px;
  }

  .action {
    max-width: 80px;
  }

  .z-table {
    a {
      color: @text-color;
      transition: all 0.3s;

      &:hover {
        color: @text-color;
        text-decoration: underline;
      }
    }
  }
}
</style>
