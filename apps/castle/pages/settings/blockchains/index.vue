<script setup lang="ts">
import { useOfflineData } from '@zsmartex/core/composables'
import { uppercase } from '@zsmartex/utils'
import { BlockchainStatus, FilterType } from '~/types'

const adminStore = useAdminStore()

const { query } = useQuery()
const total = useState(() => 0)
const loading = useState(() => false)
const tempLoading = useState<Record<string, boolean>>(() => ({}))
const filterStore = useFilterStore()
const menuActionStore = useMenuActionStore()

const blockchains = computed(() => {
  const data = adminStore.blockchains.map(item => ({
    ...item,
  }))

  total.value = data.length

  return useOfflineData(query, data)
})

menuActionStore.setMenu([
  {
    key: 'refresh',
    title: 'Refresh',
    callback: async () => {
      loading.value = true
      await adminStore.FetchBlockchains()
      loading.value = false
    },
  },
  {
    key: 'create blockchain',
    icon: 'plus',
    title: 'Create Blockchain',
    callback: () => {
      navigateTo({
        path: '/settings/blockchains/create',
      })
    },
  },
])

const columns: ZTableColumn[] = [
  {
    title: 'ID',
    key: 'id',
    sort: true,
    sortBy: SortBy.Number,
  },
  {
    title: 'Name',
    key: 'name',
    sort: true,
    sortBy: SortBy.String,
    overflow: true,
  },
  {
    title: 'Client',
    key: 'client',
    sort: true,
    sortBy: SortBy.String,
    toUpper: true,
  },
  {
    title: 'Height',
    key: 'height',
    sort: true,
    sortBy: SortBy.Number,
  },
  {
    title: 'Status',
    key: 'status',
    scopedSlots: true,
  },
  {
    title: 'Date',
    key: 'created_at',
    formatBy: Format.DateTime,
    parse: ParseType.DateTime,
    sort: true,
    sortBy: SortBy.Date,
    align: Align.Right,
  },
  {
    title: 'Action',
    key: 'action',
    scopedSlots: true,
    align: Align.Right,
  },
]

function capitalize(text: string) {
  return text[0].toUpperCase() + text.slice(1)
}

filterStore.setFilter([
  {
    key: 'key',
    label: 'Key',
    type: FilterType.Select,
    data: adminStore.blockchains,
    scroll: true,
    columns: [
      {
        key: 'key',
      },
    ],
    findBy: ['key'],
    valueKey: 'key',
    labelKey: 'key',
  },
  {
    key: 'client',
    label: 'Client',
    type: FilterType.Select,
    columns: [
      {
        key: 'value',
        scopedSlots: true,
      },
    ],
    replaceFunc: uppercase,
    data: adminStore.blockchainClients.map((item) => {
      return {
        value: item,
      }
    }),
    findBy: ['value'],
    valueKey: 'value',
    labelKey: 'value',
  },
  {
    key: 'status',
    label: 'Status',
    type: FilterType.Select,
    columns: [
      {
        key: 'value',
        scopedSlots: true,
      },
    ],
    replaceFunc: capitalize,
    data: [
      {
        value: BlockchainStatus.Active,
      },
      {
        value: BlockchainStatus.Disabled,
      },
      {
        value: BlockchainStatus.Idle,
      },
    ],
    findBy: ['value'],
    valueKey: 'value',
    labelKey: 'value',
  },
  {
    key: 'name',
    label: 'Name',
    type: FilterType.Input,
  },
])

async function UpdateBlockchainStatus(item: any) {
  tempLoading.value[item.id] = true
  await adminStore.UpdateBlockchain(
    item.id,
    {
      status: item.status === BlockchainStatus.Active ? BlockchainStatus.Disabled : BlockchainStatus.Active,
    },
  )
  tempLoading.value[item.id] = false
  item.status = item.status === BlockchainStatus.Active ? BlockchainStatus.Disabled : BlockchainStatus.Active
}

filterStore.onSubmit(async (payload) => {
  query.value = {
    page: 1,
    limit: query.value.limit,
    ...payload,
  }
})
</script>

<template>
  <div class="page-settings-blockchains py-2">
    <ZTablePro v-model:query="query" :columns="columns" :data-source="blockchains" :loading="loading">
      <template #head>
        <div class="bold-text text-xl">
          Blockchains
        </div>
      </template>
      <template #status="{ item }">
        <ZSwitch :model-value="item.status === BlockchainStatus.Active ? true : false" size="medium" :loading="tempLoading[item.id]" @change="UpdateBlockchainStatus(item)" />
      </template>
      <template #action="{ item }">
        <NuxtLink :to="`/settings/blockchains/update/${item.id}`">
          <ZIcon type="edit" class="text-[20px]" />
        </Nuxtlink>
      </template>
      <template #foot>
        <ZPagination v-model="query.page" v-model:page-size="query.limit" :loading="loading" :total="total" />
      </template>
    </ZTablePro>
  </div>
</template>

<style lang="less">
.page-settings-blockchains {
  &-status {
    &-red {
      color: @error-color;
    }

    &-green {
      color: @up-color;
    }

    &-yellow {
      color: @warn-color;
    }
  }

  .id {
    max-width: 120px;
  }

  .status {
    max-width: 100px;
  }

  .action {
    max-width: 80px;
  }
}
</style>
