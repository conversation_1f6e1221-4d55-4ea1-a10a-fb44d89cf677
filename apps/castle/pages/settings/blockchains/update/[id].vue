<script setup lang="ts">
import LayoutBlockchain from '~/layouts/admin/settings/blockchains/LayoutBlockchain.vue'

const adminStore = useAdminStore()

const blockchainID = useRoute().params.id

const { data: blockchain, pending, refresh } = await useAsyncData(() => adminStore.FetchBlockchain(blockchainID as string).then(res => res.data))
</script>

<template>
  <LayoutBlockchain :blockchain-id="Number(blockchainID)" :model-value="blockchain!" :pending="pending" :refresh="refresh" />
</template>
