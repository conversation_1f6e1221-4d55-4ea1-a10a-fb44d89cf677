<script setup lang="ts">
import { useOfflineData } from '@zsmartex/core/composables'
import { FilterType, RateLimitState } from '~/types'
import ModalRateLimit from '~/layouts/admin/settings/rate_limits/ModalRateLimit.vue'

const adminStore = useAdminStore()

const filterStore = useFilterStore()
const { query } = useQuery()
const total = useState(() => 0)
const loading = useState(() => false)
const tempLoading = useState<Record<string, boolean>>(() => ({}))
const menuActionStore = useMenuActionStore()
const tempDelete = useState<Record<string, boolean>>(() => ({}))
const tempDeleteLoading = useState<Record<string, boolean>>(() => ({}))

const modalRateLimit = ref<InstanceType<typeof ModalRateLimit>>()

const rate_limits = computed(() => {
  const data = adminStore.rate_limits.map(item => ({
    ...item,
  }))

  total.value = data.length

  return useOfflineData(query, data)
})

menuActionStore.setMenu([
  {
    key: 'refresh',
    title: 'Refresh',
    callback: async () => {
      loading.value = true
      await adminStore.FetchRateLimits()
      loading.value = false
    },
  },
  {
    key: 'create rate limit',
    icon: 'plus',
    title: 'Create Rate Limit',
    callback: () => {
      modalRateLimit.value?.openModal('create')
    },
  },
])

filterStore.setFilter([
  {
    key: 'path',
    label: 'Path',
    type: FilterType.Input,
  },
  {
    key: 'verb',
    label: 'Verb',
    type: FilterType.Select,
    columns: [
      {
        key: 'name',
        scopedSlots: true,
      },
    ],
    replaceFunc: (text: string) => text.toUpperCase(),
    data: [
      {
        name: 'ALL',
      },
      {
        name: 'GET',
      },
      {
        name: 'POST',
      },
      {
        name: 'PUT',
      },
      {
        name: 'PATCH',
      },
      {
        name: 'DELETE',
      },
      {
        name: 'OPTIONS',
      },
      {
        name: 'HEAD',
      },
    ],
    findBy: ['name'],
    valueKey: 'name',
    labelKey: 'name',
  },
])

const columns: ZTableColumn[] = [
  {
    title: 'ID',
    key: 'id',
    sort: true,
    sortBy: SortBy.Number,
  },
  {
    title: 'Max',
    key: 'max',
    sort: true,
    sortBy: SortBy.Number,
  },
  {
    title: 'Duration (s)',
    key: 'duration',
    sort: true,
    sortBy: SortBy.Number,
  },
  {
    title: 'Verb',
    key: 'verb',
    sort: true,
    sortBy: SortBy.String,
  },
  {
    title: 'State',
    key: 'state',
    scopedSlots: true,
  },
  {
    title: 'Path',
    key: 'path',
    sort: true,
    sortBy: SortBy.String,
    overflow: true,
  },
  {
    title: 'Action',
    key: 'action',
    scopedSlots: true,
    align: Align.Right,
  },
]

async function UpdateRateLimitState(item: any) {
  tempLoading.value[item.id] = true
  await adminStore.UpdateRateLimit(
    item.id,
    {
      state: item.state === RateLimitState.Active ? RateLimitState.Disabled : RateLimitState.Active,
    },
  )
  tempLoading.value[item.id] = false
  item.state = item.state === RateLimitState.Active ? RateLimitState.Disabled : RateLimitState.Active
}

async function DeleteRateLimit(item: RateLimit) {
  const index = adminStore.rate_limits.findIndex(rateLimit => rateLimit.id === item.id)
  if (index !== -1 && item.id) {
    tempDeleteLoading.value[item.id] = true

    await adminStore.DeleteRateLimit(item.id)

    tempDeleteLoading.value[item.id] = false
  }
}

filterStore.onSubmit(async (payload) => {
  query.value = {
    page: 1,
    limit: query.value.limit,
    ...payload,
  }
})
</script>

<template>
  <div class="page-settings-rate-limits py-2">
    <ZTablePro v-model:query="query" :columns="columns" :data-source="rate_limits" :loading="loading">
      <template #head>
        <div class="bold-text text-xl">
          Rate Limits
        </div>
      </template>
      <template #state="{ item }">
        <ZSwitch :model-value="item.state === RateLimitState.Active ? true : false" size="medium" :loading="tempLoading[item.id]" @change="UpdateRateLimitState(item)" />
      </template>
      <template #action="{ item }">
        <span class="mr-4 cursor-pointer" @click="modalRateLimit?.openModal('update', item)">
          <ZIcon type="edit" class="text-[20px]" />
        </span>
        <ZPopconfirm v-model="tempDelete[item.id]" :placement="Placement.TopRight" trigger="click" @click="DeleteRateLimit(item)">
          <ZButton class="delete" :loading="tempDeleteLoading[item.id]">
            Delete
          </ZButton>
        </ZPopconfirm>
      </template>
      <template #foot>
        <ZPagination v-model="query.page" v-model:page-size="query.limit" :loading="loading" :total="total" />
      </template>
    </ZTablePro>
    <ModalRateLimit ref="modalRateLimit" />
  </div>
</template>

<style lang="less">
.page-settings-rate-limits {
  .id {
    max-width: 120px;
  }

  .state {
    max-width: 100px;
  }

  .action {
    max-width: 80px;
  }
}
</style>
