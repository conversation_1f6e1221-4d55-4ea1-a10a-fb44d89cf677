<script setup lang="ts">
import { Bar } from 'vue-chartjs'
import { intervalToDuration } from 'date-fns'
import {
  BarElement,
  CategoryScale,
  Chart,
  Legend,
  LinearScale,
  Title,
  Tooltip,
} from 'chart.js'
import colors from '~/colors'

definePageMeta({
  middleware: ['backup-monitoring'],
})

const adminStore = useAdminStore()
const loading = useState(() => false)
const menuActionStore = useMenuActionStore()

menuActionStore.setMenu([
  {
    key: 'refresh',
    title: 'Refresh',
    callback: async () => {
      loading.value = true
      Promise.all([
        adminStore.FetchStatsDatabase(),
        adminStore.FetchStatsVault(),
      ]).then(() => {
        loading.value = false
      })
    },
  },
])

const chartDatabaseOptions = {
  responsive: true,
  maintainAspectRatio: false,
  scales: {
    x: {
      grid: {
        display: false,
        lineWidth: 1,
      },
      ticks: {
        display: false,
      },
    },
    y: {
      max: 3,
      border: {
        dash: [20, 5],
      },
      grid: {
        display: true,
        lineWidth: 1,
      },
      ticks: {
        display: false,
      },
    },
  },
  interaction: {
    intersect: false,
    mode: 'index',
  },
  plugins: {
    legend: {
      display: false,
    },
    tooltip: {
      position: 'average',
      callbacks: {
        label(context: any) {
          return context.parsed.y === 2 ? 'Online' : 'Offline'
        },
      },
    },
  },
  layout: {
    padding: {
      top: 12,
      left: 12,
      bottom: 0,
      right: 12,
    },
  },
} as any

function GetChartVaultOptions(vault: StatVault) {
  let max = 0

  vault.values.forEach((item) => {
    if (Number(item[1]) > max) max = Number(item[1])
  })

  return {
    responsive: true,
    maintainAspectRatio: false,
    scales: {
      x: {
        grid: {
          display: false,
          lineWidth: 1,
        },
        ticks: {
          display: false,
        },
      },
      y: {
        max: max * 1.5,
        border: {
          dash: [20, 5],
        },
        grid: {
          display: true,
          lineWidth: 1,
        },
        ticks: {
          display: false,
        },
      },
    },
    interaction: {
      intersect: false,
      mode: 'index',
    },
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        position: 'average',
        callbacks: {
          label(context: any) {
            return `${(Number(context.parsed.y) / 1000).toFixed(1)}s`
          },
        },
      },
    },
    layout: {
      padding: {
        top: 12,
        left: 12,
        bottom: 0,
        right: 12,
      },
    },
  } as any
}

if (process.client) {
  Chart.register(
    CategoryScale,
    LinearScale,
    BarElement,
    Title,
    Tooltip,
    Legend,
  )
}

function FormatChartDataDatabase(data: (string | number)[][]) {
  const labels: string[] = []

  const now = new Date()
  data.forEach((item) => {
    const timestamp = FormatDistanceTime(now, new Date((item[0] as number) * 1000))
    labels.push(timestamp)
  })

  return {
    labels,
    datasets: [
      {
        label: 'number',
        data: data.map(item => item[1] !== '2' ? '1' : '2'),
        backgroundColor: data.map(item => item[1] === '2' ? colors['up-color'] : colors['down-color']),
      },
    ],
  } as any
}

function FormatChartDataVault(data: (string | number)[][]) {
  const labels: string[] = []

  const now = new Date()
  data.forEach((item) => {
    const timestamp = FormatDistanceTime(now, new Date((item[0] as number) * 1000))
    labels.push(timestamp)
  })

  return {
    labels,
    datasets: [
      {
        label: 'number',
        data: data.map(item => item[1]),
        backgroundColor: data.map(item => (Number(item[1]) / 1000) <= 5 ? colors['up-color'] : (Number(item[1]) / 1000) <= 10 ? colors['warn-color'] : colors['down-color']),
      },
    ],
  } as any
}

function FormatDistanceTime(date1: Date, date2: Date) {
  const obj = intervalToDuration({ start: date2, end: date1 })

  if (!obj.hours && !obj.minutes) return 'now'

  let result = 'ago'
  if (obj.minutes) {
    result = `${obj.minutes} minutes ${result}`
  }

  if (obj.hours) {
    result = `${obj.hours} hours ${result}`
  }

  return result
}
</script>

<template>
  <div class="page-dashboard-settings-backup-monitoring py-2">
    <div class="flex justify-between flex-wrap">
      <ZCard v-for="(db, index) in adminStore.statsDatabase" :key="index" class="card mb-8">
        <ZLoading v-if="loading" />
        <div v-else class="card-content">
          <div class="page-dashboard-settings-backup-monitoring-head">
            <div :class="`status ${db.values[db.values.length - 1][1] !== '2' ? 'status-red' : 'status-green'}`" />
            Postgres: {{ db.metric.server }}
          </div>
          <div class="flex-auto">
            <Bar :options="chartDatabaseOptions" :data="FormatChartDataDatabase(db.values)" />
          </div>
        </div>
      </ZCard>
    </div>
    <div class="flex justify-between flex-wrap">
      <ZCard v-for="(vault, index) in adminStore.statsVault" :key="index" class="card mb-8">
        <ZLoading v-if="loading" />
        <div v-else class="card-content">
          <div class="page-dashboard-settings-backup-monitoring-head">
            <div :class="`status ${(Number(vault.values[vault.values.length - 1][1]) / 1000) <= 5 ? 'status-green' : (Number(vault.values[vault.values.length - 1][1]) / 1000) <= 10 ? 'status-warn' : 'status-red'}`" />
            Vault: {{ vault.metric.peer_id }}
          </div>
          <div class="flex-auto">
            <Bar :options="GetChartVaultOptions(vault)" :data="FormatChartDataVault(vault.values)" />
          </div>
        </div>
      </ZCard>
    </div>
  </div>
</template>

<style lang="less">
.page-dashboard-settings-backup-monitoring {
  .z-card {
    padding: 0;
  }

  &-head {
    display: flex;
    align-items: center;
    padding: 16px;
    font-size: 16px;
    background-color: rgba(@gray-color, 0.15);
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    border-bottom: 1px solid rgba(@gray-color, 0.2);
  }

  &-time {
    padding: 16px;
    font-size: 16px;
    text-align: right;
  }

  .card {
    width: 32%;
    height: 390px;

    &-content {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      height: 100%;
    }
  }

  .status {
    margin-right: 4px;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background-color: gray;

    &-green {
      background-color: @up-color;
    }

    &-warn {
      background-color: @warn-color;
    }

    &-red {
      background-color: @down-color;
    }
  }
}
</style>
