<script setup lang="ts">
import { VueDraggable } from '@zsmartex/draggable'
import { BannerState, FilterType } from '~/types'
import ModalBanner from '~/layouts/admin/settings/banners/ModalBanner.vue'
import Validate from '~/validation/validate'

const adminStore = useAdminStore()

const { query, callbacks } = useQuery()
const image = useState(() => '')
const visible = useState(() => false)

const tempDelete = useState<Record<string, boolean>>(() => ({}))
const tempDeleteLoading = useState<Record<string, boolean>>(() => ({}))
const tempUrl = useState<Record<string, string>>(() => ({}))
const tempUrlLoading = useState<Record<string, boolean>>(() => ({}))
const tempImage = useState<Record<string, File>>(() => ({}))

const uuid = useState(() => '')
const createUrl = useState(() => '')
const createImage = useState<Blob | undefined>()
const createLoading = useState(() => false)
const isCreate = useState(() => false)

const filterStore = useFilterStore()
const menuActionStore = useMenuActionStore()

const { data: banners, pending, refresh } = await useAsyncData(async () => {
  const { data } = await adminStore.FetchBanners()

  data.forEach((b) => {
    tempUrl.value[b.uuid] = b.url
  })

  return data
}, { default: () => ([]) })

callbacks.push(refresh)

menuActionStore.setMenu([
  {
    key: 'refresh',
    title: 'Refresh',
    callback: refresh,
  },
])

async function UpdateBanner(params: { uuid: string; url?: string; state?: string }, callback?: () => void) {
  const data = new FormData()
  if (params.uuid) data.append('uuid', params.uuid)
  if (params.url) data.append('url', params.url)
  if (params.state) data.append('state', params.state)
  if (tempImage.value[params.uuid]) data.append('image', tempImage.value[params.uuid])

  await adminStore.UpdateBanner(data, callback)
}

function showImage(item: Banner, type: string) {
  if (type === 'create') {
    isCreate.value = true
    visible.value = true
    image.value = createImage.value ? URL.createObjectURL(createImage.value) : ''
  } else {
    visible.value = true
    isCreate.value = false
    uuid.value = item.uuid as string
    image.value = `/api/v2/kouda/public/banners/${item.uuid}`
  }
}

function onImageChange(e: any) {
  if (isCreate.value) {
    image.value = URL.createObjectURL(e.target.files[0])
    createImage.value = e.target.files[0]
  } else {
    image.value = URL.createObjectURL(e.target.files[0])
    tempImage.value[uuid.value] = e.target.files[0]
  }
}

async function ChangeUrlBanner(item: Banner) {
  const index = banners.value.findIndex(banner => banner.uuid === item.uuid)
  if (index !== -1 && item.uuid) {
    tempUrlLoading.value[item.uuid] = true

    await UpdateBanner({
      uuid: item.uuid,
      url: tempUrl.value[item.uuid],
    }, () => {
      banners.value[index].url = tempUrl.value[item.uuid as string]
    })

    tempUrlLoading.value[item.uuid] = false
  }
}

async function UpdateBannerPosition(item: Banner, position: number) {
  const index = banners.value.findIndex(banner => banner.uuid === item.uuid)
  if (index !== -1 && item.uuid && position >= 1 && position !== item.position) {
    await adminStore.UpdateBannerPosition(item.uuid, position, () => {
      if (position < item.position) {
        for (let i = position; i < item.position; i++) {
          banners.value[i].position++
        }
        item.position = position
      } else {
        for (let i = item.position - 1; i < position; i++) {
          banners.value[i].position--
        }
        item.position = position
      }
    })
  }
}

async function CreateBanner() {
  createLoading.value = true

  const data = new FormData()
  if (createUrl.value) data.append('url', createUrl.value)
  data.append('state', 'enabled')
  if (createImage.value) data.append('image', createImage.value)

  const banner = await adminStore.CreateBanner(data, async () => {
    image.value = ''
    createUrl.value = ''
    createImage.value = undefined
  })

  if (banner) {
    banners.value.push(banner)
    tempUrl.value[banner.uuid] = banner.url
  }

  createLoading.value = false
}

async function DeleteBanner(item: Banner) {
  const index = banners.value.findIndex(banner => banner.uuid === item.uuid)
  if (index !== -1 && item.uuid) {
    tempDeleteLoading.value[item.uuid] = true

    await adminStore.DeleteBanner(item.uuid, () => {
      banners.value.splice(index, 1)
      for (let i = index; i < banners.value.length; i++) {
        banners.value[i].position--
      }
    })

    tempDeleteLoading.value[item.uuid] = false
  }
}

function capitalize(text: string) {
  return text[0].toUpperCase() + text.slice(1)
}

filterStore.setFilter([
  {
    key: 'date',
    label: 'Date',
    type: FilterType.DateRange,
  },
  {
    key: 'state',
    label: 'State',
    type: FilterType.Select,
    columns: [
      {
        key: 'value',
        scopedSlots: true,
      },
    ],
    replaceFunc: capitalize,
    data: [
      {
        value: BannerState.Enabled,
      },
      {
        value: BannerState.Disabled,
      },
    ],
    findBy: ['value'],
    valueKey: 'value',
    labelKey: 'value',
  },
])

filterStore.onSubmit<Record<string, string>>(async (payload) => {
  query.value = {
    page: 1,
    ...payload,
  }

  if (!Number(payload.time_from) || !Number(payload.time_to)) {
    delete query.value.date
  }
})

async function onUpdate(e) {
  await UpdateBannerPosition(banners.value[e.newIndex], Number(e.newIndex + 1))
}
</script>

<template>
  <div>
    <div class="page-dashboard-settings-banners py-2">
      <ZCard>
        <div class="bold-text text-xl px-[24px] py-[16px]">
          Banners
        </div>

        <div class="drag-head">
          <div class="drag-column">
            <span>UUID</span>
          </div>
          <div class="drag-column">
            <span>URL</span>
          </div>
          <div class="drag-column max-w-[100px] text-center">
            <span>Position</span>
          </div>
          <div class="drag-column max-w-[180px] text-right">
            <span>Action</span>
          </div>
        </div>
        <VueDraggable
          v-model="banners"
          :animation="150"
          @update="onUpdate"
        >
          <div
            v-for="item, index in banners"
            :key="index"
            class="drag-row"
          >
            <div class="drag-column">
              <span>{{ item.uuid }}</span>
            </div>
            <div class="drag-column">
              <ZAdminInput v-model="tempUrl[item.uuid]" :validate="[Validate.equal(item.url)]">
                <template v-if="tempUrl[item.uuid] && tempUrl[item.uuid] !== item.url" #suffix>
                  <span
                    v-if="tempUrlLoading[item.uuid]"
                    class="user-setting-button pr-2"
                    :class="{ 'user-setting-button-loading': pending }"
                  >
                    Saving...
                  </span>
                  <span
                    v-else
                    class="user-setting-button pr-2"
                    @click="ChangeUrlBanner(item)"
                  >
                    Save
                  </span>
                </template>
              </ZAdminInput>
            </div>
            <div class="drag-column max-w-[100px]">
              <div class="flex justify-center">
                {{ item.position }}
              </div>
            </div>
            <div class="w-[180px]">
              <div class="flex justify-end">
                <ZButton @click="showImage(item, 'update')">
                  Image
                </ZButton>
                <ZPopconfirm v-model="tempDelete[item.uuid]" :placement="Placement.TopRight" trigger="click" @click="DeleteBanner(item)">
                  <ZButton class="delete" :loading="tempDeleteLoading[item.uuid]">
                    Delete
                  </ZButton>
                </ZPopconfirm>
              </div>
            </div>
          </div>
        </VueDraggable>
        <div class="drag-row">
          <div class="drag-column text-left">
            AUTO
          </div>
          <div class="drag-column text-left">
            <ZAdminInput v-model="createUrl" placeholder="URL" />
          </div>
          <div class="drag-column max-w-[100px]" />
          <div class="drag-column max-w-[180px]">
            <div class="text-right flex-1 flex justify-end">
              <ZButton ref="button" type="button" @click="() => showImage({} as Banner, 'create')">
                Image
              </ZButton>
              <ZButton
                type="button"
                :disabled="!!Validate.required(createUrl)"
                :loading="createLoading"
                @click="CreateBanner"
              >
                Create
              </ZButton>
            </div>
          </div>
        </div>
      </ZCard>
    </div>
    <ModalBanner
      v-model="visible"
      :image="image"
      :is-create="isCreate"
      @change="onImageChange"
      @submit="UpdateBanner({ uuid })"
    />
  </div>
</template>

<style lang="less">
.page-dashboard-settings-banners {
  .z-card {
    padding: 0;
    padding-bottom: 24px;
  }

  .drag {
    &-head {
      padding: 0 24px;
      display: flex;
      align-items: center;
      width: 100%;
      height: 32px;
      color: @gray-color;
    }

    &-row {
      padding: 0 24px;
      display: flex;
      align-items: center;
      width: 100%;
      height: 48px;
      border-top: 1px solid rgba(33, 47, 79, 0.1);
    }

    &-column {
      flex: 1;
      display: block;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
  .image_url {
    padding: 2px 12px;
    max-width: 600px;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;

    span {
      display: inline;
    }
  }

  .z-table-pro {
    .z-table-row {
      height: 48px;
    }
  }

  .state {
    max-width: 100px;
  }

  .action {
    width: 156px;
  }

  .z-button {
    margin-left: 12px;
    padding: 0 12px;
    height: 32px;

    &.delete {
      color: @error-color;
      border: 1px solid @error-color;
      background-color: white;

      &:hover {
        background-color: @error-color !important;
        color: white;
      }
    }
  }

  &-image-example {
    width: 322px;
    height: 214px;
    background: url(https://b.peatio.com/kyc/id-front.png) no-repeat #fff;
    background-size: cover;
    padding: 24px;
  }
}
</style>
