<script setup lang="ts">
import { useOfflineData } from '@zsmartex/core/composables'
import { FilterType } from '~/types'
import ModalBlacklistAddress from '~/layouts/admin/settings/blacklist_address/ModalBlacklistAddress.vue'

const adminStore = useAdminStore()
const menuActionStore = useMenuActionStore()
const filterStore = useFilterStore()
const total = useState(() => 0)
const loading = useState(() => false)
const { query } = useQuery()
const tempDelete = useState<Record<string, boolean>>(() => ({}))
const tempDeleteLoading = useState<Record<string, boolean>>(() => ({}))

const modalBlacklistAddress = ref<InstanceType<typeof ModalBlacklistAddress>>()

const blacklist_addresses = computed(() => {
  const data = adminStore.blacklist_addresses.map(item => ({
    ...item,
  }))

  total.value = data.length

  return useOfflineData(query, data)
})

menuActionStore.setMenu([
  {
    key: 'refresh',
    title: 'Refresh',
    callback: async () => {
      loading.value = true
      await adminStore.FetchBlacklistAddress()
      loading.value = false
    },
  },
  {
    key: 'create blacklist_address',
    icon: 'plus',
    title: 'Create Blacklist Address',
    callback: () => {
      modalBlacklistAddress.value?.openModal("create")
    },
  },
])

const columns: ZTableColumn[] = [
  {
    title: 'ID',
    key: 'id',
  },
  {
    title: 'Address',
    key: 'address',
    overflow: true,
  },
  {
    title: 'Created At',
    key: 'created_at',
    formatBy: Format.DateTime,
    parse: ParseType.DateTime,
  },
  {
    title: 'Action',
    key: 'action',
    align: Align.Right,
    scopedSlots: true,
  },
]

async function DeleteBlacklistAddress(item: BlacklistAddress) {
  const index = adminStore.blacklist_addresses.findIndex(address => address.id === item.id)
  if (index !== -1 && item.id) {
    tempDeleteLoading.value[item.id] = true

    await adminStore.DeleteBlacklistAddress(item.id)

    tempDeleteLoading.value[item.id] = false
  }
}

filterStore.setFilter([
  {
    key: 'date',
    label: 'Date',
    type: FilterType.DateRange,
  },
  {
    key: 'address',
    label: 'Address',
    type: FilterType.Input,
  },
])

filterStore.onSubmit<Record<string, string>>(async (payload) => {
  query.value = {
    page: 1,
    ...payload,
  }

  if (!Number(payload.time_from) || !Number(payload.time_to)) {
    delete query.value.date
  }
})
</script>

<template>
  <div class="page-settings-blacklist-address py-2">
    <ZTablePro
      v-model:query="query"
      :columns="columns"
      :data-source="blacklist_addresses"
      :loading="loading"
    >
      <template #head>
        <div class="bold-text text-xl">
          Blacklist Address
        </div>
      </template>
      <template #action="{ item }">
        <span class="mr-4 cursor-pointer" @click="modalBlacklistAddress?.openModal('update', item)">
          <ZIcon type="edit" class="text-[20px]" />
        </span>
        <ZPopconfirm v-model="tempDelete[item.id]" :placement="Placement.TopRight" trigger="click" @click="DeleteBlacklistAddress(item)">
          <ZButton class="delete" :loading="tempDeleteLoading[item.id]">
            Delete
          </ZButton>
        </ZPopconfirm>
      </template>
      <template #foot>
        <ZPagination v-model="query.page" v-model:page-size="query.limit" :loading="loading" :total="total" />
      </template>
    </ZTablePro>
    <ModalBlacklistAddress ref="modalBlacklistAddress" />
  </div>
</template>

<style lang="less">
.page-settings-blacklist-address {
  .action {
    max-width: 200px;
  }
}
</style>
