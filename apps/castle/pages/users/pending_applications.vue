<script setup lang="ts">
import { FilterType, ProfileState } from '~/types'
import countries from '~/library/countries'

const adminStore = useAdminStore()

const total = useState(() => 0)
const filterStore = useFilterStore()
const { query, callbacks } = useQuery()
const menuActionStore = useMenuActionStore()

const { data: applications, pending, refresh } = await useAsyncData<Profile[]>(async () => {
  const { data, headers } = await adminStore.FetchProfilesPending(query.value)

  total.value = Number(headers.total)

  return data
}, { default: () => ([]) })

callbacks.push(refresh)

menuActionStore.setMenu([
  {
    key: 'refresh',
    title: 'Refresh',
    callback: refresh,
  },
])

const columns: ZTableColumn[] = [
  {
    title: 'ID',
    key: 'id',
    sort: true,
    sortKeyword: true,
  },
  {
    title: 'UID',
    key: 'uid',
  },
  {
    title: 'Full Name',
    key: 'full_name',
    overflow: true,
    sort: true,
    sortKeyword: true,
  },
  {
    title: 'Country',
    key: 'country',
    overflow: true,
    sort: true,
    sortKeyword: true,
    scopedSlots: true,
  },
  {
    title: 'Created At',
    key: 'created_at',
    formatBy: Format.DateTime,
    parse: ParseType.DateTime,
    sort: true,
  },
  {
    title: 'Updated At',
    key: 'updated_at',
    formatBy: Format.DateTime,
    parse: ParseType.DateTime,
    sort: true,
  },
  {
    title: 'Action',
    key: 'action',
    scopedSlots: true,
    align: Align.Right,
  },
]

function capitalize(text: string) {
  return text[0].toUpperCase() + text.slice(1)
}

function getCountryName(code: string) {
  const index = countries.findIndex(c => c.code === code)
  if (index !== -1) {
    return countries[index].name
  }
  return code
}

filterStore.setFilter([
  {
    key: 'date',
    label: 'Date',
    type: FilterType.DateRange,
  },
  {
    key: 'id',
    label: 'ID',
    type: FilterType.Input,
  },
  {
    key: 'uid',
    label: 'UID',
    type: FilterType.Input,
  },
  {
    key: 'email',
    label: 'Email',
    type: FilterType.Input,
  },
  {
    key: 'full_name',
    label: 'Full name',
    type: FilterType.Input,
  },
  {
    key: 'country',
    label: 'Country',
    type: FilterType.Input,
  },
  {
    key: 'document_type',
    label: 'Document Type',
    type: FilterType.Input,
  },
  {
    key: 'document_number',
    label: 'Document Number',
    type: FilterType.Input,
  },
  {
    key: 'state',
    label: 'State',
    type: FilterType.Select,
    columns: [
      {
        key: 'value',
        scopedSlots: true,
      },
    ],
    replaceFunc: capitalize,
    data: [
      {
        value: ProfileState.Pending,
      },
      {
        value: ProfileState.Reject,
      },
      {
        value: ProfileState.Temporary,
      },
      {
        value: ProfileState.Verified,
      },
    ],
    findBy: ['value'],
    valueKey: 'value',
    labelKey: 'value',
  },
])

filterStore.onSubmit(async (payload) => {
  query.value = {
    page: 1,
    limit: query.value.limit,
    ...payload,
  }
})
</script>

<template>
  <div class="page-users-pending-application">
    <ZTablePro v-model:query="query" :columns="columns" :data-source="applications" :loading="pending" :allow-sort-data="false">
      <template #head>
        <div class="bold-text text-xl">
          Profiles
        </div>
      </template>
      <template #country="{ item }">
        {{ getCountryName(item.country) }}
      </template>
      <template #action="{ item }">
        <NuxtLink v-if="item.uid !== '---'" :to="`/users/user_directory/${item.uid}/kyc`">
          <ZIcon type="edit" class="text-[20px]" />
        </NuxtLink>
      </template>
      <template #foot>
        <ZPagination v-model="query.page" v-model:page-size="query.limit" :loading="pending" :total="total" />
      </template>
    </ZTablePro>
  </div>
</template>

<style lang="less">
.page-users-pending-application {
  .id {
    max-width: 80px;
  }

  .user_id {
    max-width: 120px;
  }

  .country {
    max-width: 160px;
  }

  .created_at {
    max-width: 200px;
  }

  .updated_at {
    max-width: 200px;
  }

  .action {
    max-width: 80px;
  }
}
</style>
