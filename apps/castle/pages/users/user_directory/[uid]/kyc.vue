<script setup lang="ts">
import type { Ref } from 'vue'
import type { AsyncDataExecuteOptions } from 'nuxt/dist/app/composables/asyncData'
import ImagePreview from './ImagePreview.vue'
import { ProfileState } from '~/types'

defineProps<{
  user: User
  member: Member
  labels: Label[]
  activities: Activity[]
  devices: UserDevice[]
  refreshUser: (opts?: AsyncDataExecuteOptions | undefined) => Promise<void>
  refreshLabels: (opts?: AsyncDataExecuteOptions | undefined) => Promise<void>
}>()

const uid = useRoute().params.uid
const adminStore = useAdminStore()
const menuActionStore = useMenuActionStore()

const { data: profiles, pending, refresh } = await useAsyncData(() => adminStore.FetchUserProfiles(uid as string).then(res => res.data), { lazy: true, default: () => ([]) })

menuActionStore.setMenu([
  {
    key: 'refresh',
    title: 'Refresh',
    callback: refresh,
  },
])

const previewModal = templateRef('preview-modal') as unknown as Ref<InstanceType<typeof ImagePreview>>
const loading = useState(() => false)
const columns: ZTableColumn[] = [
  {
    title: 'Document Number',
    key: 'document_number',
  },
  {
    title: 'Document Type',
    key: 'document_type',
  },
  {
    title: 'State',
    key: 'state',
    scopedSlots: true,
  },
  {
    title: 'Front Upload',
    key: 'front_upload',
    scopedSlots: true,
  },
  {
    title: 'Back Upload',
    key: 'back_upload',
    scopedSlots: true,
  },
  {
    title: 'Portrait Upload',
    key: 'portrait_upload',
    scopedSlots: true,
  },
  {
    title: 'Date',
    key: 'created_at',
    formatBy: Format.DateTime,
    parse: ParseType.DateTime,
    align: Align.Right,
  },
  {
    title: 'Action',
    key: 'action',
    scopedSlots: true,
    align: Align.Right,
  },
]

async function UpdateProfileState(id: number, state: ProfileState) {
  loading.value = true
  await adminStore.UpdateProfileState(id, state, () => {
    profiles.value[profiles.value.findIndex(profile => profile.id === id)].state = state
  })
  loading.value = false
}
</script>

<template>
  <div class="page-dashboard-users-kyc mx-2">
    <ZTablePro :columns="columns" :loading="pending" :data-source="profiles">
      <template #head>
        <div class="bold-text text-xl">
          Profiles
        </div>
      </template>
      <template #state="{ item }">
        <span
          class="capitalize"
          :class="[
            { 'page-dashboard-users-kyc-state-gray': item.state === ProfileState.Temporary },
            { 'page-dashboard-users-kyc-state-yellow': item.state === ProfileState.Pending },
            { 'page-dashboard-users-kyc-state-green': item.state === ProfileState.Verified },
            { 'page-dashboard-users-kyc-state-red': item.state === ProfileState.Reject },
          ]"
        >
          {{ item.state }}
        </span>
      </template>

      <template #front_upload="{ item }">
        <a @click="previewModal.openModal(item.id, 'front')">
          Open
        </a>
      </template>

      <template #back_upload="{ item }">
        <a @click="previewModal.openModal(item.id, 'back')">
          Open
        </a>
      </template>

      <template #portrait_upload="{ item }">
        <a @click="previewModal.openModal(item.id, 'portrait')">
          Open
        </a>
      </template>

      <template #action="{ item }">
        <ZButton
          v-if="item.state === 'pending'"
          class="page-dashboard-users-kyc-button-red mr-2"
          :loading="loading"
          @click="UpdateProfileState(item.id, ProfileState.Reject)"
        >
          Reject
        </ZButton>
        <ZButton
          v-if="item.state === 'pending'"
          class="page-dashboard-users-kyc-button-blue"
          :loading="loading"
          @click="UpdateProfileState(item.id, ProfileState.Verified)"
        >
          Accept
        </ZButton>
      </template>
    </ZTablePro>
    <ImagePreview ref="preview-modal" />
  </div>
</template>

<style lang="less">
.page-dashboard-users-kyc {
  &-state {
    &-gray {
      color: @gray-color;
    }

    &-yellow {
      color: @warn-color;
    }

    &-green {
      color: @up-color;
    }

    &-red {
      color: @error-color;
    }
  }

  &-button {
    &-red {
      color: white !important;
      background-color: @error-color !important;
      border: 1px solid @error-color !important;
    }
  }

  .z-button {
    width: 100px;
    height: 32px;
  }

  .action {
    flex: 0 0 260px !important;
  }
}
</style>
