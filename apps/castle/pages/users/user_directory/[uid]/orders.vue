<script setup lang="ts">
import type { AsyncDataExecuteOptions } from 'nuxt/dist/app/composables/asyncData'

defineProps<{
  user: User
  member: Member
  labels: Label[]
  activities: Activity[]
  devices: UserDevice[]
  refreshUser: (opts?: AsyncDataExecuteOptions | undefined) => Promise<void>
  refreshLabels: (opts?: AsyncDataExecuteOptions | undefined) => Promise<void>
}>()

const route = useRoute()

const uid = useRoute().params.uid
const activeTab = ref('open_orders')

const tabs: ZTabItem[] = [
  {
    key: 'open_orders',
    text: 'Open Orders',
    slotName: true,
  },
  {
    key: 'orders_history',
    text: 'Orders History',
    slotName: true,
  },
]

onBeforeMount(async () => {
  const path = route.path.split(`/users/user_directory/${uid}/orders/`)[1]
  if (!path) {
    await navigateTo(`/users/user_directory/${uid}/orders/${activeTab.value}`)
    return
  }

  activeTab.value = path
})

function onClick(tabKey: string) {
  activeTab.value = tabKey
  navigateTo(`/users/user_directory/${uid}/orders/${tabKey}`)
}
</script>

<template>
  <div class="mx-2">
    <div class="page-dashboard-users-user">
      <ZCard class="no-padding-bottom">
        <ZTab :model-value="activeTab" :tabs="tabs" @click="onClick">
          <template v-for="tab in tabs" #[tab.key] :key="tab.key">
            {{ tab.text }}
          </template>
        </ZTab>
      </ZCard>
    </div>

    <NuxtPage />
  </div>
</template>
