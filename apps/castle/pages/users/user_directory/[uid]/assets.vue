<script setup lang="ts">
import type { AsyncDataExecuteOptions } from 'nuxt/dist/app/composables/asyncData'

interface Asset {
  currency: string
  name?: string
  available: number
  locked: number
  total: number
  btc_value: number
  url?: string
}

defineProps<{
  user: User
  member: Member
  labels: Label[]
  activities: Activity[]
  devices: UserDevice[]
  refreshUser: (opts?: AsyncDataExecuteOptions | undefined) => Promise<void>
  refreshLabels: (opts?: AsyncDataExecuteOptions | undefined) => Promise<void>
}>()

const adminStore = useAdminStore()
const uid = useRoute().params.uid as string

const { query } = useQuery()
const menuActionStore = useMenuActionStore()

const { data: result, pending, refresh } = await useAsyncData(async () => {
  const { data } = await adminStore.FetchAssets(uid)
  return data
}, { default: () => ([]), lazy: true })

const columns: ZTableColumn[] = [
  {
    title: 'Currency',
    key: 'currency',
    scopedSlots: true,
    sort: true,
    sortBy: SortBy.String,
  },
  {
    title: 'Total',
    key: 'total',
    align: Align.Left,
    sort: true,
    sortBy: SortBy.Number,
    scopedSlots: true,
  },
  {
    title: 'Available',
    key: 'available',
    align: Align.Left,
    sort: true,
    sortBy: SortBy.Number,
    scopedSlots: true,
  },
  {
    title: 'Locked',
    key: 'locked',
    align: Align.Center,
    sort: true,
    sortBy: SortBy.Number,
    scopedSlots: true,
  },
  {
    title: 'BTC Value',
    key: 'btc_value',
    align: Align.Right,
    scopedSlots: true,
    sort: true,
    sortBy: SortBy.Number,
  },
]

const assets = computed(() => {
  const currency = adminStore.public_currencies.find(c => c.id === 'btc')
  const btcPrice = currency ? Number(currency.price) : 1

  const assets: Asset[] = []
  result.value.forEach((r) => {
    const total = Number(r.balance) + Number(r.locked)
    const c = adminStore.public_currencies.find(c => c.id === r.currency)

    if (c) {
      assets.push({
        currency: c.id,
        name: c?.name,
        available: Number(r.balance),
        locked: Number(r.locked),
        total,
        btc_value: (Number(c?.price) * total / btcPrice) || 0,
        url: c?.icon_url,
      })
    }
  })

  return assets
})

menuActionStore.setMenu([
  {
    key: 'refresh',
    title: 'Refresh',
    callback: refresh,
  },
])
</script>

<template>
  <div class="page-dashboard-user-assets mx-2">
    <ZTablePro
      v-model:query="query"
      class="mt-5 mb-5"
      :loading="pending"
      :columns="columns"
      :data-source="assets"
      :search-enabled="true"
      :find-by="['currency', 'name']"
    >
      <template #currency="{ item }">
        <img
          :src="item.url"
        >
        <div>
          <div class="currency-code">
            {{ item.currency.toUpperCase() }}
          </div>
          <div class="currency-name">
            {{ item.name }}
          </div>
        </div>
      </template>
      <template #total="{ item }">
        {{ item.total === 0 ? '--' : Number(item.total).toFixed(8) }}
      </template>
      <template #available="{ item }">
        {{ item.available === 0 ? '--' : Number(item.available).toFixed(8) }}
      </template>
      <template #locked="{ item }">
        {{ item.locked === 0 ? '--' : Number(item.locked).toFixed(8) }}
      </template>
      <template #btc_value="{ item }">
        {{ item.btc_value === 0 ? '--' : Number(item.btc_value).toFixed(8) }}
      </template>
    </ZTablePro>
  </div>
</template>

<style lang="less">
.page-dashboard-user-assets {
  .z-table {
    &-row {
      height: 48px;
    }
  }

  .z-table-pro-head {
    justify-content: flex-end;
  }

  .currency {
    img {
      position: relative;
      height: 24px;
      margin-right: 12px;
      border-radius: 50%;
    }

    &-code {
      font-weight: 500;
      font-size: 16px;
      line-height: 1.5;
    }

    &-name {
      font-size: 12px;
      color: @gray-color;
      line-height: 1.5;
    }
  }
}
</style>
