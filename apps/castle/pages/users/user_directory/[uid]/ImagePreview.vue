<script setup lang="ts">
import { Dialog, DialogPanel, TransitionChild, TransitionRoot } from '@headlessui/vue'

const runtimeConfig = useRuntimeConfig()

const visible = ref(false)
const currentProfileID = ref<number>()
const currentAttchmentTag = ref<string>()

function openModal(id: number, tag: string) {
  visible.value = true

  currentProfileID.value = id
  currentAttchmentTag.value = tag
}

defineExpose({
  openModal,
})
</script>

<template>
  <ClientOnly>
    <TransitionRoot as="template" :show="visible">
      <Dialog as="div" class="relative z-10" @close="visible = false">
        <TransitionChild
          as="template"
          enter="ease-out duration-300"
          enter-from="opacity-0"
          enter-to="opacity-100"
          leave="ease-in duration-200"
          leave-from="opacity-100"
          leave-to="opacity-0"
        >
          <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </TransitionChild>

        <div class="fixed z-10 inset-0 overflow-y-auto">
          <div class="flex items-end sm:items-center justify-center min-h-full p-16 text-center">
            <div class="absolute top-6 right-6">
              <ZIcon type="close" class="text-2xl text-gray-500 border border-gray-700 rounded cursor-pointer" />
            </div>
            <TransitionChild
              as="template"
              enter="ease-out duration-300"
              enter-from="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              enter-to="opacity-100 translate-y-0 sm:scale-100"
              leave="ease-in duration-200"
              leave-from="opacity-100 translate-y-0 sm:scale-100"
              leave-to="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            >
              <DialogPanel class="relative rounded-lg text-left overflow-hidden transform transition-all">
                <div class="px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                  <div class="mt-6">
                    <img :src="`${runtimeConfig.public.apiUrl}auth/admin/profiles/${currentProfileID}/attachment?tag=${currentAttchmentTag}`">
                  </div>
                </div>
              </DialogPanel>
            </TransitionChild>
          </div>
        </div>
      </Dialog>
    </TransitionRoot>
  </ClientOnly>
</template>
