<script setup lang="ts">
import type { AsyncDataExecuteOptions } from 'nuxt/dist/app/composables/asyncData'
import { FilterType } from '~/types'

defineProps<{
  user: User
  member: Member
  labels: Label[]
  activities: Activity[]
  devices: UserDevice[]
  refreshUser: (opts?: AsyncDataExecuteOptions | undefined) => Promise<void>
  refreshLabels: (opts?: AsyncDataExecuteOptions | undefined) => Promise<void>
}>()

const adminStore = useAdminStore()
const uid = useRoute().params.uid

const total = useState(() => 0)
const filterStore = useFilterStore()
const { query, callbacks } = useQuery()
const menuActionStore = useMenuActionStore()

const { data: trades, pending, refresh } = await useAsyncData(async () => {
  const { data, headers } = await adminStore.FetchTrades({
    ...query.value,
    uid,
  })
  total.value = Number(headers.total)

  return data
}, { default: () => ([]), lazy: true })

callbacks.push(refresh)

const columns: ZTableColumn[] = [
  {
    title: 'Maker order email',
    key: 'maker_email',
    scopedSlots: true,
    overflow: true,
  },
  {
    title: 'Taker order email',
    key: 'taker_email',
    scopedSlots: true,
    overflow: true,
  },
  {
    title: 'Market',
    key: 'market',
    scopedSlots: true,
  },
  {
    title: 'Price',
    key: 'price',
    sort: true,
  },
  {
    title: 'Amount',
    key: 'amount',
    sort: true,
  },
  {
    title: 'Total',
    key: 'total',
    sort: true,
  },
  {
    title: 'Side',
    key: 'side',
    scopedSlots: true,
  },
  {
    title: 'Date',
    key: 'created_at',
    align: Align.Right,
    formatBy: Format.DateTime,
    parse: ParseType.DateTime,
    sort: true,
  },
]

menuActionStore.setMenu([
  {
    key: 'refresh',
    title: 'Refresh',
    callback: refresh,
  },
])

function marketBaseUnit(id: string) {
  const market = adminStore.markets.find(m => m.id === id)
  return market ? market.base_unit : ''
}

function marketQuoteUnit(id: string) {
  const market = adminStore.markets.find(m => m.id === id)
  return market ? market.quote_unit : ''
}

const sides = [
  {
    value: OrderSide.Buy,
  },
  {
    value: OrderSide.Sell,
  },
]

function capitalize(text: string) {
  return text[0].toUpperCase() + text.slice(1)
}

function isCurrency(text: string) {
  const currency = adminStore.currencies.find(c => c.id === text)
  return !!currency
}

function GetMarket(text: string) {
  const length = text.length
  let count = 1
  let a = ''
  let b = ''

  while (count < length) {
    a = text.slice(0, count)
    b = text.slice(count, length)
    if (isCurrency(a) && isCurrency(b)) return `${a.toUpperCase()}/${b.toUpperCase()}`
    count++
  }

  return text.toUpperCase()
}

filterStore.setFilter([
  {
    key: 'date',
    label: 'Date',
    type: FilterType.DateRange,
  },
  {
    key: 'market',
    type: FilterType.Select,
    label: 'Market',
    data: adminStore.markets,
    scroll: true,
    replaceFunc: GetMarket,
    columns: [
      {
        key: 'id',
        scopedSlots: true,
      },
    ],
    findBy: ['id'],
    valueKey: 'id',
    labelKey: 'id',
  },
  {
    key: 'side',
    label: 'Side',
    type: FilterType.Select,
    columns: [
      {
        key: 'value',
        scopedSlots: true,
      },
    ],
    data: sides,
    findBy: ['value'],
    valueKey: 'value',
    labelKey: 'value',
    replaceFunc: capitalize,
  },
  {
    key: 'price',
    label: 'Price',
    type: FilterType.Input,
  },
  {
    key: 'order_id',
    label: 'Order ID',
    type: FilterType.Input,
  },
])

filterStore.onSubmit(async (payload) => {
  query.value = {
    page: 1,
    limit: query.value.limit,
    ...payload,
  }
})
</script>

<template>
  <div class="page-dashboard-exchange-trades mx-2">
    <ZTablePro v-model:query="query" :columns="columns" :data-source="trades" :loading="pending" :allow-sort-data="false">
      <template #head>
        <div class="bold-text text-xl">
          Trades
        </div>
      </template>
      <template #maker_email="{ item }">
        <NuxtLink :to="`/users/user_directory/${item.maker_uid}`">
          {{ item.maker_email }}
        </NuxtLink>
      </template>
      <template #taker_email="{ item }">
        <NuxtLink :to="`/users/user_directory/${item.taker_uid}`">
          {{ item.taker_email }}
        </NuxtLink>
      </template>
      <template #market="{ item }">
        <NuxtLink :to="`/exchange/markets/update/${marketBaseUnit(item.market_id)}${marketQuoteUnit(item.market_id)}`">
          {{ (marketBaseUnit(item.market_id) as string).toUpperCase() }}/{{ (marketQuoteUnit(item.market_id) as string).toUpperCase() }}
        </NuxtLink>
      </template>
      <template #side="{ item }">
        <span
          class="page-dashboard-exchange-trades-side capitalize"
          :class="[{ 'page-dashboard-exchange-trades-side-red': item.side === OrderSide.Sell }, { 'page-dashboard-exchange-trades-side-green': item.side === OrderSide.Buy }]"
        >
          {{ item.side }}
        </span>
      </template>
      <template #foot>
        <ZPagination v-model="query.page" v-model:page-size="query.limit" :loading="pending" :total="total" />
      </template>
    </ZTablePro>
  </div>
</template>

<style lang="less">
.page-dashboard-exchange-trades {
  &-side {
    padding: 4px 12px;
    line-height: 1;
    border-radius: 4px;

    &.page-dashboard-exchange-trades-side-red {
      color: @error-color;
      background-color: rgba(@error-color, 0.15);
    }

    &.page-dashboard-exchange-trades-side-green {
      color: @up-color;
      background-color: rgba(@up-color, 0.15);
    }
  }

  .market {
    max-width: 160px;
  }

  .side {
    max-width: 80px;
  }

  .z-table {
    a {
      color: @text-color;
      transition: all 0.3s;

      &:hover {
        color: @text-color;
        text-decoration: underline;
      }
    }
  }
}
</style>
