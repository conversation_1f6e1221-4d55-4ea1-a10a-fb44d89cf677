<script setup lang="ts">
import { capitalize } from '@zsmartex/utils'
import { FilterType } from '~/types'
import { RoundNumber } from '~/mixins'

defineProps<{
  user: User
}>()

const adminStore = useAdminStore()
const uid = useRoute().params.uid as string

const total = ref(0)
const filterStore = useFilterStore()
const menuActionStore = useMenuActionStore()
const { query, callbacks } = useQuery()
const loading = ref(false)

const { data: profile } = await useAsyncData(async () => {
  const { data } = await adminStore.FetchP2PProfile(uid)

  return data
}, { default: () => ({} as P2PProfile) })

const { data: advertisements, pending, refresh } = await useAsyncData(async () => {
  const { data, headers } = await adminStore.FetchAdvertisements({
    ...query.value,
    uid,
  })

  total.value = Number(headers.total)

  return data
}, { default: () => ([]) })

callbacks.push(refresh)

menuActionStore.setMenu([
  {
    key: 'refresh',
    title: 'Refresh',
    callback: refresh,
  },
])

const columns: ZTableColumn[] = [
  {
    title: 'ID',
    key: 'id',
    sort: true,
    sortBy: SortBy.String,
  },
  {
    title: 'Coin Currency',
    key: 'coin_currency',
    sort: true,
    sortBy: SortBy.String,
    toUpper: true,
  },
  {
    title: 'Fiat Currency',
    key: 'fiat_currency',
    sort: true,
    sortBy: SortBy.String,
    toUpper: true,
  },
  {
    title: 'Side',
    key: 'side',
    sort: true,
    sortBy: SortBy.String,
    toUpper: true,
    scopedSlots: true,
  },
  {
    title: 'State',
    key: 'state',
    sort: true,
    sortBy: SortBy.String,
    scopedSlots: true,
  },
  {
    title: 'Action',
    key: 'action',
    scopedSlots: true,
    align: Align.Right,
  },
]

filterStore.setFilter([
  {
    key: 'state',
    label: 'State',
    type: FilterType.Select,
    columns: [
      {
        key: 'value',
        scopedSlots: true,
      },
    ],
    replaceFunc: capitalize,
    data: [
      {
        value: AdvertisementState.Enabled,
      },
      {
        value: AdvertisementState.Banned,
      },
      {
        value: AdvertisementState.Disabled,
      },
    ],
    findBy: ['value'],
    valueKey: 'value',
    labelKey: 'value',
  },
  {
    key: 'side',
    label: 'Side',
    type: FilterType.Select,
    columns: [
      {
        key: 'value',
        scopedSlots: true,
      },
    ],
    replaceFunc: capitalize,
    data: [
      {
        value: AdvertisementSide.Buy,
      },
      {
        value: AdvertisementSide.Sell,
      },
    ],
    findBy: ['value'],
    valueKey: 'value',
    labelKey: 'value',
  },
])

filterStore.onSubmit(async (payload) => {
  query.value = {
    page: 1,
    limit: query.value.limit,
    ...payload,
  }
})


async function VerifyProfile() {
  loading.value = true
  await adminStore.VerifyProfile(uid, () => {
    profile.value.verified = true
  })
  loading.value = false
}
</script>

<template>
  <div class="page-dashboard-users-profile mx-2">
    <ZCard class="mb-2">
      <div class="flex justify-between mb-4">
        <div class="flex items-center">
          <div class="mr-3 rounded-full bg-blue-600 h-[24px] w-[24px] flex justify-center items-center text-white">
            {{ user.username ? user.username[0].toUpperCase() : '' }}
          </div>
          <div class="text-blue-500 page-dashboard-users-profile-username flex items-center bold-text cursor-pointer text-base">
            {{ user.username }}
            <ZIconCheckCircleDuotone v-if="profile.verified" class="ml-2" />
          </div>
        </div>
        <div class="flex items-center">
          <div class="flex items-center">
            <ZButton v-if="!profile.verified" :loading="loading" @click="VerifyProfile">
              Verify
            </ZButton>
          </div>
          <div class="flex items-center ml-5 page-dashboard-users-profile-level">
            <span class="mr-1">Email</span>
            <ZIconCheckCircleDuotone />
          </div>
          <div class="flex items-center ml-5 page-dashboard-users-profile-level">
            <span class="mr-1">SMS</span>
            <ZIconCheckCircleDuotone />
          </div>
          <div class="flex items-center ml-5 page-dashboard-users-profile-level">
            <span class="mr-1">KYC</span>
            <ZIconCheckCircleDuotone />
          </div>
        </div>
      </div>
      <div class="flex justify-between">
        <div class="flex flex-col items-center">
          <div class="mb-3 text-gray">
            Total trade
          </div>
          <div class="text-[18px] bold-text">
            {{ profile?.total_trade }}
          </div>
        </div>
        <div class="flex flex-col items-center">
          <div class="mb-3 text-gray">
            Completie rate
          </div>
          <div class="text-[18px] bold-text">
            {{ profile ? `${RoundNumber(Number(profile.success_rate) * 100, 2)}%` : '0%' }}
          </div>
        </div>
        <div class="flex flex-col items-center">
          <div class="mb-3 text-gray">
            Total trade 30 days
          </div>
          <div class="text-[18px] bold-text">
            {{ profile?.total_trade }}
          </div>
        </div>
        <div class="flex flex-col items-center">
          <div class="mb-3 text-gray">
            Average unlock time
          </div>
          <div class="text-[18px] bold-text">
            {{ profile?.time_average }}
          </div>
        </div>
      </div>
    </ZCard>
    <ZTablePro
      v-model:query="query"
      :columns="columns"
      :data-source="advertisements"
      :loading="pending"
      :hover="true"
      :allow-sort-data="false"
      @click="(item: Advertisement) => navigateTo(`/exchange/p2p/advertisements/${item.id}`)"
    >
      <template #head>
        <div class="bold-text text-xl">
          Advertisements
        </div>
      </template>
      <template #side="{ item }">
        <span
          class="page-dashboard-users-profile-side capitalize" :class="[{ 'page-dashboard-users-profile-side-red': item.side === AdvertisementSide.Sell }, { 'page-dashboard-users-profile-side-green': item.side === AdvertisementSide.Buy }]"
        >
          {{ item.side }}
        </span>
      </template>
      <template #state="{ item }">
        <span
          class="capitalize" :class="[
            { 'text-red-500': item.state === AdvertisementState.Banned || item.state === AdvertisementState.Deleted || item.state === AdvertisementState.Disabled },
            { 'text-green-500': item.state === AdvertisementState.Enabled },
          ]"
        >
          {{ item.state }}
        </span>
      </template>
      <template #action="{ item }">
        <NuxtLink :to="`/exchange/currencies/${item.id}`">
          <ZIcon type="edit" class="text-[20px]" />
        </Nuxtlink>
      </template>
      <template #foot>
        <ZPagination v-model="query.page" v-model:page-size="query.limit" :loading="pending" :total="total" />
      </template>
    </ZTablePro>
  </div>
</template>

<style lang="less">
.page-dashboard-users-profile {
  &-side {
    padding: 4px 12px;
    line-height: 1;
    border-radius: 4px;

    &.page-dashboard-users-profile-side-red {
      color: @error-color;
      background-color: rgba(@error-color, 0.15);
    }

    &.page-dashboard-users-profile-side-green {
      color: @up-color;
      background-color: rgba(@up-color, 0.15);
    }
  }

  &-username {
    svg {
      width: 20px;
      height: 20px;

      .cls-1 {
        fill: @primary-color;
      }

      .cls-2 {
        fill: white;
      }
    }
  }

  &-level {
    svg {
      width: 20px;
      height: 20px;

      .cls-1 {
        fill: @up-color;
      }

      .cls-2 {
        fill: white;
      }
    }
  }

  .id {
    max-width: 100px;
  }

  .email {
    max-width: 300px;
  }

  .amount {
    max-width: 140px;
  }

  .currency_id {
    max-width: 140px;
  }

  .status {
    max-width: 160px;
  }

  .created_at {
    max-width: 180px;
  }

  .z-table {
    a {
      color: @text-color;
      transition: all 0.3s;

      &:hover {
        color: @text-color;
        text-decoration: underline;
      }
    }
  }
}
</style>
