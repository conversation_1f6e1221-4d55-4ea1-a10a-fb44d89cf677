<script setup lang="ts">
import { FilterType } from '~/types'

const adminStore = useAdminStore()
const uid = useRoute().params.uid

const total = useState(() => 0)
const { query, callbacks } = useQuery()
const filterStore = useFilterStore()
const menuActionStore = useMenuActionStore()

const { data: orders, pending, refresh } = await useAsyncData(async () => {
  const { data, headers } = await adminStore.FetchOrders({
    ...query.value,
    uid,
  })
  total.value = Number(headers.total)

  return data
}, { default: () => ([]), lazy: true })

callbacks.push(refresh)

const columns: ZTableColumn[] = [
  {
    title: 'Order ID',
    key: 'id',
    sort: true,
  },
  {
    title: 'Market',
    key: 'market',
    scopedSlots: true,
  },
  {
    title: 'Type',
    key: 'type',
    scopedSlots: true,
  },
  {
    title: 'Amount',
    key: 'origin_amount',
    sort: true,
  },
  {
    title: 'Price',
    key: 'price',
    sort: true,
  },
  {
    title: 'Side',
    key: 'side',
    scopedSlots: true,
  },
  {
    title: 'State',
    key: 'state',
    scopedSlots: true,
    sort: true,
  },
  {
    title: 'Date',
    key: 'created_at',
    align: Align.Right,
    formatBy: Format.DateTimeNoYear,
    parse: ParseType.DateTime,
    sort: true,
  },
]

menuActionStore.setMenu([
  {
    key: 'refresh',
    title: 'Refresh',
    callback: refresh,
  },
])

const states = [
  {
    value: OrderState.Cancel,
  },
  {
    value: OrderState.Done,
  },
  {
    value: OrderState.Pending,
  },
  {
    value: OrderState.Rejected,
  },
  {
    value: OrderState.Wait,
  },
]

const sides = [
  {
    value: OrderSide.Buy,
  },
  {
    value: OrderSide.Sell,
  },
]

function capitalize(text: string) {
  return text[0].toUpperCase() + text.slice(1)
}

function isCurrency(text: string) {
  const currency = adminStore.currencies.find(c => c.id === text)
  return !!currency
}

function GetMarket(text: string) {
  const length = text.length
  let count = 1
  let a = ''
  let b = ''

  while (count < length) {
    a = text.slice(0, count)
    b = text.slice(count, length)
    if (isCurrency(a) && isCurrency(b)) return `${a.toUpperCase()}/${b.toUpperCase()}`
    count++
  }

  return text.toUpperCase()
}

filterStore.setFilter([
  {
    key: 'date',
    label: 'Date',
    type: FilterType.DateRange,
  },
  {
    key: 'market',
    type: FilterType.Select,
    label: 'Market',
    data: adminStore.markets,
    scroll: true,
    replaceFunc: GetMarket,
    columns: [
      {
        key: 'id',
        scopedSlots: true,
      },
    ],
    findBy: ['id'],
    valueKey: 'id',
    labelKey: 'id',
  },
  {
    key: 'state',
    label: 'State',
    type: FilterType.Select,
    columns: [
      {
        key: 'value',
        scopedSlots: true,
      },
    ],
    data: states,
    findBy: ['value'],
    valueKey: 'value',
    labelKey: 'value',
    scroll: true,
    replaceFunc: capitalize,
  },
  {
    key: 'side',
    label: 'Side',
    type: FilterType.Select,
    columns: [
      {
        key: 'value',
        scopedSlots: true,
      },
    ],
    data: sides,
    findBy: ['value'],
    valueKey: 'value',
    labelKey: 'value',
    replaceFunc: capitalize,
  },
  {
    key: 'price',
    label: 'Price',
    type: FilterType.Input,
  },
  {
    key: 'origin_amount',
    label: 'Origin Amount',
    type: FilterType.Input,
  },
  {
    key: 'type',
    label: 'Type',
    type: FilterType.Select,
    columns: [
      {
        key: 'value',
        scopedSlots: true,
      },
    ],
    data: [
      {
        value: OrderType.Limit,
      },
      {
        value: OrderType.Market,
      },
    ],
    findBy: ['value'],
    valueKey: 'value',
    labelKey: 'value',
    replaceFunc: capitalize,
  },
])

filterStore.onSubmit(async (payload) => {
  query.value = {
    page: 1,
    limit: query.value.limit,
    ...payload,
  }
})
</script>

<template>
  <div class="page-dashboard-exchange-orders my-2">
    <ZTablePro v-model:query="query" :columns="columns" :data-source="orders" :loading="pending" :allow-sort-data="false">
      <template #head>
        <div class="bold-text text-xl">
          Orders
        </div>
      </template>
      <template #email="{ item }">
        <NuxtLink :to="`/users/user_directory/${item.uid}`">
          {{ item.email }}
        </NuxtLink>
      </template>
      <template #type="{ item }">
        <span class="capitalize">{{ item.type }}</span>
      </template>
      <template #market="{ item }">
        <NuxtLink :to="`/exchange/markets/update/${item.ask}${item.bid}`">
          {{ item.ask.toUpperCase() }}/{{ item.bid.toUpperCase() }}
        </NuxtLink>
      </template>
      <template #side="{ item }">
        <span
          class="page-dashboard-exchange-orders-side capitalize"
          :class="[{ 'page-dashboard-exchange-orders-side-red': item.side === OrderSide.Sell }, { 'page-dashboard-exchange-orders-side-green': item.side === OrderSide.Buy }]"
        >
          {{ item.side }}
        </span>
      </template>
      <template #state="{ item }">
        <span
          class="capitalize" :class="[
            { 'text-red-500': item.state === OrderState.Cancel || item.state === OrderState.Rejected },
            { 'text-green-500': item.state === OrderState.Wait },
            { 'text-blue-500': item.state === OrderState.Done },
            { 'text-gray-500': item.state === OrderState.Pending },
          ]"
        >
          {{ item.state }}
        </span>
      </template>
      <template #foot>
        <ZPagination v-model="query.page" v-model:page-size="query.limit" :loading="pending" :total="total" />
      </template>
    </ZTablePro>
  </div>
</template>

<style lang="less">
.page-dashboard-exchange-orders {
  &-side {
    padding: 4px 12px;
    line-height: 1;
    border-radius: 4px;

    &.page-dashboard-exchange-orders-side-red {
      color: @error-color;
      background-color: rgba(@error-color, 0.15);
    }

    &.page-dashboard-exchange-orders-side-green {
      color: @up-color;
      background-color: rgba(@up-color, 0.15);
    }
  }

  .id {
    max-width: 100px;
  }

  .market {
    max-width: 180px;
  }

  .type {
    max-width: 100px;
  }

  .side {
    max-width: 100px;
  }

  .state {
    max-width: 140px;
  }

  .z-table {
    a {
      color: @text-color;
      transition: all 0.3s;

      &:hover {
        color: @text-color;
        text-decoration: underline;
      }
    }
  }
}
</style>
