<script setup lang="ts">
import type { AsyncDataExecuteOptions } from 'nuxt/dist/app/composables/asyncData'
import UserLevel from '~/layouts/admin/user/account/UserLevel.vue'
import UserLabel from '~/layouts/admin/user/account/UserLabel.vue'
import Profile from '~/layouts/admin/user/Profile.vue'
import ActivityTimeline from '~/layouts/admin/user/account/ActivityTimeline.vue'
import RecentDevices from '~/layouts/admin/user/account/RecentDevices.vue'
import UserSetting from '~/layouts/admin/user/account/UserSetting.vue'

const props = defineProps<{
  user: User
  member: Member
  labels: Label[]
  activities: Activity[]
  devices: UserDevice[]
  refreshMember: (opts?: AsyncDataExecuteOptions | undefined) => Promise<void>
  refreshUser: (opts?: AsyncDataExecuteOptions | undefined) => Promise<void>
  refreshLabels: (opts?: AsyncDataExecuteOptions | undefined) => Promise<void>
}>()

const emit = defineEmits(['updateState', 'addLabel', 'updateLabel', 'deleteLabel', 'deleteDevice', 'updateNote'])

const menuActionStore = useMenuActionStore()

menuActionStore.setMenu([
  {
    key: 'refresh',
    title: 'Refresh',
    callback: async () => {
      props.refreshUser()
      props.refreshLabels()
      props.refreshMember()
    },
  },
])
</script>

<template>
  <div>
    <div class="page-dashboard-users-user flex">
      <div class="m-2 w-6/12">
        <Profile :user="user" @update-note="(note: string) => emit('updateNote', note)" />
      </div>
      <div class="m-2 w-6/12">
        <UserLevel :labels="labels" :user="user" />
        <UserSetting :model-value="member" :user="user" @update-state="(state: string) => emit('updateState', state)" />
      </div>
    </div>
    <div class="m-2 page-dashboard-users-user">
      <UserLabel
        :labels="labels"
        :user="user"
        @add-label="(label: Label) => emit('addLabel', label)"
        @update-label="(label: Label) => emit('updateLabel', label)"
        @delete-label="(label: Label) => emit('deleteLabel', label)"
      />
    </div>
    <div class="flex">
      <div class="m-2 w-6/12 h-full">
        <ActivityTimeline :activities="activities" />
      </div>
      <div class="m-2 w-6/12 h-full">
        <RecentDevices :user="user" :devices="devices" @delete-device="(device: UserDevice) => emit('deleteDevice', device)" />
      </div>
    </div>
  </div>
</template>
