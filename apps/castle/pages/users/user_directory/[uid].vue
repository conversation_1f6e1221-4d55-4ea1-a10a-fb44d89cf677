<script setup lang="ts">
const route = useRoute()

const uid = useRoute().params.uid
const adminStore = useAdminStore()

const { data: member, refresh: refreshMember } = await useAsyncData(() => adminStore.FetchMember(uid as string).then(res => res.data))
const { data: user, refresh: refreshUser } = await useAsyncData(() => adminStore.FetchUser(uid as string).then(res => res.data))
const { data: labels, refresh: refreshLabels } = await useAsyncData(() => adminStore.GetUserLabels(member.value!.uid).then(res => res.data))
const { data: activities } = await useAsyncData(() => adminStore.FetchUserActivities({
  uid: member.value!.uid,
  action: 'login',
  limit: 5,
}).then(res => res.data))
const { data: devices } = await useAsyncData(() => adminStore.FetchUserDevices(member.value!.uid).then(res => res.data))

const activeTab = ref('account')

const tabs: ZTabItem[] = [
  {
    key: 'account',
    text: 'Account',
    slotName: true,
  },
  // {
  //   key: 'kyc',
  //   text: 'KYC',
  //   slotName: true,
  // },
  {
    key: 'assets',
    text: 'Assets',
    slotName: true,
  },
  {
    key: 'orders',
    text: 'Orders',
    slotName: true,
  },
  {
    key: 'trades',
    text: 'Trades',
    slotName: true,
  },
  // {
  //   key: 'p2p_profile',
  //   text: 'P2P Profile',
  //   slotName: true,
  // },
  {
    key: 'deposits',
    text: 'Deposits',
    slotName: true,
  },
  {
    key: 'withdraws',
    text: 'Withdraws',
    slotName: true,
  },
]

function AddLabel(label: Label) {
  labels.value!.push(label)
}

function UpdateNote(note: string) {
  user.value = {
    ...user.value!,
    note,
  }
}

function UpdateState(state: string) {
  user.value = {
    ...user.value!,
    state,
  }
}

function UpdateLabel(label: Label) {
  const index = labels.value!.findIndex((l: Label) => l.key === label.key)
  if (index !== -1) {
    labels.value![index].value = label.value
    labels.value![index].scope = label.scope
  }
}

function DeleteLabel(label: Label) {
  const index = labels.value!.findIndex((l: Label) => l.key === label.key)
  if (index !== -1) {
    labels.value!.splice(index, 1)
  }
}

function DeleteDevice(device: UserDevice) {
  const index = devices.value!.findIndex((d: UserDevice) => d.session_id === device.session_id)
  if (index !== -1) {
    devices.value!.splice(index, 1)
  }
}

onBeforeMount(async () => {
  const path = route.path.split(`/users/user_directory/${uid}/`)[1]
  if (!path) {
    await navigateTo(`/users/user_directory/${uid}/${activeTab.value}`)
    return
  }

  activeTab.value = path
})

function onClick(tabKey: string) {
  navigateTo(`/users/user_directory/${uid}/${tabKey}`)
}
</script>

<template>
  <div>
    <div class="p-2 page-dashboard-users-user">
      <ZCard class="no-padding-bottom">
        <ZTab v-model="activeTab" :tabs="tabs" @click="onClick">
          <template v-for="tab in tabs" #[tab.key] :key="tab.key">
            {{ tab.text }}
          </template>
        </ZTab>
      </ZCard>
    </div>

    <NuxtPage
      :user="user!"
      :member="member!"
      :labels="labels!"
      :activities="activities!"
      :devices="devices!"
      :refresh-user="refreshUser"
      :refresh-member="refreshMember"
      :refresh-labels="refreshLabels"
      @update-state="UpdateState"
      @update-note="UpdateNote"
      @add-label="AddLabel"
      @update-label="UpdateLabel"
      @delete-label="DeleteLabel"
      @delete-device="DeleteDevice"
    />
  </div>
</template>

<style lang="less">
.no-padding-bottom {
  padding-bottom: 0;
}

.page-dashboard-users-user {
  &-dropdown {
    &-item {
      display: block;
      cursor: pointer;
      padding: 8px 12px;
      color: @text-color !important;

      &:hover {
        background-color: rgba(@gray-color, 0.1);
      }
    }

    &-item + &-item {
      border-top: 1px solid @base-border-color;
    }
  }

  .z-input {
    background-color: rgba(@gray-color, 0.05);
  }

  .z-dropdown {
    &-trigger {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 12px;
      width: 100%;
      height: 38px;
      background-color: rgba(@gray-color, 0.05);
      border: 1px solid rgba(33, 47, 79, 0.1);
      border-radius: 4px;
      transition: all 0.3s;
      &:hover {
        border: 1px solid @primary-color;
      }
    }

    &-triggered {
      border: 1px solid @primary-color;
      box-shadow: 0 0 0 2px rgba(@primary-color, 0.2);
    }

    &-overlay {
      width: 100%;
      background-color: #fff;
      box-shadow: 0 3px 6px -4px #0000001f, 0 6px 16px #00000014, 0 9px 28px 8px #0000000d;
    }
  }
}
</style>
