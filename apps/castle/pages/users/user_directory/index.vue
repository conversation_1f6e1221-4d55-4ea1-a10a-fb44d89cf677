<script setup lang="ts">
import { FilterType } from '~/types'

const adminStore = useAdminStore()
const filterStore = useFilterStore()

const total = useState(() => 0)
const tempLoading = useState<Record<string, boolean>>(() => ({}))
const { query, callbacks } = useQuery()
const menuActionStore = useMenuActionStore()

const { data: users, pending, refresh } = await useAsyncData(async () => {
  const { data, headers } = await adminStore.FetchUsers(query.value)

  total.value = Number(headers.total)

  return data
}, { default: () => ([]) })

callbacks.push(refresh)

const columns: ZTableColumn[] = [
  {
    title: 'UID',
    key: 'uid',
    overflow: true,
    sort: true,
    sortKeyword: true,
  },
  {
    title: 'Role',
    key: 'role',
    scopedSlots: true,
    sort: true,
    sortKeyword: true,
  },
  {
    title: 'Level',
    key: 'level',
    sort: true,
  },
  {
    title: 'OTP',
    key: 'otp',
    scopedSlots: true,
  },
  {
    title: 'State',
    key: 'state',
    scopedSlots: true,
    sort: true,
    sortKeyword: true,
  },
  {
    title: 'Action',
    key: 'action',
    scopedSlots: true,
    align: Align.Right,
  },
]

menuActionStore.setMenu([
  {
    key: 'refresh',
    title: 'Refresh',
    callback: refresh,
  },
])

function capitalize(text: string) {
  return text[0].toUpperCase() + text.slice(1)
}

filterStore.setFilter([
  {
    key: 'date',
    label: 'Date',
    type: FilterType.DateRange,
  },
  {
    key: 'uid',
    label: 'UID',
    type: FilterType.Input,
  },
  {
    key: 'email',
    label: 'Email',
    type: FilterType.Input,
  },
  {
    key: 'role',
    label: 'Role',
    type: FilterType.Select,
    columns: [
      {
        key: 'value',
        scopedSlots: true,
      },
    ],
    replaceFunc: capitalize,
    data: [
      {
        value: UserRole.SuperAdmin,
      },
      {
        value: UserRole.Admin,
      },
      {
        value: UserRole.Support1,
      },
      {
        value: UserRole.Support2,
      },
      {
        value: UserRole.Member,
      },
    ],
    findBy: ['value'],
    valueKey: 'value',
    labelKey: 'value',
  },
  {
    key: 'level',
    label: 'Level',
    type: FilterType.Input,
  },
  {
    key: 'state',
    label: 'State',
    type: FilterType.Select,
    columns: [
      {
        key: 'value',
        scopedSlots: true,
      },
    ],
    data: [
      {
        value: UserState.Active,
      },
      {
        value: UserState.Deleted,
      },
      {
        value: UserState.Banned,
      },
      {
        value: UserState.Pending,
      },
      {
        value: UserState.Locked,
      },
    ],
    findBy: ['value'],
    valueKey: 'value',
    labelKey: 'value',
    scroll: true,
    replaceFunc: capitalize,
  },
])

async function UpdateUserOTP(user: User) {
  tempLoading.value[user.id] = true
  await adminStore.UpdateUserOTP(user.uid, false, () => {
    user.otp = false
  })
  tempLoading.value[user.id] = false
}

filterStore.onSubmit<Record<string, string>>(async (payload) => {
  query.value = {
    page: 1,
    ...payload,
  }
})
</script>

<template>
  <div class="page-dashboard-users">
    <ZTablePro v-model:query="query" :columns="columns" :data-source="users" :loading="pending" :allow-sort-data="false">
      <template #head>
        <div class="bold-text text-xl">
          Users
        </div>
      </template>
      <template #otp="{ item }">
        <ZSwitch :model-value="item.otp" size="medium" :loading="tempLoading[item.id]" :disabled="!item.otp" @change="UpdateUserOTP(item)" />
      </template>
      <template #role="{ item }">
        <span class="capitalize">{{ item.role }}</span>
      </template>
      <template #state="{ item }">
        <div
          class="capitalize page-dashboard-users-state" :class="[
            { 'page-dashboard-users-state-red': item.state === UserState.Banned || item.state === UserState.Deleted || item.state === UserState.Locked },
            { 'page-dashboard-users-state-green': item.state === UserState.Active },
            { 'page-dashboard-users-state-blue': item.state === UserState.Pending },
          ]"
        >
          {{ item.state }}
        </div>
      </template>
      <template #action="{ item }">
        <NuxtLink v-if="Number(item.id) !== -1" :to="`/users/user_directory/${item.uid}`">
          <ZIcon type="edit" class="text-[20px]" />
        </Nuxtlink>
      </template>
      <template #foot>
        <ZPagination v-model="query.page" v-model:page-size="query.limit" :loading="pending" :total="total" />
      </template>
    </ZTablePro>
  </div>
</template>

<style lang="less">
.page-dashboard-users {
  &-state {
    padding: 4px 12px;
    line-height: 1;
    border-radius: 4px;

    &-red {
      color: @error-color;
      background-color: rgba(@error-color, 0.15)
    }

    &-green {
      color: @up-color;
      background-color: rgba(@up-color, 0.15)
    }

    &-blue {
      color: @primary-color;
      background-color: rgba(@primary-color, 0.15)
    }
  }

  .uid {
    min-width: 260px;
  }

  .role {
    min-width: 240px;
  }

  .level {
    min-width: 120px;
  }

  .otp {
    min-width: 160px;
  }

  .state {
    min-width: 120px;
  }

  .action {
    min-width: 120px;
  }
}
</style>
