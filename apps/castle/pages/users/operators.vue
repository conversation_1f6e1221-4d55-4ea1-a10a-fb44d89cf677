<script setup lang="ts">
import { FilterType } from '~/types'
import { UserAgentToBrowser, UserAgentToOS } from '~/mixins'

const adminStore = useAdminStore()

const total = useState(() => 0)
const { query, callbacks } = useQuery()
const filterStore = useFilterStore()
const menuActionStore = useMenuActionStore()
const tempData = useState<Record<string, boolean>>(() => ({}))

const { data: operators, pending, refresh } = await useAsyncData<Activity[]>(async () => {
  const { data, headers } = await adminStore.FetchActivitiesAdmin(query.value)

  total.value = Number(headers.total)
  query.value.value = Number(headers.page)
  query.value.limit = Number(headers['per-size'])

  return data
}, { default: () => ([]) })

callbacks.push(refresh)

const columns: ZTableColumn[] = [
  {
    title: 'Email',
    key: 'email',
    overflow: true,
    scopedSlots: true,
  },
  {
    title: 'Role',
    key: 'role',
    scopedSlots: true,
  },
  {
    title: 'Topic',
    key: 'topic',
    overflow: true,
    sort: true,
    sortKeyword: true,
  },
  {
    title: 'Action',
    key: 'action',
    overflow: true,
    sort: true,
    sortKeyword: true,
  },
  {
    title: 'Target',
    key: 'target_email',
    overflow: true,
  },
  {
    title: 'Browser',
    key: 'browser',
    scopedSlots: true,
  },
  {
    title: 'IP',
    key: 'user_ip',
    overflow: true,
    sort: true,
    sortKeyword: true,
  },
  {
    title: 'OS',
    key: 'os',
    scopedSlots: true,
  },
  {
    title: 'Device',
    key: 'device',
    scopedSlots: true,
    sort: true,
    sortKeyword: true,
  },
  {
    title: 'Result',
    key: 'result',
    scopedSlots: true,
    sort: true,
    sortKeyword: true,
  },
  {
    title: 'Action',
    key: 'action',
    scopedSlots: true,
    align: Align.Right,
  },
]

menuActionStore.setMenu([
  {
    key: 'refresh',
    title: 'Refresh',
    callback: refresh,
  },
])

function capitalize(text: string) {
  return text[0].toUpperCase() + text.slice(1)
}

filterStore.setFilter([
  {
    key: 'date',
    label: 'Date',
    type: FilterType.DateRange,
  },
  {
    key: 'id',
    label: 'ID',
    type: FilterType.Input,
  },
  {
    key: 'uid',
    label: 'UID',
    type: FilterType.Input,
  },
  {
    key: 'email',
    label: 'Email',
    type: FilterType.Input,
  },
  {
    key: 'role',
    label: 'Role',
    type: FilterType.Select,
    columns: [
      {
        key: 'value',
        scopedSlots: true,
      },
    ],
    replaceFunc: capitalize,
    data: [
      {
        value: UserRole.SuperAdmin,
      },
      {
        value: UserRole.Admin,
      },
      {
        value: UserRole.Support1,
      },
      {
        value: UserRole.Support2,
      },
      {
        value: UserRole.Member,
      },
    ],
    findBy: ['value'],
    valueKey: 'value',
    labelKey: 'value',
  },
  {
    key: 'target_uid',
    label: 'Target UID',
    type: FilterType.Input,
  },
  {
    key: 'user_ip',
    label: 'User IP',
    type: FilterType.Input,
  },
  {
    key: 'user_ip_country',
    label: 'User IP country',
    type: FilterType.Input,
  },
  {
    key: 'user_agent',
    label: 'User Agent',
    type: FilterType.Input,
  },
  {
    key: 'topic',
    label: 'Topic',
    type: FilterType.Input,
  },
  {
    key: 'action',
    label: 'Action',
    type: FilterType.Input,
  },
  {
    key: 'device',
    label: 'Device',
    type: FilterType.Input,
  },
  {
    key: 'result',
    label: 'Result',
    type: FilterType.Select,
    columns: [
      {
        key: 'value',
        scopedSlots: true,
      },
    ],
    replaceFunc: capitalize,
    data: [
      {
        id: 1,
        value: ActivityResult.Succeed,
      },
      {
        id: 2,
        value: ActivityResult.Failed,
      },
      {
        id: 3,
        value: ActivityResult.Denied,
      },
    ],
    findBy: ['value'],
    valueKey: 'value',
    labelKey: 'value',
  },
])

filterStore.onSubmit(async (payload) => {
  query.value = {
    page: 1,
    limit: query.value.limit,
    ...payload,
  }
})
</script>

<template>
  <div class="page-dashboard-users">
    <ZTablePro v-model:query="query" :columns="columns" :data-source="operators" :loading="pending" :allow-sort-data="false">
      <template #head>
        <div class="bold-text text-xl">
          Operators
        </div>
      </template>
      <template #role="{ item }">
        <span class="capitalize">{{ item.role }}</span>
      </template>
      <template #email="{ item }">
        <NuxtLink :to="`/users/user_directory/${item.uid}`">
          {{ item.email }}
        </NuxtLink>
      </template>
      <template #browser="{ item }">
        {{ UserAgentToBrowser(item.user_agent) }}
      </template>
      <template #os="{ item }">
        {{ UserAgentToOS(item.user_agent) }}
      </template>
      <template #device="{ item }">
        <span class="capitalize">{{ item.device }}</span>
      </template>
      <template #result="{ item }">
        <span class="capitalize">{{ item.result }}</span>
      </template>
      <template #action="{ item }">
        <ZPopover v-if="item.data" v-model="tempData[item.id]" trigger="click" :placement="Placement.TopRight">
          <ZIcon type="guandian" class="text-[20px] cursor-pointer mr-[6px] text-gray-400" />
          <template #content>
            <div v-for="(data, index) in JSON.parse(item.data)" :key="index">
              <span class="bold-text text-gray-400">{{ index }}</span>: {{ data }}
            </div>
          </template>
        </ZPopover>
      </template>
      <template #foot>
        <ZPagination v-model="query.page" v-model:page-size="query.limit" :loading="pending" :total="total" />
      </template>
    </ZTablePro>
  </div>
</template>

<style lang="less">
.page-dashboard-users {
  .z-table {
    a {
      color: @text-color;
      transition: all 0.3s;

      &:hover {
        color: @text-color;
        text-decoration: underline;
      }
    }
  }
}
</style>
