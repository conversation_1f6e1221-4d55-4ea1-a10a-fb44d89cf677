<script setup lang="ts">
import LayoutNetwork from '~/layouts/admin/exchange/currencies/LayoutNetwork.vue'

const adminStore = useAdminStore()

const id = useRoute().params.id as string
const currencyId = useRoute().params.currency_id as string

const { data: blockchainCurrency, pending, refresh } = await useAsyncData(() => adminStore.FetchBlockchainCurrency(id).then(res => res.data))
</script>

<template>
  <LayoutNetwork :id="id" :model-value="blockchainCurrency!" :pending="pending" :refresh="refresh" :currency="currencyId" />
</template>
