<script setup lang="ts">
import { useOfflineData } from '@zsmartex/core/composables'
import LayoutCurrency from '~/layouts/admin/exchange/currencies/LayoutCurrency.vue'

const adminStore = useAdminStore()

const id = useRoute().params.currency_id as string
const total = ref(0)
const { query } = useQuery()

const { data: currency, pending, refresh } = await useAsyncData(() => adminStore.FetchCurrency(id).then(res => res.data))
const { data: blockchainCurrencies, pending: loading } = await useAsyncData(async () => {
  const data = await adminStore.FetchBlockchainCurrencies({ currency: currency.value!.id }).then(res => res.data)

  total.value = data.length

  return useOfflineData(query, data)
})

const columns: ZTableColumn[] = [
  {
    title: 'ID',
    key: 'id',
    sort: true,
    sortBy: SortBy.Number,
  },
  {
    title: 'Key',
    key: 'blockchain_key',
    overflow: true,
    sort: true,
    sortBy: SortBy.String,
  },
  {
    title: 'Subunits',
    key: 'subunits',
    sort: true,
    sortBy: SortBy.Number,
  },
  {
    title: 'Visible Deposit Enabled',
    key: 'visible_deposit_enabled',
    scopedSlots: true,
    align: Align.Left,
  },
  {
    title: 'Deposit Enabled',
    key: 'deposit_enabled',
    scopedSlots: true,
    align: Align.Left,
  },
  {
    title: 'Withdraw Enabled',
    key: 'withdraw_enabled',
    scopedSlots: true,
    align: Align.Left,
  },
  {
    title: 'Action',
    key: 'action',
    scopedSlots: true,
    align: Align.Right,
  },
]

async function UpdateSoftDepositBlockchainCurrency(item: BlockchainCurrency) {
  loading.value = true
  await adminStore.UpdateBlockchainCurrency({
    id: Number(item.id),
    visible_deposit_enabled: !item.visible_deposit_enabled,
  })
  item.visible_deposit_enabled = !item.visible_deposit_enabled
  loading.value = false
}

async function UpdateHardDepositBlockchainCurrency(item: BlockchainCurrency) {
  loading.value = true
  await adminStore.UpdateBlockchainCurrency({
    id: Number(item.id),
    deposit_enabled: !item.deposit_enabled,
  })
  item.deposit_enabled = !item.deposit_enabled
  loading.value = false
}

async function UpdateWithdrawBlockchainCurrency(item: BlockchainCurrency) {
  loading.value = true
  await adminStore.UpdateBlockchainCurrency({
    id: Number(item.id),
    withdraw_enabled: !item.withdraw_enabled,
  })
  item.withdraw_enabled = !item.withdraw_enabled
  loading.value = false
}
</script>

<template>
  <div class="page-dashboard-exchange-currencies-action">
    <LayoutCurrency :id="id" :model-value="currency!" :pending="pending" :refresh="refresh" />
    <ZTablePro title="Networks" :columns="columns" :data-source="blockchainCurrencies" :query="query" class="mt-6">
      <template #visible_deposit_enabled="{ item }">
        <ZSwitch :model-value="item.visible_deposit_enabled" size="medium" :loading="loading" class="flex w-full flex-end" @change="UpdateSoftDepositBlockchainCurrency(item)" />
      </template>
      <template #deposit_enabled="{ item }">
        <ZSwitch :model-value="item.deposit_enabled" size="medium" :loading="loading" class="flex w-full flex-end" @change="UpdateHardDepositBlockchainCurrency(item)" />
      </template>
      <template #withdraw_enabled="{ item }">
        <ZSwitch :model-value="item.withdraw_enabled" size="medium" :loading="loading" class="flex w-full flex-end" @change="UpdateWithdrawBlockchainCurrency(item)" />
      </template>
      <template #action="{ item }">
        <NuxtLink :to="`/exchange/currencies/${currency!.id}/networks/${item.id}`">
          <ZIcon type="edit" class="text-[20px]" />
        </Nuxtlink>
      </template>
    </ZTablePro>
    <div class="py-4 flex justify-center">
      <ZButton>
        <NuxtLink :to="`/exchange/currencies/${currency!.id}/networks/create`">
          ADD NETWORK
        </NuxtLink>
      </ZButton>
    </div>
  </div>
</template>

<style lang="less">
.page-dashboard-exchange-currencies-action {
  .z-button {
    height: 32px;
    background-color: @primary-color;
    color: white !important;
  }
}
</style>
