<script setup lang="ts">
import { useOfflineData } from '@zsmartex/core/composables'
import { Align, ParseType } from '@zsmartex/types'
import { CurrencyStatus, FilterType } from '~/types'

const adminStore = useAdminStore()
const total = useState(() => 0)
const loading = useState(() => false)
const tempLoading = useState<Record<string, boolean>>(() => ({}))
const { query } = useQuery()
const filterStore = useFilterStore()
const menuActionStore = useMenuActionStore()

const currencies = computed(() => {
  const data = adminStore.currencies.map(item => ({
    ...item,
  }))

  total.value = data.length

  return useOfflineData(query, data)
})

const columns: ZTableColumn[] = [
  {
    title: 'ID',
    key: 'id',
    toUpper: true,
    sort: true,
    sortBy: SortBy.String,
  },
  {
    title: 'Name',
    key: 'name',
    sort: true,
    sortBy: SortBy.String,
  },
  {
    title: 'Precision',
    key: 'precision',
    align: Align.Center,
    sort: true,
    sortBy: SortBy.Number,
  },
  {
    title: 'Price',
    key: 'price',
    sort: true,
    sortBy: SortBy.Number,
    parse: ParseType.Decimal,
    align: Align.Right,
    suffix: 'USDT',
  },
  {
    title: 'Total Assets',
    key: 'total_assets',
    sort: true,
    sortBy: SortBy.Number,
    parse: ParseType.Decimal,
    align: Align.Right,
    scopedSlots: true,
  },
  {
    title: 'Status',
    key: 'status',
    scopedSlots: true,
    align: Align.Right,
  },
  {
    title: 'Action',
    key: 'action',
    scopedSlots: true,
    align: Align.Right,
  },
]

menuActionStore.setMenu([
  {
    key: 'refresh',
    title: 'Refresh',
    callback: async () => {
      loading.value = true
      await adminStore.FetchCurrencies()
      loading.value = false
    },
  },
  {
    key: 'create currency',
    icon: 'plus',
    title: 'Create Currency',
    callback: () => {
      navigateTo({
        path: '/exchange/currencies/create',
      })
    },
  },
])

function capitalize(text: string) {
  return text[0].toUpperCase() + text.slice(1)
}

filterStore.setFilter([
  {
    key: 'status',
    label: 'Status',
    type: FilterType.Select,
    columns: [
      {
        key: 'value',
        scopedSlots: true,
      },
    ],
    replaceFunc: capitalize,
    data: [
      {
        value: CurrencyStatus.Enabled,
      },
      {
        value: CurrencyStatus.Disabled,

      },
    ],
    findBy: ['value'],
    valueKey: 'value',
    labelKey: 'value',
  },
  {
    key: 'id',
    label: 'Currency',
    type: FilterType.Input,
  },
  {
    key: 'name',
    label: 'Name',
    type: FilterType.Input,
  },
])

async function changeStateCurrency(item: any) {
  tempLoading.value[item.id] = true
  await adminStore.UpdateCurrencyStatus(
    {
      id: item.id,
      status: item.status === CurrencyStatus.Enabled ? CurrencyStatus.Disabled : CurrencyStatus.Enabled,
    },
  )
  tempLoading.value[item.id] = false
  item.status = item.status === 'enabled' ? 'disabled' : 'enabled'
}

filterStore.onSubmit<Record<string, string>>(async (payload) => {
  query.value = {
    page: 1,
    limit: query.value.limit,
    ...payload,
  }
})
</script>

<template>
  <div class="page-exchange-currency">
    <ZTablePro v-model:query="query" :columns="columns" :data-source="currencies" :loading="loading">
      <template #head>
        <div class="bold-text text-xl">
          Currency
        </div>
      </template>
      <template #status="{ item }">
        <ZSwitch :model-value="item.status === 'enabled' ? true : false" size="medium" :loading="tempLoading[item.id]" @change="changeStateCurrency(item)" />
      </template>
      <template #action="{ item }">
        <NuxtLink :to="`/exchange/currencies/${item.id}`">
          <ZIcon type="edit" class="text-[20px]" />
        </Nuxtlink>
      </template>
      <template #total_assets="{ item }">
        {{ Number(item.total_assets) || '--' }} {{ item.id.toUpperCase() }}
      </template>
      <template #foot>
        <ZPagination v-model="query.page" v-model:page-size="query.limit" :loading="loading" :total="total" />
      </template>
    </ZTablePro>
  </div>
</template>

<style lang="less">
.page-exchange-currency {
  .id {
    max-width: 120px;
  }

  .status {
    max-width: 120px;
  }

  .action {
    max-width: 52px;
  }
}
</style>
