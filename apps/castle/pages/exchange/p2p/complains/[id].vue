<script setup lang="ts">
import { format as formatDate } from 'date-fns'
import type { ZAdminFormColumn } from '~/types'
import ModalDeny from '~/layouts/admin/exchange/p2p/complains/ModalDeny.vue'

const adminStore = useAdminStore()
const userStore = useUserStore()
const menuActionStore = useMenuActionStore()

const id = Number(useRoute().params.id) as number
const loading = ref(false)
const loadingProcessing = ref(false)
const visibleProcessing = ref(false)
const loadingProcessed = ref(false)
const visibleProcessed = ref(false)

const modalDeny = ref<InstanceType<typeof ModalDeny>>()

const { data: complain, pending, refresh } = await useAsyncData(() => adminStore.FetchComplain(id).then(res => res.data))

menuActionStore.setMenu([
  {
    key: 'refresh',
    title: 'Refresh',
    callback: refresh,
  },
])

const columns: ZAdminFormColumn[] = [
  {
    fields: [
      {
        key: 'static_id',
        type: ZAdminFormFieldType.Text,
        label: 'Static ID',
        value: complain.value!.static_id,
        scopedSlots: true,
      },
      {
        key: 'state',
        type: ZAdminFormFieldType.Text,
        label: 'State',
        value: complain.value!.state,
        scopedSlots: true,
      },
    ],
  },
  {
    fields: [
      {
        key: 'member_uid',
        type: ZAdminFormFieldType.Text,
        label: 'UID',
        value: complain.value!.member_uid,
        scopedSlots: true,
      },
      {
        key: 'supporter_uid',
        type: ZAdminFormFieldType.Text,
        label: 'Supporter_uid',
        value: complain.value!.supporter_uid || '--',
        scopedSlots: true,
      },
    ],
  },
  {
    fields: [
      {
        key: 'created_at',
        type: ZAdminFormFieldType.Text,
        label: 'Created At',
        value: complain.value!.created_at,
        replaceFunc: (text: string) => formatDate(new Date(text), 'yyyy-MM-dd HH:mm:ss'),
      },
      {
        key: 'response',
        type: ZAdminFormFieldType.Text,
        label: 'Response',
        value: complain.value!.response || '--',
      },
    ],
  },
]

async function ProcessingComplain() {
  loadingProcessing.value = true
  await adminStore.ProcessingComplain(id, () => {
    complain.value!.state = P2PComplainState.Processing
    complain.value!.supporter_uid = userStore.uid
  })
  loadingProcessing.value = false
}

async function ProcessedComplain() {
  loadingProcessed.value = true
  await adminStore.ProcessedComplain(id, () => {
    complain.value!.state = P2PComplainState.Processed
  })
  loadingProcessed.value = false
}

function DenyComplain(response: string) {
  complain.value!.state = P2PComplainState.Denied
  complain.value!.response = response
}
</script>

<template>
  <div class="page-dashboard-exchange-p2p-complain">
    <ZCard title="Complain Detail">
      <template #head>
        <div class="flex">
          <ZPopconfirm v-if="complain!.state === P2PComplainState.Pending" v-model="visibleProcessing" :placement="Placement.TopRight" :loading="loadingProcessing" trigger="click" @click="ProcessingComplain">
            <ZButton class="flex cursor-pointer page-dashboard-exchange-p2p-complain-button-blue items-center mr-4">
              Processing
            </ZButton>
          </ZPopconfirm>
          <ZPopconfirm v-if="complain!.state === P2PComplainState.Processing" v-model="visibleProcessed" :placement="Placement.TopRight" :loading="loadingProcessed" trigger="click" @click="ProcessedComplain">
            <ZButton class="flex cursor-pointer page-dashboard-exchange-p2p-complain-button-green items-center mr-4">
              Processed
            </ZButton>
          </ZPopconfirm>
          <ZButton v-if="complain!.state === P2PComplainState.Processing" class="flex cursor-pointer page-dashboard-exchange-p2p-complain-button-red items-center mr-4" @click="modalDeny?.openModal(id)">
            Deny
          </ZButton>
        </div>
      </template>
      <ZAdminForm :columns="columns" :loading="loading" :pending="pending" hidden-submit>
        <template #static_id>
          <div class="flex flex-col">
            <span>Static ID</span>
            <NuxtLink :to="`/exchange/p2p/trades/${complain!.static_id}`">
              {{ complain!.static_id }}
            </NuxtLink>
          </div>
        </template>
        <template #member_uid>
          <div class="flex flex-col">
            <span>Member UID</span>
            <NuxtLink :to="`/users/user_directory/${complain!.member_uid}`">
              {{ complain!.member_uid }}
            </NuxtLink>
          </div>
        </template>
        <template #supporter_uid>
          <div class="flex flex-col">
            <span>Supporter UID</span>
            <NuxtLink v-if="complain!.supporter_uid" :to="`/users/user_directory/${complain!.supporter_uid}`">
              {{ complain!.supporter_uid }}
            </NuxtLink>
            <span v-else>
              --
            </span>
          </div>
        </template>
        <template #state>
          <div class="flex flex-col">
            <span>State</span>
            <span
              class="capitalize" :class="[
                { 'text-red-500': complain!.state === P2PComplainState.Denied },
                { 'text-green-500': complain!.state === P2PComplainState.Processed },
                { 'text-gray-400': complain!.state === P2PComplainState.Pending },
                { 'text-blue-500': complain!.state === P2PComplainState.Processing },
              ]"
            >
              {{ complain!.state }}
            </span>
          </div>
        </template>
      </ZAdminForm>
      <div>
        <div>Content</div>
        <div>
          {{ complain!.content }}
        </div>
      </div>
    </ZCard>
    <ModalDeny ref="modalDeny" @click="(response) => DenyComplain(response)"/>
  </div>
</template>

<style lang="less">
.page-dashboard-exchange-p2p-complain {
  a {
    color: @text-color;
    transition: all 0.3s;

    &:hover {
      color: @text-color;
      text-decoration: underline;
    }
  }

  &-button {
    &-blue {
      color: white !important;
      border-color: @primary-color !important;
      background-color: @primary-color !important;
    }

    &-green {
      color: white !important;
      border-color: @up-color !important;
      background-color: @up-color !important;
    }

    &-red {
      color: white !important;
      border-color: @down-color !important;
      background-color: @down-color !important;
    }
  }

  &-side {
    padding: 4px 12px;
    line-height: 1;
    border-radius: 4px;

    &.page-dashboard-exchange-p2p-complain-side-red {
      color: @error-color;
      background-color: rgba(@error-color, 0.15);
    }

    &.page-dashboard-exchange-p2p-complain-side-green {
      color: @up-color;
      background-color: rgba(@up-color, 0.15);
    }
  }
}
</style>
