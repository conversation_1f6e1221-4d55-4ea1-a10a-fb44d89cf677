<script setup lang="ts">
const adminStore = useAdminStore()
const total = ref(0)
const loading = useState(() => false)
const { query, callbacks } = useQuery()
const menuActionStore = useMenuActionStore()

const { data: p2pTrades, pending, refresh } = await useAsyncData(async () => {
  const { data, headers } = await adminStore.FetchP2PTradesComplains(query.value)

  total.value = Number(headers.total)

  return data
}, { default: () => ([]) })

callbacks.push(refresh)

const columns: ZTableColumn[] = [
  {
    title: 'Maker UID',
    key: 'maker_uid',
    scopedSlots: true,
    overflow: true,
  },
  {
    title: 'Taker UID',
    key: 'taker_uid',
    scopedSlots: true,
    overflow: true,
  },
  {
    title: 'Amount',
    key: 'amount',
    scopedSlots: true,
  },
  {
    title: 'State',
    key: 'state',
    sort: true,
    sortBy: SortBy.String,
    scopedSlots: true,
  },
  {
    title: 'Action',
    key: 'action',
    scopedSlots: true,
    align: Align.Right,
  },
]

menuActionStore.setMenu([
  {
    key: 'refresh',
    title: 'Refresh',
    callback: refresh,
  },
])

// filterStore.setFilter([
//   {
//     key: 'static_id',
//     label: 'Static ID',
//     type: FilterType.Input,
//   },
//   {
//     key: 'maker_uid',
//     label: 'Maker UID',
//     type: FilterType.Input,
//   },
//   {
//     key: 'taker_uid',
//     label: 'Taker UID',
//     type: FilterType.Input,
//   },
//   {
//     key: 'advertisement_id',
//     label: 'Advertisement ID',
//     type: FilterType.Input,
//   },
//   {
//     key: 'payment',
//     label: 'Payment',
//     type: FilterType.Input,
//   },
//   {
//     key: 'state',
//     label: 'State',
//     type: FilterType.Select,
//     columns: [
//       {
//         key: 'value',
//         scopedSlots: true,
//       },
//     ],
//     replaceFunc: capitalize,
//     data: [
//       {
//         value: P2PTradeState.Pending,
//       },
//       {
//         value: P2PTradeState.Processing,
//       },
//       {
//         value: P2PTradeState.Success,
//       },
//       {
//         value: P2PTradeState.Expired,
//       },
//       {
//         value: P2PTradeState.Failed,
//       },
//     ],
//     findBy: ['value'],
//     valueKey: 'value',
//     labelKey: 'value',
//   },
// ])

// filterStore.onSubmit<Record<string, string>>(async (payload) => {
//   page.value = 1

//   query.value = {
//     page: query.value.page,
//     limit: query.value.limit,
//     ...payload,
//   }
// })
</script>

<template>
  <div class="page-p2p-trades">
    <ZTablePro v-model:query="query" :columns="columns" :data-source="p2pTrades" :loading="pending">
      <template #head>
        <div class="bold-text text-xl">
          P2P Trades
        </div>
      </template>
      <template #maker_uid="{ item }">
        <NuxtLink :to="`/users/user_directory/${item.maker_uid}`">
          {{ item.maker_uid }}
        </NuxtLink>
      </template>
      <template #taker_uid="{ item }">
        <NuxtLink :to="`/users/user_directory/${item.taker_uid}`">
          {{ item.taker_uid }}
        </NuxtLink>
      </template>
      <template #amount="{ item }">
        {{ `${item.amount} ${item.coin_currency.toUpperCase()}` }}
      </template>
      <template #state="{ item }">
        <span
          class="capitalize" :class="[
            { 'text-red-500': item.state === P2PTradeState.Failed || item.state === P2PTradeState.Expired },
            { 'text-blue-500': item.state === P2PTradeState.Processing },
            { 'text-green-500': item.state === P2PTradeState.Success },
            { 'text-gray-500': item.state === P2PTradeState.Pending },
          ]"
        >
          {{ item.state }}
        </span>
      </template>
      <template #action="{ item }">
        <NuxtLink :to="`/exchange/p2p/trades/${item.static_id}`">
          <ZIcon type="edit" class="text-[20px]" />
        </Nuxtlink>
      </template>
      <template #foot>
        <ZPagination v-model="query.page" v-model:page-size="query.limit" :loading="loading" :total="total" />
      </template>
    </ZTablePro>
  </div>
</template>

<style lang="less">
.page-p2p-trades {
  .id {
    max-width: 120px;
  }

  .z-table {
    a {
      color: @text-color;
      transition: all 0.3s;

      &:hover {
        color: @text-color;
        text-decoration: underline;
      }
    }
  }

  .action {
    max-width: 52px;
  }
}
</style>
