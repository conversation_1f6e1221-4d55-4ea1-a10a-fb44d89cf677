<script setup lang="ts">
import { FilterType } from '~/types'

const adminStore = useAdminStore()
const total = ref(0)
const loading = useState(() => false)
const filterStore = useFilterStore()
const { query, callbacks } = useQuery()
const menuActionStore = useMenuActionStore()

const { data: advertisements, pending, refresh } = await useAsyncData(async () => {
  const { data, headers } = await adminStore.FetchAdvertisements(query.value)

  total.value = Number(headers.total)

  return data
}, { default: () => ([]) })

callbacks.push(refresh)

const columns: ZTableColumn[] = [
  {
    title: 'ID',
    key: 'id',
    sort: true,
    sortBy: SortBy.String,
  },
  {
    title: 'UID',
    key: 'uid',
    sort: true,
    sortBy: SortBy.String,
    scopedSlots: true,
  },
  {
    title: 'Coin Currency',
    key: 'coin_currency',
    sort: true,
    sortBy: SortBy.String,
    toUpper: true,
  },
  {
    title: 'Fiat Currency',
    key: 'fiat_currency',
    sort: true,
    sortBy: SortBy.String,
    toUpper: true,
  },
  {
    title: 'Side',
    key: 'side',
    sort: true,
    sortBy: SortBy.String,
    toUpper: true,
    scopedSlots: true,
  },
  {
    title: 'State',
    key: 'state',
    sort: true,
    sortBy: SortBy.String,
    scopedSlots: true,
  },
  {
    title: 'Action',
    key: 'action',
    scopedSlots: true,
    align: Align.Right,
  },
]

menuActionStore.setMenu([
  {
    key: 'refresh',
    title: 'Refresh',
    callback: refresh,
  },
])

function capitalize(text: string) {
  return text[0].toUpperCase() + text.slice(1)
}

filterStore.setFilter([
  {
    key: 'state',
    label: 'State',
    type: FilterType.Select,
    columns: [
      {
        key: 'value',
        scopedSlots: true,
      },
    ],
    replaceFunc: capitalize,
    data: [
      {
        value: AdvertisementState.Enabled,
      },
      {
        value: AdvertisementState.Banned,
      },
      {
        value: AdvertisementState.Disabled,
      },
    ],
    findBy: ['value'],
    valueKey: 'value',
    labelKey: 'value',
  },
  {
    key: 'side',
    label: 'Side',
    type: FilterType.Select,
    columns: [
      {
        key: 'value',
        scopedSlots: true,
      },
    ],
    replaceFunc: capitalize,
    data: [
      {
        value: AdvertisementSide.Buy,
      },
      {
        value: AdvertisementSide.Sell,
      },
    ],
    findBy: ['value'],
    valueKey: 'value',
    labelKey: 'value',
  },
])

filterStore.onSubmit(async (payload) => {
  query.value = {
    page: 1,
    limit: query.value.limit,
    ...payload,
  }
})
</script>

<template>
  <div class="page-p2p-advertisements">
    <ZTablePro v-model:query="query" :columns="columns" :data-source="advertisements" :loading="pending">
      <template #head>
        <div class="bold-text text-xl">
          Advertisements
        </div>
      </template>
      <template #uid="{ item }">
        <NuxtLink :to="`/users/user_directory/${item.uid}`">
          {{ item.uid }}
        </NuxtLink>
      </template>
      <template #side="{ item }">
        <span
          class="page-exchange-advertisements-side capitalize" :class="[{ 'page-exchange-advertisements-side-red': item.side === AdvertisementSide.Sell }, { 'page-exchange-advertisements-side-green': item.side === AdvertisementSide.Buy }]"
        >
          {{ item.side }}
        </span>
      </template>
      <template #state="{ item }">
        <span
          class="capitalize" :class="[
            { 'text-red-500': item.state === AdvertisementState.Banned || item.state === AdvertisementState.Deleted || item.state === AdvertisementState.Disabled },
            { 'text-green-500': item.state === AdvertisementState.Enabled },
          ]"
        >
          {{ item.state }}
        </span>
      </template>
      <template #action="{ item }">
        <NuxtLink :to="`/exchange/p2p/advertisements/${item.id}`">
          <ZIcon type="edit" class="text-[20px]" />
        </Nuxtlink>
      </template>
      <template #foot>
        <ZPagination v-model="query.page" v-model:page-size="query.limit" :loading="loading" :total="total" />
      </template>
    </ZTablePro>
  </div>
</template>

<style lang="less">
.page-p2p-advertisements {
  .id {
    max-width: 120px;
  }

  &-side {
    padding: 4px 12px;
    line-height: 1;
    border-radius: 4px;

    &.page-exchange-advertisements-side-red {
      color: @error-color;
      background-color: rgba(@error-color, 0.15);
    }

    &.page-exchange-advertisements-side-green {
      color: @up-color;
      background-color: rgba(@up-color, 0.15);
    }
  }

  .z-table {
    a {
      color: @text-color;
      transition: all 0.3s;

      &:hover {
        color: @text-color;
        text-decoration: underline;
      }
    }
  }

  .action {
    max-width: 52px;
  }
}
</style>
