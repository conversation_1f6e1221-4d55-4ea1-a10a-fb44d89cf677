<script setup lang="ts">
import AdvertisementRow from '~/layouts/admin/exchange/p2p/advertisements/AdvertisementRow.vue'
import ModalState from '~/layouts/admin/exchange/p2p/payments/ModalState.vue'

const adminStore = useAdminStore()
const menuActionStore = useMenuActionStore()
const { query, callbacks } = useQuery()

const id = useRoute().params.id as string
const total = ref(0)
const loadingPayment = ref(false)
const loadingBan = ref(false)
const modalAdvertisement = ref(false)
const modalPayment = ref(false)
const payment = useState<Payment>(() => ({} as Payment))
const { data: advertisement, refresh: refreshAdvertisement } = await useAsyncData<Advertisement>(() => adminStore.FetchAdvertisement(id).then(res => res.data))

const { data: payments, pending: pendingPayment, refresh: refreshPayment } = await useAsyncData<Payment[]>(async () => {
  const { data } = await adminStore.FetchPaymentsInAdvertisement(id)

  return data
}, { default: () => ([]) })

const { data: p2pTrades, pending: pendingTrades, refresh: refreshTrades } = await useAsyncData<P2PTrade[]>(async () => {
  const { data, headers } = await adminStore.FetchP2PTradesInAdvertisement(id, query.value)
  total.value = Number(headers.total)

  return data
}, { default: () => ([]) })

callbacks.push(() => {
  refreshTrades()
  refreshPayment()
  refreshAdvertisement()
})

const columnsPayment: ZTableColumn[] = [
  {
    title: 'ID',
    key: 'id',
  },
  {
    title: 'UID',
    key: 'uid',
    scopedSlots: true,
    overflow: true,
  },
  {
    title: 'Type',
    key: 'type',
    class: 'capitalize',
  },
  {
    title: 'State',
    key: 'state',
    class: 'capitalize',
  },
  {
    title: 'Action',
    key: 'action',
    scopedSlots: true,
    align: Align.Right,
  },
]

const columnsTrade: ZTableColumn[] = [
  {
    title: 'Maker Email',
    key: 'maker_email',
    scopedSlots: true,
    overflow: true,
  },
  {
    title: 'Taker Email',
    key: 'taker_email',
    scopedSlots: true,
    overflow: true,
  },
  {
    title: 'Amount',
    key: 'amount',
    scopedSlots: true,
  },
  {
    title: 'State',
    key: 'state',
    sort: true,
    sortBy: SortBy.String,
    scopedSlots: true,
  },
  {
    title: 'Action',
    key: 'action',
    scopedSlots: true,
    align: Align.Right,
  },
]

menuActionStore.setMenu([
  {
    key: 'refresh',
    title: 'Refresh',
    callback: async () => {
      await Promise.all([
        refreshAdvertisement(),
        refreshPayment(),
        refreshTrades(),
      ])
    },
  },
])

function showModal(item: Payment) {
  payment.value = item
  modalPayment.value = true
}

async function BanAdvertisement() {
  loadingBan.value = true
  await adminStore.BanAdvertisement(id, () => {
    advertisement.value!.state = AdvertisementState.Banned
  })
  loadingBan.value = false
  modalAdvertisement.value = false
}

async function UnbanAdvertisement() {
  loadingBan.value = true
  await adminStore.UnbanAdvertisement(id, () => {
    advertisement.value!.state = AdvertisementState.Enabled
  })
  loadingBan.value = false
  modalAdvertisement.value = false
}

async function ChangeStatePayment(payment: Payment) {
  loadingPayment.value = true

  await adminStore.UpdatePayment(Number(payment.id), payment.state, () => {
    modalPayment.value = false

    const index = payments.value.findIndex(p => Number(p.id) === Number(payment.id))
    if (index !== -1) {
      payments.value[index].state = payment.state
    }
  })

  loadingPayment.value = false
}
</script>

<template>
  <div class="page-dashboard-exchange-p2p-advertisement">
    <ZCard title="Advertisement Detail">
      <template #head>
        <ZPopconfirm v-if="advertisement!.state !== AdvertisementState.Banned" v-model="modalAdvertisement" :placement="Placement.TopRight" :loading="loadingBan" trigger="click" @click="BanAdvertisement">
          <ZButton class="flex cursor-pointer items-center">
            Ban
          </ZButton>
        </ZPopconfirm>
        <ZPopconfirm v-if="advertisement!.state === AdvertisementState.Banned" v-model="modalAdvertisement" :placement="Placement.TopRight" :loading="loadingBan" trigger="click" @click="UnbanAdvertisement">
          <ZButton class="flex cursor-pointer items-center">
            Unban
          </ZButton>
        </ZPopconfirm>
      </template>
      <div class="page-dashboard-exchange-p2p-advertisement-head">
        <span>Coin Currency</span>
        <span>Fiat Currency</span>
        <span>Side</span>
        <span>Price</span>
        <span>Limit/Available</span>
        <span class="flex justify-end">State</span>
      </div>
      <AdvertisementRow :side="advertisement!.side" :advertisement="advertisement!" />
    </ZCard>
    <ZTablePro class="mt-3" :columns="columnsPayment" :data-source="payments" :loading="pendingPayment">
      <template #head>
        <div class="bold-text text-xl">
          Payments
        </div>
      </template>
      <template #uid="{ item }">
        <NuxtLink :to="`/users/user_directory/${item.uid}`">
          {{ item.uid }}
        </NuxtLink>
      </template>
      <template #taker_uid="{ item }">
        <NuxtLink :to="`/users/user_directory/${item.taker_uid}`">
          {{ item.taker_uid }}
        </NuxtLink>
      </template>
      <template #state="{ item }">
        <span
          class="capitalize" :class="[
            { 'text-red-500': item.state === P2PTradeState.Failed || item.state === P2PTradeState.Expired },
            { 'text-blue-500': item.state === P2PTradeState.Processing },
            { 'text-green-500': item.state === P2PTradeState.Success },
            { 'text-gray-500': item.state === P2PTradeState.Pending },
          ]"
        >
          {{ item.state }}
        </span>
      </template>
      <template #action="{ item }">
        <span class="cursor-pointer text-blue-500">
          <ZIcon type="edit" class="text-[20px]" @click="showModal(item)" />
        </span>
      </template>
    </ZTablePro>

    <ZTablePro v-model:query="query" class="mt-3" :columns="columnsTrade" :data-source="p2pTrades" :loading="pendingTrades">
      <template #head>
        <div class="bold-text text-xl">
          P2P Trades
        </div>
      </template>
      <template #maker_email="{ item }">
        <NuxtLink :to="`/users/user_directory/${item.maker_uid}`">
          {{ item.maker_email }}
        </NuxtLink>
      </template>
      <template #taker_email="{ item }">
        <NuxtLink :to="`/users/user_directory/${item.taker_uid}`">
          {{ item.taker_email }}
        </NuxtLink>
      </template>
      <template #amount="{ item }">
        {{ `${item.amount} ${item.coin_currency.toUpperCase()}` }}
      </template>
      <template #state="{ item }">
        <span
          class="capitalize" :class="[
            { 'text-red-500': item.state === P2PTradeState.Failed || item.state === P2PTradeState.Expired },
            { 'text-blue-500': item.state === P2PTradeState.Processing },
            { 'text-green-500': item.state === P2PTradeState.Success },
            { 'text-gray-500': item.state === P2PTradeState.Pending },
          ]"
        >
          {{ item.state }}
        </span>
      </template>
      <template #action="{ item }">
        <NuxtLink :to="`/exchange/p2p/trades/${item.static_id}`">
          <ZIcon type="edit" class="text-[20px]" />
        </Nuxtlink>
      </template>
      <template #foot>
        <ZPagination v-model="query.page" v-model:page-size="query.limit" :loading="pendingTrades" :total="total" />
      </template>
    </ZTablePro>
    <ModalState v-model="modalPayment" :data="payment" :loading="loadingPayment" @click="ChangeStatePayment" />
  </div>
</template>

<style lang="less">
.page-dashboard-exchange-p2p-advertisement {
  &-side {
    padding: 4px 12px;
    line-height: 1;
    border-radius: 4px;

    &.page-dashboard-exchange-p2p-advertisement-side-red {
      color: @error-color;
      background-color: rgba(@error-color, 0.15);
    }

    &.page-dashboard-exchange-p2p-advertisement-side-green {
      color: @up-color;
      background-color: rgba(@up-color, 0.15);
    }
  }

  .z-table {
    a {
      color: @text-color;
      transition: all 0.3s;

      &:hover {
        color: @text-color;
        text-decoration: underline;
      }
    }
  }

  &-head {
    display: flex;
    padding: 12px 24px;
    color: @gray-color;
    font-size: 14px;

    & > span {
      flex: 1;
    }
  }
}
</style>
