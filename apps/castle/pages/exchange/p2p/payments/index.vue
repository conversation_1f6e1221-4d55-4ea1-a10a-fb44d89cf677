<script setup lang="ts">
import { FilterType, PaymentState } from '~/types'
import ModalState from '~/layouts/admin/exchange/p2p/payments/ModalState.vue'

const adminStore = useAdminStore()
const total = ref(0)
const loading = useState(() => false)
const filterStore = useFilterStore()
const { query, callbacks } = useQuery()
const menuActionStore = useMenuActionStore()

const modalState = ref<InstanceType<typeof ModalState>>()

const { data: payments, pending, refresh } = await useAsyncData(async () => {
  const { data, headers } = await adminStore.FetchPayments(query.value)

  total.value = Number(headers.total)

  return data
}, { default: () => ([]) })

callbacks.push(refresh)

const columns: ZTableColumn[] = [
  {
    title: 'ID',
    key: 'id',
  },
  {
    title: 'UID',
    key: 'uid',
    scopedSlots: true,
    overflow: true,
  },
  {
    title: 'Type',
    key: 'type',
    class: 'capitalize',
  },
  {
    title: 'State',
    key: 'state',
    class: 'capitalize',
  },
  {
    title: 'Action',
    key: 'action',
    scopedSlots: true,
    align: Align.Right,
  },
]

menuActionStore.setMenu([
  {
    key: 'refresh',
    title: 'Refresh',
    callback: refresh,
  },
])

function capitalize(text: string) {
  return text[0].toUpperCase() + text.slice(1)
}

filterStore.setFilter([
  {
    key: 'uid',
    label: 'UID',
    type: FilterType.Input,
  },
  {
    key: 'email',
    label: 'Email',
    type: FilterType.Input,
  },
  {
    key: 'type',
    label: 'Type',
    type: FilterType.Input,
  },
  {
    key: 'state',
    label: 'State',
    type: FilterType.Select,
    columns: [
      {
        key: 'value',
        scopedSlots: true,
      },
    ],
    replaceFunc: capitalize,
    data: [
      {
        value: PaymentState.Enabled,
      },
      {
        value: PaymentState.Disabled,
      },
      {
        value: PaymentState.Denied,
      },
    ],
    findBy: ['value'],
    valueKey: 'value',
    labelKey: 'value',
  },
])

filterStore.onSubmit(async (payload) => {
  query.value = {
    page: 1,
    limit: query.value.limit,
    ...payload,
  }
})
</script>

<template>
  <div class="page-p2p-payments">
    <ZTablePro v-model:query="query" :columns="columns" :data-source="payments" :loading="pending">
      <template #head>
        <div class="bold-text text-xl">
          Payments
        </div>
      </template>
      <template #uid="{ item }">
        <NuxtLink :to="`/users/user_directory/${item.uid}`">
          {{ item.uid }}
        </NuxtLink>
      </template>
      <template #taker_uid="{ item }">
        <NuxtLink :to="`/users/user_directory/${item.taker_uid}`">
          {{ item.taker_uid }}
        </NuxtLink>
      </template>
      <template #state="{ item }">
        <span
          class="capitalize" :class="[
            { 'text-red-500': item.state === P2PTradeState.Failed || item.state === P2PTradeState.Expired },
            { 'text-blue-500': item.state === P2PTradeState.Processing },
            { 'text-green-500': item.state === P2PTradeState.Success },
            { 'text-gray-500': item.state === P2PTradeState.Pending },
          ]"
        >
          {{ item.state }}
        </span>
      </template>
      <template #action="{ item }">
        <span class="cursor-pointer text-blue-500">
          <ZIcon type="edit" class="text-[20px]" @click="modalState?.openModal(item)" />
        </span>
      </template>
      <template #foot>
        <ZPagination v-model="query.page" v-model:page-size="query.limit" :loading="loading" :total="total" />
      </template>
    </ZTablePro>
    <ModalState ref="modalState" />
  </div>
</template>

<style lang="less">
.page-p2p-payments {
  .id {
    max-width: 120px;
  }

  .z-table {
    a {
      color: @text-color;
      transition: all 0.3s;

      &:hover {
        color: @text-color;
        text-decoration: underline;
      }
    }
  }

  .action {
    max-width: 52px;
  }
}
</style>
