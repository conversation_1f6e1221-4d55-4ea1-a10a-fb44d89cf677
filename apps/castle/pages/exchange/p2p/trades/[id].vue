<script setup lang="ts">
import { format as formatDate } from 'date-fns'
import type { ZAdminFormColumn } from '~/types'
import ModalFail from '~/layouts/admin/exchange/p2p/trades/ModalFail.vue'
import ModalPreview from '~/layouts/admin/exchange/p2p/trades/ModalPreview.vue'

const runtimeConfig = useRuntimeConfig()
const adminStore = useAdminStore()
const userStore = useUserStore()
const menuActionStore = useMenuActionStore()

const modalFail = ref<InstanceType<typeof ModalFail>>()
const modalPreview = ref<InstanceType<typeof ModalPreview>>()

const id = useRoute().params.id as string
const loading = ref(false)
const type = ref('process')

const { data: p2pTrade, pending: pendingP2PTrade, refresh: refreshP2PTrade } = await useAsyncData(() => adminStore.FetchP2PTrade(id).then(res => res.data), { lazy: false })
const { data: conversation, pending: pendingConversation, refresh: refreshConversation } = await useAsyncData(() => adminStore.FetchP2PTradeConversations(id).then(res => res.data), { default: () => ([]) })
const { data: p2pComplains, pending: pendingComplains, refresh: refreshComplains } = await useAsyncData(() => adminStore.FetchComplainsByStaticID(id).then(res => res.data))

const columnComplains: ZTableColumn[] = [
  {
    title: 'Member Email',
    key: 'member_email',
    scopedSlots: true,
    overflow: true,
  },
  {
    title: 'Supporter Email',
    key: 'supporter_email',
    scopedSlots: true,
    overflow: true,
  },
  {
    title: 'State',
    key: 'state',
    sort: true,
    sortBy: SortBy.String,
    scopedSlots: true,
  },
  {
    title: 'Content',
    key: 'content',
  },
]

menuActionStore.setMenu([
  {
    key: 'refresh',
    title: 'Refresh',
    callback: async () => {
      await refreshP2PTrade()
      await refreshConversation()
      await refreshComplains()
    },
  },
])

const columns: ZAdminFormColumn[] = [
  {
    fields: [
      {
        key: 'static_id',
        type: ZAdminFormFieldType.Text,
        label: 'Static ID',
        value: p2pTrade.value!.static_id,
      },
      {
        key: 'payment_id',
        type: ZAdminFormFieldType.Text,
        label: 'Payment ID',
        value: p2pTrade.value!.payment_id,
        scopedSlots: true,
      },
      {
        key: 'maker_email',
        type: ZAdminFormFieldType.Text,
        label: 'Maker Email',
        value: p2pTrade.value!.maker_email,
        scopedSlots: true,
      },
      {
        key: 'amount',
        type: ZAdminFormFieldType.Text,
        label: 'Amount',
        value: p2pTrade.value!.amount,
        scopedSlots: true,
      },
      {
        key: 'maker_accept_at',
        type: ZAdminFormFieldType.Text,
        label: 'Maker Accept At',
        value: p2pTrade.value!.maker_accept_at,
        replaceFunc: (text: string) => formatDate(new Date(text), 'yyyy-MM-dd HH:mm:ss'),
      },
      {
        key: 'created_at',
        type: ZAdminFormFieldType.Text,
        label: 'Created At',
        value: p2pTrade.value!.created_at,
        replaceFunc: (text: string) => formatDate(new Date(text), 'yyyy-MM-dd HH:mm:ss'),
      },
      {
        key: 'completed_at',
        type: ZAdminFormFieldType.Text,
        label: 'Completed At',
        value: p2pTrade.value!.completed_at,
        replaceFunc: (text: string) => formatDate(new Date(text), 'yyyy-MM-dd HH:mm:ss'),
      },
      {
        key: 'unpaid_email',
        type: ZAdminFormFieldType.Text,
        label: 'Unpaid Email',
        value: p2pTrade.value!.unpaid_email,
        scopedSlots: true,
      },
    ],
  },
  {
    fields: [
      {
        key: 'advertisement_id',
        type: ZAdminFormFieldType.Text,
        label: 'Advertisement ID',
        value: p2pTrade.value!.advertisement_id,
        scopedSlots: true,
      },
      {
        key: 'state',
        type: ZAdminFormFieldType.Text,
        label: 'State',
        value: p2pTrade.value!.state,
        scopedSlots: true,
      },
      {
        key: 'taker_email',
        type: ZAdminFormFieldType.Text,
        label: 'Taker Email',
        value: p2pTrade.value!.taker_email,
        scopedSlots: true,
      },
      {
        key: 'locked',
        type: ZAdminFormFieldType.Text,
        scopedSlots: true,
      },
      {
        key: 'taker_accept_at',
        type: ZAdminFormFieldType.Text,
        label: 'Taker Accept At',
        value: p2pTrade.value!.taker_accept_at,
        replaceFunc: (text: string) => formatDate(new Date(text), 'yyyy-MM-dd HH:mm:ss'),
      },
      {
        key: 'expired_at',
        type: ZAdminFormFieldType.Text,
        label: 'Expired At',
        value: p2pTrade.value!.expired_at,
        replaceFunc: (text: string) => formatDate(new Date(text), 'yyyy-MM-dd HH:mm:ss'),
      },
      {
        key: 'supporter_email',
        type: ZAdminFormFieldType.Text,
        label: 'Supporter Email',
        value: p2pTrade.value!.supporter_email,
        scopedSlots: true,
      },
    ],
  },
]

async function HandleP2PTrade(typeModal: string, unpaidId: number, response: string) {
  type.value = typeModal

  if (type.value === 'process') {
    await adminStore.ProcessP2PTrade(id, unpaidId, () => {
      p2pTrade.value!.state = P2PTradeState.Success
      p2pTrade.value!.supporter_email = userStore.email
      p2pTrade.value!.unpaid_email = unpaidId === p2pTrade.value!.maker_id ? p2pTrade.value!.maker_email : p2pTrade.value!.taker_email
    })
  } else {
    await adminStore.FailP2PTrade(id, unpaidId, () => {
      p2pTrade.value!.state = P2PTradeState.Failed
      p2pTrade.value!.supporter_email = userStore.email
      p2pTrade.value!.unpaid_email = unpaidId === p2pTrade.value!.maker_id ? p2pTrade.value!.maker_email : p2pTrade.value!.taker_email
    })
  }

  const index = p2pComplains.value!.findIndex(c => Number(c.member_id) === Number(unpaidId))
  if (index !== -1) {
    await adminStore.DenyComplain(p2pComplains.value![index].id, response, () => {
      p2pComplains.value![index].state = P2PComplainState.Denied
      p2pComplains.value![index].response = response
    })

    if (p2pComplains.value!.length === 2) {
      await adminStore.ProcessedComplain(p2pComplains.value![1 - index].id, () => {
        p2pComplains.value![1 - index].state = P2PComplainState.Processed
      })
    }
  } else {
    if (p2pComplains.value!.length === 2) {
      await adminStore.ProcessedComplain(p2pComplains.value![0].id, () => {
        p2pComplains.value![0].state = P2PComplainState.Processed
      })
    }
  }
}
</script>

<template>
  <div class="page-dashboard-exchange-p2p-trade">
    <ZCard title="P2P Trade Detail" class="mb-4">
      <template #head>
        <div class="flex">
          <ZButton v-if="p2p_trade.state === P2PTradeState.Expired && p2p_complains.length > 0" class="flex page-dashboard-exchange-p2p-trade-process mr-4 cursor-pointer items-center" @click="modalFail?.openModal('process', p2pTrade)">
            Process
          </ZButton>
          <ZButton v-if="p2p_trade.state === P2PTradeState.Expired && p2p_complains.length > 0" class="flex page-dashboard-exchange-p2p-trade-fail cursor-pointer items-center" @click="modalFail?.openModal('fail', p2pTrade)">
            Fail
          </ZButton>
        </div>
      </template>
      <div class="flex">
        <ZAdminForm class="w-8/12" :columns="columns" :loading="loading" :pending="pendingP2PTrade || pendingConversation" hidden-submit>
          <template #maker_email>
            <div class="flex flex-col">
              <span>Maker Email</span>
              <div>
                <NuxtLink :to="`/users/user_directory/${p2p_trade.maker_uid}`">
                  {{ p2p_trade.maker_email }}
                </NuxtLink>
              </div>
            </div>
          </template>
          <template #taker_email>
            <div class="flex flex-col">
              <span>Taker Email</span>
              <div>
                <NuxtLink :to="`/users/user_directory/${p2p_trade.taker_uid}`">
                  {{ p2p_trade.taker_email }}
                </NuxtLink>
              </div>
            </div>
          </template>
          <template #payment_id>
            <div class="flex flex-col">
              <span>Payment ID</span>
              <div>
                <NuxtLink :to="`/exchange/p2p/payments/${p2p_trade.payment_id}`">
                  {{ p2p_trade.payment_id }}
                </NuxtLink>
              </div>
            </div>
          </template>
          <template #advertisement_id>
            <div class="flex flex-col">
              <span>Advertisement ID</span>
              <div>
                <NuxtLink :to="`/exchange/p2p/advertisements/${p2p_trade.advertisement_id}`">
                  {{ p2p_trade.advertisement_id }}
                </NuxtLink>
              </div>
            </div>
          </template>
          <template #supporter_email>
            <div class="flex flex-col">
              <span>Supporter Email</span>
              <div v-if="p2p_trade.supporter_uid">
                <NuxtLink :to="`/users/user_directory/${p2p_trade.supporter_uid}`">
                  {{ p2p_trade.supporter_email }}
                </NuxtLink>
              </div>
              <div v-else>
                --
              </div>
            </div>
          </template>
          <template #unpaid_email>
            <div class="flex flex-col">
              <span>Unpaid Email</span>
              <div v-if="p2p_trade.unpaid_uid">
                <NuxtLink :to="`/users/user_directory/${p2p_trade.unpaid_uid}`">
                  {{ p2p_trade.unpaid_email }}
                </NuxtLink>
              </div>
              <div v-else>
                --
              </div>
            </div>
          </template>
          <template #amount>
            <div class="flex flex-col">
              <span>Amount</span>
              <span>{{ `${p2p_trade.amount} ${p2p_trade.coin_currency.toUpperCase()}` }}</span>
            </div>
          </template>
          <template #locked>
            <div class="flex flex-col">
              <span>{{ p2p_trade.maker_locked ? 'Maker Locked' : 'Taker Locked' }}</span>
              <span>{{ `${p2p_trade.maker_locked ? p2p_trade.maker_locked : p2p_trade.taker_locked} ${p2p_trade.coin_currency.toUpperCase()}` }}</span>
            </div>
          </template>
          <template #state>
            <div class="flex flex-col">
              <span>State</span>
              <span
                class="capitalize" :class="[
                  { 'text-red-500': p2p_trade.state === P2PTradeState.Failed || p2p_trade.state === P2PTradeState.Expired },
                  { 'text-green-500': p2p_trade.state === P2PTradeState.Success },
                  { 'text-blue-500': p2p_trade.state === P2PTradeState.Processing },
                  { 'text-gray-400': p2p_trade.state === P2PTradeState.Pending },
                ]"
              >
                {{ p2p_trade.state }}
              </span>
            </div>
          </template>
        </ZAdminForm>
        <div class="w-4/12 page-dashboard-exchange-p2p-trade-chat">
          <div class="page-dashboard-exchange-p2p-trade-chat-head bold-text">
            Conversations
          </div>
          <div class="page-dashboard-exchange-p2p-trade-chat-content">
            <div
              v-for="(mess, index) in conversation"
              :key="mess.id"
              class="flex flex-col"
              :class="[
                { 'justify-start': mess.uid !== p2p_trade.taker_uid },
                { 'justify-end': mess.uid === p2p_trade.maker_uid },
              ]"
            >
              <div
                v-if="index === 0 || conversation[index].uid !== conversation[index - 1].uid"
                class="flex mb-2 text-gray"
                :class="[
                  { 'justify-start': mess.uid !== p2p_trade.taker_uid },
                  { 'justify-end': mess.uid === p2p_trade.maker_uid },
                ]"
              >
                {{ mess.email }}
              </div>
              <div
                class="flex"
                :class="[
                  { 'justify-start': mess.uid !== p2p_trade.taker_uid },
                  { 'justify-end': mess.uid === p2p_trade.maker_uid },
                ]"
              >
                <div v-if="mess.filename" class="page-dashboard-exchange-p2p-trade-chat-content-item-image">
                  <img class="max-w-[200px]" :src="`${runtimeConfig.public.apiUrl}trade/admin/p2p_trades/${p2p_trade.static_id}/conversations/${mess.filename}`" @click="modalPreview?.openModal(`${runtimeConfig.public.apiUrl}trade/admin/p2p_trades/${p2p_trade.static_id}/conversations/${mess.filename}`)">
                </div>
                <div
                  v-else
                  class="page-dashboard-exchange-p2p-trade-chat-content-item"
                >
                  {{ mess.content }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </ZCard>

    <ZTablePro v-if="p2p_complains.length > 0" :columns="columnComplains" :data-source="p2p_complains" :loading="pendingComplains">
      <template #head>
        <div class="flex justify-between">
          <div class="bold-text text-xl">
            P2P Complains
          </div>
        </div>
      </template>
      <template #member_email="{ item }">
        <NuxtLink :to="`/users/user_directory/${item.member_uid}`">
          {{ item.member_email }}
        </NuxtLink>
      </template>
      <template #static_id="{ item }">
        <NuxtLink :to="`/exchange/p2p/trades/${item.static_id}`">
          {{ item.static_id }}
        </NuxtLink>
      </template>
      <template #supporter_email="{ item }">
        <NuxtLink :to="`/users/user_directory/${item.supporter_uid}`">
          {{ item.supporter_email }}
        </NuxtLink>
      </template>
      <template #state="{ item }">
        <span
          class="capitalize" :class="[
            { 'text-red-500': item.state === P2PComplainState.Denied },
            { 'text-blue-500': item.state === P2PComplainState.Processing },
            { 'text-green-500': item.state === P2PComplainState.Processed },
            { 'text-gray-400': item.state === P2PComplainState.Pending },
          ]"
        >
          {{ item.state }}
        </span>
      </template>
    </ZTablePro>
    <ModalFail ref="modalFail" @click="(typeModal, unpaid, response) => HandleP2PTrade(typeModal, unpaid, response)"/>
    <ModalPreview ref="modalPreview" />
  </div>
</template>

<style lang="less">
.page-dashboard-exchange-p2p-trade {
  &-primary {
    color: @primary-color;
    cursor: pointer;
  }

  a {
    color: @text-color;
    transition: all 0.3s;

    &:hover {
      color: @text-color;
      text-decoration: underline;
    }
  }

  .z-table-pro {
    .member_email {
      max-width: 200px;
    }

    .supporter_email {
      max-width: 200px;
    }

    .state {
      max-width: 100px;
    }

    .action {
      max-width: 200px;
    }
  }

  &-process {
    color: white !important;
    border-color: @primary-color !important;
    background-color: @primary-color !important;
  }

  &-fail {
    color: white !important;
    border-color: @down-color !important;
    background-color: @down-color !important;
  }

  &-chat {
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    &-head {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      padding: 12px;
      font-size: 18px;
      border: 1px solid @base-border-color;
      border-top-right-radius: 4px;
      border-top-left-radius: 4px;
    }

    &-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      max-height: 640px;
      padding: 12px;
      border: 1px solid @base-border-color;
      border-top: none;
      border-bottom-right-radius: 4px;
      border-bottom-left-radius: 4px;
      background-color: rgba(@gray-color, 0.03);
      overflow-y: auto;

      &-item {
        padding: 8px 12px;
        margin-bottom: 4px;
        border-radius: 8px;
        background-color: rgba(@primary-color, 0.6);
        color: white;

        &-image {
          margin-bottom: 12px;
        }
      }
    }
  }
}
</style>
