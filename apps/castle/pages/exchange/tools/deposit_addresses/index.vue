<script setup lang="ts">
import { Align, ParseType } from '@zsmartex/types'
import ModalExport from '~/layouts/admin/exchange/deposit_addresses/ModalExport.vue';
import ModalOTP from '~/layouts/admin/exchange/deposit_addresses/ModalOTP.vue';
import { FilterType } from '~/types'

const adminStore = useAdminStore()
const { query, callbacks } = useQuery()
const filterStore = useFilterStore()
const menuActionStore = useMenuActionStore()
const tempExport = useState<Record<string, boolean>>(() => ({}))

const modalExport = ref<InstanceType<typeof ModalExport>>()
const modalOTP = ref<InstanceType<typeof ModalOTP>>()

const { data: depositAddresses, pending, refresh } = await useAsyncData(async () => {
  const { data } = await adminStore.FetchDepositAddresses(query.value)

  return data
}, { default: () => ([] as DepositAddress[]) })

const { data: nextDepositAddresses, refresh: nextRefresh } = await useAsyncData(async () => {
  const { data } = await adminStore.FetchDepositAddresses({
    ...query.value,
    page: (query.value.page || 1) * (query.value.limit || 25) + 1,
    limit: 1,
  })

  return data
}, { default: () => ([]) })

callbacks.push(refresh, nextRefresh)

const columns: ZTableColumn[] = [
  {
    title: 'ID',
    key: 'id',
    sort: true,
    sortBy: SortBy.String,
  },
  {
    title: 'Email',
    key: 'email',
    overflow: true,
    scopedSlots: true,
  },
  {
    title: 'Address',
    key: 'address',
    overflow: true,
  },
  {
    title: 'Blockchain Key',
    key: 'blockchain_key',
    scopedSlots: true,
  },
  {
    title: 'Last Exported By',
    key: 'last_exported_by',
  },
  {
    title: 'Exported At',
    key: 'exported_at',
    formatBy: Format.DateTime,
    parse: ParseType.DateTime,
    sort: true,
  },
  {
    title: 'Action',
    key: 'action',
    scopedSlots: true,
    align: Align.Right,
  },
]

menuActionStore.setMenu([
  {
    key: 'refresh',
    title: 'Refresh',
    callback: () => {
      refresh()
      nextRefresh()
    },
  },
])

filterStore.setFilter([
  {
    key: 'uid',
    label: 'UID',
    type: FilterType.Input,
  },
  {
    key: 'email',
    label: 'Email',
    type: FilterType.Input,
  },
  {
    key: 'address',
    label: 'Address',
    type: FilterType.Input,
  },
  {
    key: 'blockchain_key',
    type: FilterType.Select,
    label: 'Blockchain Key',
    data: adminStore.blockchains,
    scroll: true,
    columns: [
      {
          key: 'name',
      },
    ],
    findBy: ['key'],
    valueKey: 'key',
    labelKey: 'name',
  },
])

filterStore.onSubmit<Record<string, string>>(async (payload) => {
  query.value = {
    page: 1,
    limit: query.value.limit,
    ...payload,
  }
})

async function ExportDepositAddress(item: DepositAddress) {
  modalOTP.value?.openModal(item)
}

function OpenExportModal(data: {
    private_key: string;
    address: string;
    exported_at: string;
    last_exported_by: string;
}) {
  if (Object.keys(data).length > 0) {
    depositAddresses.value.forEach(a => {
      if (a.address === data.address) {
        a.exported_at = data.exported_at
        a.last_exported_by = data.last_exported_by
      }
    })

    modalExport.value?.openModal(data)
  }
}

function GetNameBlockchain(key: string) {
  const blockchain = adminStore.blockchains.find(b => b.key === key);
  if (!blockchain) return '---'
  return blockchain.name
}
</script>

<template>
  <div class="page-exchange-deposit-addresses">
    <ZTablePro v-model:query="query" :columns="columns" :data-source="depositAddresses" :loading="pending">
      <template #head>
        <div class="bold-text text-xl">
          Deposit Addresses
        </div>
      </template>
      <template #blockchain_key="{ item }">
        <span>{{ GetNameBlockchain(item.blockchain_key) }}</span>
      </template>
      <template #email="{ item }">
        <NuxtLink :to="`/users/user_directory/${item.uid}`">
          {{ item.email }}
        </NuxtLink>
      </template>
      <template #action="{ item }">
        <ZPopconfirm v-if="item.able_to_export" v-model="tempExport[item.id]" :placement="Placement.TopRight" trigger="click" @click="ExportDepositAddress(item)">
          <ZButton class="export">
            Export
          </ZButton>
        </ZPopconfirm>
      </template>
      <template #foot>
        <ZPagination v-model="query.page" v-model:page-size="query.limit" :is-loading="pending" :next="nextDepositAddresses.length > 0" />
      </template>
    </ZTablePro>
    <ModalExport ref="modalExport" />
    <ModalOTP ref="modalOTP" @on-submit="OpenExportModal" />
  </div>
</template>

<style lang="less">
.page-exchange-deposit-addresses {
  .id {
    max-width: 120px;
  }

  .action {
    max-width: 52px;
  }
}
</style>
