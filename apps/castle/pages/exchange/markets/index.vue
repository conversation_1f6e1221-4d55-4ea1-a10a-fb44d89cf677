<script setup lang="ts">
import { Align, MarketState } from '@zsmartex/types'
import type { ZTableColumn } from '@zsmartex/components'
import { useOfflineData } from '@zsmartex/core/composables'
import { FilterType } from '~/types'

const adminStore = useAdminStore()
const filterStore = useFilterStore()
const menuActionStore = useMenuActionStore()
const total = useState(() => 0)
const loading = useState(() => false)
const tempLoading = useState<Record<string, boolean>>(() => ({}))

const { query } = useQuery()

const markets = computed(() => {
  const data = adminStore.markets.map(item => ({
    ...item,
  }))

  total.value = data.length

  return useOfflineData(query, data)
})

const columns: ZTableColumn[] = [
  {
    title: 'Market',
    key: 'market',
    scopedSlots: true,
  },
  {
    title: 'Price precision',
    key: 'price_precision',
    sort: true,
    sortBy: SortBy.Number,
  },
  {
    title: 'Amount precision',
    key: 'amount_precision',
    sort: true,
    sortBy: SortBy.Number,
  },
  {
    title: 'Total precision',
    key: 'total_precision',
    sort: true,
    sortBy: SortBy.Number,
  },
  {
    title: 'Min Price',
    key: 'min_price',
    sort: true,
    sortBy: SortBy.Number,
  },
  {
    title: 'Max Price',
    key: 'max_price',
    sort: true,
    sortBy: SortBy.Number,
  },
  {
    title: 'Min Amount',
    key: 'min_amount',
    sort: true,
    sortBy: SortBy.Number,
  },
  {
    title: 'State',
    key: 'state',
    scopedSlots: true,
  },
  {
    title: 'Action',
    key: 'action',
    scopedSlots: true,
    align: Align.Right,
  },
]

menuActionStore.setMenu([
  {
    key: 'refresh',
    title: 'Refresh',
    callback: async () => {
      loading.value = true
      await adminStore.FetchMarkets()
      loading.value = false
    },
  },
  {
    key: 'create market',
    icon: 'plus',
    title: 'Create Market',
    callback: () => {
      navigateTo({
        path: '/exchange/markets/create',
      })
    },
  },
])

function capitalize(text: string) {
  return text[0].toUpperCase() + text.slice(1)
}

filterStore.setFilter([
  {
    key: 'state',
    label: 'State',
    type: FilterType.Select,
    columns: [
      {
        key: 'value',
        scopedSlots: true,
      },
    ],
    replaceFunc: capitalize,
    data: [
      {
        value: MarketState.Enabled,
      },
      {
        value: MarketState.Disabled,

      },
    ],
    findBy: ['value'],
    valueKey: 'value',
    labelKey: 'value',
  },
  {
    key: 'id',
    label: 'Market',
    type: FilterType.Input,
  },
  {
    key: 'base_unit',
    label: 'Base Currency',
    type: FilterType.Input,
  },
  {
    key: 'quote_unit',
    label: 'Quote Currency',
    type: FilterType.Input,
  },
])

async function changeStateMarket(item: any) {
  tempLoading.value[item.id] = true
  await adminStore.UpdateMarketState(
    item.id,
    item.state === MarketState.Enabled ? MarketState.Disabled : MarketState.Enabled,
  )
  tempLoading.value[item.id] = false
  item.state = item.state === 'enabled' ? 'disabled' : 'enabled'
}

filterStore.onSubmit<Record<string, string>>(async (payload) => {
  query.value = {
    page: 1,
    ...payload,
  }
})
</script>

<template>
  <div class="page-exchange-markets">
    <ZTablePro v-model:query="query" :columns="columns" :data-source="markets" :loading="loading">
      <template #head>
        <div class="bold-text text-xl">
          Markets
        </div>
      </template>
      <template #market="{ item }">
        <span>{{ item.base_unit.toUpperCase() }}/{{ item.quote_unit.toUpperCase() }}</span>
      </template>
      <template #state="{ item }">
        <ZSwitch :model-value="item.state === 'enabled' ? true : false" :loading="tempLoading[item.id]" size="medium" @change="changeStateMarket(item)" />
      </template>
      <template #action="{ item }">
        <NuxtLink :to="`/exchange/markets/update/${item.id}`">
          <ZIcon type="edit" class="text-[20px]" />
        </Nuxtlink>
      </template>
      <template #foot>
        <ZPagination v-model="query.page" v-model:page-size="query.limit" :loading="loading" :total="total" />
      </template>
    </ZTablePro>
  </div>
</template>

<style lang="less">
.page-exchange-markets {
  .state {
    max-width: 120px;
  }

  .action {
    max-width: 100px;
  }
}
</style>
