import { allowedRoles } from '~/constants'

export default defineNuxtRouteMiddleware(async (to) => {
  const userStore = useUserStore()
  const adminStore = useAdminStore()
  const config = useRuntimeConfig()
  const publicStore = usePublicStore()

  const baseURL = {
    server: config.public.apiUrl,
    client: '',
  }

  if (process.client) {
    baseURL.client = `${globalThis.location.origin}/api/v2/`
  }

  setBaseURL(baseURL)

  if (process.client) {
    if (userStore.isAuthenticated) {
      userStore.LoopGetLogged()

      if ([UserRole.SuperAdmin, UserRole.Admin].includes(userStore.role)) {
        if (!adminStore.statsDatabase.length) adminStore.visibleStatsWarning = true
        if (!adminStore.statsVault.length) adminStore.visibleStatsWarning = true

        const now = new Date().getTime()

        for (let i = 0; i < adminStore.statsDatabase.length; i++) {
          if (adminStore.statsDatabase[i].values.length) {
            const data = adminStore.statsDatabase[i].values[adminStore.statsDatabase[i].values.length - 1]
            const value = data[1]
            const timestamp = new Date((data[0] as number) * 1000).getTime()
            const difference = Math.abs(now - timestamp)
            if (value === '2' && difference < (5 * 60 * 1000)) continue
          }

          adminStore.visibleStatsWarning = true
        }

        for (let i = 0; i < adminStore.statsVault.length; i++) {
          if (adminStore.statsVault[i].values.length) {
            const data = adminStore.statsVault[i].values[adminStore.statsVault[i].values.length - 1]
            const value = Number(data[1]) / 1000
            const timestamp = new Date((data[0] as number) * 1000).getTime()
            const difference = Math.abs(now - timestamp)
            if (value <= 10 && difference < (5 * 60 * 1000)) continue
          }

          adminStore.visibleStatsWarning = true
        }
      }
    }
  }
  if (!publicStore.first_route) return

  publicStore.headers = useRequestHeaders()
  publicStore.first_route = false

  await userStore.GetLogged(adminStore.CallbackLogin)

  if ((!userStore.isAuthenticated || !allowedRoles.includes(userStore.role)) && !to.path.startsWith('/login')) {
    return navigateTo('/login')
  } else if (to.path === '/') {
    return navigateTo('/dashboard')
  }
})
